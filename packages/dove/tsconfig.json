{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "types": ["vite/client", "node"],
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": true,
    "skipLibCheck": true,
    "noImplicitAny": false,

    // Recommended
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,

    // `"noImplicitThis": true` is part of `strict`
    // Added again here in case some users decide to disable `strict`.
    // This enables stricter inference for data properties on `this`.
    "noImplicitThis": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    // Required in Vite
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": "./",
    "paths": {
      "@src/*": ["src/*"],
      "~/*": ["src/*"],
      "@vue/runtime-dom": ["src/helpers/override-vue.d.ts"],
      "vue": ["src/helpers/override-vue.d.ts"],
    }
  },
  "include": ["src"],
  "references": [{
    "path": "./tsconfig.node.json"
  }]
}