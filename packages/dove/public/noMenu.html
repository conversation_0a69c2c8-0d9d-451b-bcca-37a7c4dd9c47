<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>信鸽</title>
    <!-- 灵犀埋点 start -->
    <!-- Doc: -->
    <!-- https://km.sankuai.com/page/320736521#id-1.2%E5%85%A8%E6%96%B0%E9%A1%B9%E7%9B%AE%E6%8E%A5%E5%85%A55.0 -->
    <link rel="dns-prefetch" href="//lx.meituan.net"/>
    <link rel="dns-prefetch" href="//wreport1.meituan.net"/>
    <link rel="dns-prefetch" href="//report.meituan.com"/>
    <script>
        //以下这部分不能动
        !(function (win, doc, ns) {
            var cacheFunName = '_MeiTuanALogObject';
            win[cacheFunName] = ns;
            if (!win[ns]) {
            var _LX = function () {
                var t = function(){
                var inst = function(){
                    inst.q.push([arguments, +new Date]);
                }
                inst.q = [];
                t.q.push([arguments, inst]);
                return inst;
                }
                t.q = [];
                t.t = +new Date;
                _LX.q.push([arguments, t]);
                return t;
            };
            _LX.q = _LX.q || [];
            _LX.l = +new Date();
            win[ns] = _LX;
            }
        })(window, document, 'LXAnalytics');
        //以上这部分不能动

        //以下这部分是初始化代码

        //初始化配置：详情可以参考4.0接入文档第一节的配置项
        LXAnalytics('config', {
            defaultCategory:'waimai_m', //页面默认通道
            appName: 'com.sankuai.waimaicrmshareservice.fe.dove', // 页面应用名，可以改为当前工程appkey
            autoTrack: true,  // 是否开启部分事件自动埋点，预计在二期实现
            isSPA: true,   // 是否是单页面应用
          	mvDelay: 0 , // 合并mv事件的缓存秒数，0为关闭
            onWebviewAppearAutoPV: true, // 在app内嵌页时，容器显示/隐藏时的自动PV/PD开关
            onVisibilityChangeAutoPV: true, // 在pc端，切换tab页签时的自动PV/PD开关
            onWindowFocusAutoPV: true, // 在pc端，当window获得/失去焦点时的自动PV/PD开关
            onVCGap: 2, // pc端切换tab、window失焦时，间隔多久切回来才会触发自动PV/PD。最小有效值2，单位秒
            sessionScope:'top', //session种在一级域下还是当前域下，默认top为一级域，sub为当前域
            nativeReport:'on' //是否开启app内嵌页代报
        });

        // 5.0不再含有自动PV概念，若需要页面初始化时自动发送PV，则加入以下代码：
        // var defaultTracker = LXAnalytics('getTracker');
        // var defaultCid = '页面cid';
        // var lab = {};
        // var env = {};
        // defaultTracker('pageView', lab, env, defaultCid);

    </script>
    <script src="//lx.meituan.net/lx.5.min.js" async></script>
    <!-- 灵犀埋点 end -->


    <!-- raptor监控owl start -->
    <!-- Doc: -->
    <!-- https://km.sankuai.com/page/1291944429 -->
    <script>
        "use strict";!function(u,d){var t="owl",e="_Owl_",n="Owl",r="start",c="error",p="on"+c,f=u[p],h="addEventListener",l="attachEvent",v="isReady",b="dataSet";u[t]=u[t]||function(){try{u[t].q=u[t].q||[];var e=[].slice.call(arguments);e[0]===r?u[n]&&u[n][r]?u[n][r](e[1]):u[t].q.unshift(e):u[t].q.push(e)}catch(e){}},u[e]=u[e]||{preTasks:[],pageData:[],use:function(e,t){this[v]?u[n][e](t):this.preTasks.push({api:e,data:[t]})},run:function(t){if(!(t=this).runned){t.runned=!0,t[b]=[],u[p]=function(){t[v]||t[b].push({type:"jsError",data:arguments}),f&&f.apply(u,arguments)},u[h]&&u[h]("unhandledrejection",function(e){t[v]||t[b].push({type:"jsError",data:[e]})});var e=function(e){!t[v]&&e&&t[b].push({type:"resError",data:[e]})};u[h]?u[h](c,e,!0):u[l]&&u[l](p,e);var n="MutationObserver",r=u[n]||u["WebKit"+n]||u["Moz"+n],a=u.performance||u.WebKitPerformance,s="disableMutaObserver";if(r&&a&&a.now)try{var i=-1,o=u.navigator.userAgent;-1<o.indexOf("compatible")&&-1<o.indexOf("MSIE")?(new RegExp("MSIE (\\d+\\.\\d+);").test(o),i=parseFloat(RegExp.$1)):-1<o.indexOf("Trident")&&-1<o.indexOf("rv:11.0")&&(i=11),-1!==i&&i<=11?t[s]=!0:(t.observer=new r(function(e){t.pageData.push({mutations:e,startTime:a.now()})})).observe(d,{childList:!0,subtree:!0})}catch(e){}else t[s]=!0}}},u[e].runned||u[e].run()}(window,document);
    </script>
    <!-- raptor监控owl end -->

    <!-- SSO cookie防盗使用 - start -->
    <script type="text/javascript" src="https://s3plus.meituan.net/v1/mss_e6aa2b2c35b3432988a7a61f7ed79d37/h5guard/H5guard.js"></script>
    <script>if(window){function ssoGuardGetNum(res){if(res&&res.code===200&&res.data&&res.data.num&&typeof res.data.num==="string"){if(Object.defineProperty){Object.defineProperty(window,"__SSOGuardRandomNum__",{value:res.data.num,writable:false,configurable:false})}else{window.__SSOGuardRandomNum__=res.data.num}}}};</script>
    %VITE_SSO_GUARD_SCRIPTS%
    <!-- SSO cookie防盗使用 - end -->

    </head>
    <body>
        <div class="header"></div>
        <div class="page-sidebar"></div>
        <div class="page-content container-fluid" id="main-container">
            <div id="root"></div>
        </div>


        <!-- raptor监控owl start -->
        <script crossorigin="anonymous" src="//www.dpfile.com/app/owl/static/owl_1.10.1.js"></script>
        <script>
            // DOC: https://km.sankuai.com/page/192155549
            owl('start', {
                project: 'com.sankuai.waimaicrmshareservice.fe.dove', // 注意: 要求和在 Raptor/camel 中申请的项目名称保持一致
                devMode: !window.location.host.includes('meituan.com'), // TODO: find a better way
                pageUrl: window.location.href,
                autoCatch: {
                    fetch: true
                },
                enableLogTrace: true,
                resource: {
                    enableStatusCheck: true,
                    sampleApi: 1
                },
                page: {
                    sample: 1,
                    fstPerfAnalysis: true,
                    logSlowView: true,
                },
                error: {
                    formatUnhandledRejection: true
                },
                ignoreList: {
                    js: ['ResizeObserver loop limit exceeded'],
                    ajax: ['https?://dreport.meituan.net', 'https?://api.neixin.cn/dxlvs/open/v2/lgservers/json']
                },
                onBatchPush: function (instance) {
                      if (instance && instance.firstCategory === 'ajaxError' && instance.logContent === 'from: xhr abort') {
                          return undefined;
                      } else {
                          return instance;
                      }
                }
            });
        </script>
        <!-- raptor监控owl end -->

        <script
            src="//s3plus.sankuai.com/v1/mss_c4375b35f5cb4e678b5b55a48c40cf9d/waimai-mfe-common/authView/authView-1.1.js"
            type="text/javascript"
            charset="utf-8"
        ></script>
        <script type="text/javascript" charset="utf-8">
            Auth.init(function () {});
        </script>
    </body>
</html>
