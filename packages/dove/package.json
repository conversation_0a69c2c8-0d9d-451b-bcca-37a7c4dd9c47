{"name": "dove", "private": true, "version": "0.0.0", "scripts": {"dev": "VITE_API_PREFIX=/xianfu/api/dove vite", "start": "VITE_API_PREFIX=/xianfu/api/dove vite", "build": "vite build", "build-webstatic": "vite build", "preview": "vite preview", "sync-dts": "node node_modules/.bin/sync-dts", "parse-yapi": "parse-yapi", "postinstall": "yarn run sync-dts", "lint": "echo 0", "test": "echo 0", "dev:micro": "VITE_API_PREFIX=/xianfu/api/dove vite build --watch & vite preview --port 5001 --strictPort --host"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["bash -c tsc", "prettier --parser=typescript --write"]}, "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/x": "^1.1.0", "@antv/g2": "^5.2.12", "@antv/g2plot": "^2.4.32", "@cs/phone-sdk": "1.0.51", "@mfe/bellwether-route": "^1.0.9", "@mfe/cc-api-caller-pc": "^1.1.17", "@mfe/cc-ocrm-utils": "^0.0.6", "@mfe/cc-outbound": "3.1.9", "@mfe/vite-sso-plugin": "0.0.7", "@mtfe/audio-player": "^1.5.1", "@mtfe/sso-web": "^2.5.0", "@mtfe/xm-web-sdk": "4.7.6", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.0", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.0", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.0", "@radix-ui/react-dialog": "^1.1.0", "@radix-ui/react-dropdown-menu": "^2.1.0", "@radix-ui/react-hover-card": "^1.1.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.0", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.0", "@roo/roo": "^1.18.0", "@roo/roo-plus": "^0.6.8", "ahooks": "^3.8.0", "antd": "^5.3.2", "axios": "^1.3.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "dayjs": "^1.11.9", "history": "^5.0.0", "immutable": "^4.0.0-rc.12", "less": "^4.2.2", "lodash": "^4.17.21", "lucide-react": "^0.417.0", "markdown-it": "^14.1.0", "mss-sdk": "^2.0.0", "query-string": "8", "quill": "^2.0.2", "quill-mention": "latest", "rc-virtual-list": "^3.14.8", "react": "^18.2.0", "reactflow": "^11.11.4", "react-dom": "^18.2.0", "react-redux": "5.0.6", "redux": "^4.0.5", "redux-thunk": "^2.3.0", "unstated-next": "^1.1.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.3"}, "devDependencies": {"@originjs/vite-plugin-federation": "^1.3.5", "@types/node": "^22.1.0", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@typescript-eslint/eslint-plugin": "^5.55.0", "@typescript-eslint/parser": "^5.55.0", "@vitejs/plugin-react-swc": "^3.6.0", "eslint": "^8.36.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^2.7.1", "sass": "^1.63.2", "tailwindcss": "^3.4.4", "typescript": "^4.9.3", "vite": "^4.5.3", "vite-plugin-css-injected-by-js": "^3.5.1", "vite-plugin-html-template": "^1.2.0", "vite-plugin-mpa": "^1.2.0", "vite-plugin-qiankun": "^1.0.15", "vite-plugin-top-level-await": "^1.4.4", "vite-tsconfig-paths": "^4.0.5"}, "resolutions": {"cheerio": "1.0.0-rc.12", "@swc/core": "1.7.11", "@cs/phone-sdk": "1.0.51"}, "volta": {"node": "18.20.5", "yarn": "1.22.22"}}