import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react-swc';
import tsconfigPaths from 'vite-tsconfig-paths';
import htmlTemplate from 'vite-plugin-html-template';
import mpa from 'vite-plugin-mpa';
import qiankun from 'vite-plugin-qiankun';
import federation from '@originjs/vite-plugin-federation';
import { moduleFederationInjection } from './src/module/moduleFederation/plugin';
import topLevelAwait from 'vite-plugin-top-level-await';
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js';
import sso from '@mfe/vite-sso-plugin';
const exposes = {
    './callRecord': './src/pages/callRecord/root.tsx',
};
const moduleFederationInjectioPlugin = moduleFederationInjection(exposes);

// eslint-disable-next-line @typescript-eslint/no-var-requires
const uploadConfig = require('../../scripts/uploadConfig');

const SSO_COOKIE_SCRIPTS = {
    test: `<script type="text/javascript" src="https://ssosv.it.test.sankuai.com/sson/web/device/info/script/fast"></script>
      <script type="text/javascript" src="http://ssosv.it.test.sankuai.com/sson/web/device/info/script/fast"></script>`,
    prod: '<script type="text/javascript" src="https://ssosv.sankuai.com/sson/web/device/info/script/fast"></script>',
};

// const DOMAIN = 'https://wm.ocrm.meituan.com';
const DOMAIN =
    'https://selftest-250617-142207-293-sl-wm-ocrm.waimai.test.sankuai.com';

export default defineConfig(({ mode }) => {
    // vite如果需要在build time 感知.env的环境变量，需要使用loadEnv手动加载
    // 如果是runtime， 那么直接通过import.meta.env.VITE_XX拿就行了
    // 注意这个函数运行的时机是build time， node执行时，因此可以使用...解构process.env
    const env = { ...process.env, ...loadEnv(mode, process.cwd()) };
    const {
        PUBLIC_PATH = '',
        VITE_ROUTER_BASE = '',
        DEPLOY_ENV, // pipeline
        AWP_DEPLOY_ENV, // talos, test可能是test01
        WEBSTATIC_APPKEY = 'com.sankuai.waimaicrmshareservice.fe.dove',
        VITE_DEPLOY_ENV, //talos2.0
    } = env;
    const isProd =
        ['staging', 'production'].includes(DEPLOY_ENV) ||
        ['staging', 'production'].includes(AWP_DEPLOY_ENV);
    const deployEnv = JSON.stringify(
        VITE_DEPLOY_ENV || DEPLOY_ENV || AWP_DEPLOY_ENV || '',
    ).replace(/(\d+)/g, ''); // 把test01这种转成test
    const customSecret = env.VITE_ROUTER_CUSTOM_SECRET; // 自定义sso可以从env里面取
    const customClientId = env.VITE_ROUTER_CUSTOM_CLIENTID;
    return {
        plugins: [
            react(),
            tsconfigPaths(),
            mpa(),
            htmlTemplate({
                pages: {
                    generalDetailPage: {
                        template: './public/noMenu.html',
                    },
                    // bdDeciliter: {
                    //     template: './public/noMenu.html',
                    // },
                },
            }),
            qiankun('dove'),
            moduleFederationInjectioPlugin.plugin,
            federation({
                name: 'crm-dove',
                filename: 'remoteEntry.js',
                exposes: moduleFederationInjectioPlugin.exposes,
                shared: {
                    react: {},
                    antd: {},
                },
            }),
            {
                name: 'rm-css-import',
                apply: 'build',
                enforce: 'post',
                generateBundle(options, bundle: any) {
                    for (const file in bundle) {
                        if (file.includes('remoteEntry')) {
                            if (!bundle[file].code) {
                                return;
                            }
                            bundle[file].code = bundle[file].code.replace(
                                /\[\s*"[^"]*\.css"\s*(?:,\s*"[^"]*\.css"\s*)*\]/,
                                '[]',
                            );
                        }
                    }
                },
            },
            topLevelAwait({
                // The export name of top-level await promise for each chunk module
                promiseExportName: '__tla',
                // The function to generate import names of top-level await promise in each chunk module
                promiseImportName: i => `__tla_${i}`,
            }),
            cssInjectedByJsPlugin({ relativeCSSInjection: true }),
            // sso({
            //     clientId: customClientId,
            //     secret: customSecret,
            // }),
        ],
        base: uploadConfig.publicPath,
        build: {
            outDir: 'build' + VITE_ROUTER_BASE, // 指定生成构建代码的目录
            // 适配roo-plus，如果有roo-plus依赖，请自行打开注释
            rollupOptions: {
                plugins: [
                    {
                        name: 'disable-treeshake',
                        transform(_, id) {
                            if (
                                /@roo\/roo\/theme\/default\/\w+\.js$/.test(id)
                            ) {
                                return { moduleSideEffects: 'no-treeshake' };
                            }
                        },
                    },
                ],
            },
        },
        define: {
            'import.meta.env.VITE_APPKEY': JSON.stringify(WEBSTATIC_APPKEY),
            'import.meta.env.VITE_DEPLOY_ENV': JSON.stringify(deployEnv),
            'import.meta.env.VITE_SSO_GUARD_SCRIPTS': JSON.stringify(
                isProd ? SSO_COOKIE_SCRIPTS.prod : SSO_COOKIE_SCRIPTS.test,
            ),
        },
        server: {
            proxy: {
                '/doveim-public': {
                    target: 'https://selftest-250606-150528-542-sl-s3plus.vip.sankuai.com',
                    secure: false,
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/xianfu/api/dove/queryMassSendTask': {
                    target: DOMAIN,
                    secure: false,

                    changeOrigin: true,
                    logLevel: 'debug',
                    rewrite: path => path.replace(/^\/xianfu\/api/, ''),
                },
                '/xianfu/api/dove': {
                    target: DOMAIN,
                    secure: false,
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/xianfu/api-v2/dove': {
                    target: DOMAIN,
                    secure: false,
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/xianfu/api/common': {
                    target: DOMAIN,
                    secure: false,
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/xianfu/api-v2': {
                    target: DOMAIN,
                    secure: false,
                    changeOrigin: true,
                    logLevel: 'debug',
                },
                '/display': {
                    target: 'https://papi.sankuai.com/api/req/de881c40-47ce-462a-9787-8ab230850f18',
                    secure: false,
                    changeOrigin: true,
                    logLevel: 'debug',
                },
            },
        },
    };
});
