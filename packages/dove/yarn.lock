# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ai/mime-types-web@^1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@ai/mime-types-web/download/@ai/mime-types-web-1.1.2.tgz#6b3671a4afad3730fc088fd69a2088115ed31796"
  integrity sha1-azZxpK+tNzD8CI/WmiCIEV7TF5Y=

"@ai/mss-upload-js@^1.1.7":
  version "1.1.8"
  resolved "http://r.npm.sankuai.com/@ai/mss-upload-js/download/@ai/mss-upload-js-1.1.8.tgz#d4ad04117cc286f4da3d4e11fb0492026a7d1d23"
  integrity sha1-1K0EEXzChvTaPU4R+wSSAmp9HSM=
  dependencies:
    "@ai/mime-types-web" "^1.1.2"
    "@babel/polyfill" "^7.12.1"
    crypto-js "^3.3.0"
    fast-xml-parser "^4.2.2"
    magic-bytes.js "^1.0.14"
    rollup-plugin-terser "^7.0.2"

"@alloc/quick-lru@^5.2.0":
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/@alloc/quick-lru/download/@alloc/quick-lru-5.2.0.tgz#7bf68b20c0a350f936915fcae06f58e32007ce30"
  integrity sha1-e/aLIMCjUPk2kV/K4G9Y4yAHzjA=

"@ampproject/remapping@^2.2.0":
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/@ampproject/remapping/download/@ampproject/remapping-2.3.0.tgz#ed441b6fa600072520ce18b43d2c8cc8caecc7f4"
  integrity sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@ant-design/colors@^7.0.0", "@ant-design/colors@^7.1.0":
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/@ant-design/colors/download/@ant-design/colors-7.1.0.tgz#60eadfa2e21871d8948dac5d50b9f056062f8af3"
  integrity sha1-YOrfouIYcdiUjaxdULnwVgYvivM=
  dependencies:
    "@ctrl/tinycolor" "^3.6.1"

"@ant-design/cssinjs-utils@^1.1.0", "@ant-design/cssinjs-utils@^1.1.3":
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/@ant-design/cssinjs-utils/download/@ant-design/cssinjs-utils-1.1.3.tgz#5dd79126057920a6992d57b38dd84e2c0b707977"
  integrity sha1-XdeRJgV5IKaZLVezjdhOLAtweXc=
  dependencies:
    "@ant-design/cssinjs" "^1.21.0"
    "@babel/runtime" "^7.23.2"
    rc-util "^5.38.0"

"@ant-design/cssinjs@^1.21.0", "@ant-design/cssinjs@^1.21.1":
  version "1.22.1"
  resolved "http://r.npm.sankuai.com/@ant-design/cssinjs/download/@ant-design/cssinjs-1.22.1.tgz#00e943a6387a8080aba8b927df8236df3e07e964"
  integrity sha1-AOlDpjh6gICrqLkn34I23z4H6WQ=
  dependencies:
    "@babel/runtime" "^7.11.1"
    "@emotion/hash" "^0.8.0"
    "@emotion/unitless" "^0.7.5"
    classnames "^2.3.1"
    csstype "^3.1.3"
    rc-util "^5.35.0"
    stylis "^4.3.4"

"@ant-design/fast-color@^2.0.6":
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/@ant-design/fast-color/download/@ant-design/fast-color-2.0.6.tgz#ab4d4455c1542c9017d367c2fa8ca3e4215d0ba2"
  integrity sha1-q01EVcFULJAX02fC+oyj5CFdC6I=
  dependencies:
    "@babel/runtime" "^7.24.7"

"@ant-design/icons-svg@^4.4.0":
  version "4.4.2"
  resolved "http://r.npm.sankuai.com/@ant-design/icons-svg/download/@ant-design/icons-svg-4.4.2.tgz#ed2be7fb4d82ac7e1d45a54a5b06d6cecf8be6f6"
  integrity sha1-7Svn+02CrH4dRaVKWwbWzs+L5vY=

"@ant-design/icons@^5.2.6", "@ant-design/icons@^5.5.2":
  version "5.5.2"
  resolved "http://r.npm.sankuai.com/@ant-design/icons/download/@ant-design/icons-5.5.2.tgz#c4567943cc2b7c6dbe9cae68c06ffa35f755dc0d"
  integrity sha1-xFZ5Q8wrfG2+nK5owG/6NfdV3A0=
  dependencies:
    "@ant-design/colors" "^7.0.0"
    "@ant-design/icons-svg" "^4.4.0"
    "@babel/runtime" "^7.24.8"
    classnames "^2.2.6"
    rc-util "^5.31.1"

"@ant-design/icons@^5.4.0":
  version "5.6.1"
  resolved "http://r.npm.sankuai.com/@ant-design/icons/download/@ant-design/icons-5.6.1.tgz#7290fcdc3d96ff3fca793ed399053cd29ad5dbd3"
  integrity sha1-cpD83D2W/z/KeT7TmQU80prV29M=
  dependencies:
    "@ant-design/colors" "^7.0.0"
    "@ant-design/icons-svg" "^4.4.0"
    "@babel/runtime" "^7.24.8"
    classnames "^2.2.6"
    rc-util "^5.31.1"

"@ant-design/react-slick@~1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@ant-design/react-slick/download/@ant-design/react-slick-1.1.2.tgz#f84ce3e4d0dc941f02b16f1d1d6d7a371ffbb4f1"
  integrity sha1-+Ezj5NDclB8CsW8dHW16Nx/7tPE=
  dependencies:
    "@babel/runtime" "^7.10.4"
    classnames "^2.2.5"
    json2mq "^0.2.0"
    resize-observer-polyfill "^1.5.1"
    throttle-debounce "^5.0.0"

"@ant-design/x@^1.1.0":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@ant-design/x/download/@ant-design/x-1.1.0.tgz#bd0c9d3dd1280a5e9145701109fdc999ac71f536"
  integrity sha1-vQydPdEoCl6RRXARCf3Jmaxx9TY=
  dependencies:
    "@ant-design/colors" "^7.1.0"
    "@ant-design/cssinjs" "^1.21.1"
    "@ant-design/cssinjs-utils" "^1.1.0"
    "@ant-design/fast-color" "^2.0.6"
    "@ant-design/icons" "^5.4.0"
    "@babel/runtime" "^7.25.6"
    classnames "^2.5.1"
    rc-motion "^2.9.2"
    rc-util "^5.43.0"

"@antv/adjust@^0.2.1":
  version "0.2.5"
  resolved "http://r.npm.sankuai.com/@antv/adjust/download/@antv/adjust-0.2.5.tgz#bb37bb4a0a87ca3f4b660848bc9ac07f02bcf5db"
  integrity sha1-uze7SgqHyj9LZghIvJrAfwK89ds=
  dependencies:
    "@antv/util" "~2.0.0"
    tslib "^1.10.0"

"@antv/attr@^0.3.1":
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/@antv/attr/download/@antv/attr-0.3.5.tgz#0708c74fed5ad6ee03ad1e2913099ed8248f7ebf"
  integrity sha1-BwjHT+1a1u4DrR4pEwme2CSPfr8=
  dependencies:
    "@antv/color-util" "^2.0.1"
    "@antv/scale" "^0.3.0"
    "@antv/util" "~2.0.0"
    tslib "^2.3.1"

"@antv/color-util@^2.0.1", "@antv/color-util@^2.0.2", "@antv/color-util@^2.0.3", "@antv/color-util@^2.0.6":
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/@antv/color-util/download/@antv/color-util-2.0.6.tgz#5e129bb9ce3f2b9309b52102b3dc929430ccc016"
  integrity sha1-XhKbuc4/K5MJtSECs9ySlDDMwBY=
  dependencies:
    "@antv/util" "^2.0.9"
    tslib "^2.0.3"

"@antv/component@^0.8.27":
  version "0.8.35"
  resolved "http://r.npm.sankuai.com/@antv/component/download/@antv/component-0.8.35.tgz#1d5b8e11bd496cb505e646f505f5f58f0c5173e9"
  integrity sha1-HVuOEb1JbLUF5kb1BfX1jwxRc+k=
  dependencies:
    "@antv/color-util" "^2.0.3"
    "@antv/dom-util" "~2.0.1"
    "@antv/g-base" "^0.5.9"
    "@antv/matrix-util" "^3.1.0-beta.1"
    "@antv/path-util" "~2.0.7"
    "@antv/scale" "~0.3.1"
    "@antv/util" "~2.0.0"
    fecha "~4.2.0"
    tslib "^2.0.3"

"@antv/component@^2.1.2":
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/@antv/component/download/@antv/component-2.1.2.tgz#578a08abc1e70755dd2a2bad85ad7015dcfecf84"
  integrity sha1-V4oIq8HnB1XdKiutha1wFdz+z4Q=
  dependencies:
    "@antv/g" "^6.1.11"
    "@antv/scale" "^0.4.16"
    "@antv/util" "^3.3.10"
    svg-path-parser "^1.1.0"

"@antv/coord@^0.3.0":
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/@antv/coord/download/@antv/coord-0.3.1.tgz#982e261d8a1e06a198eb518ea7acc20ed875a019"
  integrity sha1-mC4mHYoeBqGY61GOp6zCDth1oBk=
  dependencies:
    "@antv/matrix-util" "^3.1.0-beta.2"
    "@antv/util" "~2.0.12"
    tslib "^2.1.0"

"@antv/coord@^0.4.7":
  version "0.4.7"
  resolved "http://r.npm.sankuai.com/@antv/coord/download/@antv/coord-0.4.7.tgz#3ef6c6e3f9ca0f024b90888549946061f35df77a"
  integrity sha1-PvbG4/nKDwJLkIiFSZRgYfNd93o=
  dependencies:
    "@antv/scale" "^0.4.12"
    "@antv/util" "^2.0.13"
    gl-matrix "^3.4.3"

"@antv/dom-util@^2.0.2", "@antv/dom-util@~2.0.1":
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/@antv/dom-util/download/@antv/dom-util-2.0.4.tgz#b09b56c56fec42896fc856edad56b595b47ab514"
  integrity sha1-sJtWxW/sQolvyFbtrVa1lbR6tRQ=
  dependencies:
    tslib "^2.0.3"

"@antv/event-emitter@^0.1.1", "@antv/event-emitter@^0.1.2", "@antv/event-emitter@^0.1.3", "@antv/event-emitter@~0.1.0":
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/@antv/event-emitter/download/@antv/event-emitter-0.1.3.tgz#3e06323b9dcd55a3241ddc7c5458cfabd2095164"
  integrity sha1-PgYyO53NVaMkHdx8VFjPq9IJUWQ=

"@antv/g-base@^0.5.11", "@antv/g-base@^0.5.12", "@antv/g-base@^0.5.9", "@antv/g-base@~0.5.6":
  version "0.5.16"
  resolved "http://r.npm.sankuai.com/@antv/g-base/download/@antv/g-base-0.5.16.tgz#22a0cbbfc810e6292e4d25e5708d0abe165912bf"
  integrity sha1-IqDLv8gQ5ikuTSXlcI0KvhZZEr8=
  dependencies:
    "@antv/event-emitter" "^0.1.1"
    "@antv/g-math" "^0.1.9"
    "@antv/matrix-util" "^3.1.0-beta.1"
    "@antv/path-util" "~2.0.5"
    "@antv/util" "~2.0.13"
    "@types/d3-timer" "^2.0.0"
    d3-ease "^1.0.5"
    d3-interpolate "^3.0.1"
    d3-timer "^1.0.9"
    detect-browser "^5.1.0"
    tslib "^2.0.3"

"@antv/g-camera-api@2.0.35":
  version "2.0.35"
  resolved "http://r.npm.sankuai.com/@antv/g-camera-api/download/@antv/g-camera-api-2.0.35.tgz#0c8f5824f4525b2fed9941170aa9e668b9c5734f"
  integrity sha1-DI9YJPRSWy/tmUEXCqnmaLnFc08=
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-canvas@^2.0.29":
  version "2.0.40"
  resolved "http://r.npm.sankuai.com/@antv/g-canvas/download/@antv/g-canvas-2.0.40.tgz#4d550e891c6bc2e51acc42eb877c21e722add68d"
  integrity sha1-TVUOiRxrwuUazELrh3wh5yKt1o0=
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/g-plugin-canvas-path-generator" "2.1.16"
    "@antv/g-plugin-canvas-picker" "2.1.19"
    "@antv/g-plugin-canvas-renderer" "2.2.19"
    "@antv/g-plugin-dom-interaction" "2.1.21"
    "@antv/g-plugin-html-renderer" "2.1.21"
    "@antv/g-plugin-image-loader" "2.1.19"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g-canvas@~0.5.10":
  version "0.5.17"
  resolved "http://r.npm.sankuai.com/@antv/g-canvas/download/@antv/g-canvas-0.5.17.tgz#2e0d263a355e167b9da5e606fbd1ad1500474fcf"
  integrity sha1-Lg0mOjVeFnudpeYG+9GtFQBHT88=
  dependencies:
    "@antv/g-base" "^0.5.12"
    "@antv/g-math" "^0.1.9"
    "@antv/matrix-util" "^3.1.0-beta.1"
    "@antv/path-util" "~2.0.5"
    "@antv/util" "~2.0.0"
    gl-matrix "^3.0.0"
    tslib "^2.0.3"

"@antv/g-dom-mutation-observer-api@2.0.32":
  version "2.0.32"
  resolved "http://r.npm.sankuai.com/@antv/g-dom-mutation-observer-api/download/@antv/g-dom-mutation-observer-api-2.0.32.tgz#171361ff66970c620fd5320b51dd79e4add3631f"
  integrity sha1-FxNh/2aXDGIP1TILUd155K3TYx8=
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@babel/runtime" "^7.25.6"

"@antv/g-lite@2.2.16":
  version "2.2.16"
  resolved "http://r.npm.sankuai.com/@antv/g-lite/download/@antv/g-lite-2.2.16.tgz#3aad1e45c7dca71d536ec7874d5dfb8a9ed4fdcb"
  integrity sha1-Oq0eRcfcpx1TbseHTV37ip7U/cs=
  dependencies:
    "@antv/g-math" "3.0.0"
    "@antv/util" "^3.3.5"
    "@antv/vendor" "^1.0.3"
    "@babel/runtime" "^7.25.6"
    eventemitter3 "^5.0.1"
    gl-matrix "^3.4.3"
    rbush "^3.0.1"
    tslib "^2.5.3"

"@antv/g-math@3.0.0":
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/@antv/g-math/download/@antv/g-math-3.0.0.tgz#834d993391546e39ae5a30452572fdc49a7c57ec"
  integrity sha1-g02ZM5FUbjmuWjBFJXL9xJp8V+w=
  dependencies:
    "@antv/util" "^3.3.5"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-math@^0.1.9":
  version "0.1.9"
  resolved "http://r.npm.sankuai.com/@antv/g-math/download/@antv/g-math-0.1.9.tgz#1f981b9aebf5c024f284389aa3e5cba8cefa1f28"
  integrity sha1-H5gbmuv1wCTyhDiao+XLqM76Hyg=
  dependencies:
    "@antv/util" "~2.0.0"
    gl-matrix "^3.0.0"

"@antv/g-plugin-canvas-path-generator@2.1.16":
  version "2.1.16"
  resolved "http://r.npm.sankuai.com/@antv/g-plugin-canvas-path-generator/download/@antv/g-plugin-canvas-path-generator-2.1.16.tgz#f60dfa687027aba12aed90d64839b97b2c3c3be0"
  integrity sha1-9g36aHAnq6Eq7ZDWSDm5eyw8O+A=
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/g-math" "3.0.0"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g-plugin-canvas-picker@2.1.19":
  version "2.1.19"
  resolved "http://r.npm.sankuai.com/@antv/g-plugin-canvas-picker/download/@antv/g-plugin-canvas-picker-2.1.19.tgz#d4503b1819808703c765a51b0e3bccef5de4e4cd"
  integrity sha1-1FA7GBmAhwPHZaUbDjvM713k5M0=
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/g-math" "3.0.0"
    "@antv/g-plugin-canvas-path-generator" "2.1.16"
    "@antv/g-plugin-canvas-renderer" "2.2.19"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-plugin-canvas-renderer@2.2.19":
  version "2.2.19"
  resolved "http://r.npm.sankuai.com/@antv/g-plugin-canvas-renderer/download/@antv/g-plugin-canvas-renderer-2.2.19.tgz#90911b38ec15edbbc946f7201ce9090bd22a43cd"
  integrity sha1-kJEbOOwV7bvJRvcgHOkJC9IqQ80=
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/g-math" "3.0.0"
    "@antv/g-plugin-canvas-path-generator" "2.1.16"
    "@antv/g-plugin-image-loader" "2.1.19"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-plugin-dom-interaction@2.1.21":
  version "2.1.21"
  resolved "http://r.npm.sankuai.com/@antv/g-plugin-dom-interaction/download/@antv/g-plugin-dom-interaction-2.1.21.tgz#7a764b270a2da8fc367a763231071f38d5ac49cf"
  integrity sha1-enZLJwotqPw2enYyMQcfONWsSc8=
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g-plugin-dragndrop@^2.0.22":
  version "2.0.32"
  resolved "http://r.npm.sankuai.com/@antv/g-plugin-dragndrop/download/@antv/g-plugin-dragndrop-2.0.32.tgz#31559d38c5401a5116a6a8b7c64ba8c939208186"
  integrity sha1-MVWdOMVAGlEWpqi3xkuoyTkggYY=
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g-plugin-html-renderer@2.1.21":
  version "2.1.21"
  resolved "http://r.npm.sankuai.com/@antv/g-plugin-html-renderer/download/@antv/g-plugin-html-renderer-2.1.21.tgz#2077e5eae60c818962f275f3cf73044a0c8aaa88"
  integrity sha1-IHfl6uYMgYli8nXzz3MESgyKqog=
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-plugin-image-loader@2.1.19":
  version "2.1.19"
  resolved "http://r.npm.sankuai.com/@antv/g-plugin-image-loader/download/@antv/g-plugin-image-loader-2.1.19.tgz#c94e63de91b99c7384ecd758687e78a21b1a202b"
  integrity sha1-yU5j3pG5nHOE7NdYaH54ohsaICs=
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    gl-matrix "^3.4.3"
    tslib "^2.5.3"

"@antv/g-svg@~0.5.6":
  version "0.5.7"
  resolved "http://r.npm.sankuai.com/@antv/g-svg/download/@antv/g-svg-0.5.7.tgz#d63db5f8590a5f3ceab097c183ec80ed143f0a50"
  integrity sha1-1j21+FkKXzzqsJfBg+yA7RQ/ClA=
  dependencies:
    "@antv/g-base" "^0.5.12"
    "@antv/g-math" "^0.1.9"
    "@antv/util" "~2.0.0"
    detect-browser "^5.0.0"
    tslib "^2.0.3"

"@antv/g-web-animations-api@2.1.21":
  version "2.1.21"
  resolved "http://r.npm.sankuai.com/@antv/g-web-animations-api/download/@antv/g-web-animations-api-2.1.21.tgz#4f8fc78d766a0dc4d51d4e37a917a91c59eb02fb"
  integrity sha1-T4/HjXZqDcTVHU43qRepHFnrAvs=
  dependencies:
    "@antv/g-lite" "2.2.16"
    "@antv/util" "^3.3.5"
    "@babel/runtime" "^7.25.6"
    tslib "^2.5.3"

"@antv/g2@^4.1.26":
  version "4.2.11"
  resolved "http://r.npm.sankuai.com/@antv/g2/download/@antv/g2-4.2.11.tgz#a3b257aca4db6004a0c7fe002dc9272795f9c18b"
  integrity sha1-o7JXrKTbYASgx/4ALcknJ5X5wYs=
  dependencies:
    "@antv/adjust" "^0.2.1"
    "@antv/attr" "^0.3.1"
    "@antv/color-util" "^2.0.2"
    "@antv/component" "^0.8.27"
    "@antv/coord" "^0.3.0"
    "@antv/dom-util" "^2.0.2"
    "@antv/event-emitter" "~0.1.0"
    "@antv/g-base" "~0.5.6"
    "@antv/g-canvas" "~0.5.10"
    "@antv/g-svg" "~0.5.6"
    "@antv/matrix-util" "^3.1.0-beta.3"
    "@antv/path-util" "^2.0.15"
    "@antv/scale" "^0.3.14"
    "@antv/util" "~2.0.5"
    tslib "^2.0.0"

"@antv/g2@^5.2.12":
  version "5.2.12"
  resolved "http://r.npm.sankuai.com/@antv/g2/download/@antv/g2-5.2.12.tgz#c19522fc005129bc2d5b46203832581bed89e99d"
  integrity sha1-wZUi/ABRKbwtW0YgODJYG+2J6Z0=
  dependencies:
    "@antv/component" "^2.1.2"
    "@antv/coord" "^0.4.7"
    "@antv/event-emitter" "^0.1.3"
    "@antv/g" "^6.1.11"
    "@antv/g-canvas" "^2.0.29"
    "@antv/g-plugin-dragndrop" "^2.0.22"
    "@antv/scale" "^0.4.16"
    "@antv/util" "^3.3.10"
    "@antv/vendor" "^1.0.8"
    flru "^1.0.2"
    fmin "0.0.2"
    pdfast "^0.2.0"

"@antv/g2plot@^2.4.32":
  version "2.4.32"
  resolved "http://r.npm.sankuai.com/@antv/g2plot/download/@antv/g2plot-2.4.32.tgz#1fecc4bcca82a790fc4e5e5f6aac03b9469a012e"
  integrity sha1-H+zEvMqCp5D8Tl5faqwDuUaaAS4=
  dependencies:
    "@antv/color-util" "^2.0.6"
    "@antv/event-emitter" "^0.1.2"
    "@antv/g-base" "^0.5.11"
    "@antv/g2" "^4.1.26"
    "@antv/matrix-util" "^3.1.0-beta.2"
    "@antv/path-util" "^3.0.1"
    "@antv/scale" "^0.3.18"
    "@antv/util" "^2.0.17"
    d3-hierarchy "^2.0.0"
    d3-regression "^1.3.5"
    fmin "^0.0.2"
    pdfast "^0.2.0"
    size-sensor "^1.0.1"
    tslib "^2.0.3"

"@antv/g@^6.1.11":
  version "6.1.21"
  resolved "http://r.npm.sankuai.com/@antv/g/download/@antv/g-6.1.21.tgz#d64e5dc8ab07a9ec6b14ed671923b7dfe4b4fc05"
  integrity sha1-1k5dyKsHqexrFO1nGSO33+S0/AU=
  dependencies:
    "@antv/g-camera-api" "2.0.35"
    "@antv/g-dom-mutation-observer-api" "2.0.32"
    "@antv/g-lite" "2.2.16"
    "@antv/g-web-animations-api" "2.1.21"
    "@babel/runtime" "^7.25.6"

"@antv/matrix-util@^3.0.4":
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/@antv/matrix-util/download/@antv/matrix-util-3.0.4.tgz#ea13f158aa2fb4ba2fb8d6b6b561ec467ea3ac20"
  integrity sha1-6hPxWKovtLovuNa2tWHsRn6jrCA=
  dependencies:
    "@antv/util" "^2.0.9"
    gl-matrix "^3.3.0"
    tslib "^2.0.3"

"@antv/matrix-util@^3.1.0-beta.1", "@antv/matrix-util@^3.1.0-beta.2", "@antv/matrix-util@^3.1.0-beta.3":
  version "3.1.0-beta.3"
  resolved "http://r.npm.sankuai.com/@antv/matrix-util/download/@antv/matrix-util-3.1.0-beta.3.tgz#e061de8fa7be04605a155c69cc5ce9082eedddee"
  integrity sha1-4GHej6e+BGBaFVxpzFzpCC7t3e4=
  dependencies:
    "@antv/util" "^2.0.9"
    gl-matrix "^3.4.3"
    tslib "^2.0.3"

"@antv/path-util@^2.0.15", "@antv/path-util@~2.0.5", "@antv/path-util@~2.0.7":
  version "2.0.15"
  resolved "http://r.npm.sankuai.com/@antv/path-util/download/@antv/path-util-2.0.15.tgz#a6f691dfc8b7bce5be7f0aabb5bd614964325631"
  integrity sha1-pvaR38i3vOW+fwqrtb1hSWQyVjE=
  dependencies:
    "@antv/matrix-util" "^3.0.4"
    "@antv/util" "^2.0.9"
    tslib "^2.0.3"

"@antv/path-util@^3.0.1":
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/@antv/path-util/download/@antv/path-util-3.0.1.tgz#42fd84222824e8ad8d1bde70f164a05924870d4a"
  integrity sha1-Qv2EIigk6K2NG95w8WSgWSSHDUo=
  dependencies:
    gl-matrix "^3.1.0"
    lodash-es "^4.17.21"
    tslib "^2.0.3"

"@antv/scale@^0.3.0", "@antv/scale@^0.3.14", "@antv/scale@^0.3.18", "@antv/scale@~0.3.1":
  version "0.3.18"
  resolved "http://r.npm.sankuai.com/@antv/scale/download/@antv/scale-0.3.18.tgz#b911f431b3e0b9547b6a65f66d0d3fa295b5ef32"
  integrity sha1-uRH0MbPguVR7amX2bQ0/opW17zI=
  dependencies:
    "@antv/util" "~2.0.3"
    fecha "~4.2.0"
    tslib "^2.0.0"

"@antv/scale@^0.4.12", "@antv/scale@^0.4.16":
  version "0.4.16"
  resolved "http://r.npm.sankuai.com/@antv/scale/download/@antv/scale-0.4.16.tgz#60557470668ccfe5217e482a01f05c0cbb706b62"
  integrity sha1-YFV0cGaMz+UhfkgqAfBcDLtwa2I=
  dependencies:
    "@antv/util" "^3.3.7"
    color-string "^1.5.5"
    fecha "^4.2.1"

"@antv/util@^2.0.13", "@antv/util@^2.0.17", "@antv/util@^2.0.9", "@antv/util@~2.0.0", "@antv/util@~2.0.12", "@antv/util@~2.0.13", "@antv/util@~2.0.3", "@antv/util@~2.0.5":
  version "2.0.17"
  resolved "http://r.npm.sankuai.com/@antv/util/download/@antv/util-2.0.17.tgz#e8ef42aca7892815b229269f3dd10c6b3c7597a9"
  integrity sha1-6O9CrKeJKBWyKSafPdEMazx1l6k=
  dependencies:
    csstype "^3.0.8"
    tslib "^2.0.3"

"@antv/util@^3.3.10", "@antv/util@^3.3.5", "@antv/util@^3.3.7":
  version "3.3.10"
  resolved "http://r.npm.sankuai.com/@antv/util/download/@antv/util-3.3.10.tgz#6fb2560c0f42df61f824e1f995a1ed1bdb00eb9a"
  integrity sha1-b7JWDA9C32H4JOH5laHtG9sA65o=
  dependencies:
    fast-deep-equal "^3.1.3"
    gl-matrix "^3.3.0"
    tslib "^2.3.1"

"@antv/vendor@^1.0.3", "@antv/vendor@^1.0.8":
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/@antv/vendor/download/@antv/vendor-1.0.10.tgz#e0d7ab6ed25946f4b6ad1b5da481182639faea41"
  integrity sha1-4NerbtJZRvS2rRtdpIEYJjn66kE=
  dependencies:
    "@types/d3-array" "^3.2.1"
    "@types/d3-color" "^3.1.3"
    "@types/d3-dispatch" "^3.0.6"
    "@types/d3-dsv" "^3.0.7"
    "@types/d3-fetch" "^3.0.7"
    "@types/d3-force" "^3.0.10"
    "@types/d3-format" "^3.0.4"
    "@types/d3-geo" "^3.1.0"
    "@types/d3-hierarchy" "^3.1.7"
    "@types/d3-interpolate" "^3.0.4"
    "@types/d3-path" "^3.1.0"
    "@types/d3-quadtree" "^3.0.6"
    "@types/d3-random" "^3.0.3"
    "@types/d3-scale" "^4.0.9"
    "@types/d3-scale-chromatic" "^3.1.0"
    "@types/d3-shape" "^3.1.7"
    "@types/d3-time" "^3.0.4"
    "@types/d3-timer" "^3.0.2"
    d3-array "^3.2.4"
    d3-color "^3.1.0"
    d3-dispatch "^3.0.1"
    d3-dsv "^3.0.1"
    d3-fetch "^3.0.1"
    d3-force "^3.0.0"
    d3-force-3d "^3.0.5"
    d3-format "^3.1.0"
    d3-geo "^3.1.1"
    d3-geo-projection "^4.0.0"
    d3-hierarchy "^3.1.2"
    d3-interpolate "^3.0.1"
    d3-path "^3.1.0"
    d3-quadtree "^3.0.1"
    d3-random "^3.0.1"
    d3-regression "^1.3.10"
    d3-scale "^4.0.2"
    d3-scale-chromatic "^3.1.0"
    d3-shape "^3.2.0"
    d3-time "^3.1.0"
    d3-timer "^3.0.1"

"@babel/cli@^7.8.0":
  version "7.26.4"
  resolved "http://r.npm.sankuai.com/@babel/cli/download/@babel/cli-7.26.4.tgz#4101ff8ee5de8447a6c395397a97921056411d20"
  integrity sha1-QQH/juXehEemw5U5epeSEFZBHSA=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.25"
    commander "^6.2.0"
    convert-source-map "^2.0.0"
    fs-readdir-recursive "^1.1.0"
    glob "^7.2.0"
    make-dir "^2.1.0"
    slash "^2.0.0"
  optionalDependencies:
    "@nicolo-ribaudo/chokidar-2" "2.1.8-no-fsevents.3"
    chokidar "^3.6.0"

"@babel/code-frame@^7.10.4", "@babel/code-frame@^7.25.9", "@babel/code-frame@^7.26.0", "@babel/code-frame@^7.26.2":
  version "7.26.2"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.26.2.tgz#4b5fab97d33338eff916235055f0ebc21e573a85"
  integrity sha1-S1+rl9MzOO/5FiNQVfDrwh5XOoU=
  dependencies:
    "@babel/helper-validator-identifier" "^7.25.9"
    js-tokens "^4.0.0"
    picocolors "^1.0.0"

"@babel/code-frame@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/code-frame/download/@babel/code-frame-7.27.1.tgz#200f715e66d52a23b221a9435534a91cc13ad5be"
  integrity sha1-IA9xXmbVKiOyIalDVTSpHME61b4=
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/compat-data@^7.22.6", "@babel/compat-data@^7.25.9", "@babel/compat-data@^7.26.0":
  version "7.26.3"
  resolved "http://r.npm.sankuai.com/@babel/compat-data/download/@babel/compat-data-7.26.3.tgz#99488264a56b2aded63983abd6a417f03b92ed02"
  integrity sha1-mUiCZKVrKt7WOYOr1qQX8DuS7QI=

"@babel/core@^7.7.5":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/core/download/@babel/core-7.26.0.tgz#d78b6023cc8f3114ccf049eb219613f74a747b40"
  integrity sha1-14tgI8yPMRTM8EnrIZYT90p0e0A=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.26.0"
    "@babel/generator" "^7.26.0"
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-module-transforms" "^7.26.0"
    "@babel/helpers" "^7.26.0"
    "@babel/parser" "^7.26.0"
    "@babel/template" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.26.0"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.26.0", "@babel/generator@^7.26.3", "@babel/generator@^7.7.4":
  version "7.26.3"
  resolved "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.26.3.tgz#ab8d4360544a425c90c248df7059881f4b2ce019"
  integrity sha1-q41DYFRKQlyQwkjfcFmIH0ss4Bk=
  dependencies:
    "@babel/parser" "^7.26.3"
    "@babel/types" "^7.26.3"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/generator@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/generator/download/@babel/generator-7.27.1.tgz#862d4fad858f7208edd487c28b58144036b76230"
  integrity sha1-hi1PrYWPcgjt1IfCi1gUQDa3YjA=
  dependencies:
    "@babel/parser" "^7.27.1"
    "@babel/types" "^7.27.1"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.25.9.tgz#d8eac4d2dc0d7b6e11fa6e535332e0d3184f06b4"
  integrity sha1-2OrE0twNe24R+m5TUzLg0xhPBrQ=
  dependencies:
    "@babel/types" "^7.25.9"

"@babel/helper-compilation-targets@^7.22.6", "@babel/helper-compilation-targets@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-compilation-targets/download/@babel/helper-compilation-targets-7.25.9.tgz#55af025ce365be3cdc0c1c1e56c6af617ce88875"
  integrity sha1-Va8CXONlvjzcDBweVsavYXzoiHU=
  dependencies:
    "@babel/compat-data" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-create-class-features-plugin@^7.18.6", "@babel/helper-create-class-features-plugin@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.25.9.tgz#7644147706bb90ff613297d49ed5266bde729f83"
  integrity sha1-dkQUdwa7kP9hMpfUntUma95yn4M=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-member-expression-to-functions" "^7.25.9"
    "@babel/helper-optimise-call-expression" "^7.25.9"
    "@babel/helper-replace-supers" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    semver "^6.3.1"

"@babel/helper-create-regexp-features-plugin@^7.18.6", "@babel/helper-create-regexp-features-plugin@^7.25.9":
  version "7.26.3"
  resolved "http://r.npm.sankuai.com/@babel/helper-create-regexp-features-plugin/download/@babel/helper-create-regexp-features-plugin-7.26.3.tgz#5169756ecbe1d95f7866b90bb555b022595302a0"
  integrity sha1-UWl1bsvh2V94ZrkLtVWwIllTAqA=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    regexpu-core "^6.2.0"
    semver "^6.3.1"

"@babel/helper-define-polyfill-provider@^0.6.2", "@babel/helper-define-polyfill-provider@^0.6.3":
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/@babel/helper-define-polyfill-provider/download/@babel/helper-define-polyfill-provider-0.6.3.tgz#f4f2792fae2ef382074bc2d713522cf24e6ddb21"
  integrity sha1-9PJ5L64u84IHS8LXE1Is8k5t2yE=
  dependencies:
    "@babel/helper-compilation-targets" "^7.22.6"
    "@babel/helper-plugin-utils" "^7.22.5"
    debug "^4.1.1"
    lodash.debounce "^4.0.8"
    resolve "^1.14.2"

"@babel/helper-member-expression-to-functions@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.25.9.tgz#9dfffe46f727005a5ea29051ac835fb735e4c1a3"
  integrity sha1-nf/+RvcnAFpeopBRrINftzXkwaM=
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-imports@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.25.9.tgz#e7f8d20602ebdbf9ebbea0a0751fb0f2a4141715"
  integrity sha1-5/jSBgLr2/nrvqCgdR+w8qQUFxU=
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-module-imports@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-imports/download/@babel/helper-module-imports-7.27.1.tgz#7ef769a323e2655e126673bb6d2d6913bbead204"
  integrity sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.25.9", "@babel/helper-module-transforms@^7.26.0":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.26.0.tgz#8ce54ec9d592695e58d84cd884b7b5c6a2fdeeae"
  integrity sha1-jOVOydWSaV5Y2EzYhLe1xqL97q4=
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-optimise-call-expression@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.25.9.tgz#3324ae50bae7e2ab3c33f60c9a877b6a0146b54e"
  integrity sha1-MySuULrn4qs8M/YMmod7agFGtU4=
  dependencies:
    "@babel/types" "^7.25.9"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.18.6", "@babel/helper-plugin-utils@^7.22.5", "@babel/helper-plugin-utils@^7.25.9", "@babel/helper-plugin-utils@^7.8.0":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.25.9.tgz#9cbdd63a9443a2c92a725cca7ebca12cc8dd9f46"
  integrity sha1-nL3WOpRDoskqclzKfryhLMjdn0Y=

"@babel/helper-plugin-utils@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.27.1.tgz#ddb2f876534ff8013e6c2b299bf4d39b3c51d44c"
  integrity sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=

"@babel/helper-remap-async-to-generator@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.25.9.tgz#e53956ab3d5b9fb88be04b3e2f31b523afd34b92"
  integrity sha1-5TlWqz1bn7iL4Es+LzG1I6/TS5I=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-wrap-function" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-replace-supers@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.25.9.tgz#ba447224798c3da3f8713fc272b145e33da6a5c5"
  integrity sha1-ukRyJHmMPaP4cT/CcrFF4z2mpcU=
  dependencies:
    "@babel/helper-member-expression-to-functions" "^7.25.9"
    "@babel/helper-optimise-call-expression" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/helper-skip-transparent-expression-wrappers@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-skip-transparent-expression-wrappers/download/@babel/helper-skip-transparent-expression-wrappers-7.25.9.tgz#0b2e1b62d560d6b1954893fd2b705dc17c91f0c9"
  integrity sha1-Cy4bYtVg1rGVSJP9K3BdwXyR8Mk=
  dependencies:
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helper-string-parser@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.25.9.tgz#1aabb72ee72ed35789b4bbcad3ca2862ce614e8c"
  integrity sha1-Gqu3Lucu01eJtLvK08ooYs5hTow=

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-string-parser/download/@babel/helper-string-parser-7.27.1.tgz#54da796097ab19ce67ed9f88b47bb2ec49367687"
  integrity sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=

"@babel/helper-validator-identifier@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.25.9.tgz#24b64e2c3ec7cd3b3c547729b8d16871f22cbdc7"
  integrity sha1-JLZOLD7HzTs8VHcpuNFocfIsvcc=

"@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.27.1.tgz#a7054dcc145a967dd4dc8fee845a57c1316c9df8"
  integrity sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=

"@babel/helper-validator-option@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-validator-option/download/@babel/helper-validator-option-7.25.9.tgz#86e45bd8a49ab7e03f276577f96179653d41da72"
  integrity sha1-huRb2KSat+A/J2V3+WF5ZT1B2nI=

"@babel/helper-wrap-function@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.25.9.tgz#d99dfd595312e6c894bd7d237470025c85eea9d0"
  integrity sha1-2Z39WVMS5siUvX0jdHACXIXuqdA=
  dependencies:
    "@babel/template" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/helpers@^7.26.0":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/helpers/download/@babel/helpers-7.26.0.tgz#30e621f1eba5aa45fe6f4868d2e9154d884119a4"
  integrity sha1-MOYh8eulqkX+b0ho0ukVTYhBGaQ=
  dependencies:
    "@babel/template" "^7.25.9"
    "@babel/types" "^7.26.0"

"@babel/parser@^7.25.9", "@babel/parser@^7.26.0", "@babel/parser@^7.26.3", "@babel/parser@^7.7.4":
  version "7.26.3"
  resolved "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.26.3.tgz#8c51c5db6ddf08134af1ddbacf16aaab48bac234"
  integrity sha1-jFHF223fCBNK8d26zxaqq0i6wjQ=
  dependencies:
    "@babel/types" "^7.26.3"

"@babel/parser@^7.27.1", "@babel/parser@^7.27.2":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/parser/download/@babel/parser-7.27.2.tgz#577518bedb17a2ce4212afd052e01f7df0941127"
  integrity sha1-V3UYvtsXos5CEq/QUuAfffCUESc=
  dependencies:
    "@babel/types" "^7.27.1"

"@babel/plugin-bugfix-firefox-class-in-computed-class-key@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-firefox-class-in-computed-class-key/download/@babel/plugin-bugfix-firefox-class-in-computed-class-key-7.25.9.tgz#cc2e53ebf0a0340777fff5ed521943e253b4d8fe"
  integrity sha1-zC5T6/CgNAd3//XtUhlD4lO02P4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-bugfix-safari-class-field-initializer-scope@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-safari-class-field-initializer-scope/download/@babel/plugin-bugfix-safari-class-field-initializer-scope-7.25.9.tgz#af9e4fb63ccb8abcb92375b2fcfe36b60c774d30"
  integrity sha1-r55PtjzLiry5I3Wy/P42tgx3TTA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/download/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.25.9.tgz#e8dc26fcd616e6c5bf2bd0d5a2c151d4f92a9137"
  integrity sha1-6Nwm/NYW5sW/K9DVosFR1PkqkTc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/download/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.25.9.tgz#807a667f9158acac6f6164b4beb85ad9ebc9e1d1"
  integrity sha1-gHpmf5FYrKxvYWS0vrha2evJ4dE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"
    "@babel/plugin-transform-optional-chaining" "^7.25.9"

"@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/download/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.25.9.tgz#de7093f1e7deaf68eadd7cc6b07f2ab82543269e"
  integrity sha1-3nCT8efer2jq3XzGsH8quCVDJp4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-proposal-class-properties@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.18.6.tgz#b110f59741895f7ec21a6fff696ec46265c446a3"
  integrity sha1-sRD1l0GJX37CGm//aW7EYmXERqM=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-proposal-decorators@^7.7.4":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.25.9.tgz#8680707f943d1a3da2cd66b948179920f097e254"
  integrity sha1-hoBwf5Q9Gj2izWa5SBeZIPCX4lQ=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-syntax-decorators" "^7.25.9"

"@babel/plugin-proposal-export-default-from@^7.7.4":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-export-default-from/download/@babel/plugin-proposal-export-default-from-7.25.9.tgz#52702be6ef8367fc8f18b8438278332beeb8f87c"
  integrity sha1-UnAr5u+DZ/yPGLhDgngzK+64+Hw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
  version "7.21.0-placeholder-for-preset-env.2"
  resolved "http://r.npm.sankuai.com/@babel/plugin-proposal-private-property-in-object/download/@babel/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz#7844f9289546efa9febac2de4cfe358a050bd703"
  integrity sha1-eET5KJVG76n+usLeTP41igUL1wM=

"@babel/plugin-syntax-decorators@^7.25.9", "@babel/plugin-syntax-decorators@^7.7.4":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.25.9.tgz#986b4ca8b7b5df3f67cee889cedeffc2e2bf14b3"
  integrity sha1-mGtMqLe13z9nzuiJzt7/wuK/FLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-dynamic-import@^7.7.4":
  version "7.8.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.8.3.tgz#62bf98b2da3cd21d626154fc96ee5b3cb68eacb3"
  integrity sha1-Yr+Ysto80h1iYVT8lu5bPLaOrLM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-flow@^7.25.9":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-flow/download/@babel/plugin-syntax-flow-7.26.0.tgz#96507595c21b45fccfc2bc758d5c45452e6164fa"
  integrity sha1-llB1lcIbRfzPwrx1jVxFRS5hZPo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-import-assertions@^7.26.0":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-import-assertions/download/@babel/plugin-syntax-import-assertions-7.26.0.tgz#620412405058efa56e4a564903b79355020f445f"
  integrity sha1-YgQSQFBY76VuSlZJA7eTVQIPRF8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-import-attributes@^7.26.0":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-import-attributes/download/@babel/plugin-syntax-import-attributes-7.26.0.tgz#3b1412847699eea739b4f2602c74ce36f6b0b0f7"
  integrity sha1-OxQShHaZ7qc5tPJgLHTONvawsPc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-jsx@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.25.9.tgz#a34313a178ea56f1951599b929c1ceacee719290"
  integrity sha1-o0MToXjqVvGVFZm5KcHOrO5xkpA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-syntax-unicode-sets-regex@^7.18.6":
  version "7.18.6"
  resolved "http://r.npm.sankuai.com/@babel/plugin-syntax-unicode-sets-regex/download/@babel/plugin-syntax-unicode-sets-regex-7.18.6.tgz#d49a3b3e6b52e5be6740022317580234a6a47357"
  integrity sha1-1Jo7PmtS5b5nQAIjF1gCNKakc1c=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.18.6"
    "@babel/helper-plugin-utils" "^7.18.6"

"@babel/plugin-transform-arrow-functions@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.25.9.tgz#7821d4410bee5daaadbb4cdd9a6649704e176845"
  integrity sha1-eCHUQQvuXaqtu0zdmmZJcE4XaEU=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-async-generator-functions@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-async-generator-functions/download/@babel/plugin-transform-async-generator-functions-7.25.9.tgz#1b18530b077d18a407c494eb3d1d72da505283a2"
  integrity sha1-GxhTCwd9GKQHxJTrPR1y2lBSg6I=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-remap-async-to-generator" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-transform-async-to-generator@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.25.9.tgz#c80008dacae51482793e5a9c08b39a5be7e12d71"
  integrity sha1-yAAI2srlFIJ5PlqcCLOaW+fhLXE=
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-remap-async-to-generator" "^7.25.9"

"@babel/plugin-transform-block-scoped-functions@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.25.9.tgz#5700691dbd7abb93de300ca7be94203764fce458"
  integrity sha1-VwBpHb16u5PeMAynvpQgN2T85Fg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-block-scoping@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.25.9.tgz#c33665e46b06759c93687ca0f84395b80c0473a1"
  integrity sha1-wzZl5GsGdZyTaHyg+EOVuAwEc6E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-class-properties@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-class-properties/download/@babel/plugin-transform-class-properties-7.25.9.tgz#a8ce84fedb9ad512549984101fa84080a9f5f51f"
  integrity sha1-qM6E/tua1RJUmYQQH6hAgKn19R8=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-class-static-block@^7.26.0":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-class-static-block/download/@babel/plugin-transform-class-static-block-7.26.0.tgz#6c8da219f4eb15cae9834ec4348ff8e9e09664a0"
  integrity sha1-bI2iGfTrFcrpg07ENI/46eCWZKA=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-classes@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.25.9.tgz#7152457f7880b593a63ade8a861e6e26a4469f52"
  integrity sha1-cVJFf3iAtZOmOt6Khh5uJqRGn1I=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-replace-supers" "^7.25.9"
    "@babel/traverse" "^7.25.9"
    globals "^11.1.0"

"@babel/plugin-transform-computed-properties@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.25.9.tgz#db36492c78460e534b8852b1d5befe3c923ef10b"
  integrity sha1-2zZJLHhGDlNLiFKx1b7+PJI+8Qs=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/template" "^7.25.9"

"@babel/plugin-transform-destructuring@^7.25.9", "@babel/plugin-transform-destructuring@^7.7.4":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.25.9.tgz#966ea2595c498224340883602d3cfd7a0c79cea1"
  integrity sha1-lm6iWVxJgiQ0CINgLTz9egx5zqE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-dotall-regex@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.25.9.tgz#bad7945dd07734ca52fe3ad4e872b40ed09bb09a"
  integrity sha1-uteUXdB3NMpS/jrU6HK0DtCbsJo=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-duplicate-keys@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.25.9.tgz#8850ddf57dce2aebb4394bb434a7598031059e6d"
  integrity sha1-iFDd9X3OKuu0OUu0NKdZgDEFnm0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-duplicate-named-capturing-groups-regex@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-duplicate-named-capturing-groups-regex/download/@babel/plugin-transform-duplicate-named-capturing-groups-regex-7.25.9.tgz#6f7259b4de127721a08f1e5165b852fcaa696d31"
  integrity sha1-b3JZtN4SdyGgjx5RZbhS/KppbTE=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-dynamic-import@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-dynamic-import/download/@babel/plugin-transform-dynamic-import-7.25.9.tgz#23e917de63ed23c6600c5dd06d94669dce79f7b8"
  integrity sha1-I+kX3mPtI8ZgDF3QbZRmnc5597g=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-exponentiation-operator@^7.25.9":
  version "7.26.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.26.3.tgz#e29f01b6de302c7c2c794277a48f04a9ca7f03bc"
  integrity sha1-4p8Btt4wLHwseUJ3pI8Eqcp/A7w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-export-namespace-from@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-export-namespace-from/download/@babel/plugin-transform-export-namespace-from-7.25.9.tgz#90745fe55053394f554e40584cda81f2c8a402a2"
  integrity sha1-kHRf5VBTOU9VTkBYTNqB8sikAqI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-flow-strip-types@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-flow-strip-types/download/@babel/plugin-transform-flow-strip-types-7.25.9.tgz#85879b42a8f5948fd6317069978e98f23ef8aec1"
  integrity sha1-hYebQqj1lI/WMXBpl46Y8j74rsE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-syntax-flow" "^7.25.9"

"@babel/plugin-transform-for-of@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.25.9.tgz#4bdc7d42a213397905d89f02350c5267866d5755"
  integrity sha1-S9x9QqITOXkF2J8CNQxSZ4ZtV1U=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"

"@babel/plugin-transform-function-name@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.25.9.tgz#939d956e68a606661005bfd550c4fc2ef95f7b97"
  integrity sha1-k52VbmimBmYQBb/VUMT8Lvlfe5c=
  dependencies:
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-transform-json-strings@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-json-strings/download/@babel/plugin-transform-json-strings-7.25.9.tgz#c86db407cb827cded902a90c707d2781aaa89660"
  integrity sha1-yG20B8uCfN7ZAqkMcH0ngaqolmA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-literals@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.25.9.tgz#1a1c6b4d4aa59bc4cad5b6b3a223a0abd685c9de"
  integrity sha1-GhxrTUqlm8TK1bazoiOgq9aFyd4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-logical-assignment-operators@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-logical-assignment-operators/download/@babel/plugin-transform-logical-assignment-operators-7.25.9.tgz#b19441a8c39a2fda0902900b306ea05ae1055db7"
  integrity sha1-sZRBqMOaL9oJApALMG6gWuEFXbc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-member-expression-literals@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-member-expression-literals/download/@babel/plugin-transform-member-expression-literals-7.25.9.tgz#63dff19763ea64a31f5e6c20957e6a25e41ed5de"
  integrity sha1-Y9/xl2PqZKMfXmwglX5qJeQe1d4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-modules-amd@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.25.9.tgz#49ba478f2295101544abd794486cd3088dddb6c5"
  integrity sha1-SbpHjyKVEBVEq9eUSGzTCI3dtsU=
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-modules-commonjs@^7.25.9":
  version "7.26.3"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.26.3.tgz#8f011d44b20d02c3de44d8850d971d8497f981fb"
  integrity sha1-jwEdRLINAsPeRNiFDZcdhJf5gfs=
  dependencies:
    "@babel/helper-module-transforms" "^7.26.0"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-modules-systemjs@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.25.9.tgz#8bd1b43836269e3d33307151a114bcf3ba6793f8"
  integrity sha1-i9G0ODYmnj0zMHFRoRS887pnk/g=
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"
    "@babel/traverse" "^7.25.9"

"@babel/plugin-transform-modules-umd@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.25.9.tgz#6710079cdd7c694db36529a1e8411e49fcbf14c9"
  integrity sha1-ZxAHnN18aU2zZSmh6EEeSfy/FMk=
  dependencies:
    "@babel/helper-module-transforms" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-named-capturing-groups-regex@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.25.9.tgz#454990ae6cc22fd2a0fa60b3a2c6f63a38064e6a"
  integrity sha1-RUmQrmzCL9Kg+mCzosb2OjgGTmo=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-new-target@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.25.9.tgz#42e61711294b105c248336dcb04b77054ea8becd"
  integrity sha1-QuYXESlLEFwkgzbcsEt3BU6ovs0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-nullish-coalescing-operator@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-nullish-coalescing-operator/download/@babel/plugin-transform-nullish-coalescing-operator-7.25.9.tgz#bcb1b0d9e948168102d5f7104375ca21c3266949"
  integrity sha1-vLGw2elIFoEC1fcQQ3XKIcMmaUk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-numeric-separator@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-numeric-separator/download/@babel/plugin-transform-numeric-separator-7.25.9.tgz#bfed75866261a8b643468b0ccfd275f2033214a1"
  integrity sha1-v+11hmJhqLZDRosMz9J18gMyFKE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-object-rest-spread@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-object-rest-spread/download/@babel/plugin-transform-object-rest-spread-7.25.9.tgz#0203725025074164808bcf1a2cfa90c652c99f18"
  integrity sha1-AgNyUCUHQWSAi88aLPqQxlLJnxg=
  dependencies:
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-transform-parameters" "^7.25.9"

"@babel/plugin-transform-object-super@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.25.9.tgz#385d5de135162933beb4a3d227a2b7e52bb4cf03"
  integrity sha1-OF1d4TUWKTO+tKPSJ6K35Su0zwM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-replace-supers" "^7.25.9"

"@babel/plugin-transform-optional-catch-binding@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-optional-catch-binding/download/@babel/plugin-transform-optional-catch-binding-7.25.9.tgz#10e70d96d52bb1f10c5caaac59ac545ea2ba7ff3"
  integrity sha1-EOcNltUrsfEMXKqsWaxUXqK6f/M=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-optional-chaining@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-optional-chaining/download/@babel/plugin-transform-optional-chaining-7.25.9.tgz#e142eb899d26ef715435f201ab6e139541eee7dd"
  integrity sha1-4ULriZ0m73FUNfIBq24TlUHu590=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"

"@babel/plugin-transform-parameters@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.25.9.tgz#b856842205b3e77e18b7a7a1b94958069c7ba257"
  integrity sha1-uFaEIgWz534Yt6ehuUlYBpx7olc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-private-methods@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-private-methods/download/@babel/plugin-transform-private-methods-7.25.9.tgz#847f4139263577526455d7d3223cd8bda51e3b57"
  integrity sha1-hH9BOSY1d1JkVdfTIjzYvaUeO1c=
  dependencies:
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-private-property-in-object@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-private-property-in-object/download/@babel/plugin-transform-private-property-in-object-7.25.9.tgz#9c8b73e64e6cc3cbb2743633885a7dd2c385fe33"
  integrity sha1-nItz5k5sw8uydDYziFp90sOF/jM=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-create-class-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-property-literals@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-property-literals/download/@babel/plugin-transform-property-literals-7.25.9.tgz#d72d588bd88b0dec8b62e36f6fda91cedfe28e3f"
  integrity sha1-1y1Yi9iLDeyLYuNvb9qRzt/ijj8=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-react-display-name@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-display-name/download/@babel/plugin-transform-react-display-name-7.25.9.tgz#4b79746b59efa1f38c8695065a92a9f5afb24f7d"
  integrity sha1-S3l0a1nvofOMhpUGWpKp9a+yT30=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-react-jsx-development@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx-development/download/@babel/plugin-transform-react-jsx-development-7.25.9.tgz#8fd220a77dd139c07e25225a903b8be8c829e0d7"
  integrity sha1-j9Igp33ROcB+JSJakDuL6Mgp4Nc=
  dependencies:
    "@babel/plugin-transform-react-jsx" "^7.25.9"

"@babel/plugin-transform-react-jsx@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-jsx/download/@babel/plugin-transform-react-jsx-7.25.9.tgz#06367940d8325b36edff5e2b9cbe782947ca4166"
  integrity sha1-BjZ5QNgyWzbt/14rnL54KUfKQWY=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/plugin-syntax-jsx" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/plugin-transform-react-pure-annotations@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-react-pure-annotations/download/@babel/plugin-transform-react-pure-annotations-7.25.9.tgz#ea1c11b2f9dbb8e2d97025f43a3b5bc47e18ae62"
  integrity sha1-6hwRsvnbuOLZcCX0OjtbxH4YrmI=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-regenerator@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.25.9.tgz#03a8a4670d6cebae95305ac6defac81ece77740b"
  integrity sha1-A6ikZw1s666VMFrG3vrIHs53dAs=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    regenerator-transform "^0.15.2"

"@babel/plugin-transform-regexp-modifiers@^7.26.0":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-regexp-modifiers/download/@babel/plugin-transform-regexp-modifiers-7.26.0.tgz#2f5837a5b5cd3842a919d8147e9903cc7455b850"
  integrity sha1-L1g3pbXNOEKpGdgUfpkDzHRVuFA=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-reserved-words@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-reserved-words/download/@babel/plugin-transform-reserved-words-7.25.9.tgz#0398aed2f1f10ba3f78a93db219b27ef417fb9ce"
  integrity sha1-A5iu0vHxC6P3ipPbIZsn70F/uc4=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-runtime@^7":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.27.1.tgz#f9fbf71949a209eb26b3e60375b1d956937b8be9"
  integrity sha1-+fv3GUmiCesms+YDdbHZVpN7i+k=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.11.0"
    babel-plugin-polyfill-regenerator "^0.6.1"
    semver "^6.3.1"

"@babel/plugin-transform-runtime@^7.22.9", "@babel/plugin-transform-runtime@^7.23.6":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.25.9.tgz#62723ea3f5b31ffbe676da9d6dae17138ae580ea"
  integrity sha1-YnI+o/WzH/vmdtqdba4XE4rlgOo=
  dependencies:
    "@babel/helper-module-imports" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.10.6"
    babel-plugin-polyfill-regenerator "^0.6.1"
    semver "^6.3.1"

"@babel/plugin-transform-shorthand-properties@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.25.9.tgz#bb785e6091f99f826a95f9894fc16fde61c163f2"
  integrity sha1-u3heYJH5n4JqlfmJT8Fv3mHBY/I=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-spread@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.25.9.tgz#24a35153931b4ba3d13cec4a7748c21ab5514ef9"
  integrity sha1-JKNRU5MbS6PRPOxKd0jCGrVRTvk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-skip-transparent-expression-wrappers" "^7.25.9"

"@babel/plugin-transform-sticky-regex@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.25.9.tgz#c7f02b944e986a417817b20ba2c504dfc1453d32"
  integrity sha1-x/ArlE6YakF4F7ILosUE38FFPTI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-template-literals@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.25.9.tgz#6dbd4a24e8fad024df76d1fac6a03cf413f60fe1"
  integrity sha1-bb1KJOj60CTfdtH6xqA89BP2D+E=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-typeof-symbol@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.25.9.tgz#224ba48a92869ddbf81f9b4a5f1204bbf5a2bc4b"
  integrity sha1-IkukipKGndv4H5tKXxIEu/WivEs=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-escapes@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-escapes/download/@babel/plugin-transform-unicode-escapes-7.25.9.tgz#a75ef3947ce15363fccaa38e2dd9bc70b2788b82"
  integrity sha1-p17zlHzhU2P8yqOOLdm8cLJ4i4I=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-property-regex@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-property-regex/download/@babel/plugin-transform-unicode-property-regex-7.25.9.tgz#a901e96f2c1d071b0d1bb5dc0d3c880ce8f53dd3"
  integrity sha1-qQHpbywdBxsNG7XcDTyIDOj1PdM=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-regex@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.25.9.tgz#5eae747fe39eacf13a8bd006a4fb0b5d1fa5e9b1"
  integrity sha1-Xq50f+OerPE6i9AGpPsLXR+l6bE=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/plugin-transform-unicode-sets-regex@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/plugin-transform-unicode-sets-regex/download/@babel/plugin-transform-unicode-sets-regex-7.25.9.tgz#65114c17b4ffc20fa5b163c63c70c0d25621fabe"
  integrity sha1-ZRFMF7T/wg+lsWPGPHDA0lYh+r4=
  dependencies:
    "@babel/helper-create-regexp-features-plugin" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"

"@babel/polyfill@^7.12.1", "@babel/polyfill@^7.7.0":
  version "7.12.1"
  resolved "http://r.npm.sankuai.com/@babel/polyfill/download/@babel/polyfill-7.12.1.tgz#1f2d6371d1261bbd961f3c5d5909150e12d0bd96"
  integrity sha1-Hy1jcdEmG72WHzxdWQkVDhLQvZY=
  dependencies:
    core-js "^2.6.5"
    regenerator-runtime "^0.13.4"

"@babel/preset-env@^7.22.9", "@babel/preset-env@^7.23.6":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/preset-env/download/@babel/preset-env-7.26.0.tgz#30e5c6bc1bcc54865bff0c5a30f6d4ccdc7fa8b1"
  integrity sha1-MOXGvBvMVIZb/wxaMPbUzNx/qLE=
  dependencies:
    "@babel/compat-data" "^7.26.0"
    "@babel/helper-compilation-targets" "^7.25.9"
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    "@babel/plugin-bugfix-firefox-class-in-computed-class-key" "^7.25.9"
    "@babel/plugin-bugfix-safari-class-field-initializer-scope" "^7.25.9"
    "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression" "^7.25.9"
    "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining" "^7.25.9"
    "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly" "^7.25.9"
    "@babel/plugin-proposal-private-property-in-object" "7.21.0-placeholder-for-preset-env.2"
    "@babel/plugin-syntax-import-assertions" "^7.26.0"
    "@babel/plugin-syntax-import-attributes" "^7.26.0"
    "@babel/plugin-syntax-unicode-sets-regex" "^7.18.6"
    "@babel/plugin-transform-arrow-functions" "^7.25.9"
    "@babel/plugin-transform-async-generator-functions" "^7.25.9"
    "@babel/plugin-transform-async-to-generator" "^7.25.9"
    "@babel/plugin-transform-block-scoped-functions" "^7.25.9"
    "@babel/plugin-transform-block-scoping" "^7.25.9"
    "@babel/plugin-transform-class-properties" "^7.25.9"
    "@babel/plugin-transform-class-static-block" "^7.26.0"
    "@babel/plugin-transform-classes" "^7.25.9"
    "@babel/plugin-transform-computed-properties" "^7.25.9"
    "@babel/plugin-transform-destructuring" "^7.25.9"
    "@babel/plugin-transform-dotall-regex" "^7.25.9"
    "@babel/plugin-transform-duplicate-keys" "^7.25.9"
    "@babel/plugin-transform-duplicate-named-capturing-groups-regex" "^7.25.9"
    "@babel/plugin-transform-dynamic-import" "^7.25.9"
    "@babel/plugin-transform-exponentiation-operator" "^7.25.9"
    "@babel/plugin-transform-export-namespace-from" "^7.25.9"
    "@babel/plugin-transform-for-of" "^7.25.9"
    "@babel/plugin-transform-function-name" "^7.25.9"
    "@babel/plugin-transform-json-strings" "^7.25.9"
    "@babel/plugin-transform-literals" "^7.25.9"
    "@babel/plugin-transform-logical-assignment-operators" "^7.25.9"
    "@babel/plugin-transform-member-expression-literals" "^7.25.9"
    "@babel/plugin-transform-modules-amd" "^7.25.9"
    "@babel/plugin-transform-modules-commonjs" "^7.25.9"
    "@babel/plugin-transform-modules-systemjs" "^7.25.9"
    "@babel/plugin-transform-modules-umd" "^7.25.9"
    "@babel/plugin-transform-named-capturing-groups-regex" "^7.25.9"
    "@babel/plugin-transform-new-target" "^7.25.9"
    "@babel/plugin-transform-nullish-coalescing-operator" "^7.25.9"
    "@babel/plugin-transform-numeric-separator" "^7.25.9"
    "@babel/plugin-transform-object-rest-spread" "^7.25.9"
    "@babel/plugin-transform-object-super" "^7.25.9"
    "@babel/plugin-transform-optional-catch-binding" "^7.25.9"
    "@babel/plugin-transform-optional-chaining" "^7.25.9"
    "@babel/plugin-transform-parameters" "^7.25.9"
    "@babel/plugin-transform-private-methods" "^7.25.9"
    "@babel/plugin-transform-private-property-in-object" "^7.25.9"
    "@babel/plugin-transform-property-literals" "^7.25.9"
    "@babel/plugin-transform-regenerator" "^7.25.9"
    "@babel/plugin-transform-regexp-modifiers" "^7.26.0"
    "@babel/plugin-transform-reserved-words" "^7.25.9"
    "@babel/plugin-transform-shorthand-properties" "^7.25.9"
    "@babel/plugin-transform-spread" "^7.25.9"
    "@babel/plugin-transform-sticky-regex" "^7.25.9"
    "@babel/plugin-transform-template-literals" "^7.25.9"
    "@babel/plugin-transform-typeof-symbol" "^7.25.9"
    "@babel/plugin-transform-unicode-escapes" "^7.25.9"
    "@babel/plugin-transform-unicode-property-regex" "^7.25.9"
    "@babel/plugin-transform-unicode-regex" "^7.25.9"
    "@babel/plugin-transform-unicode-sets-regex" "^7.25.9"
    "@babel/preset-modules" "0.1.6-no-external-plugins"
    babel-plugin-polyfill-corejs2 "^0.4.10"
    babel-plugin-polyfill-corejs3 "^0.10.6"
    babel-plugin-polyfill-regenerator "^0.6.1"
    core-js-compat "^3.38.1"
    semver "^6.3.1"

"@babel/preset-flow@^7.7.4":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/preset-flow/download/@babel/preset-flow-7.25.9.tgz#ef8b5e7e3f24a42b3711e77fb14919b87dffed0a"
  integrity sha1-74tefj8kpCs3Eed/sUkZuH3/7Qo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    "@babel/plugin-transform-flow-strip-types" "^7.25.9"

"@babel/preset-modules@0.1.6-no-external-plugins":
  version "0.1.6-no-external-plugins"
  resolved "http://r.npm.sankuai.com/@babel/preset-modules/download/@babel/preset-modules-0.1.6-no-external-plugins.tgz#ccb88a2c49c817236861fee7826080573b8a923a"
  integrity sha1-zLiKLEnIFyNoYf7ngmCAVzuKkjo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@babel/types" "^7.4.4"
    esutils "^2.0.2"

"@babel/preset-react@^7.7.4":
  version "7.26.3"
  resolved "http://r.npm.sankuai.com/@babel/preset-react/download/@babel/preset-react-7.26.3.tgz#7c5e028d623b4683c1f83a0bd4713b9100560caa"
  integrity sha1-fF4CjWI7RoPB+DoL1HE7kQBWDKo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.25.9"
    "@babel/helper-validator-option" "^7.25.9"
    "@babel/plugin-transform-react-display-name" "^7.25.9"
    "@babel/plugin-transform-react-jsx" "^7.25.9"
    "@babel/plugin-transform-react-jsx-development" "^7.25.9"
    "@babel/plugin-transform-react-pure-annotations" "^7.25.9"

"@babel/runtime-corejs2@^7.18.9":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/runtime-corejs2/download/@babel/runtime-corejs2-7.26.0.tgz#a49d689a432e27b9bfea4f34f8006abca8cbafb5"
  integrity sha1-pJ1omkMuJ7m/6k80+ABqvKjLr7U=
  dependencies:
    core-js "^2.6.12"
    regenerator-runtime "^0.14.0"

"@babel/runtime@^7":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.27.1.tgz#9fce313d12c9a77507f264de74626e87fd0dc541"
  integrity sha1-n84xPRLJp3UH8mTedGJuh/0NxUE=

"@babel/runtime@^7.0.0", "@babel/runtime@^7.1.2", "@babel/runtime@^7.10.1", "@babel/runtime@^7.10.4", "@babel/runtime@^7.11.1", "@babel/runtime@^7.11.2", "@babel/runtime@^7.12.0", "@babel/runtime@^7.16.7", "@babel/runtime@^7.18.0", "@babel/runtime@^7.18.3", "@babel/runtime@^7.18.9", "@babel/runtime@^7.20.0", "@babel/runtime@^7.20.13", "@babel/runtime@^7.20.7", "@babel/runtime@^7.21.0", "@babel/runtime@^7.22.5", "@babel/runtime@^7.23.2", "@babel/runtime@^7.23.6", "@babel/runtime@^7.23.9", "@babel/runtime@^7.24.4", "@babel/runtime@^7.24.7", "@babel/runtime@^7.24.8", "@babel/runtime@^7.25.7", "@babel/runtime@^7.7.6", "@babel/runtime@^7.8.4", "@babel/runtime@^7.9.2":
  version "7.26.0"
  resolved "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.26.0.tgz#8600c2f595f277c60815256418b85356a65173c1"
  integrity sha1-hgDC9ZXyd8YIFSVkGLhTVqZRc8E=
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/runtime@^7.25.6":
  version "7.26.10"
  resolved "http://r.npm.sankuai.com/@babel/runtime/download/@babel/runtime-7.26.10.tgz#a07b4d8fa27af131a633d7b3524db803eb4764c2"
  integrity sha1-oHtNj6J68TGmM9ezUk24A+tHZMI=
  dependencies:
    regenerator-runtime "^0.14.0"

"@babel/template@^7.25.9":
  version "7.25.9"
  resolved "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.25.9.tgz#ecb62d81a8a6f5dc5fe8abfc3901fc52ddf15016"
  integrity sha1-7LYtgaim9dxf6Kv8OQH8Ut3xUBY=
  dependencies:
    "@babel/code-frame" "^7.25.9"
    "@babel/parser" "^7.25.9"
    "@babel/types" "^7.25.9"

"@babel/template@^7.27.1":
  version "7.27.2"
  resolved "http://r.npm.sankuai.com/@babel/template/download/@babel/template-7.27.2.tgz#fa78ceed3c4e7b63ebf6cb39e5852fca45f6809d"
  integrity sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.25.9", "@babel/traverse@^7.7.4":
  version "7.26.4"
  resolved "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.26.4.tgz#ac3a2a84b908dde6d463c3bfa2c5fdc1653574bd"
  integrity sha1-rDoqhLkI3ebUY8O/osX9wWU1dL0=
  dependencies:
    "@babel/code-frame" "^7.26.2"
    "@babel/generator" "^7.26.3"
    "@babel/parser" "^7.26.3"
    "@babel/template" "^7.25.9"
    "@babel/types" "^7.26.3"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/traverse@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/traverse/download/@babel/traverse-7.27.1.tgz#4db772902b133bbddd1c4f7a7ee47761c1b9f291"
  integrity sha1-TbdykCsTO73dHE96fuR3YcG58pE=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.1"
    "@babel/parser" "^7.27.1"
    "@babel/template" "^7.27.1"
    "@babel/types" "^7.27.1"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.25.9", "@babel/types@^7.26.0", "@babel/types@^7.26.3", "@babel/types@^7.4.4", "@babel/types@^7.7.4":
  version "7.26.3"
  resolved "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.26.3.tgz#37e79830f04c2b5687acc77db97fbc75fb81f3c0"
  integrity sha1-N+eYMPBMK1aHrMd9uX+8dfuB88A=
  dependencies:
    "@babel/helper-string-parser" "^7.25.9"
    "@babel/helper-validator-identifier" "^7.25.9"

"@babel/types@^7.27.1":
  version "7.27.1"
  resolved "http://r.npm.sankuai.com/@babel/types/download/@babel/types-7.27.1.tgz#9defc53c16fc899e46941fc6901a9eea1c9d8560"
  integrity sha1-ne/FPBb8iZ5GlB/GkBqe6hydhWA=
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@cs/phone-sdk@1.0.51", "@cs/phone-sdk@^1.0.51":
  version "1.0.51"
  resolved "http://r.npm.sankuai.com/@cs/phone-sdk/download/@cs/phone-sdk-1.0.51.tgz#4e53d8f0fe19179f19faf80b68cdc132e4065294"
  integrity sha1-TlPY8P4ZF58Z+vgLaM3BMuQGUpQ=

"@ctrl/tinycolor@^3.6.1":
  version "3.6.1"
  resolved "http://r.npm.sankuai.com/@ctrl/tinycolor/download/@ctrl/tinycolor-3.6.1.tgz#b6c75a56a1947cc916ea058772d666a2c8932f31"
  integrity sha1-tsdaVqGUfMkW6gWHctZmosiTLzE=

"@datafe/apihub@^1.3.1":
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/@datafe/apihub/download/@datafe/apihub-1.3.1.tgz#1b2b07f11e7f349109574485690c885cd96157c6"
  integrity sha1-GysH8R5/NJEJV0SFaQyIXNlhV8Y=
  dependencies:
    "@types/node" "^11.13.2"
    cac "^6.4.3"
    chalk "^2.4.2"
    consola "^2.6.2"
    fs-extra "^7.0.1"
    inquirer "^6.3.1"
    joycon "^2.2.5"
    js-yaml "^3.13.1"
    lodash.find "^4.6.0"
    lodash.isequal "^4.5.0"
    lodash.uniq "^4.5.0"
    lodash.uniqwith "^4.5.0"
    node-fetch "2"
    open "^6.1.0"
    prettier "^1.15.2"
    resolve-from "^4.0.0"
    rimraf "^2.6.3"
    toml "^3.0.0"
    uuid "^3.3.2"

"@dp/cat-client@^3.0.3", "@dp/cat-client@^3.0.4":
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/@dp/cat-client/download/@dp/cat-client-3.0.4.tgz#c72dd18af0f10e22ec8199a0c809209814c4d295"
  integrity sha1-xy3RivDxDiLsgZmgyAkgmBTE0pU=
  dependencies:
    "@dp/logger-container" "^1.1.0"
    "@dp/simple-util" "^1.0.0"
    buffer-builder "^0.2.0"
    debug "^2.2.0"
    mkdirp "^0.5.1"
    moment "^2.10.6"
    node-addon-api "^3.1.0"
    request "^2.67.0"
    semver "^6.1.2"
    xml2js "^0.4.15"

"@dp/logger-container@^1.1.0", "@dp/logger-container@^1.2.0":
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/@dp/logger-container/download/@dp/logger-container-1.2.0.tgz#02ea0bc21377eff37a5072ffb514dc70c07cf26f"
  integrity sha1-AuoLwhN37/N6UHL/tRTccMB88m8=

"@dp/node-kms@^2.0.0", "@dp/node-kms@^2.0.8":
  version "2.0.13"
  resolved "http://r.npm.sankuai.com/@dp/node-kms/download/@dp/node-kms-2.0.13.tgz#68e1b3b045eb4a20c98f46821c804809597e431d"
  integrity sha1-aOGzsEXrSiDJj0aCHIBICVl+Qx0=
  dependencies:
    "@dp/server-env" "^1.0.3"
    "@dp/simple-util" "^1.1.1"
    bindings "^1.5.0"
    co-request "^1.0.0"
    ip "^1.1.9"

"@dp/owl@^1.9.2-1":
  version "1.10.3"
  resolved "http://r.npm.sankuai.com/@dp/owl/download/@dp/owl-1.10.3.tgz#41bc7a0d973ad906c7d381568b6611de7dd565c7"
  integrity sha1-Qbx6DZc62QbH04FWi2YR3n3VZcc=
  dependencies:
    int64-convert "^0.1.2"
    uuid "^3.3.2"

"@dp/patriot-sdk@^2.0.2":
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/@dp/patriot-sdk/download/@dp/patriot-sdk-2.0.2.tgz#57549214ceadc9fe5d3be01db2adc34eae3c9a00"
  integrity sha1-V1SSFM6tyf5dO+Adsq3DTq48mgA=
  dependencies:
    "@dp/logger-container" "^1.2.0"
    "@dp/node-kms" "^2.0.0"
    "@dp/simple-util" "^1.1.1"

"@dp/server-env@^1.0.3":
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/@dp/server-env/download/@dp/server-env-1.0.3.tgz#045a0945a05d46f452d97d060435416c08332731"
  integrity sha1-BFoJRaBdRvRS2X0GBDVBbAgzJzE=
  dependencies:
    "@dp/logger-container" "^1.1.0"
    properties-parser "^0.3.1"

"@dp/simple-util@^1.0.0", "@dp/simple-util@^1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@dp/simple-util/download/@dp/simple-util-1.1.1.tgz#972c7a41511aecbda67a4ac4893b2d3ebf3d5841"
  integrity sha1-lyx6QVEa7L2mekrEiTstPr89WEE=

"@emotion/hash@^0.8.0":
  version "0.8.0"
  resolved "http://r.npm.sankuai.com/@emotion/hash/download/@emotion/hash-0.8.0.tgz#bbbff68978fefdbe68ccb533bc8cbe1d1afb5413"
  integrity sha1-u7/2iXj+/b5ozLUzvIy+HRr7VBM=

"@emotion/unitless@^0.7.5":
  version "0.7.5"
  resolved "http://r.npm.sankuai.com/@emotion/unitless/download/@emotion/unitless-0.7.5.tgz#77211291c1900a700b8a78cfafda3160d76949ed"
  integrity sha1-dyESkcGQCnALinjPr9oxYNdpSe0=

"@esbuild/aix-ppc64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/aix-ppc64/download/@esbuild/aix-ppc64-0.25.3.tgz#014180d9a149cffd95aaeead37179433f5ea5437"
  integrity sha1-AUGA2aFJz/2Vqu6tNxeUM/XqVDc=

"@esbuild/android-arm64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/android-arm64/download/@esbuild/android-arm64-0.18.20.tgz#984b4f9c8d0377443cc2dfcef266d02244593622"
  integrity sha1-mEtPnI0Dd0Q8wt/O8mbQIkRZNiI=

"@esbuild/android-arm64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/android-arm64/download/@esbuild/android-arm64-0.25.3.tgz#649e47e04ddb24a27dc05c395724bc5f4c55cbfe"
  integrity sha1-ZJ5H4E3bJKJ9wFw5VyS8X0xVy/4=

"@esbuild/android-arm@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/android-arm/download/@esbuild/android-arm-0.18.20.tgz#fedb265bc3a589c84cc11f810804f234947c3682"
  integrity sha1-/tsmW8OlichMwR+BCATyNJR8NoI=

"@esbuild/android-arm@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/android-arm/download/@esbuild/android-arm-0.25.3.tgz#8a0f719c8dc28a4a6567ef7328c36ea85f568ff4"
  integrity sha1-ig9xnI3CikplZ+9zKMNuqF9Wj/Q=

"@esbuild/android-x64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/android-x64/download/@esbuild/android-x64-0.18.20.tgz#35cf419c4cfc8babe8893d296cd990e9e9f756f2"
  integrity sha1-Nc9BnEz8i6voiT0pbNmQ6en3VvI=

"@esbuild/android-x64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/android-x64/download/@esbuild/android-x64-0.25.3.tgz#e2ab182d1fd06da9bef0784a13c28a7602d78009"
  integrity sha1-4qsYLR/Qbam+8HhKE8KKdgLXgAk=

"@esbuild/darwin-arm64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/darwin-arm64/download/@esbuild/darwin-arm64-0.18.20.tgz#08172cbeccf95fbc383399a7f39cfbddaeb0d7c1"
  integrity sha1-CBcsvsz5X7w4M5mn85z73a6w18E=

"@esbuild/darwin-arm64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/darwin-arm64/download/@esbuild/darwin-arm64-0.25.3.tgz#c7f3166fcece4d158a73dcfe71b2672ca0b1668b"
  integrity sha1-x/MWb87OTRWKc9z+cbJnLKCxZos=

"@esbuild/darwin-x64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/darwin-x64/download/@esbuild/darwin-x64-0.18.20.tgz#d70d5790d8bf475556b67d0f8b7c5bdff053d85d"
  integrity sha1-1w1XkNi/R1VWtn0Pi3xb3/BT2F0=

"@esbuild/darwin-x64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/darwin-x64/download/@esbuild/darwin-x64-0.25.3.tgz#d8c5342ec1a4bf4b1915643dfe031ba4b173a87a"
  integrity sha1-2MU0LsGkv0sZFWQ9/gMbpLFzqHo=

"@esbuild/freebsd-arm64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/freebsd-arm64/download/@esbuild/freebsd-arm64-0.18.20.tgz#98755cd12707f93f210e2494d6a4b51b96977f54"
  integrity sha1-mHVc0ScH+T8hDiSU1qS1G5aXf1Q=

"@esbuild/freebsd-arm64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/freebsd-arm64/download/@esbuild/freebsd-arm64-0.25.3.tgz#9f7d789e2eb7747d4868817417cc968ffa84f35b"
  integrity sha1-n314ni63dH1IaIF0F8yWj/qE81s=

"@esbuild/freebsd-x64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/freebsd-x64/download/@esbuild/freebsd-x64-0.18.20.tgz#c1eb2bff03915f87c29cece4c1a7fa1f423b066e"
  integrity sha1-wesr/wORX4fCnOzkwaf6H0I7Bm4=

"@esbuild/freebsd-x64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/freebsd-x64/download/@esbuild/freebsd-x64-0.25.3.tgz#8ad35c51d084184a8e9e76bb4356e95350a64709"
  integrity sha1-itNcUdCEGEqOnna7Q1bpU1CmRwk=

"@esbuild/linux-arm64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-arm64/download/@esbuild/linux-arm64-0.18.20.tgz#bad4238bd8f4fc25b5a021280c770ab5fc3a02a0"
  integrity sha1-utQji9j0/CW1oCEoDHcKtfw6AqA=

"@esbuild/linux-arm64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-arm64/download/@esbuild/linux-arm64-0.25.3.tgz#3af0da3d9186092a9edd4e28fa342f57d9e3cd30"
  integrity sha1-OvDaPZGGCSqe3U4o+jQvV9njzTA=

"@esbuild/linux-arm@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-arm/download/@esbuild/linux-arm-0.18.20.tgz#3e617c61f33508a27150ee417543c8ab5acc73b0"
  integrity sha1-PmF8YfM1CKJxUO5BdUPIq1rMc7A=

"@esbuild/linux-arm@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-arm/download/@esbuild/linux-arm-0.25.3.tgz#e91cafa95e4474b3ae3d54da12e006b782e57225"
  integrity sha1-6RyvqV5EdLOuPVTaEuAGt4LlciU=

"@esbuild/linux-ia32@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-ia32/download/@esbuild/linux-ia32-0.18.20.tgz#699391cccba9aee6019b7f9892eb99219f1570a7"
  integrity sha1-aZORzMupruYBm3+YkuuZIZ8VcKc=

"@esbuild/linux-ia32@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-ia32/download/@esbuild/linux-ia32-0.25.3.tgz#81025732d85b68ee510161b94acdf7e3007ea177"
  integrity sha1-gQJXMthbaO5RAWG5Ss334wB+oXc=

"@esbuild/linux-loong64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-loong64/download/@esbuild/linux-loong64-0.18.20.tgz#e6fccb7aac178dd2ffb9860465ac89d7f23b977d"
  integrity sha1-5vzLeqwXjdL/uYYEZayJ1/I7l30=

"@esbuild/linux-loong64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-loong64/download/@esbuild/linux-loong64-0.25.3.tgz#3c744e4c8d5e1148cbe60a71a11b58ed8ee5deb8"
  integrity sha1-PHROTI1eEUjL5gpxoRtY7Y7l3rg=

"@esbuild/linux-mips64el@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-mips64el/download/@esbuild/linux-mips64el-0.18.20.tgz#eeff3a937de9c2310de30622a957ad1bd9183231"
  integrity sha1-7v86k33pwjEN4wYiqVetG9kYMjE=

"@esbuild/linux-mips64el@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-mips64el/download/@esbuild/linux-mips64el-0.25.3.tgz#1dfe2a5d63702db9034cc6b10b3087cc0424ec26"
  integrity sha1-Hf4qXWNwLbkDTMaxCzCHzAQk7CY=

"@esbuild/linux-ppc64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-ppc64/download/@esbuild/linux-ppc64-0.18.20.tgz#2f7156bde20b01527993e6881435ad79ba9599fb"
  integrity sha1-L3FWveILAVJ5k+aIFDWtebqVmfs=

"@esbuild/linux-ppc64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-ppc64/download/@esbuild/linux-ppc64-0.25.3.tgz#2e85d9764c04a1ebb346dc0813ea05952c9a5c56"
  integrity sha1-LoXZdkwEoeuzRtwIE+oFlSyaXFY=

"@esbuild/linux-riscv64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-riscv64/download/@esbuild/linux-riscv64-0.18.20.tgz#6628389f210123d8b4743045af8caa7d4ddfc7a6"
  integrity sha1-Zig4nyEBI9i0dDBFr4yqfU3fx6Y=

"@esbuild/linux-riscv64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-riscv64/download/@esbuild/linux-riscv64-0.25.3.tgz#a9ea3334556b09f85ccbfead58c803d305092415"
  integrity sha1-qeozNFVrCfhcy/6tWMgD0wUJJBU=

"@esbuild/linux-s390x@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-s390x/download/@esbuild/linux-s390x-0.18.20.tgz#255e81fb289b101026131858ab99fba63dcf0071"
  integrity sha1-JV6B+yibEBAmExhYq5n7pj3PAHE=

"@esbuild/linux-s390x@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-s390x/download/@esbuild/linux-s390x-0.25.3.tgz#f6a7cb67969222b200974de58f105dfe8e99448d"
  integrity sha1-9qfLZ5aSIrIAl03ljxBd/o6ZRI0=

"@esbuild/linux-x64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-x64/download/@esbuild/linux-x64-0.18.20.tgz#c7690b3417af318a9b6f96df3031a8865176d338"
  integrity sha1-x2kLNBevMYqbb5bfMDGohlF20zg=

"@esbuild/linux-x64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/linux-x64/download/@esbuild/linux-x64-0.25.3.tgz#a237d3578ecdd184a3066b1f425e314ade0f8033"
  integrity sha1-ojfTV47N0YSjBmsfQl4xSt4PgDM=

"@esbuild/netbsd-arm64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/netbsd-arm64/download/@esbuild/netbsd-arm64-0.25.3.tgz#4c15c68d8149614ddb6a56f9c85ae62ccca08259"
  integrity sha1-TBXGjYFJYU3balb5yFrmLMygglk=

"@esbuild/netbsd-x64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/netbsd-x64/download/@esbuild/netbsd-x64-0.18.20.tgz#30e8cd8a3dded63975e2df2438ca109601ebe0d1"
  integrity sha1-MOjNij3e1jl14t8kOMoQlgHr4NE=

"@esbuild/netbsd-x64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/netbsd-x64/download/@esbuild/netbsd-x64-0.25.3.tgz#12f6856f8c54c2d7d0a8a64a9711c01a743878d5"
  integrity sha1-EvaFb4xUwtfQqKZKlxHAGnQ4eNU=

"@esbuild/openbsd-arm64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/openbsd-arm64/download/@esbuild/openbsd-arm64-0.25.3.tgz#ca078dad4a34df192c60233b058db2ca3d94bc5c"
  integrity sha1-ygeNrUo03xksYCM7BY2yyj2UvFw=

"@esbuild/openbsd-x64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/openbsd-x64/download/@esbuild/openbsd-x64-0.18.20.tgz#7812af31b205055874c8082ea9cf9ab0da6217ae"
  integrity sha1-eBKvMbIFBVh0yAguqc+asNpiF64=

"@esbuild/openbsd-x64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/openbsd-x64/download/@esbuild/openbsd-x64-0.25.3.tgz#c9178adb60e140e03a881d0791248489c79f95b2"
  integrity sha1-yReK22DhQOA6iB0HkSSEiceflbI=

"@esbuild/sunos-x64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/sunos-x64/download/@esbuild/sunos-x64-0.18.20.tgz#d5c275c3b4e73c9b0ecd38d1ca62c020f887ab9d"
  integrity sha1-1cJ1w7TnPJsOzTjRymLAIPiHq50=

"@esbuild/sunos-x64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/sunos-x64/download/@esbuild/sunos-x64-0.25.3.tgz#03765eb6d4214ff27e5230af779e80790d1ee09f"
  integrity sha1-A3ZettQhT/J+UjCvd56AeQ0e4J8=

"@esbuild/win32-arm64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-arm64/download/@esbuild/win32-arm64-0.18.20.tgz#73bc7f5a9f8a77805f357fab97f290d0e4820ac9"
  integrity sha1-c7x/Wp+Kd4BfNX+rl/KQ0OSCCsk=

"@esbuild/win32-arm64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-arm64/download/@esbuild/win32-arm64-0.25.3.tgz#f1c867bd1730a9b8dfc461785ec6462e349411ea"
  integrity sha1-8chnvRcwqbjfxGF4XsZGLjSUEeo=

"@esbuild/win32-ia32@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-ia32/download/@esbuild/win32-ia32-0.18.20.tgz#ec93cbf0ef1085cc12e71e0d661d20569ff42102"
  integrity sha1-7JPL8O8QhcwS5x4NZh0gVp/0IQI=

"@esbuild/win32-ia32@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-ia32/download/@esbuild/win32-ia32-0.25.3.tgz#77491f59ef6c9ddf41df70670d5678beb3acc322"
  integrity sha1-d0kfWe9snd9B33BnDVZ4vrOswyI=

"@esbuild/win32-x64@0.18.20":
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-x64/download/@esbuild/win32-x64-0.18.20.tgz#786c5f41f043b07afb1af37683d7c33668858f6d"
  integrity sha1-eGxfQfBDsHr7GvN2g9fDNmiFj20=

"@esbuild/win32-x64@0.25.3":
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/@esbuild/win32-x64/download/@esbuild/win32-x64-0.25.3.tgz#b17a2171f9074df9e91bfb07ef99a892ac06412a"
  integrity sha1-sXohcfkHTfnpG/sH75mokqwGQSo=

"@eslint-community/eslint-utils@^4.2.0":
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.4.1.tgz#d1145bf2c20132d6400495d6df4bf59362fd9d56"
  integrity sha1-0RRb8sIBMtZABJXW30v1k2L9nVY=
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.4.0", "@eslint-community/regexpp@^4.6.1":
  version "4.12.1"
  resolved "http://r.npm.sankuai.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.12.1.tgz#cfc6cffe39df390a3841cde2abccf92eaa7ae0e0"
  integrity sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=

"@eslint/eslintrc@^2.1.4":
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/@eslint/eslintrc/download/@eslint/eslintrc-2.1.4.tgz#388a269f0f25c1b6adc317b5a2c55714894c70ad"
  integrity sha1-OIomnw8lwbatwxe1osVXFIlMcK0=
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.57.1":
  version "8.57.1"
  resolved "http://r.npm.sankuai.com/@eslint/js/download/@eslint/js-8.57.1.tgz#de633db3ec2ef6a3c89e2f19038063e8a122e2c2"
  integrity sha1-3mM9s+wu9qPIni8ZA4Bj6KEi4sI=

"@floating-ui/core@^1.6.0":
  version "1.6.9"
  resolved "http://r.npm.sankuai.com/@floating-ui/core/download/@floating-ui/core-1.6.9.tgz#64d1da251433019dafa091de9b2886ff35ec14e6"
  integrity sha1-ZNHaJRQzAZ2voJHemyiG/zXsFOY=
  dependencies:
    "@floating-ui/utils" "^0.2.9"

"@floating-ui/dom@^1.0.0":
  version "1.6.13"
  resolved "http://r.npm.sankuai.com/@floating-ui/dom/download/@floating-ui/dom-1.6.13.tgz#a8a938532aea27a95121ec16e667a7cbe8c59e34"
  integrity sha1-qKk4UyrqJ6lRIewW5meny+jFnjQ=
  dependencies:
    "@floating-ui/core" "^1.6.0"
    "@floating-ui/utils" "^0.2.9"

"@floating-ui/react-dom@^2.0.0":
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/@floating-ui/react-dom/download/@floating-ui/react-dom-2.1.2.tgz#a1349bbf6a0e5cb5ded55d023766f20a4d439a31"
  integrity sha1-oTSbv2oOXLXe1V0CN2byCk1DmjE=
  dependencies:
    "@floating-ui/dom" "^1.0.0"

"@floating-ui/utils@^0.2.9":
  version "0.2.9"
  resolved "http://r.npm.sankuai.com/@floating-ui/utils/download/@floating-ui/utils-0.2.9.tgz#50dea3616bc8191fb8e112283b49eaff03e78429"
  integrity sha1-UN6jYWvIGR+44RIoO0nq/wPnhCk=

"@humanwhocodes/config-array@^0.13.0":
  version "0.13.0"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.13.0.tgz#fb907624df3256d04b9aa2df50d7aa97ec648748"
  integrity sha1-+5B2JN8yVtBLmqLfUNeql+xkh0g=
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.3"
    debug "^4.3.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/module-importer/download/@humanwhocodes/module-importer-1.0.1.tgz#af5b2691a22b44be847b0ca81641c5fb6ad0172c"
  integrity sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=

"@humanwhocodes/object-schema@^2.0.3":
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-2.0.3.tgz#4a2868d75d6d6963e423bcf90b7fd1be343409d3"
  integrity sha1-Siho111taWPkI7z5C3/RvjQ0CdM=

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "http://r.npm.sankuai.com/@isaacs/cliui/download/@isaacs/cliui-8.0.2.tgz#b37667b7bc181c168782259bab42474fbf52b550"
  integrity sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@jridgewell/gen-mapping@^0.3.2", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.8"
  resolved "http://r.npm.sankuai.com/@jridgewell/gen-mapping/download/@jridgewell/gen-mapping-0.3.8.tgz#4f0e06362e01362f823d348f1872b08f666d8142"
  integrity sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/@jridgewell/resolve-uri/download/@jridgewell/resolve-uri-3.1.2.tgz#7a0ee601f60f99a20c7c7c5ff0c80388c1189bd6"
  integrity sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/@jridgewell/set-array/download/@jridgewell/set-array-1.2.1.tgz#558fb6472ed16a4c850b889530e6b36438c49280"
  integrity sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=

"@jridgewell/source-map@^0.3.3":
  version "0.3.6"
  resolved "http://r.npm.sankuai.com/@jridgewell/source-map/download/@jridgewell/source-map-0.3.6.tgz#9d71ca886e32502eb9362c9a74a46787c36df81a"
  integrity sha1-nXHKiG4yUC65NiyadKRnh8Nt+Bo=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.13", "@jridgewell/sourcemap-codec@^1.4.14":
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/@jridgewell/sourcemap-codec/download/@jridgewell/sourcemap-codec-1.5.0.tgz#3188bcb273a414b0d215fd22a58540b989b9409a"
  integrity sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "http://r.npm.sankuai.com/@jridgewell/trace-mapping/download/@jridgewell/trace-mapping-0.3.25.tgz#15f190e98895f3fc23276ee14bc76b675c2e50f0"
  integrity sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@ljharb/resumer@~0.0.1":
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/@ljharb/resumer/download/@ljharb/resumer-0.0.1.tgz#8a940a9192dd31f6a1df17564bbd26dc6ad3e68d"
  integrity sha1-ipQKkZLdMfah3xdWS70m3GrT5o0=
  dependencies:
    "@ljharb/through" "^2.3.9"

"@ljharb/through@^2.3.9", "@ljharb/through@~2.3.9":
  version "2.3.14"
  resolved "http://r.npm.sankuai.com/@ljharb/through/download/@ljharb/through-2.3.14.tgz#a5df44295f44dc23bfe106af59426dd0677760b1"
  integrity sha1-pd9EKV9E3CO/4QavWUJt0Gd3YLE=
  dependencies:
    call-bind "^1.0.8"

"@mfe/bellwether-route@^1.0.9":
  version "1.0.9"
  resolved "http://r.npm.sankuai.com/@mfe/bellwether-route/download/@mfe/bellwether-route-1.0.9.tgz#dc277d518bd7aa9f807a0d137cabb44979490dad"
  integrity sha1-3Cd9UYvXqp+Aeg0TfKu0SXlJDa0=

"@mfe/cc-api-caller-pc@^1.1.17":
  version "1.1.17"
  resolved "http://r.npm.sankuai.com/@mfe/cc-api-caller-pc/download/@mfe/cc-api-caller-pc-1.1.17.tgz#5eb89b17a8424a05d4b9a4f8636f370d9f212957"
  integrity sha1-XribF6hCSgXUuaT4Y283DZ8hKVc=
  dependencies:
    "@mfe/cc-api-caller" "^1.1.17"

"@mfe/cc-api-caller@^1.1.17":
  version "1.1.17"
  resolved "http://r.npm.sankuai.com/@mfe/cc-api-caller/download/@mfe/cc-api-caller-1.1.17.tgz#e1352c6c9ae52b3b56e91a58d9717e28695cbc18"
  integrity sha1-4TUsbJrlKztW6RpY2XF+KGlcvBg=
  dependencies:
    ejs "^3.1.6"
    got "11.8.6"
    ora "^5.4.0"
  optionalDependencies:
    "@datafe/apihub" "^1.3.1"
    "@mtfe/yapi2service" "^1.1.5"

"@mfe/cc-ocrm-utils@^0.0.6":
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/@mfe/cc-ocrm-utils/download/@mfe/cc-ocrm-utils-0.0.6.tgz#e4491792a422a50d450f6bf289425bdb5867b448"
  integrity sha1-5EkXkqQipQ1FD2vyiUJb21hntEg=

"@mfe/cc-outbound@3.1.9":
  version "3.1.9"
  resolved "http://r.npm.sankuai.com/@mfe/cc-outbound/download/@mfe/cc-outbound-3.1.9.tgz#a86504b91e59b49cb26b2d8594a123706c3e61a0"
  integrity sha1-qGUEuR5ZtJyyay2FlKEjcGw+YaA=
  dependencies:
    "@babel/runtime" "^7.18.9"
    "@cs/phone-sdk" "^1.0.51"
    "@types/axios" "^0.14.0"

"@mfe/vite-sso-plugin@0.0.7":
  version "0.0.7"
  resolved "http://r.npm.sankuai.com/@mfe/vite-sso-plugin/download/@mfe/vite-sso-plugin-0.0.7.tgz#beaa8a1094bf80feb0ce2b58e55c35b8aaf8c2d7"
  integrity sha1-vqqKEJS/gP6wzitY5Vw1uKr4wtc=
  dependencies:
    "@mtfe/sso-client" "^3.3.0"
    tsup "^8.0.1"

"@mtfe/audio-player@^1.5.1":
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/@mtfe/audio-player/download/@mtfe/audio-player-1.5.1.tgz#1be0df95b1d312e57f832f7d028a4d75508a4a7f"
  integrity sha1-G+DflbHTEuV/gy99AopNdVCKSn8=
  dependencies:
    classnames "^2.2.6"

"@mtfe/basic-auth@^0.3.2":
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/@mtfe/basic-auth/download/@mtfe/basic-auth-0.3.2.tgz#a2db52f5b72ec741d3f08396b7b53719e6cba6fa"
  integrity sha1-ottS9bcux0HT8IOWt7U3GebLpvo=

"@mtfe/cat@^1.1.0":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@mtfe/cat/download/@mtfe/cat-1.1.0.tgz#37663ef5afbc9d09e0d9b4008406d611ad78d008"
  integrity sha1-N2Y+9a+8nQng2bQAhAbWEa140Ag=
  dependencies:
    "@dp/cat-client" "^3.0.3"
    debug "^4.3.3"

"@mtfe/knb-core@^0.4.9":
  version "0.4.9"
  resolved "http://r.npm.sankuai.com/@mtfe/knb-core/download/@mtfe/knb-core-0.4.9.tgz#1a9a14411ab521e2a4e0a1975ffec7fb19d12a53"
  integrity sha1-GpoUQRq1IeKk4KGXX/7H+xnRKlM=

"@mtfe/mns-util@^2.0.0", "@mtfe/mns-util@^2.0.1":
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/@mtfe/mns-util/download/@mtfe/mns-util-2.0.1.tgz#7ed17fb6db9b8292e3ee86523e5d4ab39e0059ca"
  integrity sha1-ftF/ttubgpLj7oZSPl1Ks54AWco=
  dependencies:
    ini "^1.3.5"
    ip "^1.1.5"

"@mtfe/mns-util@^3.1.0", "@mtfe/mns-util@^3.2.0":
  version "3.2.4"
  resolved "http://r.npm.sankuai.com/@mtfe/mns-util/download/@mtfe/mns-util-3.2.4.tgz#69f2c8c942d9b8e06b99c39e70766d005a173e08"
  integrity sha1-afLIyULZuOBrmcOecHZtAFoXPgg=
  dependencies:
    core-js "^3.41.0"
    debug "^4.1.1"
    fast-xml-parser "^4.4.1"
    ini "^1.3.5"
    ip "^1.1.5"

"@mtfe/mtraceid@^0.1.0":
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/@mtfe/mtraceid/download/@mtfe/mtraceid-0.1.0.tgz#a5ce78e47da2489a925b26901be80bc0323c6129"
  integrity sha1-pc545H2iSJqSWyaQG+gLwDI8YSk=
  dependencies:
    int64-convert "^0.1.2"
    uuid "^3.3.2"

"@mtfe/mtracer@^0.1.16":
  version "0.1.16"
  resolved "http://r.npm.sankuai.com/@mtfe/mtracer/download/@mtfe/mtracer-0.1.16.tgz#6df297d400a8c52ec0f94343c97a52149213b18b"
  integrity sha1-bfKX1ACoxS7A+UNDyXpSFJITsYs=
  dependencies:
    "@mtfe/mns-util" "^2.0.1"
    "@mtfe/mtraceid" "^0.1.0"
    "@mtfe/sg-agent" "^0.1.0"
    bluebird "^3.5.3"
    debug "^4.1.1"
    ip "^1.1.5"
    thrift "^0.11.0"

"@mtfe/octo-auth@^0.2.0":
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/@mtfe/octo-auth/download/@mtfe/octo-auth-0.2.0.tgz#83e82f1752d490aae1e7565104e8840399f717b1"
  integrity sha1-g+gvF1LUkKrh51ZRBOiEA5n3F7E=
  dependencies:
    "@dp/node-kms" "^2.0.0"
    debug "^3.1.0"

"@mtfe/octo-idls@^0.1.0":
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/@mtfe/octo-idls/download/@mtfe/octo-idls-0.1.3.tgz#d9418828c35547567886c4eec46377b05220fdf6"
  integrity sha1-2UGIKMNVR1Z4hsTuxGN3sFIg/fY=
  dependencies:
    thrift "^0.11.0"

"@mtfe/octo-idls@^1.1.0":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@mtfe/octo-idls/download/@mtfe/octo-idls-1.1.0.tgz#92974c35646daaaca2984c7ab03881a930657947"
  integrity sha1-kpdMNWRtqqyimEx6sDiBqTBleUc=
  dependencies:
    thrift "^0.11.0"

"@mtfe/request@^0.10.1":
  version "0.10.1"
  resolved "http://r.npm.sankuai.com/@mtfe/request/download/@mtfe/request-0.10.1.tgz#219b07e85d47702f3b768cca1a76cd522b81dea1"
  integrity sha1-IZsH6F1HcC87dozKGnbNUiuB3qE=
  dependencies:
    "@mtfe/mns-util" "^2.0.0"
    event-emitter "^0.3.3"
    http-proxy-agent "^2.1.0"
    qs "^2.4.1"
    retry "^0.6.1"

"@mtfe/sg-agent@^0.1.0":
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/@mtfe/sg-agent/download/@mtfe/sg-agent-0.1.0.tgz#defbffcbede5f5191095ad2c168d0a95888ae9df"
  integrity sha1-3vv/y+3l9RkQla0sFo0KlYiK6d8=
  dependencies:
    "@mtfe/octo-idls" "^0.1.0"
    "@mtfe/sg-sentinel" "^0.1.0"
    bluebird "^3.5.3"
    thrift "^0.11.0"

"@mtfe/sg-sentinel@^0.1.0":
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/@mtfe/sg-sentinel/download/@mtfe/sg-sentinel-0.1.3.tgz#68d24350a0cd5a180e9567275ec10b7d21f69e9b"
  integrity sha1-aNJDUKDNWhgOlWcnXsELfSH2nps=
  dependencies:
    "@mtfe/mns-util" "^2.0.0"
    axios "^0.18.0"
    debug "^4.1.1"
    lru-cache "^5.1.1"

"@mtfe/sso-client@^3.3.0":
  version "3.3.9"
  resolved "http://r.npm.sankuai.com/@mtfe/sso-client/download/@mtfe/sso-client-3.3.9.tgz#d259122f7440a1bc5ecf52daa89f1c2bbb207e91"
  integrity sha1-0lkSL3RAobxez1LaqJ8cK7sgfpE=
  dependencies:
    "@dp/cat-client" "^3.0.4"
    "@dp/node-kms" "^2.0.8"
    "@mtfe/basic-auth" "^0.3.2"
    "@mtfe/mns-util" "^3.1.0"
    "@mtfe/mtracer" "^0.1.16"
    "@mtfe/request" "^0.10.1"
    "@mtfe/thrift" ">=4"
    "@thrift-api/it-iam-ssoopenapi" "1.0.13-RELEASE"
    "@thrift-api/it-iam-ssoservice" "1.0.19-SNAPSHOT"
    debug "^4.4.0"
    ejs "^3.1.6"
    ipv4 "^1.0.4"
    koa-convert "^1.2.0"
    minimatch "^3.0.4"
    node-rsa "^1.1.1"
    sdk-base "^3.6.0"

"@mtfe/sso-web@^2.5.0":
  version "2.6.0"
  resolved "http://r.npm.sankuai.com/@mtfe/sso-web/download/@mtfe/sso-web-2.6.0.tgz#3d9f8f5892105b70a7ba87934bee596f692ffe2d"
  integrity sha1-PZ+PWJIQW3CnuoeTS+5Zb2kv/i0=
  dependencies:
    crypto-js "^3.1.9-1"
    eventemitter3 "^5.0.1"
    minimatch "^3.0.4"
    ts-polyfill "^3.0.1"
    whatwg-fetch "^2.0.4"

"@mtfe/thrift@>=4":
  version "4.3.2"
  resolved "http://r.npm.sankuai.com/@mtfe/thrift/download/@mtfe/thrift-4.3.2.tgz#85aad1cf24fda7b16f1bee85406272fb660881c3"
  integrity sha1-harRzyT9p7FvG+6FQGJy+2YIgcM=
  dependencies:
    "@dp/patriot-sdk" "^2.0.2"
    "@mtfe/cat" "^1.1.0"
    "@mtfe/mns-util" "^3.2.0"
    "@mtfe/octo-auth" "^0.2.0"
    "@mtfe/octo-idls" "^1.1.0"
    adler-32 "^1.0.0"
    co "^4.6.0"
    debug "^4.1.1"
    int64-transform "^0.1.13"
    ip "^1.1.5"
    memory-cache "^0.2.0"
    retry "^0.10.1"
    snappyjs "^0.5.0"
    thrift "0.11.0"
    uuid "^3.3.2"

"@mtfe/xm-web-sdk@4.7.6":
  version "4.7.6"
  resolved "http://r.npm.sankuai.com/@mtfe/xm-web-sdk/download/@mtfe/xm-web-sdk-4.7.6.tgz#63c7e37fbff95903b2bb690b139c65ecf15b2c52"
  integrity sha1-Y8fjf7/5WQOyu2kLE5xl7PFbLFI=
  dependencies:
    "@dp/owl" "^1.9.2-1"
    "@mtfe/knb-core" "^0.4.9"
    "@xm/analytics" "^1.3.24"
    axios "^0.19.2"
    eventemitter3 "^4.0.4"

"@mtfe/yapi2service@^1.1.5":
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/@mtfe/yapi2service/download/@mtfe/yapi2service-1.2.1.tgz#f6f020404e6392da7758d3c1f54d1baf41debca9"
  integrity sha1-9vAgQE5jktp3WNPB9U0br0HevKk=
  dependencies:
    axios "^0.24.0"
    chalk "^4.1.2"
    commander "^8.3.0"
    cross-spawn "^7.0.3"
    ejs "3.1.6"
    inquirer "^8.2.0"
    ora "^5.4.1"
    owner "^0.1.0"

"@nicolo-ribaudo/chokidar-2@2.1.8-no-fsevents.3":
  version "2.1.8-no-fsevents.3"
  resolved "http://r.npm.sankuai.com/@nicolo-ribaudo/chokidar-2/download/@nicolo-ribaudo/chokidar-2-2.1.8-no-fsevents.3.tgz#323d72dd25103d0c4fbdce89dadf574a787b1f9b"
  integrity sha1-Mj1y3SUQPQxPvc6J2t9XSnh7H5s=

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@originjs/vite-plugin-federation@^1.3.5":
  version "1.3.6"
  resolved "http://r.npm.sankuai.com/@originjs/vite-plugin-federation/download/@originjs/vite-plugin-federation-1.3.6.tgz#80d8396d43e252153da7c2fcde6487a6b1c32105"
  integrity sha1-gNg5bUPiUhU9p8L83mSHprHDIQU=
  dependencies:
    estree-walker "^3.0.2"
    magic-string "^0.27.0"

"@parcel/watcher-android-arm64@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-android-arm64/download/@parcel/watcher-android-arm64-2.5.0.tgz#e32d3dda6647791ee930556aee206fcd5ea0fb7a"
  integrity sha1-4y092mZHeR7pMFVq7iBvzV6g+3o=

"@parcel/watcher-darwin-arm64@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-darwin-arm64/download/@parcel/watcher-darwin-arm64-2.5.0.tgz#0d9e680b7e9ec1c8f54944f1b945aa8755afb12f"
  integrity sha1-DZ5oC36ewcj1SUTxuUWqh1WvsS8=

"@parcel/watcher-darwin-x64@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-darwin-x64/download/@parcel/watcher-darwin-x64-2.5.0.tgz#f9f1d5ce9d5878d344f14ef1856b7a830c59d1bb"
  integrity sha1-+fHVzp1YeNNE8U7xhWt6gwxZ0bs=

"@parcel/watcher-freebsd-x64@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-freebsd-x64/download/@parcel/watcher-freebsd-x64-2.5.0.tgz#2b77f0c82d19e84ff4c21de6da7f7d096b1a7e82"
  integrity sha1-K3fwyC0Z6E/0wh3m2n99CWsafoI=

"@parcel/watcher-linux-arm-glibc@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm-glibc/download/@parcel/watcher-linux-arm-glibc-2.5.0.tgz#92ed322c56dbafa3d2545dcf2803334aee131e42"
  integrity sha1-ku0yLFbbr6PSVF3PKAMzSu4THkI=

"@parcel/watcher-linux-arm-musl@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm-musl/download/@parcel/watcher-linux-arm-musl-2.5.0.tgz#cd48e9bfde0cdbbd2ecd9accfc52967e22f849a4"
  integrity sha1-zUjpv94M270uzZrM/FKWfiL4SaQ=

"@parcel/watcher-linux-arm64-glibc@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm64-glibc/download/@parcel/watcher-linux-arm64-glibc-2.5.0.tgz#7b81f6d5a442bb89fbabaf6c13573e94a46feb03"
  integrity sha1-e4H21aRCu4n7q69sE1c+lKRv6wM=

"@parcel/watcher-linux-arm64-musl@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-arm64-musl/download/@parcel/watcher-linux-arm64-musl-2.5.0.tgz#dcb8ff01077cdf59a18d9e0a4dff7a0cfe5fd732"
  integrity sha1-3Lj/AQd831mhjZ4KTf96DP5f1zI=

"@parcel/watcher-linux-x64-glibc@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-x64-glibc/download/@parcel/watcher-linux-x64-glibc-2.5.0.tgz#2e254600fda4e32d83942384d1106e1eed84494d"
  integrity sha1-LiVGAP2k4y2DlCOE0RBuHu2ESU0=

"@parcel/watcher-linux-x64-musl@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-linux-x64-musl/download/@parcel/watcher-linux-x64-musl-2.5.0.tgz#01fcea60fedbb3225af808d3f0a7b11229792eef"
  integrity sha1-AfzqYP7bsyJa+AjT8KexEil5Lu8=

"@parcel/watcher-win32-arm64@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-win32-arm64/download/@parcel/watcher-win32-arm64-2.5.0.tgz#87cdb16e0783e770197e52fb1dc027bb0c847154"
  integrity sha1-h82xbgeD53AZflL7HcAnuwyEcVQ=

"@parcel/watcher-win32-ia32@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-win32-ia32/download/@parcel/watcher-win32-ia32-2.5.0.tgz#778c39b56da33e045ba21c678c31a9f9d7c6b220"
  integrity sha1-d4w5tW2jPgRbohxnjDGp+dfGsiA=

"@parcel/watcher-win32-x64@2.5.0":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher-win32-x64/download/@parcel/watcher-win32-x64-2.5.0.tgz#33873876d0bbc588aacce38e90d1d7480ce81cb7"
  integrity sha1-M4c4dtC7xYiqzOOOkNHXSAzoHLc=

"@parcel/watcher@^2.4.1":
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/@parcel/watcher/download/@parcel/watcher-2.5.0.tgz#5c88818b12b8de4307a9d3e6dc3e28eba0dfbd10"
  integrity sha1-XIiBixK43kMHqdPm3D4o66DfvRA=
  dependencies:
    detect-libc "^1.0.3"
    is-glob "^4.0.3"
    micromatch "^4.0.5"
    node-addon-api "^7.0.0"
  optionalDependencies:
    "@parcel/watcher-android-arm64" "2.5.0"
    "@parcel/watcher-darwin-arm64" "2.5.0"
    "@parcel/watcher-darwin-x64" "2.5.0"
    "@parcel/watcher-freebsd-x64" "2.5.0"
    "@parcel/watcher-linux-arm-glibc" "2.5.0"
    "@parcel/watcher-linux-arm-musl" "2.5.0"
    "@parcel/watcher-linux-arm64-glibc" "2.5.0"
    "@parcel/watcher-linux-arm64-musl" "2.5.0"
    "@parcel/watcher-linux-x64-glibc" "2.5.0"
    "@parcel/watcher-linux-x64-musl" "2.5.0"
    "@parcel/watcher-win32-arm64" "2.5.0"
    "@parcel/watcher-win32-ia32" "2.5.0"
    "@parcel/watcher-win32-x64" "2.5.0"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "http://r.npm.sankuai.com/@pkgjs/parseargs/download/@pkgjs/parseargs-0.11.0.tgz#a77ea742fab25775145434eb1d2328cf5013ac33"
  integrity sha1-p36nQvqyV3UUVDTrHSMoz1ATrDM=

"@popperjs/core@^2.11.5":
  version "2.11.8"
  resolved "http://r.npm.sankuai.com/@popperjs/core/download/@popperjs/core-2.11.8.tgz#6b79032e760a0899cd4204710beede972a3a185f"
  integrity sha1-a3kDLnYKCJnNQgRxC+7elyo6GF8=

"@radix-ui/number@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/number/download/@radix-ui/number-1.1.1.tgz#7b2c9225fbf1b126539551f5985769d0048d9090"
  integrity sha1-eyySJfvxsSZTlVH1mFdp0ASNkJA=

"@radix-ui/primitive@1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@radix-ui/primitive/download/@radix-ui/primitive-1.1.2.tgz#83f415c4425f21e3d27914c12b3272a32e3dae65"
  integrity sha1-g/QVxEJfIePSeRTBKzJyoy49rmU=

"@radix-ui/react-accordion@^1.2.0":
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-accordion/download/@radix-ui/react-accordion-1.2.8.tgz#338f8a11c90199831a02c0adf9faa1fe06e324f6"
  integrity sha1-M4+KEckBmYMaAsCt+fqh/gbjJPY=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collapsible" "1.1.8"
    "@radix-ui/react-collection" "1.1.4"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-alert-dialog@^1.1.0":
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-alert-dialog/download/@radix-ui/react-alert-dialog-1.1.11.tgz#a4f7af5e315fed338e97499aa89d96716db8c14d"
  integrity sha1-pPevXjFf7TOOl0maqJ2WcW24wU0=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dialog" "1.1.11"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-slot" "1.2.0"

"@radix-ui/react-arrow@1.1.4":
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-arrow/download/@radix-ui/react-arrow-1.1.4.tgz#08e263c692b3a56a3f1c4bdc8405b7f73f070963"
  integrity sha1-COJjxpKzpWo/HEvchAW39z8HCWM=
  dependencies:
    "@radix-ui/react-primitive" "2.1.0"

"@radix-ui/react-aspect-ratio@^1.1.0":
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-aspect-ratio/download/@radix-ui/react-aspect-ratio-1.1.4.tgz#0300c5b2d08206bed19e938b48b129cbeda160ea"
  integrity sha1-AwDFstCCBr7RnpOLSLEpy+2hYOo=
  dependencies:
    "@radix-ui/react-primitive" "2.1.0"

"@radix-ui/react-avatar@^1.1.0":
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-avatar/download/@radix-ui/react-avatar-1.1.7.tgz#6a1334db6292f7e7a977a831dca6cbbb10a3f938"
  integrity sha1-ahM022KS9+epd6gx3KbLuxCj+Tg=
  dependencies:
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-is-hydrated" "0.1.0"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-checkbox@^1.1.0":
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-checkbox/download/@radix-ui/react-checkbox-1.2.3.tgz#808a4116f7ee6236b99cd62d6d7dc03b9cc9a04e"
  integrity sha1-gIpBFvfuYja5nNYtbX3AO5zJoE4=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-collapsible@1.1.8", "@radix-ui/react-collapsible@^1.1.0":
  version "1.1.8"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-collapsible/download/@radix-ui/react-collapsible-1.1.8.tgz#133554937046480339d640bf7d80d341af205dc0"
  integrity sha1-EzVUk3BGSAM51kC/fYDTQa8gXcA=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-collection@1.1.4":
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-collection/download/@radix-ui/react-collection-1.1.4.tgz#45fb4215ca26a84bd61b9b1337105e4d4e01b686"
  integrity sha1-RftCFcomqEvWG5sTNxBeTU4BtoY=
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-slot" "1.2.0"

"@radix-ui/react-compose-refs@1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-compose-refs/download/@radix-ui/react-compose-refs-1.1.2.tgz#a2c4c47af6337048ee78ff6dc0d090b390d2bb30"
  integrity sha1-osTEevYzcEjueP9twNCQs5DSuzA=

"@radix-ui/react-context-menu@^2.2.0":
  version "2.2.12"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-context-menu/download/@radix-ui/react-context-menu-2.2.12.tgz#23741b9ec6819316979d71058958074d2aaf6c67"
  integrity sha1-I3QbnsaBkxaXnXEFiVgHTSqvbGc=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-menu" "2.1.12"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-context@1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-context/download/@radix-ui/react-context-1.1.2.tgz#61628ef269a433382c364f6f1e3788a6dc213a36"
  integrity sha1-YWKO8mmkMzgsNk9vHjeIptwhOjY=

"@radix-ui/react-dialog@1.1.11", "@radix-ui/react-dialog@^1.1.0":
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-dialog/download/@radix-ui/react-dialog-1.1.11.tgz#1144609cbc9f8b36bcc288beb880f6b72cbd85ee"
  integrity sha1-EURgnLyfiza8woi+uID2tyy9he4=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.7"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.4"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-portal" "1.1.6"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-slot" "1.2.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-direction@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-direction/download/@radix-ui/react-direction-1.1.1.tgz#39e5a5769e676c753204b792fbe6cf508e550a14"
  integrity sha1-OeWldp5nbHUyBLeS++bPUI5VChQ=

"@radix-ui/react-dismissable-layer@1.1.7":
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-dismissable-layer/download/@radix-ui/react-dismissable-layer-1.1.7.tgz#80b5c23a0d29cfe56850399210c603376c27091f"
  integrity sha1-gLXCOg0pz+VoUDmSEMYDN2wnCR8=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-escape-keydown" "1.1.1"

"@radix-ui/react-dropdown-menu@^2.1.0":
  version "2.1.12"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-dropdown-menu/download/@radix-ui/react-dropdown-menu-2.1.12.tgz#18c71e1588c5bd436e8d32d0464b6237c0647e4b"
  integrity sha1-GMceFYjFvUNujTLQRktiN8Bkfks=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-menu" "2.1.12"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-focus-guards@1.1.2":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-focus-guards/download/@radix-ui/react-focus-guards-1.1.2.tgz#4ec9a7e50925f7fb661394460045b46212a33bed"
  integrity sha1-Tsmn5Qkl9/tmE5RGAEW0YhKjO+0=

"@radix-ui/react-focus-scope@1.1.4":
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-focus-scope/download/@radix-ui/react-focus-scope-1.1.4.tgz#dbe9ed31b36ff9aadadf4b59aa733a4e91799d15"
  integrity sha1-2+ntMbNv+ara30tZqnM6TpF5nRU=
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-hover-card@^1.1.0":
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-hover-card/download/@radix-ui/react-hover-card-1.1.11.tgz#f57d322bc4a3cd19b5f2ce18fca621dd249a2e43"
  integrity sha1-9X0yK8SjzRm18s4Y/KYh3SSaLkM=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.7"
    "@radix-ui/react-popper" "1.2.4"
    "@radix-ui/react-portal" "1.1.6"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-id@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-id/download/@radix-ui/react-id-1.1.1.tgz#1404002e79a03fe062b7e3864aa01e24bd1471f7"
  integrity sha1-FAQALnmgP+Bit+OGSqAeJL0Ucfc=
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-label@^2.1.0":
  version "2.1.4"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-label/download/@radix-ui/react-label-2.1.4.tgz#e89607486b82381f2d28ce1ce022e7fb5f5a158c"
  integrity sha1-6JYHSGuCOB8tKM4c4CLn+19aFYw=
  dependencies:
    "@radix-ui/react-primitive" "2.1.0"

"@radix-ui/react-menu@2.1.12":
  version "2.1.12"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-menu/download/@radix-ui/react-menu-2.1.12.tgz#011eb9c0dfc58a01bc8d7eea04ed62450a0b6563"
  integrity sha1-AR65wN/FigG8jX7qBO1iRQoLZWM=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.4"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.7"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.4"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.4"
    "@radix-ui/react-portal" "1.1.6"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-roving-focus" "1.1.7"
    "@radix-ui/react-slot" "1.2.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-menubar@^1.1.0":
  version "1.1.12"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-menubar/download/@radix-ui/react-menubar-1.1.12.tgz#dc6ea82bdb8dab80fbc46144eb3d757a6799676c"
  integrity sha1-3G6oK9uNq4D7xGFE6z11emeZZ2w=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.4"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-menu" "2.1.12"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-roving-focus" "1.1.7"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-navigation-menu@^1.2.0":
  version "1.2.10"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-navigation-menu/download/@radix-ui/react-navigation-menu-1.2.10.tgz#3677d37f8205719b03ec2c67585ebbb42142d90d"
  integrity sha1-NnfTf4IFcZsD7CxnWF67tCFC2Q0=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.4"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.2.0"

"@radix-ui/react-popover@^1.1.0":
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-popover/download/@radix-ui/react-popover-1.1.11.tgz#141efda5a03a103c7f229c74da3443d70add4d11"
  integrity sha1-FB79paA6EDx/Ipx02jRD1wrdTRE=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.7"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.4"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.4"
    "@radix-ui/react-portal" "1.1.6"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-slot" "1.2.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-popper@1.2.4":
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-popper/download/@radix-ui/react-popper-1.2.4.tgz#8fd6d954fca9e5d1341c7d9153cc88e05d5ed84e"
  integrity sha1-j9bZVPyp5dE0HH2RU8yI4F1e2E4=
  dependencies:
    "@floating-ui/react-dom" "^2.0.0"
    "@radix-ui/react-arrow" "1.1.4"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-rect" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-portal@1.1.6":
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-portal/download/@radix-ui/react-portal-1.1.6.tgz#4202e1bb34afdac612e4e982eca8efd36cbc611f"
  integrity sha1-QgLhuzSv2sYS5OmC7Kjv02y8YR8=
  dependencies:
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-presence@1.1.4":
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-presence/download/@radix-ui/react-presence-1.1.4.tgz#253ac0ad4946c5b4a9c66878335f5cf07c967ced"
  integrity sha1-JTrArUlGxbSpxmh4M19c8HyWfO0=
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-primitive@2.1.0":
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-primitive/download/@radix-ui/react-primitive-2.1.0.tgz#9233e17a22d0010195086f8b5eb1808ebbca8437"
  integrity sha1-kjPheiLQAQGVCG+LXrGAjrvKhDc=
  dependencies:
    "@radix-ui/react-slot" "1.2.0"

"@radix-ui/react-progress@^1.1.0":
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-progress/download/@radix-ui/react-progress-1.1.4.tgz#c11e3fd6bfd39fb123bc397d2875aa648898c043"
  integrity sha1-wR4/1r/Tn7EjvDl9KHWqZIiYwEM=
  dependencies:
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.0"

"@radix-ui/react-radio-group@^1.2.0":
  version "1.3.4"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-radio-group/download/@radix-ui/react-radio-group-1.3.4.tgz#4af1216b4171fc93eeca4998088b161ae744101e"
  integrity sha1-SvEha0Fx/JPuykmYCIsWGudEEB4=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-roving-focus" "1.1.7"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-roving-focus@1.1.7":
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-roving-focus/download/@radix-ui/react-roving-focus-1.1.7.tgz#02077705ab0c712d2d9692459a7194c5a4e5236d"
  integrity sha1-Agd3BasMcS0tlpJFmnGUxaTlI20=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.4"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-scroll-area@^1.1.0":
  version "1.2.6"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-scroll-area/download/@radix-ui/react-scroll-area-1.2.6.tgz#cd4b113e812e92f63ef6959f609ac7e8eaadb1aa"
  integrity sha1-zUsRPoEukvY+9pWfYJrH6Oqtsao=
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-select@^2.1.0":
  version "2.2.2"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-select/download/@radix-ui/react-select-2.2.2.tgz#96759b9dcf4e80f6f39c1ad706718f1b2928ba21"
  integrity sha1-lnWbnc9OgPbznBrXBnGPGykouiE=
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.4"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.7"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.4"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.4"
    "@radix-ui/react-portal" "1.1.6"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-slot" "1.2.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.2.0"
    aria-hidden "^1.2.4"
    react-remove-scroll "^2.6.3"

"@radix-ui/react-separator@^1.1.0":
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-separator/download/@radix-ui/react-separator-1.1.4.tgz#86663cc4f89c2f66cae91629501c05126a5cf8ec"
  integrity sha1-hmY8xPicL2bK6RYpUBwFEmpc+Ow=
  dependencies:
    "@radix-ui/react-primitive" "2.1.0"

"@radix-ui/react-slider@^1.2.0":
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-slider/download/@radix-ui/react-slider-1.3.2.tgz#0849ba97f841a03b1be6a3ec5622575aa356f098"
  integrity sha1-CEm6l/hBoDsb5qPsViJXWqNW8Jg=
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.4"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-slot@1.2.0", "@radix-ui/react-slot@^1.1.0":
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-slot/download/@radix-ui/react-slot-1.2.0.tgz#57727fc186ddb40724ccfbe294e1a351d92462ba"
  integrity sha1-V3J/wYbdtAckzPvilOGjUdkkYro=
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"

"@radix-ui/react-switch@^1.1.0":
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-switch/download/@radix-ui/react-switch-1.2.2.tgz#aee51a72b93b49d625e201e32c43deb7957e4641"
  integrity sha1-ruUacrk7SdYl4gHjLEPet5V+RkE=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-tabs@^1.1.0":
  version "1.1.9"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-tabs/download/@radix-ui/react-tabs-1.1.9.tgz#4170432717f32d2e75de3a9853ad0a557ccc1346"
  integrity sha1-QXBDJxfzLS513jqYU60KVXzME0Y=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-roving-focus" "1.1.7"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-toast@^1.2.0":
  version "1.2.11"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-toast/download/@radix-ui/react-toast-1.2.11.tgz#24b54f11a149e2bfa96c91490ea417671e5194f2"
  integrity sha1-JLVPEaFJ4r+pbJFJDqQXZx5RlPI=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.4"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.7"
    "@radix-ui/react-portal" "1.1.6"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.2.0"

"@radix-ui/react-toggle-group@^1.1.0":
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-toggle-group/download/@radix-ui/react-toggle-group-1.1.7.tgz#0eaca9e4f8fbf2536f01e33a6211eac4d6cfb83e"
  integrity sha1-Dqyp5Pj78lNvAeM6YhHqxNbPuD4=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-roving-focus" "1.1.7"
    "@radix-ui/react-toggle" "1.1.6"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-toggle@1.1.6", "@radix-ui/react-toggle@^1.1.0":
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-toggle/download/@radix-ui/react-toggle-1.1.6.tgz#8d993d88d2abcd327fd18a6126adb2e6e0848a3c"
  integrity sha1-jZk9iNKrzTJ/0YphJq2y5uCEijw=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-tooltip@^1.1.0":
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-tooltip/download/@radix-ui/react-tooltip-1.2.4.tgz#d9111cfccb47891cf19e6e025b7583f3ceadb4bc"
  integrity sha1-2REc/MtHiRzxnm4CW3WD886ttLw=
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.4"
    "@radix-ui/react-portal" "1.1.6"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.0"
    "@radix-ui/react-slot" "1.2.0"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-visually-hidden" "1.2.0"

"@radix-ui/react-use-callback-ref@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-callback-ref/download/@radix-ui/react-use-callback-ref-1.1.1.tgz#62a4dba8b3255fdc5cc7787faeac1c6e4cc58d40"
  integrity sha1-YqTbqLMlX9xcx3h/rqwcbkzFjUA=

"@radix-ui/react-use-controllable-state@1.2.2":
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-controllable-state/download/@radix-ui/react-use-controllable-state-1.2.2.tgz#905793405de57d61a439f4afebbb17d0645f3190"
  integrity sha1-kFeTQF3lfWGkOfSv67sX0GRfMZA=
  dependencies:
    "@radix-ui/react-use-effect-event" "0.0.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-use-effect-event@0.0.2":
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-effect-event/download/@radix-ui/react-use-effect-event-0.0.2.tgz#090cf30d00a4c7632a15548512e9152217593907"
  integrity sha1-CQzzDQCkx2MqFVSFEukVIhdZOQc=
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-use-escape-keydown@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-escape-keydown/download/@radix-ui/react-use-escape-keydown-1.1.1.tgz#b3fed9bbea366a118f40427ac40500aa1423cc29"
  integrity sha1-s/7Zu+o2ahGPQEJ6xAUAqhQjzCk=
  dependencies:
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-use-is-hydrated@0.1.0":
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-is-hydrated/download/@radix-ui/react-use-is-hydrated-0.1.0.tgz#544da73369517036c77659d7cdd019dc0f5ff9a0"
  integrity sha1-VE2nM2lRcDbHdlnXzdAZ3A9f+aA=
  dependencies:
    use-sync-external-store "^1.5.0"

"@radix-ui/react-use-layout-effect@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-layout-effect/download/@radix-ui/react-use-layout-effect-1.1.1.tgz#0c4230a9eed49d4589c967e2d9c0d9d60a23971e"
  integrity sha1-DEIwqe7UnUWJyWfi2cDZ1gojlx4=

"@radix-ui/react-use-previous@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-previous/download/@radix-ui/react-use-previous-1.1.1.tgz#1a1ad5568973d24051ed0af687766f6c7cb9b5b5"
  integrity sha1-GhrVVolz0kBR7Qr2h3ZvbHy5tbU=

"@radix-ui/react-use-rect@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-rect/download/@radix-ui/react-use-rect-1.1.1.tgz#01443ca8ed071d33023c1113e5173b5ed8769152"
  integrity sha1-AUQ8qO0HHTMCPBET5Rc7Xth2kVI=
  dependencies:
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-use-size@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-use-size/download/@radix-ui/react-use-size-1.1.1.tgz#6de276ffbc389a537ffe4316f5b0f24129405b37"
  integrity sha1-beJ2/7w4mlN//kMW9bDyQSlAWzc=
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-visually-hidden@1.2.0":
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/@radix-ui/react-visually-hidden/download/@radix-ui/react-visually-hidden-1.2.0.tgz#7692a590b4789bebf7e02d73e6d1390704a97920"
  integrity sha1-dpKlkLR4m+v34C1z5tE5BwSpeSA=
  dependencies:
    "@radix-ui/react-primitive" "2.1.0"

"@radix-ui/rect@1.1.1":
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/@radix-ui/rect/download/@radix-ui/rect-1.1.1.tgz#78244efe12930c56fd255d7923865857c41ac8cb"
  integrity sha1-eCRO/hKTDFb9JV15I4ZYV8QayMs=

"@rc-component/async-validator@^5.0.3":
  version "5.0.4"
  resolved "http://r.npm.sankuai.com/@rc-component/async-validator/download/@rc-component/async-validator-5.0.4.tgz#5291ad92f00a14b6766fc81735c234277f83e948"
  integrity sha1-UpGtkvAKFLZ2b8gXNcI0J3+D6Ug=
  dependencies:
    "@babel/runtime" "^7.24.4"

"@rc-component/color-picker@~2.0.1":
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/@rc-component/color-picker/download/@rc-component/color-picker-2.0.1.tgz#6b9b96152466a9d4475cbe72b40b594bfda164be"
  integrity sha1-a5uWFSRmqdRHXL5ytAtZS/2hZL4=
  dependencies:
    "@ant-design/fast-color" "^2.0.6"
    "@babel/runtime" "^7.23.6"
    classnames "^2.2.6"
    rc-util "^5.38.1"

"@rc-component/context@^1.4.0":
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/@rc-component/context/download/@rc-component/context-1.4.0.tgz#dc6fb021d6773546af8f016ae4ce9aea088395e8"
  integrity sha1-3G+wIdZ3NUavjwFq5M6a6giDleg=
  dependencies:
    "@babel/runtime" "^7.10.1"
    rc-util "^5.27.0"

"@rc-component/mini-decimal@^1.0.1":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@rc-component/mini-decimal/download/@rc-component/mini-decimal-1.1.0.tgz#7b7a362b14a0a54cb5bc6fd2b82731f29f11d9b0"
  integrity sha1-e3o2KxSgpUy1vG/SuCcx8p8R2bA=
  dependencies:
    "@babel/runtime" "^7.18.0"

"@rc-component/mutate-observer@^1.1.0":
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/@rc-component/mutate-observer/download/@rc-component/mutate-observer-1.1.0.tgz#ee53cc88b78aade3cd0653609215a44779386fd8"
  integrity sha1-7lPMiLeKrePNBlNgkhWkR3k4b9g=
  dependencies:
    "@babel/runtime" "^7.18.0"
    classnames "^2.3.2"
    rc-util "^5.24.4"

"@rc-component/portal@^1.0.0-8", "@rc-component/portal@^1.0.0-9", "@rc-component/portal@^1.0.2", "@rc-component/portal@^1.1.0", "@rc-component/portal@^1.1.1":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@rc-component/portal/download/@rc-component/portal-1.1.2.tgz#55db1e51d784e034442e9700536faaa6ab63fc71"
  integrity sha1-VdseUdeE4DRELpcAU2+qpqtj/HE=
  dependencies:
    "@babel/runtime" "^7.18.0"
    classnames "^2.3.2"
    rc-util "^5.24.4"

"@rc-component/qrcode@~1.0.0":
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/@rc-component/qrcode/download/@rc-component/qrcode-1.0.0.tgz#48a8de5eb11d0e65926f1377c4b1ef4c888997f5"
  integrity sha1-SKjeXrEdDmWSbxN3xLHvTIiJl/U=
  dependencies:
    "@babel/runtime" "^7.24.7"
    classnames "^2.3.2"
    rc-util "^5.38.0"

"@rc-component/tour@~1.15.1":
  version "1.15.1"
  resolved "http://r.npm.sankuai.com/@rc-component/tour/download/@rc-component/tour-1.15.1.tgz#9b79808254185fc19e964172d99e25e8c6800ded"
  integrity sha1-m3mAglQYX8GelkFy2Z4l6MaADe0=
  dependencies:
    "@babel/runtime" "^7.18.0"
    "@rc-component/portal" "^1.0.0-9"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.3.2"
    rc-util "^5.24.4"

"@rc-component/trigger@^2.0.0", "@rc-component/trigger@^2.1.1", "@rc-component/trigger@^2.2.5", "@rc-component/trigger@^2.2.6":
  version "2.2.6"
  resolved "http://r.npm.sankuai.com/@rc-component/trigger/download/@rc-component/trigger-2.2.6.tgz#bfe6602313b3fadd659687746511f813299d5ea4"
  integrity sha1-v+ZgIxOz+t1llod0ZRH4EymdXqQ=
  dependencies:
    "@babel/runtime" "^7.23.2"
    "@rc-component/portal" "^1.1.0"
    classnames "^2.3.2"
    rc-motion "^2.0.0"
    rc-resize-observer "^1.3.1"
    rc-util "^5.44.0"

"@reactflow/background@11.3.14":
  version "11.3.14"
  resolved "http://r.npm.sankuai.com/@reactflow/background/download/@reactflow/background-11.3.14.tgz#778ca30174f3de77fc321459ab3789e66e71a699"
  integrity sha1-d4yjAXTz3nf8MhRZqzeJ5m5xppk=
  dependencies:
    "@reactflow/core" "11.11.4"
    classcat "^5.0.3"
    zustand "^4.4.1"

"@reactflow/controls@11.2.14":
  version "11.2.14"
  resolved "http://r.npm.sankuai.com/@reactflow/controls/download/@reactflow/controls-11.2.14.tgz#508ed2c40d23341b3b0919dd11e76fd49cf850c7"
  integrity sha1-UI7SxA0jNBs7CRndEedv1Jz4UMc=
  dependencies:
    "@reactflow/core" "11.11.4"
    classcat "^5.0.3"
    zustand "^4.4.1"

"@reactflow/core@11.11.4":
  version "11.11.4"
  resolved "http://r.npm.sankuai.com/@reactflow/core/download/@reactflow/core-11.11.4.tgz#89bd86d1862aa1416f3f49926cede7e8c2aab6a7"
  integrity sha1-ib2G0YYqoUFvP0mSbO3n6MKqtqc=
  dependencies:
    "@types/d3" "^7.4.0"
    "@types/d3-drag" "^3.0.1"
    "@types/d3-selection" "^3.0.3"
    "@types/d3-zoom" "^3.0.1"
    classcat "^5.0.3"
    d3-drag "^3.0.0"
    d3-selection "^3.0.0"
    d3-zoom "^3.0.0"
    zustand "^4.4.1"

"@reactflow/minimap@11.7.14":
  version "11.7.14"
  resolved "http://r.npm.sankuai.com/@reactflow/minimap/download/@reactflow/minimap-11.7.14.tgz#298d7a63cb1da06b2518c99744f716560c88ca73"
  integrity sha1-KY16Y8sdoGslGMmXRPcWVgyIynM=
  dependencies:
    "@reactflow/core" "11.11.4"
    "@types/d3-selection" "^3.0.3"
    "@types/d3-zoom" "^3.0.1"
    classcat "^5.0.3"
    d3-selection "^3.0.0"
    d3-zoom "^3.0.0"
    zustand "^4.4.1"

"@reactflow/node-resizer@2.2.14":
  version "2.2.14"
  resolved "http://r.npm.sankuai.com/@reactflow/node-resizer/download/@reactflow/node-resizer-2.2.14.tgz#1810c0ce51aeb936f179466a6660d1e02c7a77a8"
  integrity sha1-GBDAzlGuuTbxeUZqZmDR4Cx6d6g=
  dependencies:
    "@reactflow/core" "11.11.4"
    classcat "^5.0.4"
    d3-drag "^3.0.0"
    d3-selection "^3.0.0"
    zustand "^4.4.1"

"@reactflow/node-toolbar@1.3.14":
  version "1.3.14"
  resolved "http://r.npm.sankuai.com/@reactflow/node-toolbar/download/@reactflow/node-toolbar-1.3.14.tgz#c6ffc76f82acacdce654f2160dc9852162d6e7c9"
  integrity sha1-xv/Hb4KsrNzmVPIWDcmFIWLW58k=
  dependencies:
    "@reactflow/core" "11.11.4"
    classcat "^5.0.3"
    zustand "^4.4.1"

"@rollup/plugin-virtual@^3.0.2":
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/@rollup/plugin-virtual/download/@rollup/plugin-virtual-3.0.2.tgz#17e17eeecb4c9fa1c0a6e72c9e5f66382fddbb82"
  integrity sha1-F+F+7stMn6HApucsnl9mOC/du4I=

"@rollup/rollup-android-arm-eabi@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-android-arm-eabi/download/@rollup/rollup-android-arm-eabi-4.40.0.tgz#d964ee8ce4d18acf9358f96adc408689b6e27fe3"
  integrity sha1-2WTujOTRis+TWPlq3ECGibbif+M=

"@rollup/rollup-android-arm64@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-android-arm64/download/@rollup/rollup-android-arm64-4.40.0.tgz#9b5e130ecc32a5fc1e96c09ff371743ee71a62d3"
  integrity sha1-m14TDswypfwelsCf83F0PucaYtM=

"@rollup/rollup-darwin-arm64@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-darwin-arm64/download/@rollup/rollup-darwin-arm64-4.40.0.tgz#ef439182c739b20b3c4398cfc03e3c1249ac8903"
  integrity sha1-70ORgsc5sgs8Q5jPwD48EkmsiQM=

"@rollup/rollup-darwin-x64@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-darwin-x64/download/@rollup/rollup-darwin-x64-4.40.0.tgz#d7380c1531ab0420ca3be16f17018ef72dd3d504"
  integrity sha1-1zgMFTGrBCDKO+FvFwGO9y3T1QQ=

"@rollup/rollup-freebsd-arm64@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-freebsd-arm64/download/@rollup/rollup-freebsd-arm64-4.40.0.tgz#cbcbd7248823c6b430ce543c59906dd3c6df0936"
  integrity sha1-y8vXJIgjxrQwzlQ8WZBt08bfCTY=

"@rollup/rollup-freebsd-x64@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-freebsd-x64/download/@rollup/rollup-freebsd-x64-4.40.0.tgz#96bf6ff875bab5219c3472c95fa6eb992586a93b"
  integrity sha1-lr9v+HW6tSGcNHLJX6brmSWGqTs=

"@rollup/rollup-linux-arm-gnueabihf@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm-gnueabihf/download/@rollup/rollup-linux-arm-gnueabihf-4.40.0.tgz#d80cd62ce6d40f8e611008d8dbf03b5e6bbf009c"
  integrity sha1-2AzWLObUD45hEAjY2/A7Xmu/AJw=

"@rollup/rollup-linux-arm-musleabihf@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm-musleabihf/download/@rollup/rollup-linux-arm-musleabihf-4.40.0.tgz#75440cfc1e8d0f87a239b4c31dfeaf4719b656b7"
  integrity sha1-dUQM/B6ND4eiObTDHf6vRxm2Vrc=

"@rollup/rollup-linux-arm64-gnu@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm64-gnu/download/@rollup/rollup-linux-arm64-gnu-4.40.0.tgz#ac527485ecbb619247fb08253ec8c551a0712e7c"
  integrity sha1-rFJ0hey7YZJH+wglPsjFUaBxLnw=

"@rollup/rollup-linux-arm64-musl@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-arm64-musl/download/@rollup/rollup-linux-arm64-musl-4.40.0.tgz#74d2b5cb11cf714cd7d1682e7c8b39140e908552"
  integrity sha1-dNK1yxHPcUzX0WgufIs5FA6QhVI=

"@rollup/rollup-linux-loongarch64-gnu@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-loongarch64-gnu/download/@rollup/rollup-linux-loongarch64-gnu-4.40.0.tgz#a0a310e51da0b5fea0e944b0abd4be899819aef6"
  integrity sha1-oKMQ5R2gtf6g6USwq9S+iZgZrvY=

"@rollup/rollup-linux-powerpc64le-gnu@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-powerpc64le-gnu/download/@rollup/rollup-linux-powerpc64le-gnu-4.40.0.tgz#4077e2862b0ac9f61916d6b474d988171bd43b83"
  integrity sha1-QHfihisKyfYZFta0dNmIFxvUO4M=

"@rollup/rollup-linux-riscv64-gnu@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-riscv64-gnu/download/@rollup/rollup-linux-riscv64-gnu-4.40.0.tgz#5812a1a7a2f9581cbe12597307cc7ba3321cf2f3"
  integrity sha1-WBKhp6L5WBy+EllzB8x7ozIc8vM=

"@rollup/rollup-linux-riscv64-musl@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-riscv64-musl/download/@rollup/rollup-linux-riscv64-musl-4.40.0.tgz#973aaaf4adef4531375c36616de4e01647f90039"
  integrity sha1-lzqq9K3vRTE3XDZhbeTgFkf5ADk=

"@rollup/rollup-linux-s390x-gnu@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-s390x-gnu/download/@rollup/rollup-linux-s390x-gnu-4.40.0.tgz#9bad59e907ba5bfcf3e9dbd0247dfe583112f70b"
  integrity sha1-m61Z6Qe6W/zz6dvQJH3+WDES9ws=

"@rollup/rollup-linux-x64-gnu@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-x64-gnu/download/@rollup/rollup-linux-x64-gnu-4.40.0.tgz#68b045a720bd9b4d905f462b997590c2190a6de0"
  integrity sha1-aLBFpyC9m02QX0YrmXWQwhkKbeA=

"@rollup/rollup-linux-x64-musl@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-linux-x64-musl/download/@rollup/rollup-linux-x64-musl-4.40.0.tgz#8e703e2c2ad19ba7b2cb3d8c3a4ad11d4ee3a282"
  integrity sha1-jnA+LCrRm6eyyz2MOkrRHU7jooI=

"@rollup/rollup-win32-arm64-msvc@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-arm64-msvc/download/@rollup/rollup-win32-arm64-msvc-4.40.0.tgz#c5bee19fa670ff5da5f066be6a58b4568e9c650b"
  integrity sha1-xb7hn6Zw/12l8Ga+ali0Vo6cZQs=

"@rollup/rollup-win32-ia32-msvc@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-ia32-msvc/download/@rollup/rollup-win32-ia32-msvc-4.40.0.tgz#846e02c17044bd922f6f483a3b4d36aac6e2b921"
  integrity sha1-hG4CwXBEvZIvb0g6O002qsbiuSE=

"@rollup/rollup-win32-x64-msvc@4.40.0":
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/@rollup/rollup-win32-x64-msvc/download/@rollup/rollup-win32-x64-msvc-4.40.0.tgz#fd92d31a2931483c25677b9c6698106490cbbc76"
  integrity sha1-/ZLTGikxSDwlZ3ucZpgQZJDLvHY=

"@roo/analyze@^0.0.7":
  version "0.0.7"
  resolved "http://r.npm.sankuai.com/@roo/analyze/download/@roo/analyze-0.0.7.tgz#5a2aa8ee2d17766a906e1a5fc88497c0d6b1c67a"
  integrity sha1-Wiqo7i0XdmqQbhpfyISXwNaxxno=
  dependencies:
    "@babel/cli" "^7.8.0"
    "@babel/core" "^7.7.5"
    "@babel/generator" "^7.7.4"
    "@babel/parser" "^7.7.4"
    "@babel/plugin-proposal-decorators" "^7.7.4"
    "@babel/plugin-proposal-export-default-from" "^7.7.4"
    "@babel/plugin-syntax-decorators" "^7.7.4"
    "@babel/plugin-syntax-dynamic-import" "^7.7.4"
    "@babel/plugin-transform-destructuring" "^7.7.4"
    "@babel/polyfill" "^7.7.0"
    "@babel/preset-flow" "^7.7.4"
    "@babel/preset-react" "^7.7.4"
    "@babel/traverse" "^7.7.4"
    "@babel/types" "^7.7.4"
    dayjs "^1.9.1"
    lodash "^4.17.15"
    minimist "^1.2.5"
    moment "^2.24.0"

"@roo/create-react-ref@0.0.2":
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/@roo/create-react-ref/download/@roo/create-react-ref-0.0.2.tgz#bf74a0cc1de3dca91bab98ec2104b9f08bf1c919"
  integrity sha1-v3SgzB3j3Kkbq5jsIQS58IvxyRk=

"@roo/react-color@^1.0.2":
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/@roo/react-color/download/@roo/react-color-1.0.2.tgz#19abe8adf84e384e5766af071faf0bfffd045559"
  integrity sha1-GavorfhOOE5XZq8HH68L//0EVVk=
  dependencies:
    lodash "^4.17.11"
    prop-types "^15.5.10"
    reactcss "^1.2.0"
    tinycolor2 "^1.4.1"

"@roo/roo-cooperation-report@^0.0.9":
  version "0.0.9"
  resolved "http://r.npm.sankuai.com/@roo/roo-cooperation-report/download/@roo/roo-cooperation-report-0.0.9.tgz#3dbdf41d5bed378ef71a057dffc05d8d15550c61"
  integrity sha1-Pb30HVvtN473GgV9/8BdjRVVDGE=
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.18.6"
    "@babel/plugin-transform-runtime" "^7.22.9"
    "@babel/preset-env" "^7.22.9"
    "@roo/analyze" "^0.0.7"
    request "^2.88.2"

"@roo/roo-plus-report@0.0.3":
  version "0.0.3"
  resolved "http://r.npm.sankuai.com/@roo/roo-plus-report/download/@roo/roo-plus-report-0.0.3.tgz#bca7c83c026b64433419beb7346195c548307676"
  integrity sha1-vKfIPAJrZEM0Gb63NGGVxUgwdnY=
  dependencies:
    "@babel/plugin-proposal-class-properties" "^7.18.6"
    "@babel/plugin-transform-runtime" "^7.23.6"
    "@babel/preset-env" "^7.23.6"
    "@roo/analyze" "^0.0.7"
    request "^2.88.2"

"@roo/roo-plus@^0.6.8":
  version "0.6.9"
  resolved "http://r.npm.sankuai.com/@roo/roo-plus/download/@roo/roo-plus-0.6.9.tgz#fdb33741ef83604a10689821ccd3dceea6eb71a5"
  integrity sha1-/bM3Qe+DYEoQaJghzNPc7qbrcaU=
  dependencies:
    "@babel/runtime" "^7.20.13"
    "@roo/roo-plus-report" "0.0.3"
    "@roo/roo-theme-var" "^1.4.5"
    "@utiljs/clone" "^0.2.8"
    "@utiljs/functional" "^0.6.5"
    "@utiljs/guid" "^0.5.7"
    "@utiljs/is" "^0.11.10"
    "@utiljs/param" "^0.6.11"
    "@wangeditor/editor" "^5.1.23"
    "@wangeditor/editor-for-react" "^1.0.6"
    "@yyfe/Copy" "^1.0.15"
    axios "^0.19.0"
    classnames "^2.2.6"
    copy-to-clipboard "^3.2.0"
    create-react-context "^0.3.0"
    cropperjs "^1.5.13"
    immer "^9.0.17"
    immutability-helper "^3.0.1"
    lodash "^4.17.21"
    qs "^6.11.0"
    quill-image-resize-module-react "^3.0.0"
    rc-progress "^3.5.1"
    react-load-script "0.0.6"
    react-quill "^2.0.0"
    react-router "^4.3.1"
    react-router-dom "^4.3.1"
    uuid "^9.0.0"
    warning "^4.0.3"

"@roo/roo-theme-var@^1.4.5":
  version "1.4.5"
  resolved "http://r.npm.sankuai.com/@roo/roo-theme-var/download/@roo/roo-theme-var-1.4.5.tgz#f21d206bb207cd54563dae0827110c5683ea05e5"
  integrity sha1-8h0ga7IHzVRWPa4IJxEMVoPqBeU=

"@roo/roo@^1.18.0":
  version "1.18.1"
  resolved "http://r.npm.sankuai.com/@roo/roo/download/@roo/roo-1.18.1.tgz#2b44167a158ddf84790d5a7f4da94e72f5e1916a"
  integrity sha1-K0QWehWN34R5DVp/TalOcvXhkWo=
  dependencies:
    "@ai/mss-upload-js" "^1.1.7"
    "@babel/runtime-corejs2" "^7.18.9"
    "@popperjs/core" "^2.11.5"
    "@rc-component/trigger" "^2.2.5"
    "@roo/create-react-ref" "0.0.2"
    "@roo/react-color" "^1.0.2"
    "@roo/roo-theme-var" "^1.4.5"
    "@utiljs/clone" "^0.2.8"
    "@utiljs/cookie" "^0.1.6"
    "@utiljs/dom" "^0.2.6"
    "@utiljs/functional" "^0.6.5"
    "@utiljs/guid" "^0.5.7"
    "@utiljs/is" "^0.11.10"
    "@utiljs/param" "^0.6.11"
    "@utiljs/type" "^0.5.5"
    "@utiljs/use-request" "^0.2.5-beta.35"
    "@wangeditor/editor" "^5.1.23"
    "@yyfe/Copy" "^1.0.21"
    async-validator "^1.10.0"
    axios "^0.18.0"
    classnames "^2.2.6"
    cropperjs "^1.5.13"
    dayjs "1.11.13"
    eslint-config-prettier "^6.15.0"
    eslint-plugin-prettier "^3.4.1"
    hoist-non-react-statics "^3.3.1"
    lodash "^4.17.15"
    lodash-es "^4.17.21"
    memoize-one "^5.1.1"
    moment "^2.29.4"
    prop-types "^15.8.1"
    raf-schd "^4.0.3"
    rc-field-form "~1.38.2"
    rc-menu "~9.16.0"
    rc-motion "^2.9.5"
    rc-progress "~3.2.1"
    rc-table "^7.48.1"
    rc-util "^5.32.2"
    rc-virtual-list "^3.14.2"
    react-click-outside "^3.0.1"
    react-drag-listview "^2.0.0"
    react-fast-compare "^2.0.4"
    react-is "^18.2.0"
    react-lifecycles-compat "^3.0.4"
    react-popper "^2.3.0"
    react-slick "^0.30.2"
    react-transition-group "^2.5.3"
    react-window "^1.8.8"
    resize-observer-polyfill "^1.5.1"
    scroll-into-view-if-needed "^2.2.31"
    warning "^4.0.3"

"@sindresorhus/is@^4.0.0":
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/@sindresorhus/is/download/@sindresorhus/is-4.6.0.tgz#3c7c9c46e678feefe7a2e5bb609d3dbd665ffb3f"
  integrity sha1-PHycRuZ4/u/nouW7YJ09vWZf+z8=

"@swc/core-darwin-arm64@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@swc/core-darwin-arm64/download/@swc/core-darwin-arm64-1.7.11.tgz#53cd615d55330329ed5ac843b8f6aedb5de7c82f"
  integrity sha1-U81hXVUzAyntWshDuPau213nyC8=

"@swc/core-darwin-x64@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@swc/core-darwin-x64/download/@swc/core-darwin-x64-1.7.11.tgz#c0e3f248d075160b86f12b21b9dafee48196f52e"
  integrity sha1-wOPySNB1FguG8Sshudr+5IGW9S4=

"@swc/core-linux-arm-gnueabihf@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-arm-gnueabihf/download/@swc/core-linux-arm-gnueabihf-1.7.11.tgz#677f87c806261243afe4903fde3dfac11e9f159b"
  integrity sha1-Z3+HyAYmEkOv5JA/3j36wR6fFZs=

"@swc/core-linux-arm64-gnu@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-arm64-gnu/download/@swc/core-linux-arm64-gnu-1.7.11.tgz#ad38860e7ebed7ece215ea02f1a134798275ce2c"
  integrity sha1-rTiGDn6+1+ziFeoC8aE0eYJ1ziw=

"@swc/core-linux-arm64-musl@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-arm64-musl/download/@swc/core-linux-arm64-musl-1.7.11.tgz#ffe7cf7e23b6c4022c66b274cc2ff068c0a7cede"
  integrity sha1-/+fPfiO2xAIsZrJ0zC/waMCnzt4=

"@swc/core-linux-x64-gnu@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-x64-gnu/download/@swc/core-linux-x64-gnu-1.7.11.tgz#697fb7dcb453509d8a08da781e7ec337b112f54b"
  integrity sha1-aX+33LRTUJ2KCNp4Hn7DN7ES9Us=

"@swc/core-linux-x64-musl@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@swc/core-linux-x64-musl/download/@swc/core-linux-x64-musl-1.7.11.tgz#70deeedd81d77deb062c71d68cab79b36219f79f"
  integrity sha1-cN7u3YHXfesGLHHWjKt5s2IZ958=

"@swc/core-win32-arm64-msvc@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@swc/core-win32-arm64-msvc/download/@swc/core-win32-arm64-msvc-1.7.11.tgz#f232c2d5ea93a0aa6650e5a8c49f5b23db6a218b"
  integrity sha1-8jLC1eqToKpmUOWoxJ9bI9tqIYs=

"@swc/core-win32-ia32-msvc@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@swc/core-win32-ia32-msvc/download/@swc/core-win32-ia32-msvc-1.7.11.tgz#3fa43c3bf4b1593fa9abe017b55080651e7fff06"
  integrity sha1-P6Q8O/SxWT+pq+AXtVCAZR5//wY=

"@swc/core-win32-x64-msvc@1.7.11":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@swc/core-win32-x64-msvc/download/@swc/core-win32-x64-msvc-1.7.11.tgz#433bac0a04a0a49c9d9c8f1fe45f5555c88deca7"
  integrity sha1-QzusCgSgpJydnI8f5F9VVciN7Kc=

"@swc/core@1.7.11", "@swc/core@^1.7.0", "@swc/core@^1.7.26":
  version "1.7.11"
  resolved "http://r.npm.sankuai.com/@swc/core/download/@swc/core-1.7.11.tgz#167f46ee64f7fdd0eb58e62d0a5643fa65b86559"
  integrity sha1-Fn9G7mT3/dDrWOYtClZD+mW4ZVk=
  dependencies:
    "@swc/counter" "^0.1.3"
    "@swc/types" "^0.1.12"
  optionalDependencies:
    "@swc/core-darwin-arm64" "1.7.11"
    "@swc/core-darwin-x64" "1.7.11"
    "@swc/core-linux-arm-gnueabihf" "1.7.11"
    "@swc/core-linux-arm64-gnu" "1.7.11"
    "@swc/core-linux-arm64-musl" "1.7.11"
    "@swc/core-linux-x64-gnu" "1.7.11"
    "@swc/core-linux-x64-musl" "1.7.11"
    "@swc/core-win32-arm64-msvc" "1.7.11"
    "@swc/core-win32-ia32-msvc" "1.7.11"
    "@swc/core-win32-x64-msvc" "1.7.11"

"@swc/counter@^0.1.3":
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/@swc/counter/download/@swc/counter-0.1.3.tgz#cc7463bd02949611c6329596fccd2b0ec782b0e9"
  integrity sha1-zHRjvQKUlhHGMpWW/M0rDseCsOk=

"@swc/types@^0.1.12":
  version "0.1.17"
  resolved "http://r.npm.sankuai.com/@swc/types/download/@swc/types-0.1.17.tgz#bd1d94e73497f27341bf141abdf4c85230d41e7c"
  integrity sha1-vR2U5zSX8nNBvxQavfTIUjDUHnw=
  dependencies:
    "@swc/counter" "^0.1.3"

"@szmarczak/http-timer@^4.0.5":
  version "4.0.6"
  resolved "http://r.npm.sankuai.com/@szmarczak/http-timer/download/@szmarczak/http-timer-4.0.6.tgz#b4a914bb62e7c272d4e5989fe4440f812ab1d807"
  integrity sha1-tKkUu2LnwnLU5Zif5EQPgSqx2Ac=
  dependencies:
    defer-to-connect "^2.0.0"

"@thrift-api/it-iam-ssoopenapi@1.0.13-RELEASE":
  version "1.0.13-RELEASE"
  resolved "http://r.npm.sankuai.com/@thrift-api/it-iam-ssoopenapi/download/@thrift-api/it-iam-ssoopenapi-1.0.13-RELEASE.tgz#8de00289b9e4a3f598939a9e610e28e1213c7830"
  integrity sha1-jeACibnko/WYk5qeYQ4o4SE8eDA=

"@thrift-api/it-iam-ssoservice@1.0.19-SNAPSHOT":
  version "1.0.19-SNAPSHOT"
  resolved "http://r.npm.sankuai.com/@thrift-api/it-iam-ssoservice/download/@thrift-api/it-iam-ssoservice-1.0.19-SNAPSHOT.tgz#e72a3d475a372e03c95fbc28b247b3a9f504ca09"
  integrity sha1-5yo9R1o3LgPJX7woskezqfUEygk=

"@transloadit/prettier-bytes@0.0.7":
  version "0.0.7"
  resolved "http://r.npm.sankuai.com/@transloadit/prettier-bytes/download/@transloadit/prettier-bytes-0.0.7.tgz#cdb5399f445fdd606ed833872fa0cabdbc51686b"
  integrity sha1-zbU5n0Rf3WBu2DOHL6DKvbxRaGs=

"@types/axios@^0.14.0":
  version "0.14.4"
  resolved "http://r.npm.sankuai.com/@types/axios/download/@types/axios-0.14.4.tgz#174e3a05fe7677f13bc719f0d2a427f5defacedf"
  integrity sha1-F046Bf52d/E7xxnw0qQn9d76zt8=
  dependencies:
    axios "*"

"@types/cacheable-request@^6.0.1":
  version "6.0.3"
  resolved "http://r.npm.sankuai.com/@types/cacheable-request/download/@types/cacheable-request-6.0.3.tgz#a430b3260466ca7b5ca5bfd735693b36e7a9d183"
  integrity sha1-pDCzJgRmyntcpb/XNWk7Nuep0YM=
  dependencies:
    "@types/http-cache-semantics" "*"
    "@types/keyv" "^3.1.4"
    "@types/node" "*"
    "@types/responselike" "^1.0.0"

"@types/d3-array@*", "@types/d3-array@^3.2.1":
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/@types/d3-array/download/@types/d3-array-3.2.1.tgz#1f6658e3d2006c4fceac53fde464166859f8b8c5"
  integrity sha1-H2ZY49IAbE/OrFP95GQWaFn4uMU=

"@types/d3-axis@*":
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/@types/d3-axis/download/@types/d3-axis-3.0.6.tgz#e760e5765b8188b1defa32bc8bb6062f81e4c795"
  integrity sha1-52DldluBiLHe+jK8i7YGL4Hkx5U=
  dependencies:
    "@types/d3-selection" "*"

"@types/d3-brush@*":
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/@types/d3-brush/download/@types/d3-brush-3.0.6.tgz#c2f4362b045d472e1b186cdbec329ba52bdaee6c"
  integrity sha1-wvQ2KwRdRy4bGGzb7DKbpSva7mw=
  dependencies:
    "@types/d3-selection" "*"

"@types/d3-chord@*":
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/@types/d3-chord/download/@types/d3-chord-3.0.6.tgz#1706ca40cf7ea59a0add8f4456efff8f8775793d"
  integrity sha1-FwbKQM9+pZoK3Y9EVu//j4d1eT0=

"@types/d3-color@*", "@types/d3-color@^3.1.3":
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/@types/d3-color/download/@types/d3-color-3.1.3.tgz#368c961a18de721da8200e80bf3943fb53136af2"
  integrity sha1-NoyWGhjech2oIA6AvzlD+1MTavI=

"@types/d3-contour@*":
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/@types/d3-contour/download/@types/d3-contour-3.0.6.tgz#9ada3fa9c4d00e3a5093fed0356c7ab929604231"
  integrity sha1-mto/qcTQDjpQk/7QNWx6uSlgQjE=
  dependencies:
    "@types/d3-array" "*"
    "@types/geojson" "*"

"@types/d3-delaunay@*":
  version "6.0.4"
  resolved "http://r.npm.sankuai.com/@types/d3-delaunay/download/@types/d3-delaunay-6.0.4.tgz#185c1a80cc807fdda2a3fe960f7c11c4a27952e1"
  integrity sha1-GFwagMyAf92io/6WD3wRxKJ5UuE=

"@types/d3-dispatch@*", "@types/d3-dispatch@^3.0.6":
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/@types/d3-dispatch/download/@types/d3-dispatch-3.0.6.tgz#096efdf55eb97480e3f5621ff9a8da552f0961e7"
  integrity sha1-CW799V65dIDj9WIf+ajaVS8JYec=

"@types/d3-drag@*", "@types/d3-drag@^3.0.1":
  version "3.0.7"
  resolved "http://r.npm.sankuai.com/@types/d3-drag/download/@types/d3-drag-3.0.7.tgz#b13aba8b2442b4068c9a9e6d1d82f8bcea77fc02"
  integrity sha1-sTq6iyRCtAaMmp5tHYL4vOp3/AI=
  dependencies:
    "@types/d3-selection" "*"

"@types/d3-dsv@*", "@types/d3-dsv@^3.0.7":
  version "3.0.7"
  resolved "http://r.npm.sankuai.com/@types/d3-dsv/download/@types/d3-dsv-3.0.7.tgz#0a351f996dc99b37f4fa58b492c2d1c04e3dac17"
  integrity sha1-CjUfmW3Jmzf0+li0ksLRwE49rBc=

"@types/d3-ease@*":
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/@types/d3-ease/download/@types/d3-ease-3.0.2.tgz#e28db1bfbfa617076f7770dd1d9a48eaa3b6c51b"
  integrity sha1-4o2xv7+mFwdvd3DdHZpI6qO2xRs=

"@types/d3-fetch@*", "@types/d3-fetch@^3.0.7":
  version "3.0.7"
  resolved "http://r.npm.sankuai.com/@types/d3-fetch/download/@types/d3-fetch-3.0.7.tgz#c04a2b4f23181aa376f30af0283dbc7b3b569980"
  integrity sha1-wEorTyMYGqN28wrwKD28eztWmYA=
  dependencies:
    "@types/d3-dsv" "*"

"@types/d3-force@*", "@types/d3-force@^3.0.10":
  version "3.0.10"
  resolved "http://r.npm.sankuai.com/@types/d3-force/download/@types/d3-force-3.0.10.tgz#6dc8fc6e1f35704f3b057090beeeb7ac674bff1a"
  integrity sha1-bcj8bh81cE87BXCQvu63rGdL/xo=

"@types/d3-format@*", "@types/d3-format@^3.0.4":
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/@types/d3-format/download/@types/d3-format-3.0.4.tgz#b1e4465644ddb3fdf3a263febb240a6cd616de90"
  integrity sha1-seRGVkTds/3zomP+uyQKbNYW3pA=

"@types/d3-geo@*", "@types/d3-geo@^3.1.0":
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/@types/d3-geo/download/@types/d3-geo-3.1.0.tgz#b9e56a079449174f0a2c8684a9a4df3f60522440"
  integrity sha1-ueVqB5RJF08KLIaEqaTfP2BSJEA=
  dependencies:
    "@types/geojson" "*"

"@types/d3-hierarchy@*", "@types/d3-hierarchy@^3.1.7":
  version "3.1.7"
  resolved "http://r.npm.sankuai.com/@types/d3-hierarchy/download/@types/d3-hierarchy-3.1.7.tgz#6023fb3b2d463229f2d680f9ac4b47466f71f17b"
  integrity sha1-YCP7Oy1GMiny1oD5rEtHRm9x8Xs=

"@types/d3-interpolate@*", "@types/d3-interpolate@^3.0.4":
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/@types/d3-interpolate/download/@types/d3-interpolate-3.0.4.tgz#412b90e84870285f2ff8a846c6eb60344f12a41c"
  integrity sha1-QSuQ6EhwKF8v+KhGxutgNE8SpBw=
  dependencies:
    "@types/d3-color" "*"

"@types/d3-path@*", "@types/d3-path@^3.1.0":
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/@types/d3-path/download/@types/d3-path-3.1.1.tgz#f632b380c3aca1dba8e34aa049bcd6a4af23df8a"
  integrity sha1-9jKzgMOsoduo40qgSbzWpK8j34o=

"@types/d3-polygon@*":
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/@types/d3-polygon/download/@types/d3-polygon-3.0.2.tgz#dfae54a6d35d19e76ac9565bcb32a8e54693189c"
  integrity sha1-***************************=

"@types/d3-quadtree@*", "@types/d3-quadtree@^3.0.6":
  version "3.0.6"
  resolved "http://r.npm.sankuai.com/@types/d3-quadtree/download/@types/d3-quadtree-3.0.6.tgz#d4740b0fe35b1c58b66e1488f4e7ed02952f570f"
  integrity sha1-1HQLD+NbHFi2bhSI9OftApUvVw8=

"@types/d3-random@*", "@types/d3-random@^3.0.3":
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/@types/d3-random/download/@types/d3-random-3.0.3.tgz#ed995c71ecb15e0cd31e22d9d5d23942e3300cfb"
  integrity sha1-7ZlcceyxXgzTHiLZ1dI5QuMwDPs=

"@types/d3-scale-chromatic@*", "@types/d3-scale-chromatic@^3.1.0":
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/@types/d3-scale-chromatic/download/@types/d3-scale-chromatic-3.1.0.tgz#dc6d4f9a98376f18ea50bad6c39537f1b5463c39"
  integrity sha1-3G1Pmpg3bxjqULrWw5U38bVGPDk=

"@types/d3-scale@*", "@types/d3-scale@^4.0.9":
  version "4.0.9"
  resolved "http://r.npm.sankuai.com/@types/d3-scale/download/@types/d3-scale-4.0.9.tgz#57a2f707242e6fe1de81ad7bfcccaaf606179afb"
  integrity sha1-V6L3ByQub+Hega17/Myq9gYXmvs=
  dependencies:
    "@types/d3-time" "*"

"@types/d3-selection@*", "@types/d3-selection@^3.0.3":
  version "3.0.11"
  resolved "http://r.npm.sankuai.com/@types/d3-selection/download/@types/d3-selection-3.0.11.tgz#bd7a45fc0a8c3167a631675e61bc2ca2b058d4a3"
  integrity sha1-vXpF/AqMMWemMWdeYbwsorBY1KM=

"@types/d3-shape@*", "@types/d3-shape@^3.1.7":
  version "3.1.7"
  resolved "http://r.npm.sankuai.com/@types/d3-shape/download/@types/d3-shape-3.1.7.tgz#2b7b423dc2dfe69c8c93596e673e37443348c555"
  integrity sha1-K3tCPcLf5pyMk1luZz43RDNIxVU=
  dependencies:
    "@types/d3-path" "*"

"@types/d3-time-format@*":
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/@types/d3-time-format/download/@types/d3-time-format-4.0.3.tgz#d6bc1e6b6a7db69cccfbbdd4c34b70632d9e9db2"
  integrity sha1-1rwea2p9tpzM+73Uw0twYy2enbI=

"@types/d3-time@*", "@types/d3-time@^3.0.4":
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/@types/d3-time/download/@types/d3-time-3.0.4.tgz#8472feecd639691450dd8000eb33edd444e1323f"
  integrity sha1-hHL+7NY5aRRQ3YAA6zPt1EThMj8=

"@types/d3-timer@*", "@types/d3-timer@^3.0.2":
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/@types/d3-timer/download/@types/d3-timer-3.0.2.tgz#70bbda77dc23aa727413e22e214afa3f0e852f70"
  integrity sha1-cLvad9wjqnJ0E+IuIUr6Pw6FL3A=

"@types/d3-timer@^2.0.0":
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/@types/d3-timer/download/@types/d3-timer-2.0.3.tgz#d74350a9eb5991f054b2cf8e92efaf22be3e1a25"
  integrity sha1-10NQqetZkfBUss+Oku+vIr4+GiU=

"@types/d3-transition@*":
  version "3.0.9"
  resolved "http://r.npm.sankuai.com/@types/d3-transition/download/@types/d3-transition-3.0.9.tgz#1136bc57e9ddb3c390dccc9b5ff3b7d2b8d94706"
  integrity sha1-ETa8V+nds8OQ3MybX/O30rjZRwY=
  dependencies:
    "@types/d3-selection" "*"

"@types/d3-zoom@*", "@types/d3-zoom@^3.0.1":
  version "3.0.8"
  resolved "http://r.npm.sankuai.com/@types/d3-zoom/download/@types/d3-zoom-3.0.8.tgz#dccb32d1c56b1e1c6e0f1180d994896f038bc40b"
  integrity sha1-3Msy0cVrHhxuDxGA2ZSJbwOLxAs=
  dependencies:
    "@types/d3-interpolate" "*"
    "@types/d3-selection" "*"

"@types/d3@^7.4.0":
  version "7.4.3"
  resolved "http://r.npm.sankuai.com/@types/d3/download/@types/d3-7.4.3.tgz#d4550a85d08f4978faf0a4c36b848c61eaac07e2"
  integrity sha1-1FUKhdCPSXj68KTDa4SMYeqsB+I=
  dependencies:
    "@types/d3-array" "*"
    "@types/d3-axis" "*"
    "@types/d3-brush" "*"
    "@types/d3-chord" "*"
    "@types/d3-color" "*"
    "@types/d3-contour" "*"
    "@types/d3-delaunay" "*"
    "@types/d3-dispatch" "*"
    "@types/d3-drag" "*"
    "@types/d3-dsv" "*"
    "@types/d3-ease" "*"
    "@types/d3-fetch" "*"
    "@types/d3-force" "*"
    "@types/d3-format" "*"
    "@types/d3-geo" "*"
    "@types/d3-hierarchy" "*"
    "@types/d3-interpolate" "*"
    "@types/d3-path" "*"
    "@types/d3-polygon" "*"
    "@types/d3-quadtree" "*"
    "@types/d3-random" "*"
    "@types/d3-scale" "*"
    "@types/d3-scale-chromatic" "*"
    "@types/d3-selection" "*"
    "@types/d3-shape" "*"
    "@types/d3-time" "*"
    "@types/d3-time-format" "*"
    "@types/d3-timer" "*"
    "@types/d3-transition" "*"
    "@types/d3-zoom" "*"

"@types/estree@1.0.7":
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/@types/estree/download/@types/estree-1.0.7.tgz#4158d3105276773d5b7695cd4834b1722e4f37a8"
  integrity sha1-QVjTEFJ2dz1bdpXNSDSxci5PN6g=

"@types/estree@^1.0.0":
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/@types/estree/download/@types/estree-1.0.6.tgz#628effeeae2064a1b4e79f78e81d87b7e5fc7b50"
  integrity sha1-Yo7/7q4gZKG055946B2Ht+X8e1A=

"@types/event-emitter@^0.3.3":
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/@types/event-emitter/download/@types/event-emitter-0.3.5.tgz#ce9b513f72c50dcf0443a12165a93a79ba7a7092"
  integrity sha1-zptRP3LFDc8EQ6EhZak6ebp6cJI=

"@types/geojson@*":
  version "7946.0.16"
  resolved "http://r.npm.sankuai.com/@types/geojson/download/@types/geojson-7946.0.16.tgz#8ebe53d69efada7044454e3305c19017d97ced2a"
  integrity sha1-jr5T1p762nBERU4zBcGQF9l87So=

"@types/http-cache-semantics@*":
  version "4.0.4"
  resolved "http://r.npm.sankuai.com/@types/http-cache-semantics/download/@types/http-cache-semantics-4.0.4.tgz#b979ebad3919799c979b17c72621c0bc0a31c6c4"
  integrity sha1-uXnrrTkZeZyXmxfHJiHAvAoxxsQ=

"@types/json-schema@^7.0.9":
  version "7.0.15"
  resolved "http://r.npm.sankuai.com/@types/json-schema/download/@types/json-schema-7.0.15.tgz#596a1747233694d50f6ad8a7869fcb6f56cf5841"
  integrity sha1-WWoXRyM2lNUPatinhp/Lb1bPWEE=

"@types/keyv@^3.1.4":
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/@types/keyv/download/@types/keyv-3.1.4.tgz#3ccdb1c6751b0c7e52300bcdacd5bcbf8faa75b6"
  integrity sha1-PM2xxnUbDH5SMAvNrNW8v4+qdbY=
  dependencies:
    "@types/node" "*"

"@types/node@*", "@types/node@^22.1.0":
  version "22.10.2"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-22.10.2.tgz#a485426e6d1fdafc7b0d4c7b24e2c78182ddabb9"
  integrity sha1-pIVCbm0f2vx7DUx7JOLHgYLdq7k=
  dependencies:
    undici-types "~6.20.0"

"@types/node@^11.13.2":
  version "11.15.54"
  resolved "http://r.npm.sankuai.com/@types/node/download/@types/node-11.15.54.tgz#59ed60e7b0d56905a654292e8d73275034eb6283"
  integrity sha1-We1g57DVaQWmVCkujXMnUDTrYoM=

"@types/prop-types@*":
  version "15.7.14"
  resolved "http://r.npm.sankuai.com/@types/prop-types/download/@types/prop-types-15.7.14.tgz#1433419d73b2a7ebfc6918dcefd2ec0d5cd698f2"
  integrity sha1-FDNBnXOyp+v8aRjc79LsDVzWmPI=

"@types/quill@^1.3.10":
  version "1.3.10"
  resolved "http://r.npm.sankuai.com/@types/quill/download/@types/quill-1.3.10.tgz#dc1f7b6587f7ee94bdf5291bc92289f6f0497613"
  integrity sha1-3B97ZYf37pS99SkbySKJ9vBJdhM=
  dependencies:
    parchment "^1.1.2"

"@types/react-dom@^18.0.10":
  version "18.3.5"
  resolved "http://r.npm.sankuai.com/@types/react-dom/download/@types/react-dom-18.3.5.tgz#45f9f87398c5dcea085b715c58ddcf1faf65f716"
  integrity sha1-Rfn4c5jF3OoIW3FcWN3PH69l9xY=

"@types/react@^18.0.27":
  version "18.3.17"
  resolved "http://r.npm.sankuai.com/@types/react/download/@types/react-18.3.17.tgz#d86ca0e081c7a5e979b7db175f9515a41038cea7"
  integrity sha1-2Gyg4IHHpel5t9sXX5UVpBA4zqc=
  dependencies:
    "@types/prop-types" "*"
    csstype "^3.0.2"

"@types/responselike@^1.0.0":
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/@types/responselike/download/@types/responselike-1.0.3.tgz#cc29706f0a397cfe6df89debfe4bf5cea159db50"
  integrity sha1-zClwbwo5fP5t+J3r/kv1zqFZ21A=
  dependencies:
    "@types/node" "*"

"@types/semver@^7.3.12":
  version "7.5.8"
  resolved "http://r.npm.sankuai.com/@types/semver/download/@types/semver-7.5.8.tgz#8268a8c57a3e4abd25c165ecd36237db7948a55e"
  integrity sha1-gmioxXo+Sr0lwWXs02I323lIpV4=

"@typescript-eslint/eslint-plugin@^5.55.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-5.62.0.tgz#aeef0328d172b9e37d9bab6dbc13b87ed88977db"
  integrity sha1-ru8DKNFyueN9m6ttvBO4ftiJd9s=
  dependencies:
    "@eslint-community/regexpp" "^4.4.0"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/type-utils" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    natural-compare-lite "^1.4.0"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/parser@^5.55.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/parser/download/@typescript-eslint/parser-5.62.0.tgz#1b63d082d849a2fcae8a569248fbe2ee1b8a56c7"
  integrity sha1-G2PQgthJovyuilaSSPvi7huKVsc=
  dependencies:
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-5.62.0.tgz#d9457ccc6a0b8d6b37d0eb252a23022478c5460c"
  integrity sha1-2UV8zGoLjWs30OslKiMCJHjFRgw=
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"

"@typescript-eslint/type-utils@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-5.62.0.tgz#286f0389c41681376cdad96b309cedd17d70346a"
  integrity sha1-KG8DicQWgTds2tlrMJzt0X1wNGo=
  dependencies:
    "@typescript-eslint/typescript-estree" "5.62.0"
    "@typescript-eslint/utils" "5.62.0"
    debug "^4.3.4"
    tsutils "^3.21.0"

"@typescript-eslint/types@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/types/download/@typescript-eslint/types-5.62.0.tgz#258607e60effa309f067608931c3df6fed41fd2f"
  integrity sha1-JYYH5g7/ownwZ2CJMcPfb+1B/S8=

"@typescript-eslint/typescript-estree@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-5.62.0.tgz#7d17794b77fabcac615d6a48fb143330d962eb9b"
  integrity sha1-fRd5S3f6vKxhXWpI+xQzMNli65s=
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/visitor-keys" "5.62.0"
    debug "^4.3.4"
    globby "^11.1.0"
    is-glob "^4.0.3"
    semver "^7.3.7"
    tsutils "^3.21.0"

"@typescript-eslint/utils@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/utils/download/@typescript-eslint/utils-5.62.0.tgz#141e809c71636e4a75daa39faed2fb5f4b10df86"
  integrity sha1-FB6AnHFjbkp12qOfrtL7X0sQ34Y=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@types/json-schema" "^7.0.9"
    "@types/semver" "^7.3.12"
    "@typescript-eslint/scope-manager" "5.62.0"
    "@typescript-eslint/types" "5.62.0"
    "@typescript-eslint/typescript-estree" "5.62.0"
    eslint-scope "^5.1.1"
    semver "^7.3.7"

"@typescript-eslint/visitor-keys@5.62.0":
  version "5.62.0"
  resolved "http://r.npm.sankuai.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-5.62.0.tgz#2174011917ce582875954ffe2f6912d5931e353e"
  integrity sha1-IXQBGRfOWCh1lU/+L2kS1ZMeNT4=
  dependencies:
    "@typescript-eslint/types" "5.62.0"
    eslint-visitor-keys "^3.3.0"

"@ungap/structured-clone@^1.2.0":
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/@ungap/structured-clone/download/@ungap/structured-clone-1.2.1.tgz#28fa185f67daaf7b7a1a8c1d445132c5d979f8bd"
  integrity sha1-KPoYX2far3t6GowdRFEyxdl5+L0=

"@uppy/companion-client@^2.2.2":
  version "2.2.2"
  resolved "http://r.npm.sankuai.com/@uppy/companion-client/download/@uppy/companion-client-2.2.2.tgz#c70b42fdcca728ef88b3eebf7ee3e2fa04b4923b"
  integrity sha1-xwtC/cynKO+Is+6/fuPi+gS0kjs=
  dependencies:
    "@uppy/utils" "^4.1.2"
    namespace-emitter "^2.0.1"

"@uppy/core@^2.1.1":
  version "2.3.4"
  resolved "http://r.npm.sankuai.com/@uppy/core/download/@uppy/core-2.3.4.tgz#260b85b6bf3aa03cdc67da231f8c69cfbfdcc84a"
  integrity sha1-JguFtr86oDzcZ9ojH4xpz7/cyEo=
  dependencies:
    "@transloadit/prettier-bytes" "0.0.7"
    "@uppy/store-default" "^2.1.1"
    "@uppy/utils" "^4.1.3"
    lodash.throttle "^4.1.1"
    mime-match "^1.0.2"
    namespace-emitter "^2.0.1"
    nanoid "^3.1.25"
    preact "^10.5.13"

"@uppy/store-default@^2.1.1":
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/@uppy/store-default/download/@uppy/store-default-2.1.1.tgz#62a656a099bdaa012306e054d093754cb2d36e3e"
  integrity sha1-YqZWoJm9qgEjBuBU0JN1TLLTbj4=

"@uppy/utils@^4.1.2", "@uppy/utils@^4.1.3":
  version "4.1.3"
  resolved "http://r.npm.sankuai.com/@uppy/utils/download/@uppy/utils-4.1.3.tgz#9d0be6ece4df25f228d30ef40be0f14208258ce3"
  integrity sha1-nQvm7OTfJfIo0w70C+DxQggljOM=
  dependencies:
    lodash.throttle "^4.1.1"

"@uppy/xhr-upload@^2.0.3":
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/@uppy/xhr-upload/download/@uppy/xhr-upload-2.1.3.tgz#0d4e355332fe0c6eb372d7731315e04d02aeeb18"
  integrity sha1-DU41UzL+DG6zctdzExXgTQKu6xg=
  dependencies:
    "@uppy/companion-client" "^2.2.2"
    "@uppy/utils" "^4.1.2"
    nanoid "^3.1.25"

"@utiljs/clone@^0.2.8":
  version "0.2.8"
  resolved "http://r.npm.sankuai.com/@utiljs/clone/download/@utiljs/clone-0.2.8.tgz#cb73dbb9ce60b3256fee764b95cf5651b6b5c7ad"
  integrity sha1-y3Pbuc5gsyVv7nZLlc9WUba1x60=
  dependencies:
    "@utiljs/type" "0.5.4"

"@utiljs/console@0.1.5":
  version "0.1.5"
  resolved "http://r.npm.sankuai.com/@utiljs/console/download/@utiljs/console-0.1.5.tgz#eca3720104d6fc814c4f71dcf5c2b6acc6ce4956"
  integrity sha1-7KNyAQTW/IFMT3Hc9cK2rMbOSVY=

"@utiljs/console@0.1.6":
  version "0.1.6"
  resolved "http://r.npm.sankuai.com/@utiljs/console/download/@utiljs/console-0.1.6.tgz#90f150c749121f40ec363eeac585605e49488ac7"
  integrity sha1-kPFQx0kSH0DsNj7qxYVgXklIisc=

"@utiljs/cookie@^0.1.6":
  version "0.1.6"
  resolved "http://r.npm.sankuai.com/@utiljs/cookie/download/@utiljs/cookie-0.1.6.tgz#cbd08b495196dff6c0a64daf724590ca8ddf3bf5"
  integrity sha1-y9CLSVGW3/bApk2vckWQyo3fO/U=

"@utiljs/dom@^0.2.6":
  version "0.2.6"
  resolved "http://r.npm.sankuai.com/@utiljs/dom/download/@utiljs/dom-0.2.6.tgz#8269852f7838f5f05fc919faea56f0be8bae4471"
  integrity sha1-gmmFL3g49fBfyRn66lbwvouuRHE=
  dependencies:
    "@utiljs/console" "0.1.6"
    "@utiljs/is" "0.11.10"

"@utiljs/extend@0.1.9":
  version "0.1.9"
  resolved "http://r.npm.sankuai.com/@utiljs/extend/download/@utiljs/extend-0.1.9.tgz#6f5980e67a2ab8d895dc595038a7bb8dab39337a"
  integrity sha1-b1mA5noquNiV3FlQOKe7jas5M3o=
  dependencies:
    "@utiljs/is" "0.11.10"

"@utiljs/functional@^0.6.5":
  version "0.6.5"
  resolved "http://r.npm.sankuai.com/@utiljs/functional/download/@utiljs/functional-0.6.5.tgz#bc896b92ea02350abd4323ddc8895d7a674b1171"
  integrity sha1-vIlrkuoCNQq9QyPdyIldemdLEXE=
  dependencies:
    "@utiljs/type" "0.5.4"

"@utiljs/guid@^0.5.7":
  version "0.5.7"
  resolved "http://r.npm.sankuai.com/@utiljs/guid/download/@utiljs/guid-0.5.7.tgz#bd2c341cfa490062ab97d08f533468759b6ca6e9"
  integrity sha1-vSw0HPpJAGKrl9CPUzRodZtspuk=

"@utiljs/is@0.11.10", "@utiljs/is@^0.11.10":
  version "0.11.10"
  resolved "http://r.npm.sankuai.com/@utiljs/is/download/@utiljs/is-0.11.10.tgz#3b26d426bb9078c6bfd8eacf9aba7cd6d6e1716e"
  integrity sha1-OybUJruQeMa/2OrPmrp81tbhcW4=
  dependencies:
    "@utiljs/string" "0.6.6"
    "@utiljs/type" "0.5.5"

"@utiljs/param@^0.6.11":
  version "0.6.11"
  resolved "http://r.npm.sankuai.com/@utiljs/param/download/@utiljs/param-0.6.11.tgz#a3eb83a4d97c94972fedf75aa1be47d827546dd0"
  integrity sha1-o+uDpNl8lJcv7fdaob5H2CdUbdA=
  dependencies:
    "@utiljs/extend" "0.1.9"
    "@utiljs/type" "0.5.5"

"@utiljs/send-request@^1.0.0":
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/@utiljs/send-request/download/@utiljs/send-request-1.0.1.tgz#7ff09b1a7e81a4dbc3872b2a016156c4ec909db8"
  integrity sha1-f/CbGn6BpNvDhysqAWFWxOyQnbg=
  dependencies:
    "@babel/plugin-transform-runtime" "^7"
    "@babel/runtime" "^7"
    core-js "^3"

"@utiljs/string@0.6.6":
  version "0.6.6"
  resolved "http://r.npm.sankuai.com/@utiljs/string/download/@utiljs/string-0.6.6.tgz#628571ec5c0ac12bb1ea3526586f9eed774c9b7f"
  integrity sha1-YoVx7FwKwSux6jUmWG+e7XdMm38=
  dependencies:
    "@utiljs/console" "0.1.5"
    "@utiljs/type" "0.5.4"

"@utiljs/type@0.5.4":
  version "0.5.4"
  resolved "http://r.npm.sankuai.com/@utiljs/type/download/@utiljs/type-0.5.4.tgz#9638197892b5d43a1857bbc1c6507c0dd00d18c6"
  integrity sha1-ljgZeJK11DoYV7vBxlB8DdANGMY=

"@utiljs/type@0.5.5", "@utiljs/type@^0.5.5":
  version "0.5.5"
  resolved "http://r.npm.sankuai.com/@utiljs/type/download/@utiljs/type-0.5.5.tgz#5bc799133a85118fffbffaf1c6b5d14b0ca2e8cc"
  integrity sha1-W8eZEzqFEY//v/rxxrXRSwyi6Mw=

"@utiljs/use-request@^0.2.5-beta.35":
  version "0.2.5"
  resolved "http://r.npm.sankuai.com/@utiljs/use-request/download/@utiljs/use-request-0.2.5.tgz#dc2fc84b17321748a16008be326c04c1dc4b1849"
  integrity sha1-3C/ISxcyF0ihYAi+MmwEwdxLGEk=
  dependencies:
    "@babel/plugin-transform-runtime" "^7"
    "@babel/runtime" "^7"
    "@utiljs/send-request" "^1.0.0"
    core-js "^3"
    lodash "^4"

"@vitejs/plugin-react-swc@^3.6.0":
  version "3.7.2"
  resolved "http://r.npm.sankuai.com/@vitejs/plugin-react-swc/download/@vitejs/plugin-react-swc-3.7.2.tgz#b0958dd44c48dbd37b5ef887bdb8b8d1276f24cd"
  integrity sha1-sJWN1ExI29N7XviHvbi40SdvJM0=
  dependencies:
    "@swc/core" "^1.7.26"

"@wangeditor/basic-modules@^1.1.7":
  version "1.1.7"
  resolved "http://r.npm.sankuai.com/@wangeditor/basic-modules/download/@wangeditor/basic-modules-1.1.7.tgz#a9c3ccf4ef53332f29550d59d3676e15f395946f"
  integrity sha1-qcPM9O9TMy8pVQ1Z02duFfOVlG8=
  dependencies:
    is-url "^1.2.4"

"@wangeditor/code-highlight@^1.0.3":
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/@wangeditor/code-highlight/download/@wangeditor/code-highlight-1.0.3.tgz#90256857714d5c0cf83ac475aea64db7bf29a7cd"
  integrity sha1-kCVoV3FNXAz4OsR1rqZNt78pp80=
  dependencies:
    prismjs "^1.23.0"

"@wangeditor/core@^1.1.19":
  version "1.1.19"
  resolved "http://r.npm.sankuai.com/@wangeditor/core/download/@wangeditor/core-1.1.19.tgz#f9155f7fd92d03cb1982405b3b82e54c31f1c2b0"
  integrity sha1-+RVff9ktA8sZgkBbO4LlTDHxwrA=
  dependencies:
    "@types/event-emitter" "^0.3.3"
    event-emitter "^0.3.5"
    html-void-elements "^2.0.0"
    i18next "^20.4.0"
    scroll-into-view-if-needed "^2.2.28"
    slate-history "^0.66.0"

"@wangeditor/editor-for-react@^1.0.6":
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/@wangeditor/editor-for-react/download/@wangeditor/editor-for-react-1.0.6.tgz#c77fa5651e196bb7e5a63e4abf0e32d54d4f38af"
  integrity sha1-x3+lZR4Za7flpj5Kvw4y1U1POK8=

"@wangeditor/editor@^5.1.23":
  version "5.1.23"
  resolved "http://r.npm.sankuai.com/@wangeditor/editor/download/@wangeditor/editor-5.1.23.tgz#c9d2007b7cb0ceef6b72692b4ee87b01ee2367b3"
  integrity sha1-ydIAe3ywzu9rcmkrTuh7Ae4jZ7M=
  dependencies:
    "@uppy/core" "^2.1.1"
    "@uppy/xhr-upload" "^2.0.3"
    "@wangeditor/basic-modules" "^1.1.7"
    "@wangeditor/code-highlight" "^1.0.3"
    "@wangeditor/core" "^1.1.19"
    "@wangeditor/list-module" "^1.0.5"
    "@wangeditor/table-module" "^1.1.4"
    "@wangeditor/upload-image-module" "^1.0.2"
    "@wangeditor/video-module" "^1.1.4"
    dom7 "^3.0.0"
    is-hotkey "^0.2.0"
    lodash.camelcase "^4.3.0"
    lodash.clonedeep "^4.5.0"
    lodash.debounce "^4.0.8"
    lodash.foreach "^4.5.0"
    lodash.isequal "^4.5.0"
    lodash.throttle "^4.1.1"
    lodash.toarray "^4.4.0"
    nanoid "^3.2.0"
    slate "^0.72.0"
    snabbdom "^3.1.0"

"@wangeditor/list-module@^1.0.5":
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/@wangeditor/list-module/download/@wangeditor/list-module-1.0.5.tgz#3fc0b167acddf885536b45fa0c127f9c6adaea33"
  integrity sha1-P8CxZ6zd+IVTa0X6DBJ/nGra6jM=

"@wangeditor/table-module@^1.1.4":
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/@wangeditor/table-module/download/@wangeditor/table-module-1.1.4.tgz#757d4a5868b2b658041cd323854a4d707c8347e9"
  integrity sha1-dX1KWGiytlgEHNMjhUpNcHyDR+k=

"@wangeditor/upload-image-module@^1.0.2":
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/@wangeditor/upload-image-module/download/@wangeditor/upload-image-module-1.0.2.tgz#89e9b9467e10cbc6b11dc5748e08dd23aaebee30"
  integrity sha1-iem5Rn4Qy8axHcV0jgjdI6rr7jA=

"@wangeditor/video-module@^1.1.4":
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/@wangeditor/video-module/download/@wangeditor/video-module-1.1.4.tgz#b9df1b3ab2cd53f678b19b4d927e200774a6f532"
  integrity sha1-ud8bOrLNU/Z4sZtNkn4gB3Sm9TI=

"@xm/analytics@^1.3.24":
  version "1.4.3"
  resolved "http://r.npm.sankuai.com/@xm/analytics/download/@xm/analytics-1.4.3.tgz#d5f80fe7798208820b01dfea5ae68bce33b46455"
  integrity sha1-1fgP53mCCIILAd/qWuaLzjO0ZFU=

"@yyfe/Copy@^1.0.15", "@yyfe/Copy@^1.0.21":
  version "1.0.22"
  resolved "http://r.npm.sankuai.com/@yyfe/Copy/download/@yyfe/Copy-1.0.22.tgz#a00b7d37520b2d06f57ceb74083613cbb4b10988"
  integrity sha1-oAt9N1ILLQb1fOt0CDYTy7SxCYg=
  dependencies:
    "@roo/roo-cooperation-report" "^0.0.9"
    copy-html-to-clipboard "^4.0.1"
    copy-image-clipboard "^2.0.1"

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn@^8.8.2, acorn@^8.9.0:
  version "8.14.0"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-8.14.0.tgz#063e2c70cac5fb4f6467f0b11152e04c682795b0"
  integrity sha1-Bj4scMrF+09kZ/CxEVLgTGgnlbA=

address@^1.0.3:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/address/download/address-1.2.2.tgz#2b5248dac5485a6390532c6a517fda2e3faac89e"
  integrity sha1-K1JI2sVIWmOQUyxqUX/aLj+qyJ4=

adler-32@^1.0.0:
  version "1.3.1"
  resolved "http://r.npm.sankuai.com/adler-32/download/adler-32-1.3.1.tgz#1dbf0b36dda0012189a32b3679061932df1821e2"
  integrity sha1-Hb8LNt2gASGJoys2eQYZMt8YIeI=

agent-base@4:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/agent-base/download/agent-base-4.3.0.tgz#8165f01c436009bccad0b1d122f05ed770efc6ee"
  integrity sha1-gWXwHENgCbzK0LHRIvBe13Dvxu4=
  dependencies:
    es6-promisify "^5.0.0"

ahooks@^3.8.0:
  version "3.8.4"
  resolved "http://r.npm.sankuai.com/ahooks/download/ahooks-3.8.4.tgz#ee2a22d52b6ee57743a1f6ab51c91a7c36bcd7c6"
  integrity sha1-7ioi1Stu5XdDofarUckafDa818Y=
  dependencies:
    "@babel/runtime" "^7.21.0"
    dayjs "^1.9.1"
    intersection-observer "^0.12.0"
    js-cookie "^3.0.5"
    lodash "^4.17.21"
    react-fast-compare "^3.2.2"
    resize-observer-polyfill "^1.5.1"
    screenfull "^5.0.0"
    tslib "^2.4.1"

ajv@^6.12.3, ajv@^6.12.4:
  version "6.12.6"
  resolved "http://r.npm.sankuai.com/ajv/download/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

align-text@^0.1.1, align-text@^0.1.3:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/align-text/download/align-text-0.1.4.tgz#0cd90a561093f35d0a99256c22b7069433fad117"
  integrity sha1-DNkKVhCT810KmSVsIrcGlDP60Rc=
  dependencies:
    kind-of "^3.0.2"
    longest "^1.0.1"
    repeat-string "^1.5.2"

amdefine@>=0.0.4:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/amdefine/download/amdefine-1.0.1.tgz#4a5282ac164729e93619bcfd3ad151f817ce91f5"
  integrity sha1-SlKCrBZHKek2Gbz9OtFR+BfOkfU=

ansi-escapes@^3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-3.2.0.tgz#8780b98ff9dbf5638152d1f1fe5c1d7b4442976b"
  integrity sha1-h4C5j/nb9WOBUtHx/lwde0RCl2s=

ansi-escapes@^4.2.1:
  version "4.3.2"
  resolved "http://r.npm.sankuai.com/ansi-escapes/download/ansi-escapes-4.3.2.tgz#6b2291d1db7d98b6521d5f1efa42d0f3a9feb65e"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-2.1.1.tgz#c3b33ab5ee360d86e0e628f0468ae7ef27d654df"
  integrity sha1-w7M6te42DYbg5ijwRorn7yfWVN8=

ansi-regex@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-3.0.1.tgz#123d6479e92ad45ad897d4054e3c7ca7db4944e1"
  integrity sha1-Ej1keekq1FrYl9QFTjx8p9tJROE=

ansi-regex@^4.1.0:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-4.1.1.tgz#164daac87ab2d6f6db3a29875e2d1766582dabed"
  integrity sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0=

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-regex@^6.0.1:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/ansi-regex/download/ansi-regex-6.1.0.tgz#95ec409c69619d6cb1b8b34f14b660ef28ebd654"
  integrity sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=

ansi-styles@^2.2.1:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-2.2.1.tgz#b432dd3358b634cf75e1e4664368240533c1ddbe"
  integrity sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=

ansi-styles@^3.2.1:
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-3.2.1.tgz#41fbb20243e50b12be0f04b8dedbf07520ce841d"
  integrity sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=
  dependencies:
    color-convert "^1.9.0"

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "http://r.npm.sankuai.com/ansi-styles/download/ansi-styles-6.2.1.tgz#0e62320cf99c21afff3b3012192546aacbfb05c5"
  integrity sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=

antd@^5.3.2:
  version "5.22.6"
  resolved "http://r.npm.sankuai.com/antd/download/antd-5.22.6.tgz#453aa60429e31ce70e1c82d78b7866e648cabb76"
  integrity sha1-RTqmBCnjHOcOHILXi3hm5kjKu3Y=
  dependencies:
    "@ant-design/colors" "^7.1.0"
    "@ant-design/cssinjs" "^1.21.1"
    "@ant-design/cssinjs-utils" "^1.1.3"
    "@ant-design/icons" "^5.5.2"
    "@ant-design/react-slick" "~1.1.2"
    "@babel/runtime" "^7.25.7"
    "@ctrl/tinycolor" "^3.6.1"
    "@rc-component/color-picker" "~2.0.1"
    "@rc-component/mutate-observer" "^1.1.0"
    "@rc-component/qrcode" "~1.0.0"
    "@rc-component/tour" "~1.15.1"
    "@rc-component/trigger" "^2.2.6"
    classnames "^2.5.1"
    copy-to-clipboard "^3.3.3"
    dayjs "^1.11.11"
    rc-cascader "~3.30.0"
    rc-checkbox "~3.3.0"
    rc-collapse "~3.9.0"
    rc-dialog "~9.6.0"
    rc-drawer "~7.2.0"
    rc-dropdown "~4.2.1"
    rc-field-form "~2.7.0"
    rc-image "~7.11.0"
    rc-input "~1.6.4"
    rc-input-number "~9.3.0"
    rc-mentions "~2.17.0"
    rc-menu "~9.16.0"
    rc-motion "^2.9.5"
    rc-notification "~5.6.2"
    rc-pagination "~5.0.0"
    rc-picker "~4.8.3"
    rc-progress "~4.0.0"
    rc-rate "~2.13.0"
    rc-resize-observer "^1.4.3"
    rc-segmented "~2.5.0"
    rc-select "~14.16.4"
    rc-slider "~11.1.7"
    rc-steps "~6.0.1"
    rc-switch "~4.1.0"
    rc-table "~7.49.0"
    rc-tabs "~15.4.0"
    rc-textarea "~1.8.2"
    rc-tooltip "~6.2.1"
    rc-tree "~5.10.1"
    rc-tree-select "~5.24.5"
    rc-upload "~4.8.1"
    rc-util "^5.44.2"
    scroll-into-view-if-needed "^3.1.0"
    throttle-debounce "^5.0.2"

any-promise@^1.0.0, any-promise@^1.1.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/any-promise/download/any-promise-1.3.0.tgz#abc6afeedcea52e809cdc0376aed3ce39635d17f"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@~3.1.2:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/anymatch/download/anymatch-3.1.3.tgz#790c58b19ba1720a84205b57c618d5ad8524973e"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arg@^5.0.2:
  version "5.0.2"
  resolved "http://r.npm.sankuai.com/arg/download/arg-5.0.2.tgz#c81433cc427c92c4dcf4865142dbca6f15acd59c"
  integrity sha1-yBQzzEJ8ksTc9IZRQtvKbxWs1Zw=

argparse@^1.0.7:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/argparse/download/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/argparse/download/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

aria-hidden@^1.2.4:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/aria-hidden/download/aria-hidden-1.2.4.tgz#b78e383fdbc04d05762c78b4a25a501e736c4522"
  integrity sha1-t444P9vATQV2LHi0olpQHnNsRSI=
  dependencies:
    tslib "^2.0.0"

array-buffer-byte-length@^1.0.1, array-buffer-byte-length@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/array-buffer-byte-length/download/array-buffer-byte-length-1.0.2.tgz#384d12a37295aec3769ab022ad323a18a51ccf8b"
  integrity sha1-OE0So3KVrsN2mrAirTI6GKUcz4s=
  dependencies:
    call-bound "^1.0.3"
    is-array-buffer "^3.0.5"

array-includes@^3.1.6, array-includes@^3.1.8:
  version "3.1.8"
  resolved "http://r.npm.sankuai.com/array-includes/download/array-includes-3.1.8.tgz#5e370cbe172fdd5dd6530c1d4aadda25281ba97d"
  integrity sha1-XjcMvhcv3V3WUwwdSq3aJSgbqX0=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.4"
    is-string "^1.0.7"

array-union@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/array-union/download/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array.prototype.findlast@^1.2.5:
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/array.prototype.findlast/download/array.prototype.findlast-1.2.5.tgz#3e4fbcb30a15a7f5bf64cf2faae22d139c2e4904"
  integrity sha1-Pk+8swoVp/W/ZM8vquItE5wuSQQ=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-shim-unscopables "^1.0.2"

array.prototype.flat@^1.3.1:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/array.prototype.flat/download/array.prototype.flat-1.3.3.tgz#534aaf9e6e8dd79fb6b9a9917f839ef1ec63afe5"
  integrity sha1-U0qvnm6N15+2uamRf4Oe8exjr+U=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.flatmap@^1.3.3:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/array.prototype.flatmap/download/array.prototype.flatmap-1.3.3.tgz#712cc792ae70370ae40586264629e33aab5dd38b"
  integrity sha1-cSzHkq5wNwrkBYYmRinjOqtd04s=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.tosorted@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/array.prototype.tosorted/download/array.prototype.tosorted-1.1.4.tgz#fe954678ff53034e717ea3352a03f0b0b86f7ffc"
  integrity sha1-/pVGeP9TA05xfqM1KgPwsLhvf/w=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.3"
    es-errors "^1.3.0"
    es-shim-unscopables "^1.0.2"

arraybuffer.prototype.slice@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/arraybuffer.prototype.slice/download/arraybuffer.prototype.slice-1.0.4.tgz#9d760d84dbdd06d0cbf92c8849615a1a7ab3183c"
  integrity sha1-nXYNhNvdBtDL+SyISWFaGnqzGDw=
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    is-array-buffer "^3.0.4"

asn1@^0.2.4, asn1@~0.2.3:
  version "0.2.6"
  resolved "http://r.npm.sankuai.com/asn1/download/asn1-0.2.6.tgz#0d3a7bb6e64e02a90c0303b31f292868ea09a08d"
  integrity sha1-DTp7tuZOAqkMAwOzHykoaOoJoI0=
  dependencies:
    safer-buffer "~2.1.0"

assert-plus@1.0.0, assert-plus@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/assert-plus/download/assert-plus-1.0.0.tgz#f12e0f3c5d77b0b1cdd9146942e4e96c1e4dd525"
  integrity sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=

async-function@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/async-function/download/async-function-1.0.0.tgz#509c9fca60eaf85034c6829838188e4e4c8ffb2b"
  integrity sha1-UJyfymDq+FA0xoKYOBiOTkyP+ys=

async-validator@^1.10.0:
  version "1.12.2"
  resolved "http://r.npm.sankuai.com/async-validator/download/async-validator-1.12.2.tgz#beae671e7174d2938b7b4b69d2fb7e722b7fd72c"
  integrity sha1-vq5nHnF00pOLe0tp0vt+cit/1yw=

async-validator@^4.1.0:
  version "4.2.5"
  resolved "http://r.npm.sankuai.com/async-validator/download/async-validator-4.2.5.tgz#c96ea3332a521699d0afaaceed510a54656c6339"
  integrity sha1-yW6jMypSFpnQr6rO7VEKVGVsYzk=

async@^3.2.3:
  version "3.2.6"
  resolved "http://r.npm.sankuai.com/async/download/async-3.2.6.tgz#1b0728e14929d51b85b449b7f06e27c1145e38ce"
  integrity sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/available-typed-arrays/download/available-typed-arrays-1.0.7.tgz#a5cc375d6a03c2efc87a553f3e0b1522def14846"
  integrity sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=
  dependencies:
    possible-typed-array-names "^1.0.0"

await-event@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/await-event/download/await-event-2.1.0.tgz#78e9f92684bae4022f9fa0b5f314a11550f9aa76"
  integrity sha1-eOn5JoS65AIvn6C18xShFVD5qnY=

await-first@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/await-first/download/await-first-1.0.0.tgz#06afa6db7cebe412be9be54e82dd8c6cb4cdb241"
  integrity sha1-Bq+m23zr5BK+m+VOgt2MbLTNskE=
  dependencies:
    ee-first "^1.1.1"

aws-sign2@~0.7.0:
  version "0.7.0"
  resolved "http://r.npm.sankuai.com/aws-sign2/download/aws-sign2-0.7.0.tgz#b46e890934a9591f2d2f6f86d7e6a9f1b3fe76a8"
  integrity sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=

aws4@^1.8.0:
  version "1.13.2"
  resolved "http://r.npm.sankuai.com/aws4/download/aws4-1.13.2.tgz#0aa167216965ac9474ccfa83892cfb6b3e1e52ef"
  integrity sha1-CqFnIWllrJR0zPqDiSz7az4eUu8=

axios@*, axios@^1.3.4:
  version "1.7.9"
  resolved "http://r.npm.sankuai.com/axios/download/axios-1.7.9.tgz#d7d071380c132a24accda1b2cfc1535b79ec650a"
  integrity sha1-19BxOAwTKiSszaGyz8FTW3nsZQo=
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

axios@^0.18.0:
  version "0.18.1"
  resolved "http://r.npm.sankuai.com/axios/download/axios-0.18.1.tgz#ff3f0de2e7b5d180e757ad98000f1081b87bcea3"
  integrity sha1-/z8N4ue10YDnV62YAA8Qgbh7zqM=
  dependencies:
    follow-redirects "1.5.10"
    is-buffer "^2.0.2"

axios@^0.19.0, axios@^0.19.2:
  version "0.19.2"
  resolved "http://r.npm.sankuai.com/axios/download/axios-0.19.2.tgz#3ea36c5d8818d0d5f8a8a97a6d36b86cdc00cb27"
  integrity sha1-PqNsXYgY0NX4qKl6bTa4bNwAyyc=
  dependencies:
    follow-redirects "1.5.10"

axios@^0.24.0:
  version "0.24.0"
  resolved "http://r.npm.sankuai.com/axios/download/axios-0.24.0.tgz#804e6fa1e4b9c5288501dd9dff56a7a0940d20d6"
  integrity sha1-gE5voeS5xSiFAd2d/1anoJQNINY=
  dependencies:
    follow-redirects "^1.14.4"

babel-plugin-polyfill-corejs2@^0.4.10:
  version "0.4.12"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-corejs2/download/babel-plugin-polyfill-corejs2-0.4.12.tgz#ca55bbec8ab0edeeef3d7b8ffd75322e210879a9"
  integrity sha1-ylW77Iqw7e7vPXuP/XUyLiEIeak=
  dependencies:
    "@babel/compat-data" "^7.22.6"
    "@babel/helper-define-polyfill-provider" "^0.6.3"
    semver "^6.3.1"

babel-plugin-polyfill-corejs3@^0.10.6:
  version "0.10.6"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.10.6.tgz#2deda57caef50f59c525aeb4964d3b2f867710c7"
  integrity sha1-Le2lfK71D1nFJa60lk07L4Z3EMc=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.2"
    core-js-compat "^3.38.0"

babel-plugin-polyfill-corejs3@^0.11.0:
  version "0.11.1"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-corejs3/download/babel-plugin-polyfill-corejs3-0.11.1.tgz#4e4e182f1bb37c7ba62e2af81d8dd09df31344f6"
  integrity sha1-Tk4YLxuzfHumLir4HY3QnfMTRPY=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.3"
    core-js-compat "^3.40.0"

babel-plugin-polyfill-regenerator@^0.6.1:
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/babel-plugin-polyfill-regenerator/download/babel-plugin-polyfill-regenerator-0.6.3.tgz#abeb1f3f1c762eace37587f42548b08b57789bc8"
  integrity sha1-q+sfPxx2LqzjdYf0JUiwi1d4m8g=
  dependencies:
    "@babel/helper-define-polyfill-provider" "^0.6.3"

babel-runtime@^6.26.0:
  version "6.26.0"
  resolved "http://r.npm.sankuai.com/babel-runtime/download/babel-runtime-6.26.0.tgz#965c7058668e82b55d7bfe04ff2337bc8b5647fe"
  integrity sha1-llxwWGaOgrVde/4E/yM3vItWR/4=
  dependencies:
    core-js "^2.4.0"
    regenerator-runtime "^0.11.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base64-js@^1.3.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/base64-js/download/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

bcrypt-pbkdf@^1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz#a4301d389b6a43f9b67ff3ca11a3f6637e360e9e"
  integrity sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=
  dependencies:
    tweetnacl "^0.14.3"

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/binary-extensions/download/binary-extensions-2.3.0.tgz#f6e14a97858d327252200242d4ccfe522c445522"
  integrity sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=

bindings@^1.3.0, bindings@^1.5.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/bindings/download/bindings-1.5.0.tgz#10353c9e945334bc0511a6d90b38fbc7c9c504df"
  integrity sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=
  dependencies:
    file-uri-to-path "1.0.0"

bl@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/bl/download/bl-4.1.0.tgz#451535264182bec2fbbc83a62ab98cf11d9f7b3a"
  integrity sha1-RRU1JkGCvsL7vIOmKrmM8R2fezo=
  dependencies:
    buffer "^5.5.0"
    inherits "^2.0.4"
    readable-stream "^3.4.0"

bluebird@^3.5.3:
  version "3.7.2"
  resolved "http://r.npm.sankuai.com/bluebird/download/bluebird-3.7.2.tgz#9f229c15be272454ffa973ace0dbee79a1b0c36f"
  integrity sha1-nyKcFb4nJFT/qXOs4NvueaGww28=

boolbase@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/boolbase/download/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e"
  integrity sha1-aN/1++YMUes3cl6p4+0xDcwed24=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/brace-expansion/download/brace-expansion-2.0.1.tgz#1edc459e0f0c548486ecf9fc99f2221364b9a0ae"
  integrity sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/braces/download/braces-3.0.3.tgz#490332f40919452272d55a8480adc0c441358789"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

browserslist@^4.24.0, browserslist@^4.24.2:
  version "4.24.3"
  resolved "http://r.npm.sankuai.com/browserslist/download/browserslist-4.24.3.tgz#5fc2725ca8fb3c1432e13dac278c7cc103e026d2"
  integrity sha1-X8JyXKj7PBQy4T2sJ4x8wQPgJtI=
  dependencies:
    caniuse-lite "^1.0.30001688"
    electron-to-chromium "^1.5.73"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.1"

browserslist@^4.24.4:
  version "4.24.5"
  resolved "http://r.npm.sankuai.com/browserslist/download/browserslist-4.24.5.tgz#aa0f5b8560fe81fde84c6dcb38f759bafba0e11b"
  integrity sha1-qg9bhWD+gf3oTG3LOPdZuvug4Rs=
  dependencies:
    caniuse-lite "^1.0.30001716"
    electron-to-chromium "^1.5.149"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.3"

buffer-builder@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/buffer-builder/download/buffer-builder-0.2.0.tgz#3322cd307d8296dab1f604618593b261a3fade8f"
  integrity sha1-MyLNMH2Cltqx9gRhhZOyYaP63o8=

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/buffer-from/download/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer@^5.5.0:
  version "5.7.1"
  resolved "http://r.npm.sankuai.com/buffer/download/buffer-5.7.1.tgz#ba62e7c13133053582197160851a8f648e99eed0"
  integrity sha1-umLnwTEzBTWCGXFghRqPZI6Z7tA=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.1.13"

bundle-require@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/bundle-require/download/bundle-require-5.1.0.tgz#8db66f41950da3d77af1ef3322f4c3e04009faee"
  integrity sha1-jbZvQZUNo9d68e8zIvTD4EAJ+u4=
  dependencies:
    load-tsconfig "^0.2.3"

cac@^6.4.3, cac@^6.7.14:
  version "6.7.14"
  resolved "http://r.npm.sankuai.com/cac/download/cac-6.7.14.tgz#804e1e6f506ee363cb0e3ccbb09cad5dd9870959"
  integrity sha1-gE4eb1Bu42PLDjzLsJytXdmHCVk=

cacheable-lookup@^5.0.3:
  version "5.0.4"
  resolved "http://r.npm.sankuai.com/cacheable-lookup/download/cacheable-lookup-5.0.4.tgz#5a6b865b2c44357be3d5ebc2a467b032719a7005"
  integrity sha1-WmuGWyxENXvj1evCpGewMnGacAU=

cacheable-request@^7.0.2:
  version "7.0.4"
  resolved "http://r.npm.sankuai.com/cacheable-request/download/cacheable-request-7.0.4.tgz#7a33ebf08613178b403635be7b899d3e69bbe817"
  integrity sha1-ejPr8IYTF4tANjW+e4mdPmm76Bc=
  dependencies:
    clone-response "^1.0.2"
    get-stream "^5.1.0"
    http-cache-semantics "^4.0.0"
    keyv "^4.0.0"
    lowercase-keys "^2.0.0"
    normalize-url "^6.0.1"
    responselike "^2.0.0"

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.1.tgz#32e5892e6361b29b0b545ba6f7763378daca2840"
  integrity sha1-MuWJLmNhspsLVFum93YzeNrKKEA=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz#4b5428c222be985d79c3d82657479dbe0b59b2d6"
  integrity sha1-S1QowiK+mF15w9gmV0edvgtZstY=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.2, call-bind@^1.0.7, call-bind@^1.0.8, call-bind@~1.0.2:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/call-bind/download/call-bind-1.0.8.tgz#0736a9660f537e3388826f440d5ec45f744eaa4c"
  integrity sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/call-bound/download/call-bound-1.0.3.tgz#41cfd032b593e39176a71533ab4f384aa04fd681"
  integrity sha1-Qc/QMrWT45F2pxUzq084SqBP1oE=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    get-intrinsic "^1.2.6"

call-bound@^1.0.3, call-bound@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/call-bound/download/call-bound-1.0.4.tgz#238de935d2a2a692928c538c7ccfa91067fd062a"
  integrity sha1-I43pNdKippKSjFOMfM+pEGf9Bio=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase-css@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/camelcase-css/download/camelcase-css-2.0.1.tgz#ee978f6947914cc30c6b44741b6ed1df7f043fd5"
  integrity sha1-7pePaUeRTMMMa0R0G27R338EP9U=

camelcase@^1.0.2:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/camelcase/download/camelcase-1.2.1.tgz#9bb5304d2e0b56698b2c758b08a3eaa9daa58a39"
  integrity sha1-m7UwTS4LVmmLLHWLCKPqqdqlijk=

caniuse-lite@^1.0.30001688:
  version "1.0.30001690"
  resolved "http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001690.tgz#f2d15e3aaf8e18f76b2b8c1481abde063b8104c8"
  integrity sha1-8tFeOq+OGPdrK4wUgaveBjuBBMg=

caniuse-lite@^1.0.30001716:
  version "1.0.30001718"
  resolved "http://r.npm.sankuai.com/caniuse-lite/download/caniuse-lite-1.0.30001718.tgz#dae13a9c80d517c30c6197515a96131c194d8f82"
  integrity sha1-2uE6nIDVF8MMYZdRWpYTHBlNj4I=

caseless@~0.12.0:
  version "0.12.0"
  resolved "http://r.npm.sankuai.com/caseless/download/caseless-0.12.0.tgz#1b681c21ff84033c826543090689420d187151dc"
  integrity sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=

center-align@^0.1.1:
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/center-align/download/center-align-0.1.3.tgz#aa0d32629b6ee972200411cbd4461c907bc2b7ad"
  integrity sha1-qg0yYptu6XIgBBHL1EYckHvCt60=
  dependencies:
    align-text "^0.1.3"
    lazy-cache "^1.0.3"

chalk@^1.1.1, chalk@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-1.1.3.tgz#a8115c55e4a702fe4d150abd3872822a7e09fc98"
  integrity sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=
  dependencies:
    ansi-styles "^2.2.1"
    escape-string-regexp "^1.0.2"
    has-ansi "^2.0.0"
    strip-ansi "^3.0.0"
    supports-color "^2.0.0"

chalk@^2.4.2:
  version "2.4.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-2.4.2.tgz#cd42541677a54333cf541a49108c1432b44c9424"
  integrity sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=
  dependencies:
    ansi-styles "^3.2.1"
    escape-string-regexp "^1.0.5"
    supports-color "^5.3.0"

chalk@^4.0.0, chalk@^4.0.2, chalk@^4.1.0, chalk@^4.1.1, chalk@^4.1.2:
  version "4.1.2"
  resolved "http://r.npm.sankuai.com/chalk/download/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chardet@^0.7.0:
  version "0.7.0"
  resolved "http://r.npm.sankuai.com/chardet/download/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

cheerio-select@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/cheerio-select/download/cheerio-select-2.1.0.tgz#4d8673286b8126ca2a8e42740d5e3c4884ae21b4"
  integrity sha1-TYZzKGuBJsoqjkJ0DV48SISuIbQ=
  dependencies:
    boolbase "^1.0.0"
    css-select "^5.1.0"
    css-what "^6.1.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"

cheerio@1.0.0-rc.12, cheerio@^1.0.0-rc.10:
  version "1.0.0-rc.12"
  resolved "http://r.npm.sankuai.com/cheerio/download/cheerio-1.0.0-rc.12.tgz#788bf7466506b1c6bf5fae51d24a2c4d62e47683"
  integrity sha1-eIv3RmUGsca/X65R0kosTWLkdoM=
  dependencies:
    cheerio-select "^2.1.0"
    dom-serializer "^2.0.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"
    htmlparser2 "^8.0.1"
    parse5 "^7.0.0"
    parse5-htmlparser2-tree-adapter "^7.0.0"

chokidar@^3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/chokidar/download/chokidar-3.6.0.tgz#197c6cc669ef2a8dc5e7b4d97ee4e092c3eb0d5b"
  integrity sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chokidar@^4.0.0, chokidar@^4.0.3:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/chokidar/download/chokidar-4.0.3.tgz#7be37a4c03c9aee1ecfe862a4a23b2c70c205d30"
  integrity sha1-e+N6TAPJruHs/oYqSiOyxwwgXTA=
  dependencies:
    readdirp "^4.0.1"

class-variance-authority@^0.7.0:
  version "0.7.1"
  resolved "http://r.npm.sankuai.com/class-variance-authority/download/class-variance-authority-0.7.1.tgz#4008a798a0e4553a781a57ac5177c9fb5d043787"
  integrity sha1-QAinmKDkVTp4GlesUXfJ+10EN4c=
  dependencies:
    clsx "^2.1.1"

classcat@^5.0.3, classcat@^5.0.4:
  version "5.0.5"
  resolved "http://r.npm.sankuai.com/classcat/download/classcat-5.0.5.tgz#8c209f359a93ac302404a10161b501eba9c09c77"
  integrity sha1-jCCfNZqTrDAkBKEBYbUB66nAnHc=

classnames@2.x, classnames@^2.2.1, classnames@^2.2.3, classnames@^2.2.5, classnames@^2.2.6, classnames@^2.3.1, classnames@^2.3.2, classnames@^2.5.1:
  version "2.5.1"
  resolved "http://r.npm.sankuai.com/classnames/download/classnames-2.5.1.tgz#ba774c614be0f016da105c858e7159eae8e7687b"
  integrity sha1-undMYUvg8BbaEFyFjnFZ6ujnaHs=

cli-cursor@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-2.1.0.tgz#b35dac376479facc3e94747d41d0d0f5238ffcb5"
  integrity sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=
  dependencies:
    restore-cursor "^2.0.0"

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/cli-cursor/download/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-spinners@^2.5.0:
  version "2.9.2"
  resolved "http://r.npm.sankuai.com/cli-spinners/download/cli-spinners-2.9.2.tgz#1773a8f4b9c4d6ac31563df53b3fc1d79462fe41"
  integrity sha1-F3Oo9LnE1qwxVj31Oz/B15Ri/kE=

cli-width@^2.0.0:
  version "2.2.1"
  resolved "http://r.npm.sankuai.com/cli-width/download/cli-width-2.2.1.tgz#b0433d0b4e9c847ef18868a4ef16fd5fc8271c48"
  integrity sha1-sEM9C06chH7xiGik7xb9X8gnHEg=

cli-width@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/cli-width/download/cli-width-3.0.0.tgz#a2f48437a2caa9a22436e794bf071ec9e61cedf6"
  integrity sha1-ovSEN6LKqaIkNueUvwceyeYc7fY=

cliui@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-2.1.0.tgz#4b475760ff80264c762c3a1719032e91c7fea0d1"
  integrity sha1-S0dXYP+AJkx2LDoXGQMukcf+oNE=
  dependencies:
    center-align "^0.1.1"
    right-align "^0.1.1"
    wordwrap "0.0.2"

cliui@^7.0.2:
  version "7.0.4"
  resolved "http://r.npm.sankuai.com/cliui/download/cliui-7.0.4.tgz#a0265ee655476fc807aea9df3df8df7783808b4f"
  integrity sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

clone-response@^1.0.2:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/clone-response/download/clone-response-1.0.3.tgz#af2032aa47816399cf5f0a1d0db902f517abb8c3"
  integrity sha1-ryAyqkeBY5nPXwodDbkC9ReruMM=
  dependencies:
    mimic-response "^1.0.0"

clone@^1.0.2:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/clone/download/clone-1.0.4.tgz#da309cc263df15994c688ca902179ca3c7cd7c7e"
  integrity sha1-2jCcwmPfFZlMaIypAheco8fNfH4=

clone@^2.1.1:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/clone/download/clone-2.1.2.tgz#1b7f4b9f591f1e8f83670401600345a02887435f"
  integrity sha1-G39Ln1kfHo+DZwQBYANFoCiHQ18=

clsx@^2.1.0, clsx@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/clsx/download/clsx-2.1.1.tgz#eed397c9fd8bd882bfb18deab7102049a2f32999"
  integrity sha1-7tOXyf2L2IK/sY3qtxAgSaLzKZk=

co-request@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/co-request/download/co-request-1.0.0.tgz#8eb5fb656c2ee1e82e36c4ccfe9376846406b260"
  integrity sha1-jrX7ZWwu4eguNsTM/pN2hGQGsmA=
  dependencies:
    request "*"

co@^4.6.0:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/co/download/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

color-convert@^1.9.0:
  version "1.9.3"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-1.9.3.tgz#bb71850690e1f136567de629d2d5471deda4c1e8"
  integrity sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=
  dependencies:
    color-name "1.1.3"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.3.tgz#a7d0558bd89c42f795dd42328f740831ca53bc25"
  integrity sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=

color-name@^1.0.0, color-name@~1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

color-string@^1.5.5:
  version "1.9.1"
  resolved "http://r.npm.sankuai.com/color-string/download/color-string-1.9.1.tgz#4467f9146f036f855b764dfb5bf8582bf342c7a4"
  integrity sha1-RGf5FG8Db4Vbdk37W/hYK/NCx6Q=
  dependencies:
    color-name "^1.0.0"
    simple-swizzle "^0.2.2"

combined-stream@^1.0.6, combined-stream@^1.0.8, combined-stream@~1.0.6:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

commander@7:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/commander/download/commander-7.2.0.tgz#a36cb57d0b501ce108e4d20559a150a391d97ab7"
  integrity sha1-o2y1fQtQHOEI5NIFWaFQo5HZerc=

commander@^2.20.0:
  version "2.20.3"
  resolved "http://r.npm.sankuai.com/commander/download/commander-2.20.3.tgz#fd485e84c03eb4881c20722ba48035e8531aeb33"
  integrity sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=

commander@^4.0.0:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/commander/download/commander-4.1.1.tgz#9fd602bd936294e9e9ef46a3f4d6964044b18068"
  integrity sha1-n9YCvZNilOnp70aj9NaWQESxgGg=

commander@^6.2.0:
  version "6.2.1"
  resolved "http://r.npm.sankuai.com/commander/download/commander-6.2.1.tgz#0792eb682dfbc325999bb2b84fddddba110ac73c"
  integrity sha1-B5LraC37wyWZm7K4T93duhEKxzw=

commander@^8.3.0:
  version "8.3.0"
  resolved "http://r.npm.sankuai.com/commander/download/commander-8.3.0.tgz#4837ea1b2da67b9c616a67afbb0fafee567bca66"
  integrity sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=

compute-scroll-into-view@^1.0.20:
  version "1.0.20"
  resolved "http://r.npm.sankuai.com/compute-scroll-into-view/download/compute-scroll-into-view-1.0.20.tgz#1768b5522d1172754f5d0c9b02de3af6be506a43"
  integrity sha1-F2i1Ui0RcnVPXQybAt469r5QakM=

compute-scroll-into-view@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/compute-scroll-into-view/download/compute-scroll-into-view-3.1.0.tgz#753f11d972596558d8fe7c6bcbc8497690ab4c87"
  integrity sha1-dT8R2XJZZVjY/nxry8hJdpCrTIc=

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

connect-history-api-fallback@1.6.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz#8b32089359308d111115d81cad3fceab888f97bc"
  integrity sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=

consola@^2.6.2:
  version "2.15.3"
  resolved "http://r.npm.sankuai.com/consola/download/consola-2.15.3.tgz#2e11f98d6a4be71ff72e0bdf07bd23e12cb61550"
  integrity sha1-LhH5jWpL5x/3LgvfB70j4Sy2FVA=

consola@^3.4.0:
  version "3.4.2"
  resolved "http://r.npm.sankuai.com/consola/download/consola-3.4.2.tgz#5af110145397bb67afdab77013fdc34cae590ea7"
  integrity sha1-WvEQFFOXu2ev2rdwE/3DTK5ZDqc=

contour_plot@^0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/contour_plot/download/contour_plot-0.0.1.tgz#475870f032b8e338412aa5fc507880f0bf495c77"
  integrity sha1-R1hw8DK44zhBKqX8UHiA8L9JXHc=

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/convert-source-map/download/convert-source-map-2.0.0.tgz#4b560f649fc4e918dd0ab75cf4961e8bc882d82a"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

copy-anything@^2.0.1:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/copy-anything/download/copy-anything-2.0.6.tgz#092454ea9584a7b7ad5573062b2a87f5900fc480"
  integrity sha1-CSRU6pWEp7etVXMGKyqH9ZAPxIA=
  dependencies:
    is-what "^3.14.1"

copy-html-to-clipboard@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/copy-html-to-clipboard/download/copy-html-to-clipboard-4.0.1.tgz#6eed95ad56fe0689f47772e65b75443834319786"
  integrity sha1-bu2VrVb+Bon0d3LmW3VEODQxl4Y=
  dependencies:
    toggle-selection "^1.0.3"

copy-image-clipboard@^2.0.1:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/copy-image-clipboard/download/copy-image-clipboard-2.1.2.tgz#7965038faa036b1aed451df207f14b52838286a1"
  integrity sha1-eWUDj6oDaxrtRR3yB/FLUoOChqE=

copy-paste@^1.3.0:
  version "1.5.3"
  resolved "http://r.npm.sankuai.com/copy-paste/download/copy-paste-1.5.3.tgz#ee9e775858d05c57a91ea2a063188ab686840797"
  integrity sha1-7p53WFjQXFepHqKgYxiKtoaEB5c=
  dependencies:
    iconv-lite "^0.4.8"

copy-to-clipboard@^3.2.0, copy-to-clipboard@^3.3.3:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/copy-to-clipboard/download/copy-to-clipboard-3.3.3.tgz#55ac43a1db8ae639a4bd99511c148cdd1b83a1b0"
  integrity sha1-VaxDoduK5jmkvZlRHBSM3RuDobA=
  dependencies:
    toggle-selection "^1.0.6"

core-js-compat@^3.38.0, core-js-compat@^3.38.1:
  version "3.39.0"
  resolved "http://r.npm.sankuai.com/core-js-compat/download/core-js-compat-3.39.0.tgz#b12dccb495f2601dc860bdbe7b4e3ffa8ba63f61"
  integrity sha1-sS3MtJXyYB3IYL2+e04/+oumP2E=
  dependencies:
    browserslist "^4.24.2"

core-js-compat@^3.40.0:
  version "3.42.0"
  resolved "http://r.npm.sankuai.com/core-js-compat/download/core-js-compat-3.42.0.tgz#ce19c29706ee5806e26d3cb3c542d4cfc0ed51bb"
  integrity sha1-zhnClwbuWAbibTyzxULUz8DtUbs=
  dependencies:
    browserslist "^4.24.4"

core-js@^2.4.0, core-js@^2.6.12, core-js@^2.6.5:
  version "2.6.12"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-2.6.12.tgz#d9333dfa7b065e347cc5682219d6f690859cc2ec"
  integrity sha1-2TM9+nsGXjR8xWgiGdb2kIWcwuw=

core-js@^3:
  version "3.42.0"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-3.42.0.tgz#edbe91f78ac8cfb6df8d997e74d368a68082fe37"
  integrity sha1-7b6R94rIz7bfjZl+dNNopoCC/jc=

core-js@^3.41.0:
  version "3.41.0"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-3.41.0.tgz#57714dafb8c751a6095d028a7428f1fb5834a776"
  integrity sha1-V3FNr7jHUaYJXQKKdCjx+1g0p3Y=

core-js@^3.6.4:
  version "3.39.0"
  resolved "http://r.npm.sankuai.com/core-js/download/core-js-3.39.0.tgz#57f7647f4d2d030c32a72ea23a0555b2eaa30f83"
  integrity sha1-V/dkf00tAwwypy6iOgVVsuqjD4M=

core-util-is@1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.2.tgz#b5fd54220aa2bc5ab57aab7140c940754503c1a7"
  integrity sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=

core-util-is@^1.0.2:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/core-util-is/download/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

create-react-context@^0.3.0:
  version "0.3.0"
  resolved "http://r.npm.sankuai.com/create-react-context/download/create-react-context-0.3.0.tgz#546dede9dc422def0d3fc2fe03afe0bc0f4f7d8c"
  integrity sha1-VG3t6dxCLe8NP8L+A6/gvA9PfYw=
  dependencies:
    gud "^1.0.0"
    warning "^4.0.3"

cropperjs@^1.5.13:
  version "1.6.2"
  resolved "http://r.npm.sankuai.com/cropperjs/download/cropperjs-1.6.2.tgz#d1a5d627d880581cca41b7901f06923500e4201b"
  integrity sha1-0aXWJ9iAWBzKQbeQHwaSNQDkIBs=

cross-spawn@^7.0.2, cross-spawn@^7.0.3, cross-spawn@^7.0.6:
  version "7.0.6"
  resolved "http://r.npm.sankuai.com/cross-spawn/download/cross-spawn-7.0.6.tgz#8a58fe78f00dcd70c370451759dfbfaf03e8ee9f"
  integrity sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-js@^3.1.9-1, crypto-js@^3.3.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/crypto-js/download/crypto-js-3.3.0.tgz#846dd1cce2f68aacfa156c8578f926a609b7976b"
  integrity sha1-hG3RzOL2iqz6FWyFePkmpgm3l2s=

css-select@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/css-select/download/css-select-5.1.0.tgz#b8ebd6554c3637ccc76688804ad3f6a6fdaea8a6"
  integrity sha1-uOvWVUw2N8zHZoiAStP2pv2uqKY=
  dependencies:
    boolbase "^1.0.0"
    css-what "^6.1.0"
    domhandler "^5.0.2"
    domutils "^3.0.1"
    nth-check "^2.0.1"

css-what@^6.1.0:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/css-what/download/css-what-6.1.0.tgz#fb5effcf76f1ddea2c81bdfaa4de44e79bac70f4"
  integrity sha1-+17/z3bx3eosgb36pN5E55uscPQ=

cssesc@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/cssesc/download/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

csstype@^3.0.2, csstype@^3.0.8, csstype@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/csstype/download/csstype-3.1.3.tgz#d80ff294d114fb0e6ac500fbf85b60137d7eff81"
  integrity sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=

"d3-array@1 - 3", "d3-array@2 - 3", "d3-array@2.10.0 - 3", "d3-array@2.5.0 - 3", d3-array@^3.2.4:
  version "3.2.4"
  resolved "http://r.npm.sankuai.com/d3-array/download/d3-array-3.2.4.tgz#15fec33b237f97ac5d7c986dc77da273a8ed0bb5"
  integrity sha1-Ff7DOyN/l6xdfJhtx32ic6jtC7U=
  dependencies:
    internmap "1 - 2"

d3-binarytree@1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/d3-binarytree/download/d3-binarytree-1.0.2.tgz#ed43ebc13c70fbabfdd62df17480bc5a425753cc"
  integrity sha1-7UPrwTxw+6v91i3xdIC8WkJXU8w=

"d3-color@1 - 3", d3-color@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/d3-color/download/d3-color-3.1.0.tgz#395b2833dfac71507f12ac2f7af23bf819de24e2"
  integrity sha1-OVsoM9+scVB/EqwvevI7+BneJOI=

"d3-dispatch@1 - 3", d3-dispatch@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/d3-dispatch/download/d3-dispatch-3.0.1.tgz#5fc75284e9c2375c36c839411a0cf550cbfc4d5e"
  integrity sha1-X8dShOnCN1w2yDlBGgz1UMv8TV4=

"d3-drag@2 - 3", d3-drag@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/d3-drag/download/d3-drag-3.0.0.tgz#994aae9cd23c719f53b5e10e3a0a6108c69607ba"
  integrity sha1-mUqunNI8cZ9TteEOOgphCMaWB7o=
  dependencies:
    d3-dispatch "1 - 3"
    d3-selection "3"

"d3-dsv@1 - 3", d3-dsv@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/d3-dsv/download/d3-dsv-3.0.1.tgz#c63af978f4d6a0d084a52a673922be2160789b73"
  integrity sha1-xjr5ePTWoNCEpSpnOSK+IWB4m3M=
  dependencies:
    commander "7"
    iconv-lite "0.6"
    rw "1"

"d3-ease@1 - 3":
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/d3-ease/download/d3-ease-3.0.1.tgz#9658ac38a2140d59d346160f1f6c30fda0bd12f4"
  integrity sha1-llisOKIUDVnTRhYPH2ww/aC9EvQ=

d3-ease@^1.0.5:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/d3-ease/download/d3-ease-1.0.7.tgz#9a834890ef8b8ae8c558b2fe55bd57f5993b85e2"
  integrity sha1-moNIkO+LiujFWLL+Vb1X9Zk7heI=

d3-fetch@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/d3-fetch/download/d3-fetch-3.0.1.tgz#83141bff9856a0edb5e38de89cdcfe63d0a60a22"
  integrity sha1-gxQb/5hWoO21443onNz+Y9CmCiI=
  dependencies:
    d3-dsv "1 - 3"

d3-force-3d@^3.0.5:
  version "3.0.5"
  resolved "http://r.npm.sankuai.com/d3-force-3d/download/d3-force-3d-3.0.5.tgz#9c8931b49acc3554f9110e128bc580cd3ab830f2"
  integrity sha1-nIkxtJrMNVT5EQ4Si8WAzTq4MPI=
  dependencies:
    d3-binarytree "1"
    d3-dispatch "1 - 3"
    d3-octree "1"
    d3-quadtree "1 - 3"
    d3-timer "1 - 3"

d3-force@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/d3-force/download/d3-force-3.0.0.tgz#3e2ba1a61e70888fe3d9194e30d6d14eece155c4"
  integrity sha1-Piuhph5wiI/j2RlOMNbRTuzhVcQ=
  dependencies:
    d3-dispatch "1 - 3"
    d3-quadtree "1 - 3"
    d3-timer "1 - 3"

"d3-format@1 - 3", d3-format@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/d3-format/download/d3-format-3.1.0.tgz#9260e23a28ea5cb109e93b21a06e24e2ebd55641"
  integrity sha1-kmDiOijqXLEJ6TshoG4k4uvVVkE=

d3-geo-projection@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/d3-geo-projection/download/d3-geo-projection-4.0.0.tgz#dc229e5ead78d31869a4e87cf1f45bd2716c48ca"
  integrity sha1-3CKeXq140xhppOh88fRb0nFsSMo=
  dependencies:
    commander "7"
    d3-array "1 - 3"
    d3-geo "1.12.0 - 3"

"d3-geo@1.12.0 - 3", d3-geo@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/d3-geo/download/d3-geo-3.1.1.tgz#6027cf51246f9b2ebd64f99e01dc7c3364033a4d"
  integrity sha1-YCfPUSRvmy69ZPmeAdx8M2QDOk0=
  dependencies:
    d3-array "2.5.0 - 3"

d3-hierarchy@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/d3-hierarchy/download/d3-hierarchy-2.0.0.tgz#dab88a58ca3e7a1bc6cab390e89667fcc6d20218"
  integrity sha1-2riKWMo+ehvGyrOQ6JZn/MbSAhg=

d3-hierarchy@^3.1.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/d3-hierarchy/download/d3-hierarchy-3.1.2.tgz#b01cd42c1eed3d46db77a5966cf726f8c09160c6"
  integrity sha1-sBzULB7tPUbbd6WWbPcm+MCRYMY=

"d3-interpolate@1 - 3", "d3-interpolate@1.2.0 - 3", d3-interpolate@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/d3-interpolate/download/d3-interpolate-3.0.1.tgz#3c47aa5b32c5b3dfb56ef3fd4342078a632b400d"
  integrity sha1-PEeqWzLFs9+1bvP9Q0IHimMrQA0=
  dependencies:
    d3-color "1 - 3"

d3-octree@1:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/d3-octree/download/d3-octree-1.1.0.tgz#f07e353b76df872644e7130ab1a74c5ef2f4287e"
  integrity sha1-8H41O3bfhyZE5xMKsadMXvL0KH4=

d3-path@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/d3-path/download/d3-path-3.1.0.tgz#22df939032fb5a71ae8b1800d61ddb7851c42526"
  integrity sha1-It+TkDL7WnGuixgA1h3beFHEJSY=

"d3-quadtree@1 - 3", d3-quadtree@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/d3-quadtree/download/d3-quadtree-3.0.1.tgz#6dca3e8be2b393c9a9d514dabbd80a92deef1a4f"
  integrity sha1-bco+i+Kzk8mp1RTau9gKkt7vGk8=

d3-random@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/d3-random/download/d3-random-3.0.1.tgz#d4926378d333d9c0bfd1e6fa0194d30aebaa20f4"
  integrity sha1-1JJjeNMz2cC/0eb6AZTTCuuqIPQ=

d3-regression@^1.3.10, d3-regression@^1.3.5:
  version "1.3.10"
  resolved "http://r.npm.sankuai.com/d3-regression/download/d3-regression-1.3.10.tgz#d1a411ab45044d9e8d5b8aec05f2e598e1a621c9"
  integrity sha1-0aQRq0UETZ6NW4rsBfLlmOGmIck=

d3-scale-chromatic@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/d3-scale-chromatic/download/d3-scale-chromatic-3.1.0.tgz#34c39da298b23c20e02f1a4b239bd0f22e7f1314"
  integrity sha1-NMOdopiyPCDgLxpLI5vQ8i5/ExQ=
  dependencies:
    d3-color "1 - 3"
    d3-interpolate "1 - 3"

d3-scale@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/d3-scale/download/d3-scale-4.0.2.tgz#82b38e8e8ff7080764f8dcec77bd4be393689396"
  integrity sha1-grOOjo/3CAdk+Nzsd71L45Nok5Y=
  dependencies:
    d3-array "2.10.0 - 3"
    d3-format "1 - 3"
    d3-interpolate "1.2.0 - 3"
    d3-time "2.1.1 - 3"
    d3-time-format "2 - 4"

"d3-selection@2 - 3", d3-selection@3, d3-selection@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/d3-selection/download/d3-selection-3.0.0.tgz#c25338207efa72cc5b9bd1458a1a41901f1e1b31"
  integrity sha1-wlM4IH76csxbm9FFihpBkB8eGzE=

d3-shape@^3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/d3-shape/download/d3-shape-3.2.0.tgz#a1a839cbd9ba45f28674c69d7f855bcf91dfc6a5"
  integrity sha1-oag5y9m6RfKGdMadf4Vbz5HfxqU=
  dependencies:
    d3-path "^3.1.0"

"d3-time-format@2 - 4":
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/d3-time-format/download/d3-time-format-4.1.0.tgz#7ab5257a5041d11ecb4fe70a5c7d16a195bb408a"
  integrity sha1-erUlelBB0R7LT+cKXH0WoZW7QIo=
  dependencies:
    d3-time "1 - 3"

"d3-time@1 - 3", "d3-time@2.1.1 - 3", d3-time@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/d3-time/download/d3-time-3.1.0.tgz#9310db56e992e3c0175e1ef385e545e48a9bb5c7"
  integrity sha1-kxDbVumS48AXXh7zheVF5Iqbtcc=
  dependencies:
    d3-array "2 - 3"

"d3-timer@1 - 3", d3-timer@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/d3-timer/download/d3-timer-3.0.1.tgz#6284d2a2708285b1abb7e201eda4380af35e63b0"
  integrity sha1-YoTSonCChbGrt+IB7aQ4CvNeY7A=

d3-timer@^1.0.9:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/d3-timer/download/d3-timer-1.0.10.tgz#dfe76b8a91748831b13b6d9c793ffbd508dd9de5"
  integrity sha1-3+dripF0iDGxO22ceT/71QjdneU=

"d3-transition@2 - 3":
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/d3-transition/download/d3-transition-3.0.1.tgz#6869fdde1448868077fdd5989200cb61b2a1645f"
  integrity sha1-aGn93hRIhoB3/dWYkgDLYbKhZF8=
  dependencies:
    d3-color "1 - 3"
    d3-dispatch "1 - 3"
    d3-ease "1 - 3"
    d3-interpolate "1 - 3"
    d3-timer "1 - 3"

d3-zoom@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/d3-zoom/download/d3-zoom-3.0.0.tgz#d13f4165c73217ffeaa54295cd6969b3e7aee8f3"
  integrity sha1-0T9BZccyF//qpUKVzWlps+eu6PM=
  dependencies:
    d3-dispatch "1 - 3"
    d3-drag "2 - 3"
    d3-interpolate "1 - 3"
    d3-selection "2 - 3"
    d3-transition "2 - 3"

d@1, d@^1.0.1, d@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/d/download/d-1.0.2.tgz#2aefd554b81981e7dccf72d6842ae725cb17e5de"
  integrity sha1-Ku/VVLgZgefcz3LWhCrnJcsX5d4=
  dependencies:
    es5-ext "^0.10.64"
    type "^2.7.2"

dashdash@^1.12.0:
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/dashdash/download/dashdash-1.14.1.tgz#853cfa0f7cbe2fed5de20326b8dd581035f6e2f0"
  integrity sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=
  dependencies:
    assert-plus "^1.0.0"

data-view-buffer@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/data-view-buffer/download/data-view-buffer-1.0.2.tgz#211a03ba95ecaf7798a8c7198d79536211f88570"
  integrity sha1-IRoDupXsr3eYqMcZjXlTYhH4hXA=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-length@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/data-view-byte-length/download/data-view-byte-length-1.0.2.tgz#9e80f7ca52453ce3e93d25a35318767ea7704735"
  integrity sha1-noD3ylJFPOPpPSWjUxh2fqdwRzU=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-offset@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/data-view-byte-offset/download/data-view-byte-offset-1.0.1.tgz#068307f9b71ab76dbbe10291389e020856606191"
  integrity sha1-BoMH+bcat2274QKROJ4CCFZgYZE=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

dayjs@1.11.13, dayjs@^1.11.11, dayjs@^1.11.9, dayjs@^1.9.1:
  version "1.11.13"
  resolved "http://r.npm.sankuai.com/dayjs/download/dayjs-1.11.13.tgz#92430b0139055c3ebb60150aa13e860a4b5a366c"
  integrity sha1-kkMLATkFXD67YBUKoT6GCktaNmw=

debug@3.1.0, debug@=3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/debug/download/debug-3.1.0.tgz#5bb5a0672628b64149566ba16819e61518c67261"
  integrity sha1-W7WgZyYotkFJVmuhaBnmFRjGcmE=
  dependencies:
    ms "2.0.0"

debug@^2.2.0:
  version "2.6.9"
  resolved "http://r.npm.sankuai.com/debug/download/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^3.1.0:
  version "3.2.7"
  resolved "http://r.npm.sankuai.com/debug/download/debug-3.2.7.tgz#72580b7e9145fb39b6676f9c5e5fb100b934179a"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.3, debug@^4.3.4, debug@^4.4.0:
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/debug/download/debug-4.4.0.tgz#2b3f2aea2ffeb776477460267377dc8710faba8a"
  integrity sha1-Kz8q6i/+t3ZHdGAmc3fchxD6uoo=
  dependencies:
    ms "^2.1.3"

decamelize@^1.0.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/decamelize/download/decamelize-1.2.0.tgz#f6534d15148269b20352e7bee26f501f9a191290"
  integrity sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=

decode-uri-component@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/decode-uri-component/download/decode-uri-component-0.4.1.tgz#2ac4859663c704be22bf7db760a1494a49ab2cc5"
  integrity sha1-KsSFlmPHBL4iv323YKFJSkmrLMU=

decompress-response@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/decompress-response/download/decompress-response-6.0.0.tgz#ca387612ddb7e104bd16d85aab00d5ecf09c66fc"
  integrity sha1-yjh2Et234QS9FthaqwDV7PCcZvw=
  dependencies:
    mimic-response "^3.1.0"

deep-equal@^1.0.1, deep-equal@~1.1.1:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/deep-equal/download/deep-equal-1.1.2.tgz#78a561b7830eef3134c7f6f3a3d6af272a678761"
  integrity sha1-eKVht4MO7zE0x/bzo9avJypnh2E=
  dependencies:
    is-arguments "^1.1.1"
    is-date-object "^1.0.5"
    is-regex "^1.1.4"
    object-is "^1.1.5"
    object-keys "^1.1.1"
    regexp.prototype.flags "^1.5.1"

deep-is@^0.1.3:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/deep-is/download/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

defaults@^1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/defaults/download/defaults-1.0.4.tgz#b0b02062c1e2aa62ff5d9528f0f98baa90978d7a"
  integrity sha1-sLAgYsHiqmL/XZUo8PmLqpCXjXo=
  dependencies:
    clone "^1.0.2"

defer-to-connect@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/defer-to-connect/download/defer-to-connect-2.0.1.tgz#8016bdb4143e4632b77a3449c6236277de520587"
  integrity sha1-gBa9tBQ+RjK3ejRJxiNid95SBYc=

define-data-property@^1.0.1, define-data-property@^1.1.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/define-data-property/download/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
  integrity sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-properties@^1.1.3, define-properties@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/define-properties/download/define-properties-1.2.1.tgz#10781cc616eb951a80a034bafcaa7377f6af2b6c"
  integrity sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

defined@~1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/defined/download/defined-1.0.1.tgz#c0b9db27bfaffd95d6f61399419b893df0f91ebf"
  integrity sha1-wLnbJ7+v/ZXW9hOZQZuJPfD5Hr8=

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

detect-browser@^5.0.0, detect-browser@^5.1.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/detect-browser/download/detect-browser-5.3.0.tgz#9705ef2bddf46072d0f7265a1fe300e36fe7ceca"
  integrity sha1-lwXvK930YHLQ9yZaH+MA42/nzso=

detect-libc@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/detect-libc/download/detect-libc-1.0.3.tgz#fa137c4bd698edf55cd5cd02ac559f91a4c4ba9b"
  integrity sha1-+hN8S9aY7fVc1c0CrFWfkaTEups=

detect-node-es@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/detect-node-es/download/detect-node-es-1.1.0.tgz#163acdf643330caa0b4cd7c21e7ee7755d6fa493"
  integrity sha1-FjrN9kMzDKoLTNfCHn7ndV1vpJM=

didyoumean@^1.2.2:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/didyoumean/download/didyoumean-1.2.2.tgz#989346ffe9e839b4555ecf5666edea0d3e8ad037"
  integrity sha1-mJNG/+noObRVXs9WZu3qDT6K0Dc=

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/dir-glob/download/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

dlv@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/dlv/download/dlv-1.1.3.tgz#5c198a8a11453596e751494d49874bc7732f2e79"
  integrity sha1-XBmKihFFNZbnUUlNSYdLx3MvLnk=

doctrine@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-2.1.0.tgz#5cd01fc101621b42c4cd7f5d1a66243716d3f39d"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-helpers@^3.4.0:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/dom-helpers/download/dom-helpers-3.4.0.tgz#e9b369700f959f62ecde5a6babde4bccd9169af8"
  integrity sha1-6bNpcA+Vn2Ls3lprq95LzNkWmvg=
  dependencies:
    "@babel/runtime" "^7.1.2"

dom-serializer@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/dom-serializer/download/dom-serializer-2.0.0.tgz#e41b802e1eedf9f6cae183ce5e622d789d7d8e53"
  integrity sha1-5BuALh7t+fbK4YPOXmIteJ19jlM=
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.2"
    entities "^4.2.0"

dom7@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/dom7/download/dom7-3.0.0.tgz#b861ce5d67a6becd7aaa3ad02942ff14b1240331"
  integrity sha1-uGHOXWemvs16qjrQKUL/FLEkAzE=
  dependencies:
    ssr-window "^3.0.0-alpha.1"

domelementtype@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/domelementtype/download/domelementtype-2.3.0.tgz#5c45e8e869952626331d7aab326d01daf65d589d"
  integrity sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=

domhandler@^5.0.2, domhandler@^5.0.3:
  version "5.0.3"
  resolved "http://r.npm.sankuai.com/domhandler/download/domhandler-5.0.3.tgz#cc385f7f751f1d1fc650c21374804254538c7d31"
  integrity sha1-zDhff3UfHR/GUMITdIBCVFOMfTE=
  dependencies:
    domelementtype "^2.3.0"

domutils@^3.0.1:
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/domutils/download/domutils-3.2.1.tgz#b39f4c390a1ae6f6a2c56a5f5a16d6438b6bce28"
  integrity sha1-s59MOQoa5vaixWpfWhbWQ4trzig=
  dependencies:
    dom-serializer "^2.0.0"
    domelementtype "^2.3.0"
    domhandler "^5.0.3"

dotignore@~0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/dotignore/download/dotignore-0.1.2.tgz#f942f2200d28c3a76fbdd6f0ee9f3257c8a2e905"
  integrity sha1-+ULyIA0ow6dvvdbw7p8yV8ii6QU=
  dependencies:
    minimatch "^3.0.4"

dunder-proto@^1.0.0, dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/dunder-proto/download/dunder-proto-1.0.1.tgz#d7ae667e1dc83482f8b70fd0f6eefc50da30f58a"
  integrity sha1-165mfh3INIL4tw/Q9u78UNow9Yo=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/eastasianwidth/download/eastasianwidth-0.2.0.tgz#696ce2ec0aa0e6ea93a397ffcf24aa7840c827cb"
  integrity sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=

ecc-jsbn@~0.1.1:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz#3a83a904e54353287874c564b7549386849a98c9"
  integrity sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=
  dependencies:
    jsbn "~0.1.0"
    safer-buffer "^2.1.0"

ee-first@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/ee-first/download/ee-first-1.1.1.tgz#590c61156b0ae2f4f0255732a158b266bc56b21d"
  integrity sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=

ejs@3.1.6:
  version "3.1.6"
  resolved "http://r.npm.sankuai.com/ejs/download/ejs-3.1.6.tgz#5bfd0a0689743bb5268b3550cceeebbc1702822a"
  integrity sha1-W/0KBol0O7UmizVQzO7rvBcCgio=
  dependencies:
    jake "^10.6.1"

ejs@^3.1.6:
  version "3.1.10"
  resolved "http://r.npm.sankuai.com/ejs/download/ejs-3.1.10.tgz#69ab8358b14e896f80cc39e62087b88500c3ac3b"
  integrity sha1-aauDWLFOiW+AzDnmIIe4hQDDrDs=
  dependencies:
    jake "^10.8.5"

electron-to-chromium@^1.5.149:
  version "1.5.155"
  resolved "http://r.npm.sankuai.com/electron-to-chromium/download/electron-to-chromium-1.5.155.tgz#809dd0ae9ae1db87c358e0c0c17c09a2ffc432d1"
  integrity sha1-gJ3Qrprh24fDWODAwXwJov/EMtE=

electron-to-chromium@^1.5.73:
  version "1.5.76"
  resolved "http://r.npm.sankuai.com/electron-to-chromium/download/electron-to-chromium-1.5.76.tgz#db20295c5061b68f07c8ea4dfcbd701485d94a3d"
  integrity sha1-2yApXFBhto8HyOpN/L1wFIXZSj0=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "http://r.npm.sankuai.com/emoji-regex/download/emoji-regex-9.2.2.tgz#840c8803b0d8047f4ff0cf963176b32d4ef3ed72"
  integrity sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=

end-of-stream@^1.1.0:
  version "1.4.4"
  resolved "http://r.npm.sankuai.com/end-of-stream/download/end-of-stream-1.4.4.tgz#5ae64a5f45057baf3626ec14da0ca5e4b2431eb0"
  integrity sha1-WuZKX0UFe682JuwU2gyl5LJDHrA=
  dependencies:
    once "^1.4.0"

enquire.js@^2.1.6:
  version "2.1.6"
  resolved "http://r.npm.sankuai.com/enquire.js/download/enquire.js-2.1.6.tgz#3e8780c9b8b835084c3f60e166dbc3c2a3c89814"
  integrity sha1-PoeAybi4NQhMP2DhZtvDwqPImBQ=

entities@^4.2.0, entities@^4.4.0, entities@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/entities/download/entities-4.5.0.tgz#5d268ea5e7113ec74c4d033b79ea5a35a488fb48"
  integrity sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=

errno@^0.1.1:
  version "0.1.8"
  resolved "http://r.npm.sankuai.com/errno/download/errno-0.1.8.tgz#8bb3e9c7d463be4976ff888f76b4809ebc2e811f"
  integrity sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=
  dependencies:
    prr "~1.0.1"

es-abstract@^1.17.5, es-abstract@^1.23.2, es-abstract@^1.23.3, es-abstract@^1.23.5, es-abstract@^1.23.6, es-abstract@^1.23.9:
  version "1.23.9"
  resolved "http://r.npm.sankuai.com/es-abstract/download/es-abstract-1.23.9.tgz#5b45994b7de78dada5c1bebf1379646b32b9d606"
  integrity sha1-W0WZS33nja2lwb6/E3lkazK51gY=
  dependencies:
    array-buffer-byte-length "^1.0.2"
    arraybuffer.prototype.slice "^1.0.4"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    data-view-buffer "^1.0.2"
    data-view-byte-length "^1.0.2"
    data-view-byte-offset "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    es-set-tostringtag "^2.1.0"
    es-to-primitive "^1.3.0"
    function.prototype.name "^1.1.8"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.0"
    get-symbol-description "^1.1.0"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    internal-slot "^1.1.0"
    is-array-buffer "^3.0.5"
    is-callable "^1.2.7"
    is-data-view "^1.0.2"
    is-regex "^1.2.1"
    is-shared-array-buffer "^1.0.4"
    is-string "^1.1.1"
    is-typed-array "^1.1.15"
    is-weakref "^1.1.0"
    math-intrinsics "^1.1.0"
    object-inspect "^1.13.3"
    object-keys "^1.1.1"
    object.assign "^4.1.7"
    own-keys "^1.0.1"
    regexp.prototype.flags "^1.5.3"
    safe-array-concat "^1.1.3"
    safe-push-apply "^1.0.0"
    safe-regex-test "^1.1.0"
    set-proto "^1.0.0"
    string.prototype.trim "^1.2.10"
    string.prototype.trimend "^1.0.9"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.3"
    typed-array-byte-length "^1.0.3"
    typed-array-byte-offset "^1.0.4"
    typed-array-length "^1.0.7"
    unbox-primitive "^1.1.0"
    which-typed-array "^1.1.18"

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.1.tgz#983eb2f9a6724e9303f61addf011c72e09e0b0fa"
  integrity sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=

es-errors@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-iterator-helpers@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/es-iterator-helpers/download/es-iterator-helpers-1.2.1.tgz#d1dd0f58129054c0ad922e6a9a1e65eef435fe75"
  integrity sha1-0d0PWBKQVMCtki5qmh5l7vQ1/nU=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-set-tostringtag "^2.0.3"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.6"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    iterator.prototype "^1.1.4"
    safe-array-concat "^1.1.3"

es-object-atoms@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.0.0.tgz#ddb55cd47ac2e240701260bc2a8e31ecb643d941"
  integrity sha1-3bVc1HrC4kBwEmC8Ko4x7LZD2UE=
  dependencies:
    es-errors "^1.3.0"

es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz#1c4f2c4837327597ce69d2ca190a7fdd172338c1"
  integrity sha1-HE8sSDcydZfOadLKGQp/3RcjOME=
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.0.3, es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz#f31dbbe0c183b00a6d26eb6325c810c0fd18bd4d"
  integrity sha1-8x274MGDsAptJutjJcgQwP0YvU0=
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

es-shim-unscopables@^1.0.2:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/es-shim-unscopables/download/es-shim-unscopables-1.1.0.tgz#438df35520dac5d105f3943d927549ea3b00f4b5"
  integrity sha1-Q43zVSDaxdEF85Q9knVJ6jsA9LU=
  dependencies:
    hasown "^2.0.2"

es-to-primitive@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/es-to-primitive/download/es-to-primitive-1.3.0.tgz#96c89c82cc49fd8794a24835ba3e1ff87f214e18"
  integrity sha1-lsicgsxJ/YeUokg1uj4f+H8hThg=
  dependencies:
    is-callable "^1.2.7"
    is-date-object "^1.0.5"
    is-symbol "^1.0.4"

es5-ext@^0.10.35, es5-ext@^0.10.62, es5-ext@^0.10.64, es5-ext@~0.10.14:
  version "0.10.64"
  resolved "http://r.npm.sankuai.com/es5-ext/download/es5-ext-0.10.64.tgz#12e4ffb48f1ba2ea777f1fcdd1918ef73ea21714"
  integrity sha1-EuT/tI8boup3fx/N0ZGO9z6iFxQ=
  dependencies:
    es6-iterator "^2.0.3"
    es6-symbol "^3.1.3"
    esniff "^2.0.1"
    next-tick "^1.1.0"

es6-iterator@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/es6-iterator/download/es6-iterator-2.0.3.tgz#a7de889141a05a94b0854403b2d0a0fbfa98f3b7"
  integrity sha1-p96IkUGgWpSwhUQDstCg+/qY87c=
  dependencies:
    d "1"
    es5-ext "^0.10.35"
    es6-symbol "^3.1.1"

es6-promise@^4.0.3:
  version "4.2.8"
  resolved "http://r.npm.sankuai.com/es6-promise/download/es6-promise-4.2.8.tgz#4eb21594c972bc40553d276e510539143db53e0a"
  integrity sha1-TrIVlMlyvEBVPSduUQU5FD21Pgo=

es6-promisify@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/es6-promisify/download/es6-promisify-5.0.0.tgz#5109d62f3e56ea967c4b63505aef08291c8a5203"
  integrity sha1-UQnWLz5W6pZ8S2NQWu8IKRyKUgM=
  dependencies:
    es6-promise "^4.0.3"

es6-symbol@^3.1.1, es6-symbol@^3.1.3:
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/es6-symbol/download/es6-symbol-3.1.4.tgz#f4e7d28013770b4208ecbf3e0bf14d3bcb557b8c"
  integrity sha1-9OfSgBN3C0II7L8+C/FNO8tVe4w=
  dependencies:
    d "^1.0.2"
    ext "^1.7.0"

esbuild@^0.18.10:
  version "0.18.20"
  resolved "http://r.npm.sankuai.com/esbuild/download/esbuild-0.18.20.tgz#4709f5a34801b43b799ab7d6d82f7284a9b7a7a6"
  integrity sha1-Rwn1o0gBtDt5mrfW2C9yhKm3p6Y=
  optionalDependencies:
    "@esbuild/android-arm" "0.18.20"
    "@esbuild/android-arm64" "0.18.20"
    "@esbuild/android-x64" "0.18.20"
    "@esbuild/darwin-arm64" "0.18.20"
    "@esbuild/darwin-x64" "0.18.20"
    "@esbuild/freebsd-arm64" "0.18.20"
    "@esbuild/freebsd-x64" "0.18.20"
    "@esbuild/linux-arm" "0.18.20"
    "@esbuild/linux-arm64" "0.18.20"
    "@esbuild/linux-ia32" "0.18.20"
    "@esbuild/linux-loong64" "0.18.20"
    "@esbuild/linux-mips64el" "0.18.20"
    "@esbuild/linux-ppc64" "0.18.20"
    "@esbuild/linux-riscv64" "0.18.20"
    "@esbuild/linux-s390x" "0.18.20"
    "@esbuild/linux-x64" "0.18.20"
    "@esbuild/netbsd-x64" "0.18.20"
    "@esbuild/openbsd-x64" "0.18.20"
    "@esbuild/sunos-x64" "0.18.20"
    "@esbuild/win32-arm64" "0.18.20"
    "@esbuild/win32-ia32" "0.18.20"
    "@esbuild/win32-x64" "0.18.20"

esbuild@^0.25.0:
  version "0.25.3"
  resolved "http://r.npm.sankuai.com/esbuild/download/esbuild-0.25.3.tgz#371f7cb41283e5b2191a96047a7a89562965a285"
  integrity sha1-Nx98tBKD5bIZGpYEenqJVillooU=
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.25.3"
    "@esbuild/android-arm" "0.25.3"
    "@esbuild/android-arm64" "0.25.3"
    "@esbuild/android-x64" "0.25.3"
    "@esbuild/darwin-arm64" "0.25.3"
    "@esbuild/darwin-x64" "0.25.3"
    "@esbuild/freebsd-arm64" "0.25.3"
    "@esbuild/freebsd-x64" "0.25.3"
    "@esbuild/linux-arm" "0.25.3"
    "@esbuild/linux-arm64" "0.25.3"
    "@esbuild/linux-ia32" "0.25.3"
    "@esbuild/linux-loong64" "0.25.3"
    "@esbuild/linux-mips64el" "0.25.3"
    "@esbuild/linux-ppc64" "0.25.3"
    "@esbuild/linux-riscv64" "0.25.3"
    "@esbuild/linux-s390x" "0.25.3"
    "@esbuild/linux-x64" "0.25.3"
    "@esbuild/netbsd-arm64" "0.25.3"
    "@esbuild/netbsd-x64" "0.25.3"
    "@esbuild/openbsd-arm64" "0.25.3"
    "@esbuild/openbsd-x64" "0.25.3"
    "@esbuild/sunos-x64" "0.25.3"
    "@esbuild/win32-arm64" "0.25.3"
    "@esbuild/win32-ia32" "0.25.3"
    "@esbuild/win32-x64" "0.25.3"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/escalade/download/escalade-3.2.0.tgz#011a3f69856ba189dffa7dc8fcce99d2a87903e5"
  integrity sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=

escape-string-regexp@^1.0.2, escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-config-prettier@^6.15.0:
  version "6.15.0"
  resolved "http://r.npm.sankuai.com/eslint-config-prettier/download/eslint-config-prettier-6.15.0.tgz#7f93f6cb7d45a92f1537a70ecc06366e1ac6fed9"
  integrity sha1-f5P2y31FqS8VN6cOzAY2bhrG/tk=
  dependencies:
    get-stdin "^6.0.0"

eslint-config-prettier@^8.7.0:
  version "8.10.0"
  resolved "http://r.npm.sankuai.com/eslint-config-prettier/download/eslint-config-prettier-8.10.0.tgz#3a06a662130807e2502fc3ff8b4143d8a0658e11"
  integrity sha1-OgamYhMIB+JQL8P/i0FD2KBljhE=

eslint-plugin-prettier@^3.4.1:
  version "3.4.1"
  resolved "http://r.npm.sankuai.com/eslint-plugin-prettier/download/eslint-plugin-prettier-3.4.1.tgz#e9ddb200efb6f3d05ffe83b1665a716af4a387e5"
  integrity sha1-6d2yAO+289Bf/oOxZlpxavSjh+U=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-prettier@^4.2.1:
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/eslint-plugin-prettier/download/eslint-plugin-prettier-4.2.1.tgz#651cbb88b1dab98bfd42f017a12fa6b2d993f94b"
  integrity sha1-ZRy7iLHauYv9QvAXoS+mstmT+Us=
  dependencies:
    prettier-linter-helpers "^1.0.0"

eslint-plugin-react-hooks@^5.2.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/eslint-plugin-react-hooks/download/eslint-plugin-react-hooks-5.2.0.tgz#1be0080901e6ac31ce7971beed3d3ec0a423d9e3"
  integrity sha1-G+AICQHmrDHOeXG+7T0+wKQj2eM=

eslint-plugin-react@^7.37.5:
  version "7.37.5"
  resolved "http://r.npm.sankuai.com/eslint-plugin-react/download/eslint-plugin-react-7.37.5.tgz#2975511472bdda1b272b34d779335c9b0e877065"
  integrity sha1-KXVRFHK92hsnKzTXeTNcmw6HcGU=
  dependencies:
    array-includes "^3.1.8"
    array.prototype.findlast "^1.2.5"
    array.prototype.flatmap "^1.3.3"
    array.prototype.tosorted "^1.1.4"
    doctrine "^2.1.0"
    es-iterator-helpers "^1.2.1"
    estraverse "^5.3.0"
    hasown "^2.0.2"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.9"
    object.fromentries "^2.0.8"
    object.values "^1.2.1"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.5"
    semver "^6.3.1"
    string.prototype.matchall "^4.0.12"
    string.prototype.repeat "^1.0.0"

eslint-scope@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-5.1.1.tgz#e786e59a66cb92b3f6c1fb0d508aab174848f48c"
  integrity sha1-54blmmbLkrP2wfsNUIqrF0hI9Iw=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^4.1.1"

eslint-scope@^7.2.2:
  version "7.2.2"
  resolved "http://r.npm.sankuai.com/eslint-scope/download/eslint-scope-7.2.2.tgz#deb4f92563390f32006894af62a22dba1c46423f"
  integrity sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.3.0, eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz#0cd72fe8550e3c2eae156a96a4dddcd1c8ac5800"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

eslint@^8.36.0:
  version "8.57.1"
  resolved "http://r.npm.sankuai.com/eslint/download/eslint-8.57.1.tgz#7df109654aba7e3bbe5c8eae533c5e461d3c6ca9"
  integrity sha1-ffEJZUq6fju+XI6uUzxeRh08bKk=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.57.1"
    "@humanwhocodes/config-array" "^0.13.0"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

esniff@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/esniff/download/esniff-2.0.1.tgz#a4d4b43a5c71c7ec51c51098c1d8a29081f9b308"
  integrity sha1-pNS0Olxxx+xRxRCYwdiikIH5swg=
  dependencies:
    d "^1.0.1"
    es5-ext "^0.10.62"
    event-emitter "^0.3.5"
    type "^2.7.2"

espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  resolved "http://r.npm.sankuai.com/espree/download/espree-9.6.1.tgz#a2a17b8e434690a5432f2f8018ce71d331a48c6f"
  integrity sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esprima@^4.0.0:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.4.2:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/esquery/download/esquery-1.6.0.tgz#91419234f804d852a82dceec3e16cdc22cf9dae7"
  integrity sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/esrecurse/download/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^4.1.1:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-4.3.0.tgz#398ad3f3c5a24948be7725e83d11a7de28cdbd1d"
  integrity sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

estree-walker@^3.0.2:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/estree-walker/download/estree-walker-3.0.3.tgz#67c3e549ec402a487b4fc193d1953a524752340d"
  integrity sha1-Z8PlSexAKkh7T8GT0ZU6UkdSNA0=
  dependencies:
    "@types/estree" "^1.0.0"

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

event-emitter@^0.3.3, event-emitter@^0.3.5:
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/event-emitter/download/event-emitter-0.3.5.tgz#df8c69eef1647923c7157b9ce83840610b02cc39"
  integrity sha1-34xp7vFkeSPHFXuc6DhAYQsCzDk=
  dependencies:
    d "1"
    es5-ext "~0.10.14"

eventemitter3@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-2.0.3.tgz#b5e1079b59fb5e1ba2771c0a993be060a58c99ba"
  integrity sha1-teEHm1n7XhuidxwKmTvgYKWMmbo=

eventemitter3@^4.0.4:
  version "4.0.7"
  resolved "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-4.0.7.tgz#2de9b68f6528d5644ef5c59526a1b4a07306169f"
  integrity sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=

eventemitter3@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/eventemitter3/download/eventemitter3-5.0.1.tgz#53f5ffd0a492ac800721bb42c66b841de96423c4"
  integrity sha1-U/X/0KSSrIAHIbtCxmuEHelkI8Q=

ext@^1.7.0:
  version "1.7.0"
  resolved "http://r.npm.sankuai.com/ext/download/ext-1.7.0.tgz#0ea4383c0103d60e70be99e9a7f11027a33c4f5f"
  integrity sha1-DqQ4PAED1g5wvpnpp/EQJ6M8T18=
  dependencies:
    type "^2.7.2"

extend@^3.0.2, extend@~3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/extend/download/extend-3.0.2.tgz#f8b1136b4071fbd8eb140aff858b1019ec2915fa"
  integrity sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=

external-editor@^3.0.3:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/external-editor/download/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

extsprintf@1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/extsprintf/download/extsprintf-1.3.0.tgz#96918440e3041a7a414f8c52e3c574eb3c3e1e05"
  integrity sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=

extsprintf@^1.2.0:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/extsprintf/download/extsprintf-1.4.1.tgz#8d172c064867f235c0c84a596806d279bf4bcc07"
  integrity sha1-jRcsBkhn8jXAyEpZaAbSeb9LzAc=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/fast-diff/download/fast-diff-1.1.2.tgz#4b62c42b8e03de3f848460b639079920695d0154"
  integrity sha1-S2LEK44D3j+EhGC2OQeZIGldAVQ=

fast-diff@^1.1.2, fast-diff@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/fast-diff/download/fast-diff-1.3.0.tgz#ece407fa550a64d638536cd727e129c61616e0f0"
  integrity sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=

fast-glob@^3.2.9:
  version "3.3.2"
  resolved "http://r.npm.sankuai.com/fast-glob/download/fast-glob-3.3.2.tgz#a904501e57cfdd2ffcded45e99a54fef55e46129"
  integrity sha1-qQRQHlfP3S/83tRemaVP71XkYSk=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.4"

fast-glob@^3.3.2:
  version "3.3.3"
  resolved "http://r.npm.sankuai.com/fast-glob/download/fast-glob-3.3.3.tgz#d06d585ce8dba90a16b0505c543c3ccfb3aeb818"
  integrity sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fast-xml-parser@^4.2.2:
  version "4.5.1"
  resolved "http://r.npm.sankuai.com/fast-xml-parser/download/fast-xml-parser-4.5.1.tgz#a7e665ff79b7919100a5202f23984b6150f9b31e"
  integrity sha1-p+Zl/3m3kZEApSAvI5hLYVD5sx4=
  dependencies:
    strnum "^1.0.5"

fast-xml-parser@^4.4.1:
  version "4.5.3"
  resolved "http://r.npm.sankuai.com/fast-xml-parser/download/fast-xml-parser-4.5.3.tgz#c54d6b35aa0f23dc1ea60b6c884340c006dc6efb"
  integrity sha1-xU1rNaoPI9wepgtsiENAwAbcbvs=
  dependencies:
    strnum "^1.1.1"

fastq@^1.6.0:
  version "1.18.0"
  resolved "http://r.npm.sankuai.com/fastq/download/fastq-1.18.0.tgz#d631d7e25faffea81887fe5ea8c9010e1b36fee0"
  integrity sha1-1jHX4l+v/qgYh/5eqMkBDhs2/uA=
  dependencies:
    reusify "^1.0.4"

fdir@^6.4.4:
  version "6.4.4"
  resolved "http://r.npm.sankuai.com/fdir/download/fdir-6.4.4.tgz#1cfcf86f875a883e19a8fab53622cfe992e8d2f9"
  integrity sha1-HPz4b4daiD4ZqPq1NiLP6ZLo0vk=

fecha@^4.2.1, fecha@~4.2.0:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/fecha/download/fecha-4.2.3.tgz#4d9ccdbc61e8629b259fdca67e65891448d569fd"
  integrity sha1-TZzNvGHoYpsln9ymfmWJFEjVaf0=

figures@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/figures/download/figures-2.0.0.tgz#3ab1a2d2a62c8bfb431a0c94cb797a2fce27c962"
  integrity sha1-OrGi0qYsi/tDGgyUy3l6L84nyWI=
  dependencies:
    escape-string-regexp "^1.0.5"

figures@^3.0.0:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/figures/download/figures-3.2.0.tgz#625c18bd293c604dc4a8ddb2febf0c88341746af"
  integrity sha1-YlwYvSk8YE3EqN2y/r8MiDQXRq8=
  dependencies:
    escape-string-regexp "^1.0.5"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz#211b2dd9659cb0394b073e7323ac3c933d522027"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

file-uri-to-path@1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/file-uri-to-path/download/file-uri-to-path-1.0.0.tgz#553a7b8446ff6f684359c445f1e37a05dacc33dd"
  integrity sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=

filelist@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/filelist/download/filelist-1.0.4.tgz#f78978a1e944775ff9e62e744424f215e58352b5"
  integrity sha1-94l4oelEd1/55i50RCTyFeWDUrU=
  dependencies:
    minimatch "^5.0.1"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "http://r.npm.sankuai.com/fill-range/download/fill-range-7.1.1.tgz#44265d3cac07e3ea7dc247516380643754a05292"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

filter-obj@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/filter-obj/download/filter-obj-5.1.0.tgz#5bd89676000a713d7db2e197f660274428e524ed"
  integrity sha1-W9iWdgAKcT19suGX9mAnRCjlJO0=

find-up@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/find-up/download/find-up-5.0.0.tgz#4c92819ecb7083561e4f4a240a86be5198f536fc"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "http://r.npm.sankuai.com/flat-cache/download/flat-cache-3.2.0.tgz#2c0c2d5040c99b1632771a9d105725c0115363ee"
  integrity sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatted@^3.2.9:
  version "3.3.2"
  resolved "http://r.npm.sankuai.com/flatted/download/flatted-3.3.2.tgz#adba1448a9841bec72b42c532ea23dbbedef1a27"
  integrity sha1-rboUSKmEG+xytCxTLqI9u+3vGic=

flru@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/flru/download/flru-1.0.2.tgz#1ae514c62b8b035ffff9ca9e4563ddcc817f4845"
  integrity sha1-GuUUxiuLA1//+cqeRWPdzIF/SEU=

fmin@0.0.2, fmin@^0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/fmin/download/fmin-0.0.2.tgz#59bbb40d43ffdc1c94cd00a568c41f95f1973017"
  integrity sha1-Wbu0DUP/3ByUzQClaMQflfGXMBc=
  dependencies:
    contour_plot "^0.0.1"
    json2module "^0.0.3"
    rollup "^0.25.8"
    tape "^4.5.1"
    uglify-js "^2.6.2"

follow-redirects@1.5.10:
  version "1.5.10"
  resolved "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.5.10.tgz#7b7a9f9aea2fdff36786a94ff643ed07f4ff5e2a"
  integrity sha1-e3qfmuov3/NnhqlP9kPtB/T/Xio=
  dependencies:
    debug "=3.1.0"

follow-redirects@^1.14.4, follow-redirects@^1.15.6:
  version "1.15.9"
  resolved "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.9.tgz#a604fa10e443bf98ca94228d9eebcc2e8a2c8ee1"
  integrity sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=

for-each@^0.3.3, for-each@^0.3.5, for-each@~0.3.3:
  version "0.3.5"
  resolved "http://r.npm.sankuai.com/for-each/download/for-each-0.3.5.tgz#d650688027826920feeb0af747ee7b9421a41d47"
  integrity sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=
  dependencies:
    is-callable "^1.2.7"

foreground-child@^3.1.0:
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/foreground-child/download/foreground-child-3.3.1.tgz#32e8e9ed1b68a3497befb9ac2b6adf92a638576f"
  integrity sha1-Mujp7Rtoo0l777msK2rfkqY4V28=
  dependencies:
    cross-spawn "^7.0.6"
    signal-exit "^4.0.1"

forever-agent@~0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/forever-agent/download/forever-agent-0.6.1.tgz#fbc71f0c41adeb37f96c577ad1ed42d8fdacca91"
  integrity sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=

form-data@^4.0.0:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-4.0.1.tgz#ba1076daaaa5bfd7e99c1a6cb02aa0a5cff90d48"
  integrity sha1-uhB22qqlv9fpnBpssCqgpc/5DUg=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

form-data@~2.3.2:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-2.3.3.tgz#dcce52c05f644f298c6a7ab936bd724ceffbf3a6"
  integrity sha1-3M5SwF9kTymManq5Nr1yTO/786Y=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.6"
    mime-types "^2.1.12"

fs-extra@^7.0.1:
  version "7.0.1"
  resolved "http://r.npm.sankuai.com/fs-extra/download/fs-extra-7.0.1.tgz#4f189c44aa123b895f722804f55ea23eadc348e9"
  integrity sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-readdir-recursive@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/fs-readdir-recursive/download/fs-readdir-recursive-1.1.0.tgz#e32fc030a2ccee44a6b5371308da54be0b397d27"
  integrity sha1-4y/AMKLM7kSmtTcTCNpUvgs5fSc=

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@~2.3.2:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

function.prototype.name@^1.1.6, function.prototype.name@^1.1.8:
  version "1.1.8"
  resolved "http://r.npm.sankuai.com/function.prototype.name/download/function.prototype.name-1.1.8.tgz#e68e1df7b259a5c949eeef95cdbde53edffabb78"
  integrity sha1-5o4d97JZpclJ7u+Vzb3lPt/6u3g=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    functions-have-names "^1.2.3"
    hasown "^2.0.2"
    is-callable "^1.2.7"

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/functions-have-names/download/functions-have-names-1.2.3.tgz#0404fe4ee2ba2f607f0e0ec3c80bae994133b834"
  integrity sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "http://r.npm.sankuai.com/gensync/download/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/get-caller-file/download/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.2.4, get-intrinsic@^1.2.5, get-intrinsic@^1.2.6:
  version "1.2.6"
  resolved "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.2.6.tgz#43dd3dd0e7b49b82b2dfcad10dc824bf7fc265d5"
  integrity sha1-Q9090Oe0m4Ky38rRDcgkv3/CZdU=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    dunder-proto "^1.0.0"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    function-bind "^1.1.2"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.0.0"

get-intrinsic@^1.2.7, get-intrinsic@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz#743f0e3b6964a93a5491ed1bffaae054d7f98d01"
  integrity sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-nonce@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/get-nonce/download/get-nonce-1.0.1.tgz#fdf3f0278073820d2ce9426c18f07481b1e0cdf3"
  integrity sha1-/fPwJ4Bzgg0s6UJsGPB0gbHgzfM=

get-proto@^1.0.0, get-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/get-proto/download/get-proto-1.0.1.tgz#150b3f2743869ef3e851ec0c49d15b1d14d00ee1"
  integrity sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-stdin@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/get-stdin/download/get-stdin-6.0.0.tgz#9e09bf712b360ab9225e812048f71fde9c89657b"
  integrity sha1-ngm/cSs2CrkiXoEgSPcf3pyJZXs=

get-stream@^5.1.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/get-stream/download/get-stream-5.2.0.tgz#4966a1795ee5ace65e706c4b7beb71257d6e22d3"
  integrity sha1-SWaheV7lrOZecGxLe+txJX1uItM=
  dependencies:
    pump "^3.0.0"

get-symbol-description@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/get-symbol-description/download/get-symbol-description-1.1.0.tgz#7bdd54e0befe8ffc9f3b4e203220d9f1e881b6ee"
  integrity sha1-e91U4L7+j/yfO04gMiDZ8eiBtu4=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"

getpass@^0.1.1:
  version "0.1.7"
  resolved "http://r.npm.sankuai.com/getpass/download/getpass-0.1.7.tgz#5eff8e3e684d569ae4cb2b1282604e8ba62149fa"
  integrity sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=
  dependencies:
    assert-plus "^1.0.0"

gl-matrix@^3.0.0, gl-matrix@^3.1.0, gl-matrix@^3.3.0, gl-matrix@^3.4.3:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/gl-matrix/download/gl-matrix-3.4.3.tgz#fc1191e8320009fd4d20e9339595c6041ddc22c9"
  integrity sha1-/BGR6DIACf1NIOkzlZXGBB3cIsk=

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "http://r.npm.sankuai.com/glob-parent/download/glob-parent-6.0.2.tgz#6d237d99083950c79290f24c7642a3de9a28f9e3"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

glob@^10.3.10:
  version "10.4.5"
  resolved "http://r.npm.sankuai.com/glob/download/glob-10.4.5.tgz#f4d9f0b90ffdbab09c9d77f5f29b4262517b0956"
  integrity sha1-9NnwuQ/9urCcnXf18ptCYlF7CVY=
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^7.0.0, glob@^7.1.3, glob@^7.2.0, glob@~7.2.3:
  version "7.2.3"
  resolved "http://r.npm.sankuai.com/glob/download/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^13.19.0:
  version "13.24.0"
  resolved "http://r.npm.sankuai.com/globals/download/globals-13.24.0.tgz#8432a19d78ce0c1e833949c36adb345400bb1171"
  integrity sha1-hDKhnXjODB6DOUnDats0VAC7EXE=
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/globalthis/download/globalthis-1.0.4.tgz#7430ed3a975d97bfb59bcce41f5cabbafa651236"
  integrity sha1-dDDtOpddl7+1m8zkH1yruvplEjY=
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

globby@^11.1.0:
  version "11.1.0"
  resolved "http://r.npm.sankuai.com/globby/download/globby-11.1.0.tgz#bd4be98bb042f83d796f7e3811991fbe82a0d34b"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

globrex@^0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/globrex/download/globrex-0.1.2.tgz#dd5d9ec826232730cd6793a5e33a9302985e6098"
  integrity sha1-3V2eyCYjJzDNZ5Ol4zqTApheYJg=

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/gopd/download/gopd-1.2.0.tgz#89f56b8217bdbc8802bd299df6d7f1081d7e51a1"
  integrity sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=

got@11.8.6:
  version "11.8.6"
  resolved "http://r.npm.sankuai.com/got/download/got-11.8.6.tgz#276e827ead8772eddbcfc97170590b841823233a"
  integrity sha1-J26Cfq2Hcu3bz8lxcFkLhBgjIzo=
  dependencies:
    "@sindresorhus/is" "^4.0.0"
    "@szmarczak/http-timer" "^4.0.5"
    "@types/cacheable-request" "^6.0.1"
    "@types/responselike" "^1.0.0"
    cacheable-lookup "^5.0.3"
    cacheable-request "^7.0.2"
    decompress-response "^6.0.0"
    http2-wrapper "^1.0.0-beta.5.2"
    lowercase-keys "^2.0.0"
    p-cancelable "^2.0.0"
    responselike "^2.0.0"

graceful-fs@^4.1.2, graceful-fs@^4.1.6:
  version "4.2.11"
  resolved "http://r.npm.sankuai.com/graceful-fs/download/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=

graphemer@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/graphemer/download/graphemer-1.4.0.tgz#fb2f1d55e0e3a1849aeffc90c4fa0dd53a0e66c6"
  integrity sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=

gud@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/gud/download/gud-1.0.0.tgz#a489581b17e6a70beca9abe3ae57de7a499852c0"
  integrity sha1-pIlYGxfmpwvsqavjrlfeekmYUsA=

har-schema@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/har-schema/download/har-schema-2.0.0.tgz#a94c2224ebcac04782a0d9035521f24735b7ec92"
  integrity sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=

har-validator@~5.1.3:
  version "5.1.5"
  resolved "http://r.npm.sankuai.com/har-validator/download/har-validator-5.1.5.tgz#1f0803b9f8cb20c0fa13822df1ecddb36bde1efd"
  integrity sha1-HwgDufjLIMD6E4It8ezds2veHv0=
  dependencies:
    ajv "^6.12.3"
    har-schema "^2.0.0"

has-ansi@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/has-ansi/download/has-ansi-2.0.0.tgz#34f5049ce1ecdf2b0649af3ef24e45ed35416d91"
  integrity sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=
  dependencies:
    ansi-regex "^2.0.0"

has-bigints@^1.0.2:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/has-bigints/download/has-bigints-1.1.0.tgz#28607e965ac967e03cd2a2c70a2636a1edad49fe"
  integrity sha1-KGB+llrJZ+A80qLHCiY2oe2tSf4=

has-flag@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-3.0.0.tgz#b5d454dc2199ae225699f3467e5a07f3b955bafd"
  integrity sha1-tdRU3CGZriJWmfNGfloH87lVuv0=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-property-descriptors/download/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
  integrity sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/has-proto/download/has-proto-1.2.0.tgz#5de5a6eabd95fdffd9818b43055e8065e39fe9d5"
  integrity sha1-XeWm6r2V/f/ZgYtDBV6AZeOf6dU=
  dependencies:
    dunder-proto "^1.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.1.0.tgz#fc9c6a783a084951d0b971fe1018de813707a338"
  integrity sha1-/JxqeDoISVHQuXH+EBjegTcHozg=

has-tostringtag@^1.0.0, has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
  integrity sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=
  dependencies:
    has-symbols "^1.0.3"

has@~1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/has/download/has-1.0.4.tgz#2eb2860e000011dae4f1406a86fe80e530fb2ec6"
  integrity sha1-LrKGDgAAEdrk8UBqhv6A5TD7LsY=

hasown@^2.0.0, hasown@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

history@^4.7.2:
  version "4.10.1"
  resolved "http://r.npm.sankuai.com/history/download/history-4.10.1.tgz#33371a65e3a83b267434e2b3f3b1b4c58aad4cf3"
  integrity sha1-MzcaZeOoOyZ0NOKz87G0xYqtTPM=
  dependencies:
    "@babel/runtime" "^7.1.2"
    loose-envify "^1.2.0"
    resolve-pathname "^3.0.0"
    tiny-invariant "^1.0.2"
    tiny-warning "^1.0.0"
    value-equal "^1.0.1"

history@^5.0.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/history/download/history-5.3.0.tgz#1548abaa245ba47992f063a0783db91ef201c73b"
  integrity sha1-FUirqiRbpHmS8GOgeD25HvIBxzs=
  dependencies:
    "@babel/runtime" "^7.7.6"

hoist-non-react-statics@^2.1.1, hoist-non-react-statics@^2.2.1, hoist-non-react-statics@^2.5.0:
  version "2.5.5"
  resolved "http://r.npm.sankuai.com/hoist-non-react-statics/download/hoist-non-react-statics-2.5.5.tgz#c5903cf409c0dfd908f388e619d86b9c1174cb47"
  integrity sha1-xZA89AnA39kI84jmGdhrnBF0y0c=

hoist-non-react-statics@^3.3.1:
  version "3.3.2"
  resolved "http://r.npm.sankuai.com/hoist-non-react-statics/download/hoist-non-react-statics-3.3.2.tgz#ece0acaf71d62c2969c2ec59feff42a4b1a85b45"
  integrity sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=
  dependencies:
    react-is "^16.7.0"

html-void-elements@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/html-void-elements/download/html-void-elements-2.0.1.tgz#29459b8b05c200b6c5ee98743c41b979d577549f"
  integrity sha1-KUWbiwXCALbF7ph0PEG5edV3VJ8=

htmlparser2@^8.0.1:
  version "8.0.2"
  resolved "http://r.npm.sankuai.com/htmlparser2/download/htmlparser2-8.0.2.tgz#f002151705b383e62433b5cf466f5b716edaec21"
  integrity sha1-8AIVFwWzg+YkM7XPRm9bcW7a7CE=
  dependencies:
    domelementtype "^2.3.0"
    domhandler "^5.0.3"
    domutils "^3.0.1"
    entities "^4.4.0"

http-cache-semantics@^4.0.0:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/http-cache-semantics/download/http-cache-semantics-4.1.1.tgz#abe02fcb2985460bf0323be664436ec3476a6d5a"
  integrity sha1-q+AvyymFRgvwMjvmZENuw0dqbVo=

http-proxy-agent@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/http-proxy-agent/download/http-proxy-agent-2.1.0.tgz#e4821beef5b2142a2026bd73926fe537631c5405"
  integrity sha1-5IIb7vWyFCogJr1zkm/lN2McVAU=
  dependencies:
    agent-base "4"
    debug "3.1.0"

http-signature@~1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/http-signature/download/http-signature-1.2.0.tgz#9aecd925114772f3d95b65a60abb8f7c18fbace1"
  integrity sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=
  dependencies:
    assert-plus "^1.0.0"
    jsprim "^1.2.2"
    sshpk "^1.7.0"

http2-wrapper@^1.0.0-beta.5.2:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/http2-wrapper/download/http2-wrapper-1.0.3.tgz#b8f55e0c1f25d4ebd08b3b0c2c079f9590800b3d"
  integrity sha1-uPVeDB8l1OvQizsMLAeflZCACz0=
  dependencies:
    quick-lru "^5.1.1"
    resolve-alpn "^1.0.0"

i18next@^20.4.0:
  version "20.6.1"
  resolved "http://r.npm.sankuai.com/i18next/download/i18next-20.6.1.tgz#535e5f6e5baeb685c7d25df70db63bf3cc0aa345"
  integrity sha1-U15fbluutoXH0l33DbY788wKo0U=
  dependencies:
    "@babel/runtime" "^7.12.0"

iconv-lite@0.6, iconv-lite@^0.6.3:
  version "0.6.3"
  resolved "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.6.3.tgz#a52f80bf38da1952eb5c681790719871a1a72501"
  integrity sha1-pS+AvzjaGVLrXGgXkHGYcaGnJQE=
  dependencies:
    safer-buffer ">= 2.1.2 < 3.0.0"

iconv-lite@^0.4.24, iconv-lite@^0.4.8:
  version "0.4.24"
  resolved "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ieee754@^1.1.13:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/ieee754/download/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

ignore@^5.2.0:
  version "5.3.2"
  resolved "http://r.npm.sankuai.com/ignore/download/ignore-5.3.2.tgz#3cd40e729f3643fd87cb04e50bf0eb722bc596f5"
  integrity sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=

image-size@~0.5.0:
  version "0.5.5"
  resolved "http://r.npm.sankuai.com/image-size/download/image-size-0.5.5.tgz#09dfd4ab9d20e29eb1c3e80b8990378df9e3cb9c"
  integrity sha1-Cd/Uq50g4p6xw+gLiZA3jfnjy5w=

immer@^9.0.17, immer@^9.0.6:
  version "9.0.21"
  resolved "http://r.npm.sankuai.com/immer/download/immer-9.0.21.tgz#1e025ea31a40f24fb064f1fef23e931496330176"
  integrity sha1-HgJeoxpA8k+wZPH+8j6TFJYzAXY=

immutability-helper@^3.0.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/immutability-helper/download/immutability-helper-3.1.1.tgz#2b86b2286ed3b1241c9e23b7b21e0444f52f77b7"
  integrity sha1-K4ayKG7TsSQcniO3sh4ERPUvd7c=

immutable@^4.0.0-rc.12:
  version "4.3.7"
  resolved "http://r.npm.sankuai.com/immutable/download/immutable-4.3.7.tgz#c70145fc90d89fb02021e65c84eb0226e4e5a381"
  integrity sha1-xwFF/JDYn7AgIeZchOsCJuTlo4E=

immutable@^5.0.2:
  version "5.0.3"
  resolved "http://r.npm.sankuai.com/immutable/download/immutable-5.0.3.tgz#aa037e2313ea7b5d400cd9298fa14e404c933db1"
  integrity sha1-qgN+IxPqe11ADNkpj6FOQEyTPbE=

import-fresh@^3.2.1:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/import-fresh/download/import-fresh-3.3.0.tgz#37162c25fcb9ebaa2e6e53d5b4d88ce17d9e0c2b"
  integrity sha1-NxYsJfy566oublPVtNiM4X2eDCs=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://r.npm.sankuai.com/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

ini@^1.3.5:
  version "1.3.8"
  resolved "http://r.npm.sankuai.com/ini/download/ini-1.3.8.tgz#a29da425b48806f34767a4efce397269af28432c"
  integrity sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=

inquirer@^6.3.1:
  version "6.5.2"
  resolved "http://r.npm.sankuai.com/inquirer/download/inquirer-6.5.2.tgz#ad50942375d036d327ff528c08bd5fab089928ca"
  integrity sha1-rVCUI3XQNtMn/1KMCL1fqwiZKMo=
  dependencies:
    ansi-escapes "^3.2.0"
    chalk "^2.4.2"
    cli-cursor "^2.1.0"
    cli-width "^2.0.0"
    external-editor "^3.0.3"
    figures "^2.0.0"
    lodash "^4.17.12"
    mute-stream "0.0.7"
    run-async "^2.2.0"
    rxjs "^6.4.0"
    string-width "^2.1.0"
    strip-ansi "^5.1.0"
    through "^2.3.6"

inquirer@^8.2.0:
  version "8.2.6"
  resolved "http://r.npm.sankuai.com/inquirer/download/inquirer-8.2.6.tgz#733b74888195d8d400a67ac332011b5fae5ea562"
  integrity sha1-czt0iIGV2NQApnrDMgEbX65epWI=
  dependencies:
    ansi-escapes "^4.2.1"
    chalk "^4.1.1"
    cli-cursor "^3.1.0"
    cli-width "^3.0.0"
    external-editor "^3.0.3"
    figures "^3.0.0"
    lodash "^4.17.21"
    mute-stream "0.0.8"
    ora "^5.4.1"
    run-async "^2.4.0"
    rxjs "^7.5.5"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"
    through "^2.3.6"
    wrap-ansi "^6.0.1"

int64-convert@^0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/int64-convert/download/int64-convert-0.1.2.tgz#0f00a5a4431268c85291895afb923e4c8d218e26"
  integrity sha1-DwClpEMSaMhSkYla+5I+TI0hjiY=

int64-transform@^0.1.13:
  version "0.1.17"
  resolved "http://r.npm.sankuai.com/int64-transform/download/int64-transform-0.1.17.tgz#1dd3e3f39a64529e413f9ae0db140c065832266b"
  integrity sha1-HdPj85pkUp5BP5rg2xQMBlgyJms=
  dependencies:
    bindings "^1.3.0"
    int64-convert "^0.1.2"
    node-addon-api "^1.4.0"

internal-slot@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/internal-slot/download/internal-slot-1.1.0.tgz#1eac91762947d2f7056bc838d93e13b2e9604961"
  integrity sha1-HqyRdilH0vcFa8g42T4TsulgSWE=
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.2"
    side-channel "^1.1.0"

"internmap@1 - 2":
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/internmap/download/internmap-2.0.3.tgz#6685f23755e43c524e251d29cbc97248e3061009"
  integrity sha1-ZoXyN1XkPFJOJR0py8lySOMGEAk=

interpret@^1.0.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/interpret/download/interpret-1.4.0.tgz#665ab8bc4da27a774a40584e812e3e0fa45b1a1e"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

intersection-observer@^0.12.0:
  version "0.12.2"
  resolved "http://r.npm.sankuai.com/intersection-observer/download/intersection-observer-0.12.2.tgz#4a45349cc0cd91916682b1f44c28d7ec737dc375"
  integrity sha1-SkU0nMDNkZFmgrH0TCjX7HN9w3U=

invariant@^2.0.0, invariant@^2.2.4:
  version "2.2.4"
  resolved "http://r.npm.sankuai.com/invariant/download/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

ip@^1.1.5, ip@^1.1.9:
  version "1.1.9"
  resolved "http://r.npm.sankuai.com/ip/download/ip-1.1.9.tgz#8dfbcc99a754d07f425310b86a99546b1151e396"
  integrity sha1-jfvMmadU0H9CUxC4aplUaxFR45Y=

ipv4@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/ipv4/download/ipv4-1.0.4.tgz#8e4cfe98ac7b28e6b0d19431da2999e09b4add58"
  integrity sha1-jkz+mKx7KOaw0ZQx2imZ4JtK3Vg=
  dependencies:
    address "^1.0.3"
    chalk "^1.1.3"
    copy-paste "^1.3.0"

is-arguments@^1.1.1:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/is-arguments/download/is-arguments-1.2.0.tgz#ad58c6aecf563b78ef2bf04df540da8f5d7d8e1b"
  integrity sha1-rVjGrs9WO3jvK/BN9UDaj119jhs=
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-array-buffer@^3.0.4, is-array-buffer@^3.0.5:
  version "3.0.5"
  resolved "http://r.npm.sankuai.com/is-array-buffer/download/is-array-buffer-3.0.5.tgz#65742e1e687bd2cc666253068fd8707fe4d44280"
  integrity sha1-ZXQuHmh70sxmYlMGj9hwf+TUQoA=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-arrayish@^0.3.1:
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/is-arrayish/download/is-arrayish-0.3.2.tgz#4574a2ae56f7ab206896fb431eaeed066fdf8f03"
  integrity sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=

is-async-function@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/is-async-function/download/is-async-function-2.1.1.tgz#3e69018c8e04e73b738793d020bfe884b9fd3523"
  integrity sha1-PmkBjI4E5ztzh5PQIL/ohLn9NSM=
  dependencies:
    async-function "^1.0.0"
    call-bound "^1.0.3"
    get-proto "^1.0.1"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-bigint@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-bigint/download/is-bigint-1.1.0.tgz#dda7a3445df57a42583db4228682eba7c4170672"
  integrity sha1-3aejRF31ekJYPbQihoLrp8QXBnI=
  dependencies:
    has-bigints "^1.0.2"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/is-binary-path/download/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.2.1:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/is-boolean-object/download/is-boolean-object-1.2.2.tgz#7067f47709809a393c71ff5bb3e135d8a9215d9e"
  integrity sha1-cGf0dwmAmjk8cf9bs+E12KkhXZ4=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-buffer@^1.1.5:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/is-buffer/download/is-buffer-1.1.6.tgz#efaa2ea9daa0d7ab2ea13a97b2b8ad51fefbe8be"
  integrity sha1-76ouqdqg16suoTqXsritUf776L4=

is-buffer@^2.0.2:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/is-buffer/download/is-buffer-2.0.5.tgz#ebc252e400d22ff8d77fa09888821a24a658c191"
  integrity sha1-68JS5ADSL/jXf6CYiIIaJKZYwZE=

is-callable@^1.2.7:
  version "1.2.7"
  resolved "http://r.npm.sankuai.com/is-callable/download/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=

is-class-hotfix@~0.0.6:
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/is-class-hotfix/download/is-class-hotfix-0.0.6.tgz#a527d31fb23279281dde5f385c77b5de70a72435"
  integrity sha1-pSfTH7IyeSgd3l84XHe13nCnJDU=

is-core-module@^2.13.0, is-core-module@^2.16.0:
  version "2.16.1"
  resolved "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.16.1.tgz#2a98801a849f43e2add644fbb6bc6229b19a4ef4"
  integrity sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=
  dependencies:
    hasown "^2.0.2"

is-data-view@^1.0.1, is-data-view@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/is-data-view/download/is-data-view-1.0.2.tgz#bae0a41b9688986c2188dda6657e56b8f9e63b8e"
  integrity sha1-uuCkG5aImGwhiN2mZX5WuPnmO44=
  dependencies:
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    is-typed-array "^1.1.13"

is-date-object@^1.0.5, is-date-object@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-date-object/download/is-date-object-1.1.0.tgz#ad85541996fc7aa8b2729701d27b7319f95d82f7"
  integrity sha1-rYVUGZb8eqiycpcB0ntzGfldgvc=
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finalizationregistry@^1.1.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-finalizationregistry/download/is-finalizationregistry-1.1.1.tgz#eefdcdc6c94ddd0674d9c85887bf93f944a97c90"
  integrity sha1-7v3NxslN3QZ02chYh7+T+USpfJA=
  dependencies:
    call-bound "^1.0.3"

is-fullwidth-code-point@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz#a3b30a5c4f199183167aaab93beefae3ddfb654f"
  integrity sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-function@^1.0.10:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-generator-function/download/is-generator-function-1.1.0.tgz#bf3eeda931201394f57b5dba2800f91a238309ca"
  integrity sha1-vz7tqTEgE5T1e126KAD5GiODCco=
  dependencies:
    call-bound "^1.0.3"
    get-proto "^1.0.0"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/is-glob/download/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-hotkey@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/is-hotkey/download/is-hotkey-0.2.0.tgz#1835a68171a91e5c9460869d96336947c8340cef"
  integrity sha1-GDWmgXGpHlyUYIadljNpR8g0DO8=

is-interactive@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-interactive/download/is-interactive-1.0.0.tgz#cea6e6ae5c870a7b0a0004070b7b587e0252912e"
  integrity sha1-zqbmrlyHCnsKAAQHC3tYfgJSkS4=

is-map@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/is-map/download/is-map-2.0.3.tgz#ede96b7fe1e270b3c4465e3a465658764926d62e"
  integrity sha1-7elrf+HicLPERl46RlZYdkkm1i4=

is-number-object@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-number-object/download/is-number-object-1.1.1.tgz#144b21e95a1bc148205dcc2814a9134ec41b2541"
  integrity sha1-FEsh6VobwUggXcwoFKkTTsQbJUE=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/is-path-inside/download/is-path-inside-3.0.3.tgz#d231362e53a07ff2b0e0ea7fed049161ffd16283"
  integrity sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=

is-plain-object@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/is-plain-object/download/is-plain-object-5.0.0.tgz#4427f50ab3429e9025ea7d52e9043a9ef4159344"
  integrity sha1-RCf1CrNCnpAl6n1S6QQ6nvQVk0Q=

is-regex@^1.1.4, is-regex@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/is-regex/download/is-regex-1.2.1.tgz#76d70a3ed10ef9be48eb577887d74205bf0cad22"
  integrity sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-regex@~1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/is-regex/download/is-regex-1.1.4.tgz#eef5663cd59fa4c0ae339505323df6854bb15958"
  integrity sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=
  dependencies:
    call-bind "^1.0.2"
    has-tostringtag "^1.0.0"

is-set@^2.0.3:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/is-set/download/is-set-2.0.3.tgz#8ab209ea424608141372ded6e0cb200ef1d9d01d"
  integrity sha1-irIJ6kJGCBQTct7W4MsgDvHZ0B0=

is-shared-array-buffer@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/is-shared-array-buffer/download/is-shared-array-buffer-1.0.4.tgz#9b67844bd9b7f246ba0708c3a93e34269c774f6f"
  integrity sha1-m2eES9m38ka6BwjDqT40Jpx3T28=
  dependencies:
    call-bound "^1.0.3"

is-string@^1.0.7, is-string@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-string/download/is-string-1.1.1.tgz#92ea3f3d5c5b6e039ca8677e5ac8d07ea773cbb9"
  integrity sha1-kuo/PVxbbgOcqGd+WsjQfqdzy7k=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-symbol@^1.0.4, is-symbol@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-symbol/download/is-symbol-1.1.1.tgz#f47761279f532e2b05a7024a7506dbbedacd0634"
  integrity sha1-9HdhJ59TLisFpwJKdQbbvtrNBjQ=
  dependencies:
    call-bound "^1.0.2"
    has-symbols "^1.1.0"
    safe-regex-test "^1.1.0"

is-type-of@^1.2.1:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/is-type-of/download/is-type-of-1.4.0.tgz#3ed175a0eee888b1da4983332e7714feb8a8fb2b"
  integrity sha1-PtF1oO7oiLHaSYMzLncU/rio+ys=
  dependencies:
    core-util-is "^1.0.2"
    is-class-hotfix "~0.0.6"
    isstream "~0.1.2"

is-typed-array@^1.1.13, is-typed-array@^1.1.14, is-typed-array@^1.1.15:
  version "1.1.15"
  resolved "http://r.npm.sankuai.com/is-typed-array/download/is-typed-array-1.1.15.tgz#4bfb4a45b61cee83a5a46fba778e4e8d59c0ce0b"
  integrity sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=
  dependencies:
    which-typed-array "^1.1.16"

is-typedarray@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/is-typedarray/download/is-typedarray-1.0.0.tgz#e479c80858df0c1b11ddda6940f96011fcda4a9a"
  integrity sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=

is-unicode-supported@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/is-unicode-supported/download/is-unicode-supported-0.1.0.tgz#3f26c76a809593b52bfa2ecb5710ed2779b522a7"
  integrity sha1-PybHaoCVk7Ur+i7LVxDtJ3m1Iqc=

is-url@^1.2.4:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/is-url/download/is-url-1.2.4.tgz#04a4df46d28c4cff3d73d01ff06abeb318a1aa52"
  integrity sha1-BKTfRtKMTP89c9Af8Gq+sxihqlI=

is-weakmap@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/is-weakmap/download/is-weakmap-2.0.2.tgz#bf72615d649dfe5f699079c54b83e47d1ae19cfd"
  integrity sha1-v3JhXWSd/l9pkHnFS4PkfRrhnP0=

is-weakref@^1.0.2, is-weakref@^1.1.0:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/is-weakref/download/is-weakref-1.1.1.tgz#eea430182be8d64174bd96bffbc46f21bf3f9293"
  integrity sha1-7qQwGCvo1kF0vZa/+8RvIb8/kpM=
  dependencies:
    call-bound "^1.0.3"

is-weakset@^2.0.3:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/is-weakset/download/is-weakset-2.0.4.tgz#c9f5deb0bc1906c6d6f1027f284ddf459249daca"
  integrity sha1-yfXesLwZBsbW8QJ/KE3fRZJJ2so=
  dependencies:
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-what@^3.14.1:
  version "3.14.1"
  resolved "http://r.npm.sankuai.com/is-what/download/is-what-3.14.1.tgz#e1222f46ddda85dead0fd1c9df131760e77755c1"
  integrity sha1-4SIvRt3ahd6tD9HJ3xMXYOd3VcE=

is-wsl@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/is-wsl/download/is-wsl-1.1.0.tgz#1f16e4aa22b04d1336b66188a66af3c600c3a66d"
  integrity sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=

isarray@0.0.1:
  version "0.0.1"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@^2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/isarray/download/isarray-2.0.5.tgz#8af1e4c1221244cc62459faf38940d4e644a5723"
  integrity sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

isstream@~0.1.2:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/isstream/download/isstream-0.1.2.tgz#47e63f7af55afa6f92e1500e690eb8b8529c099a"
  integrity sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=

iterator.prototype@^1.1.4:
  version "1.1.5"
  resolved "http://r.npm.sankuai.com/iterator.prototype/download/iterator.prototype-1.1.5.tgz#12c959a29de32de0aa3bbbb801f4d777066dae39"
  integrity sha1-EslZop3jLeCqO7u4AfTXdwZtrjk=
  dependencies:
    define-data-property "^1.1.4"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    get-proto "^1.0.0"
    has-symbols "^1.1.0"
    set-function-name "^2.0.2"

jackspeak@^3.1.2:
  version "3.4.3"
  resolved "http://r.npm.sankuai.com/jackspeak/download/jackspeak-3.4.3.tgz#8833a9d89ab4acde6188942bd1c53b6390ed5a8a"
  integrity sha1-iDOp2Jq0rN5hiJQr0cU7Y5DtWoo=
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jake@^10.6.1, jake@^10.8.5:
  version "10.9.2"
  resolved "http://r.npm.sankuai.com/jake/download/jake-10.9.2.tgz#6ae487e6a69afec3a5e167628996b59f35ae2b7f"
  integrity sha1-auSH5qaa/sOl4WdiiZa1nzWuK38=
  dependencies:
    async "^3.2.3"
    chalk "^4.0.2"
    filelist "^1.0.4"
    minimatch "^3.1.2"

jest-worker@^26.2.1:
  version "26.6.2"
  resolved "http://r.npm.sankuai.com/jest-worker/download/jest-worker-26.6.2.tgz#7f72cbc4d643c365e27b9fd775f9d0eaa9c7a8ed"
  integrity sha1-f3LLxNZDw2Xie5/XdfnQ6qnHqO0=
  dependencies:
    "@types/node" "*"
    merge-stream "^2.0.0"
    supports-color "^7.0.0"

jiti@^1.21.6:
  version "1.21.7"
  resolved "http://r.npm.sankuai.com/jiti/download/jiti-1.21.7.tgz#9dd81043424a3d28458b193d965f0d18a2300ba9"
  integrity sha1-ndgQQ0JKPShFixk9ll8NGKIwC6k=

joycon@^2.2.5:
  version "2.2.5"
  resolved "http://r.npm.sankuai.com/joycon/download/joycon-2.2.5.tgz#8d4cf4cbb2544d7b7583c216fcdfec19f6be1615"
  integrity sha1-jUz0y7JUTXt1g8IW/N/sGfa+FhU=

joycon@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/joycon/download/joycon-3.1.1.tgz#bce8596d6ae808f8b68168f5fc69280996894f03"
  integrity sha1-vOhZbWroCPi2gWj1/GkoCZaJTwM=

js-cookie@^3.0.5:
  version "3.0.5"
  resolved "http://r.npm.sankuai.com/js-cookie/download/js-cookie-3.0.5.tgz#0b7e2fd0c01552c58ba86e0841f94dc2557dcdbc"
  integrity sha1-C34v0MAVUsWLqG4IQflNwlV9zbw=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^3.13.1:
  version "3.14.1"
  resolved "http://r.npm.sankuai.com/js-yaml/download/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/js-yaml/download/js-yaml-4.1.0.tgz#c1fb65f8f5017901cdd2c951864ba18458a10602"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsbn@~0.1.0:
  version "0.1.1"
  resolved "http://r.npm.sankuai.com/jsbn/download/jsbn-0.1.1.tgz#a5e654c2e5a2deb5f201d96cefbca80c0ef2f513"
  integrity sha1-peZUwuWi3rXyAdls77yoDA7y9RM=

jsesc@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-3.1.0.tgz#74d335a234f67ed19907fdadfac7ccf9d409825d"
  integrity sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=

jsesc@~3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/jsesc/download/jsesc-3.0.2.tgz#bb8b09a6597ba426425f2e4a07245c3d00b9343e"
  integrity sha1-u4sJpll7pCZCXy5KByRcPQC5ND4=

json-buffer@3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/json-buffer/download/json-buffer-3.0.1.tgz#9338802a30d3b6605fbe0613e094008ca8c05a13"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema@0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/json-schema/download/json-schema-0.4.0.tgz#f7de4cf6efab838ebaeb3236474cbba5a1930ab5"
  integrity sha1-995M9u+rg4666zI2R0y7paGTCrU=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json-stringify-safe@~5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz#1296a2d58fd45f19a0f6ce01d65701e2c735b6eb"
  integrity sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=

json2module@^0.0.3:
  version "0.0.3"
  resolved "http://r.npm.sankuai.com/json2module/download/json2module-0.0.3.tgz#00fb5f4a9b7adfc3f0647c29cb17bcd1979be9b2"
  integrity sha1-APtfSpt638PwZHwpyxe80Zeb6bI=
  dependencies:
    rw "^1.3.2"

json2mq@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/json2mq/download/json2mq-0.2.0.tgz#b637bd3ba9eabe122c83e9720483aeb10d2c904a"
  integrity sha1-tje9O6nqvhIsg+lyBIOusQ0skEo=
  dependencies:
    string-convert "^0.2.0"

json5@^2.2.3:
  version "2.2.3"
  resolved "http://r.npm.sankuai.com/json5/download/json5-2.2.3.tgz#78cd6f1a19bdc12b73db5ad0c61efd66c1e29283"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/jsonfile/download/jsonfile-4.0.0.tgz#8771aae0799b64076b76640fca058f9c10e33ecb"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsprim@^1.2.2:
  version "1.4.2"
  resolved "http://r.npm.sankuai.com/jsprim/download/jsprim-1.4.2.tgz#712c65533a15c878ba59e9ed5f0e26d5b77c5feb"
  integrity sha1-cSxlUzoVyHi6WentXw4m1bd8X+s=
  dependencies:
    assert-plus "1.0.0"
    extsprintf "1.3.0"
    json-schema "0.4.0"
    verror "1.10.0"

"jsx-ast-utils@^2.4.1 || ^3.0.0":
  version "3.3.5"
  resolved "http://r.npm.sankuai.com/jsx-ast-utils/download/jsx-ast-utils-3.3.5.tgz#4766bd05a8e2a11af222becd19e15575e52a853a"
  integrity sha1-R2a9BajioRryIr7NGeFVdeUqhTo=
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

keyv@^4.0.0, keyv@^4.5.3:
  version "4.5.4"
  resolved "http://r.npm.sankuai.com/keyv/download/keyv-4.5.4.tgz#a879a99e29452f942439f2a405e3af8b31d4de93"
  integrity sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=
  dependencies:
    json-buffer "3.0.1"

kind-of@^3.0.2:
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/kind-of/download/kind-of-3.2.2.tgz#31ea21a734bab9bbb0f32466d893aea51e4a3c64"
  integrity sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=
  dependencies:
    is-buffer "^1.1.5"

koa-compose@^3.0.0:
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/koa-compose/download/koa-compose-3.2.1.tgz#a85ccb40b7d986d8e5a345b3a1ace8eabcf54de7"
  integrity sha1-qFzLQLfZhtjlo0Wzoazo6rz1Tec=
  dependencies:
    any-promise "^1.1.0"

koa-convert@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/koa-convert/download/koa-convert-1.2.0.tgz#da40875df49de0539098d1700b50820cebcd21d0"
  integrity sha1-2kCHXfSd4FOQmNFwC1CCDOvNIdA=
  dependencies:
    co "^4.6.0"
    koa-compose "^3.0.0"

lazy-cache@^1.0.3:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/lazy-cache/download/lazy-cache-1.0.4.tgz#a1d78fc3a50474cb80845d3b3b6e1da49a446e8e"
  integrity sha1-odePw6UEdMuAhF07O24dpJpEbo4=

less@^4.2.2:
  version "4.2.2"
  resolved "http://r.npm.sankuai.com/less/download/less-4.2.2.tgz#4b59ede113933b58ab152190edf9180fc36846d8"
  integrity sha1-S1nt4ROTO1irFSGQ7fkYD8NoRtg=
  dependencies:
    copy-anything "^2.0.1"
    parse-node-version "^1.0.1"
    tslib "^2.3.0"
  optionalDependencies:
    errno "^0.1.1"
    graceful-fs "^4.1.2"
    image-size "~0.5.0"
    make-dir "^2.1.0"
    mime "^1.4.1"
    needle "^3.1.0"
    source-map "~0.6.0"

levn@^0.4.1:
  version "0.4.1"
  resolved "http://r.npm.sankuai.com/levn/download/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lilconfig@^3.0.0, lilconfig@^3.1.1, lilconfig@^3.1.3:
  version "3.1.3"
  resolved "http://r.npm.sankuai.com/lilconfig/download/lilconfig-3.1.3.tgz#a1bcfd6257f9585bf5ae14ceeebb7b559025e4c4"
  integrity sha1-obz9Ylf5WFv1rhTO7rt7VZAl5MQ=

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "http://r.npm.sankuai.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz#eca284f75d2965079309dc0ad9255abb2ebc1632"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

linkify-it@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/linkify-it/download/linkify-it-5.0.0.tgz#9ef238bfa6dc70bd8e7f9572b52d369af569b421"
  integrity sha1-nvI4v6bccL2Of5VytS02mvVptCE=
  dependencies:
    uc.micro "^2.0.0"

load-tsconfig@^0.2.3:
  version "0.2.5"
  resolved "http://r.npm.sankuai.com/load-tsconfig/download/load-tsconfig-0.2.5.tgz#453b8cd8961bfb912dea77eb6c168fe8cca3d3a1"
  integrity sha1-RTuM2JYb+5Et6nfrbBaP6Myj06E=

locate-path@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/locate-path/download/locate-path-6.0.0.tgz#55321eb309febbc59c4801d931a72452a681d286"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash-es@^4.17.21, lodash-es@^4.2.0:
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/lodash-es/download/lodash-es-4.17.21.tgz#43e626c46e6591b7750beb2b50117390c609e3ee"
  integrity sha1-Q+YmxG5lkbd1C+srUBFzkMYJ4+4=

lodash.camelcase@^4.3.0:
  version "4.3.0"
  resolved "http://r.npm.sankuai.com/lodash.camelcase/download/lodash.camelcase-4.3.0.tgz#b28aa6288a2b9fc651035c7711f65ab6190331a6"
  integrity sha1-soqmKIorn8ZRA1x3EfZathkDMaY=

lodash.clonedeep@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.clonedeep/download/lodash.clonedeep-4.5.0.tgz#e23f3f9c4f8fbdde872529c1071857a086e5ccef"
  integrity sha1-4j8/nE+Pvd6HJSnBBxhXoIblzO8=

lodash.debounce@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/lodash.debounce/download/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.find@^4.6.0:
  version "4.6.0"
  resolved "http://r.npm.sankuai.com/lodash.find/download/lodash.find-4.6.0.tgz#cb0704d47ab71789ffa0de8b97dd926fb88b13b1"
  integrity sha1-ywcE1Hq3F4n/oN6Ll92Sb7iLE7E=

lodash.foreach@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.foreach/download/lodash.foreach-4.5.0.tgz#1a6a35eace401280c7f06dddec35165ab27e3e53"
  integrity sha1-Gmo16s5AEoDH8G3d7DUWWrJ+PlM=

lodash.isequal@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.isequal/download/lodash.isequal-4.5.0.tgz#415c4478f2bcc30120c22ce10ed3226f7d3e18e0"
  integrity sha1-QVxEePK8wwEgwizhDtMib30+GOA=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "http://r.npm.sankuai.com/lodash.merge/download/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.sortby@^4.7.0:
  version "4.7.0"
  resolved "http://r.npm.sankuai.com/lodash.sortby/download/lodash.sortby-4.7.0.tgz#edd14c824e2cc9c1e0b0a1b42bb5210516a42438"
  integrity sha1-7dFMgk4sycHgsKG0K7UhBRakJDg=

lodash.throttle@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/lodash.throttle/download/lodash.throttle-4.1.1.tgz#c23e91b710242ac70c37f1e1cda9274cc39bf2f4"
  integrity sha1-wj6RtxAkKscMN/HhzaknTMOb8vQ=

lodash.toarray@^4.4.0:
  version "4.4.0"
  resolved "http://r.npm.sankuai.com/lodash.toarray/download/lodash.toarray-4.4.0.tgz#24c4bfcd6b2fba38bfd0594db1179d8e9b656561"
  integrity sha1-JMS/zWsvuji/0FlNsRedjptlZWE=

lodash.uniq@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.uniq/download/lodash.uniq-4.5.0.tgz#d0225373aeb652adc1bc82e4945339a842754773"
  integrity sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=

lodash.uniqwith@^4.5.0:
  version "4.5.0"
  resolved "http://r.npm.sankuai.com/lodash.uniqwith/download/lodash.uniqwith-4.5.0.tgz#7a0cbf65f43b5928625a9d4d0dc54b18cadc7ef3"
  integrity sha1-egy/ZfQ7WShiWp1NDcVLGMrcfvM=

lodash@^4, lodash@^4.0.1, lodash@^4.17.11, lodash@^4.17.12, lodash@^4.17.15, lodash@^4.17.21, lodash@^4.17.4, lodash@^4.2.0:
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

log-symbols@^4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/log-symbols/download/log-symbols-4.1.0.tgz#3fbdbb95b4683ac9fc785111e792e558d4abd503"
  integrity sha1-P727lbRoOsn8eFER55LlWNSr1QM=
  dependencies:
    chalk "^4.1.0"
    is-unicode-supported "^0.1.0"

longest@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/longest/download/longest-1.0.1.tgz#30a0b2da38f73770e8294a0d22e6625ed77d0097"
  integrity sha1-MKCy2jj3N3DoKUoNIuZiXtd9AJc=

loose-envify@^1.0.0, loose-envify@^1.1.0, loose-envify@^1.2.0, loose-envify@^1.3.1, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lowercase-keys@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/lowercase-keys/download/lowercase-keys-2.0.0.tgz#2603e78b7b4b0006cbca2fbcc8a3202558ac9479"
  integrity sha1-JgPni3tLAAbLyi+8yKMgJVislHk=

lru-cache@^10.2.0:
  version "10.4.3"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-10.4.3.tgz#410fc8a17b70e598013df257c2446b7f3383f119"
  integrity sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/lru-cache/download/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lucide-react@^0.417.0:
  version "0.417.0"
  resolved "http://r.npm.sankuai.com/lucide-react/download/lucide-react-0.417.0.tgz#caa24fdbeb33d62abda2fd9e64337fcc85224777"
  integrity sha1-yqJP2+sz1iq9ov2eZDN/zIUiR3c=

magic-bytes.js@^1.0.14:
  version "1.10.0"
  resolved "http://r.npm.sankuai.com/magic-bytes.js/download/magic-bytes.js-1.10.0.tgz#c41cf4bc2f802992b05e64962411c9dd44fdef92"
  integrity sha1-xBz0vC+AKZKwXmSWJBHJ3UT975I=

magic-string@^0.27.0:
  version "0.27.0"
  resolved "http://r.npm.sankuai.com/magic-string/download/magic-string-0.27.0.tgz#e4a3413b4bab6d98d2becffd48b4a257effdbbf3"
  integrity sha1-5KNBO0urbZjSvs/9SLSiV+/9u/M=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.13"

make-dir@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/make-dir/download/make-dir-2.1.0.tgz#5f0310e18b8be898cc07009295a30ae41e91e6f5"
  integrity sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=
  dependencies:
    pify "^4.0.1"
    semver "^5.6.0"

markdown-it@^14.1.0:
  version "14.1.0"
  resolved "http://r.npm.sankuai.com/markdown-it/download/markdown-it-14.1.0.tgz#3c3c5992883c633db4714ccb4d7b5935d98b7d45"
  integrity sha1-PDxZkog8Yz20cUzLTXtZNdmLfUU=
  dependencies:
    argparse "^2.0.1"
    entities "^4.4.0"
    linkify-it "^5.0.0"
    mdurl "^2.0.0"
    punycode.js "^2.3.1"
    uc.micro "^2.1.0"

math-intrinsics@^1.0.0, math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz#a0dd74be81e2aa5c2f27e65ce283605ee4e2b7f9"
  integrity sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=

mdurl@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/mdurl/download/mdurl-2.0.0.tgz#80676ec0433025dd3e17ee983d0fe8de5a2237e0"
  integrity sha1-gGduwEMwJd0+F+6YPQ/o3loiN+A=

"memoize-one@>=3.1.1 <6", memoize-one@^5.1.1:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/memoize-one/download/memoize-one-5.2.1.tgz#8337aa3c4335581839ec01c3d594090cebe8f00e"
  integrity sha1-gzeqPEM1WBg57AHD1ZQJDOvo8A4=

memory-cache@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/memory-cache/download/memory-cache-0.2.0.tgz#7890b01d52c00c8ebc9d533e1f8eb17e3034871a"
  integrity sha1-eJCwHVLADI68nVM+H46xfjA0hxo=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/merge-stream/download/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/merge2/download/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

micromatch@^4.0.4, micromatch@^4.0.5, micromatch@^4.0.8:
  version "4.0.8"
  resolved "http://r.npm.sankuai.com/micromatch/download/micromatch-4.0.8.tgz#d66fa18f3a47076789320b9b1af32bd86d9fa202"
  integrity sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-match@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/mime-match/download/mime-match-1.0.2.tgz#3f87c31e9af1a5fd485fb9db134428b23bbb7ba8"
  integrity sha1-P4fDHprxpf1IX7nbE0Qosju7e6g=
  dependencies:
    wildcard "^1.1.0"

mime-types@^2.1.12, mime-types@~2.1.19:
  version "2.1.35"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mime@^1.4.1:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/mime/download/mime-1.6.0.tgz#32cd9e5c64553bd58d19a568af452acff04981b1"
  integrity sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=

mimic-fn@^1.0.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-1.2.0.tgz#820c86a39334640e99516928bd03fca88057d022"
  integrity sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/mimic-fn/download/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mimic-response@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/mimic-response/download/mimic-response-1.0.1.tgz#4923538878eef42063cb8a3e3b0798781487ab1b"
  integrity sha1-SSNTiHju9CBjy4o+OweYeBSHqxs=

mimic-response@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/mimic-response/download/mimic-response-3.1.0.tgz#2d1d59af9c1b129815accc2c46a022a5ce1fa3c9"
  integrity sha1-LR1Zr5wbEpgVrMwsRqAipc4fo8k=

minimatch@^3.0.4, minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^5.0.1:
  version "5.1.6"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-5.1.6.tgz#1cfcb8cf5522ea69952cd2af95ae09477f122a96"
  integrity sha1-HPy4z1Ui6mmVLNKvla4JR38SKpY=
  dependencies:
    brace-expansion "^2.0.1"

minimatch@^9.0.4:
  version "9.0.5"
  resolved "http://r.npm.sankuai.com/minimatch/download/minimatch-9.0.5.tgz#d74f9dd6b57d83d8e98cfb82133b03978bc929e5"
  integrity sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=
  dependencies:
    brace-expansion "^2.0.1"

minimist@^1.2.0, minimist@^1.2.5, minimist@^1.2.6, minimist@~1.2.8:
  version "1.2.8"
  resolved "http://r.npm.sankuai.com/minimist/download/minimist-1.2.8.tgz#c1a464e7693302e082a075cee0c057741ac4772c"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.1.2:
  version "7.1.2"
  resolved "http://r.npm.sankuai.com/minipass/download/minipass-7.1.2.tgz#93a9626ce5e5e66bd4db86849e7515e92340a707"
  integrity sha1-k6libOXl5mvU24aEnnUV6SNApwc=

mkdirp@^0.5.1:
  version "0.5.6"
  resolved "http://r.npm.sankuai.com/mkdirp/download/mkdirp-0.5.6.tgz#7def03d2432dcae4ba1d611445c48396062255f6"
  integrity sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=
  dependencies:
    minimist "^1.2.6"

mock-property@~1.0.0:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/mock-property/download/mock-property-1.0.3.tgz#3e37c50a56609d548cabd56559fde3dd8767b10c"
  integrity sha1-PjfFClZgnVSMq9VlWf3j3YdnsQw=
  dependencies:
    define-data-property "^1.1.1"
    functions-have-names "^1.2.3"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.0"
    hasown "^2.0.0"
    isarray "^2.0.5"

moment@^2.10.6, moment@^2.24.0, moment@^2.29.4:
  version "2.30.1"
  resolved "http://r.npm.sankuai.com/moment/download/moment-2.30.1.tgz#f8c91c07b7a786e30c59926df530b4eac96974ae"
  integrity sha1-+MkcB7enhuMMWZJt9TC06slpdK4=

ms@2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@^2.1.1, ms@^2.1.3:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

"mss-sdk-apis@>= 1.0.0":
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/mss-sdk-apis/download/mss-sdk-apis-1.0.0.tgz#952463c663779a32f7143426e74a65a952b29f97"
  integrity sha1-lSRjxmN3mjL3FDQm50plqVKyn5c=

mss-sdk@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/mss-sdk/download/mss-sdk-2.0.0.tgz#bce555b4737e586cd9d51de9edf15ee40ab73374"
  integrity sha1-vOVVtHN+WGzZ1R3p7fFe5Aq3M3Q=
  dependencies:
    mss-sdk-apis ">= 1.0.0"
    xml2js "0.2.6"
    xmlbuilder "0.4.2"

mute-stream@0.0.7:
  version "0.0.7"
  resolved "http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.7.tgz#3075ce93bc21b8fab43e1bc4da7e8115ed1e7bab"
  integrity sha1-MHXOk7whuPq0PhvE2n6BFe0ee6s=

mute-stream@0.0.8:
  version "0.0.8"
  resolved "http://r.npm.sankuai.com/mute-stream/download/mute-stream-0.0.8.tgz#1630c42b2251ff81e2a283de96a5497ea92e5e0d"
  integrity sha1-FjDEKyJR/4HiooPelqVJfqkuXg0=

mz@^2.7.0:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/mz/download/mz-2.7.0.tgz#95008057a56cafadc2bc63dde7f9ff6955948e32"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

namespace-emitter@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/namespace-emitter/download/namespace-emitter-2.0.1.tgz#978d51361c61313b4e6b8cf6f3853d08dfa2b17c"
  integrity sha1-l41RNhxhMTtOa4z284U9CN+isXw=

nanoid@^3.1.25, nanoid@^3.2.0, nanoid@^3.3.7:
  version "3.3.8"
  resolved "http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.8.tgz#b1be3030bee36aaff18bacb375e5cce521684baf"
  integrity sha1-sb4wML7jaq/xi6yzdeXM5SFoS68=

nanoid@^3.3.8:
  version "3.3.11"
  resolved "http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.11.tgz#4f4f112cefbe303202f2199838128936266d185b"
  integrity sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=

natural-compare-lite@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/natural-compare-lite/download/natural-compare-lite-1.4.0.tgz#17b09581988979fddafe0201e931ba933c96cbb4"
  integrity sha1-F7CVgZiJef3a/gIB6TG6kzyWy7Q=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

needle@^3.1.0:
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/needle/download/needle-3.3.1.tgz#63f75aec580c2e77e209f3f324e2cdf3d29bd049"
  integrity sha1-Y/da7FgMLnfiCfPzJOLN89Kb0Ek=
  dependencies:
    iconv-lite "^0.6.3"
    sax "^1.2.4"

next-tick@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/next-tick/download/next-tick-1.1.0.tgz#1836ee30ad56d67ef281b22bd199f709449b35eb"
  integrity sha1-GDbuMK1W1n7ygbIr0Zn3CUSbNes=

node-addon-api@^1.4.0:
  version "1.7.2"
  resolved "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-1.7.2.tgz#3df30b95720b53c24e59948b49532b662444f54d"
  integrity sha1-PfMLlXILU8JOWZSLSVMrZiRE9U0=

node-addon-api@^3.1.0:
  version "3.2.1"
  resolved "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-3.2.1.tgz#81325e0a2117789c0128dab65e7e38f07ceba161"
  integrity sha1-gTJeCiEXeJwBKNq2Xn448HzroWE=

node-addon-api@^7.0.0:
  version "7.1.1"
  resolved "http://r.npm.sankuai.com/node-addon-api/download/node-addon-api-7.1.1.tgz#1aba6693b0f255258a049d621329329322aad558"
  integrity sha1-Grpmk7DyVSWKBJ1iEykykyKq1Vg=

node-fetch@2:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/node-fetch/download/node-fetch-2.7.0.tgz#d0f0fa6e3e2dc1d27efcd8ad99d550bda94d187d"
  integrity sha1-0PD6bj4twdJ+/NitmdVQvalNGH0=
  dependencies:
    whatwg-url "^5.0.0"

node-int64@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/node-int64/download/node-int64-0.4.0.tgz#87a9065cdb355d3182d8f94ce11188b825c68a3b"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-releases@^2.0.19:
  version "2.0.19"
  resolved "http://r.npm.sankuai.com/node-releases/download/node-releases-2.0.19.tgz#9e445a52950951ec4d177d843af370b411caf314"
  integrity sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=

node-rsa@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/node-rsa/download/node-rsa-1.1.1.tgz#efd9ad382097782f506153398496f79e4464434d"
  integrity sha1-79mtOCCXeC9QYVM5hJb3nkRkQ00=
  dependencies:
    asn1 "^0.2.4"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-url@^6.0.1:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/normalize-url/download/normalize-url-6.1.0.tgz#40d0885b535deffe3f3147bec877d05fe4c5668a"
  integrity sha1-QNCIW1Nd7/4/MUe+yHfQX+TFZoo=

nth-check@^2.0.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/nth-check/download/nth-check-2.1.1.tgz#c9eab428effce36cd6b92c924bdb000ef1f1ed1d"
  integrity sha1-yeq0KO/842zWuSySS9sADvHx7R0=
  dependencies:
    boolbase "^1.0.0"

oauth-sign@~0.9.0:
  version "0.9.0"
  resolved "http://r.npm.sankuai.com/oauth-sign/download/oauth-sign-0.9.0.tgz#47a7b016baa68b5fa0ecf3dee08a85c679ac6455"
  integrity sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=

object-assign@^4.0.1, object-assign@^4.1.1:
  version "4.1.1"
  resolved "http://r.npm.sankuai.com/object-assign/download/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-hash@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/object-hash/download/object-hash-3.0.0.tgz#73f97f753e7baffc0e2cc9d6e079079744ac82e9"
  integrity sha1-c/l/dT57r/wOLMnW4HkHl0Ssguk=

object-inspect@^1.13.3:
  version "1.13.3"
  resolved "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.13.3.tgz#f14c183de51130243d6d18ae149375ff50ea488a"
  integrity sha1-8UwYPeURMCQ9bRiuFJN1/1DqSIo=

object-inspect@~1.12.3:
  version "1.12.3"
  resolved "http://r.npm.sankuai.com/object-inspect/download/object-inspect-1.12.3.tgz#ba62dffd67ee256c8c086dfae69e016cd1f198b9"
  integrity sha1-umLf/WfuJWyMCG365p4BbNHxmLk=

object-is@^1.1.5:
  version "1.1.6"
  resolved "http://r.npm.sankuai.com/object-is/download/object-is-1.1.6.tgz#1a6a53aed2dd8f7e6775ff870bea58545956ab07"
  integrity sha1-GmpTrtLdj35ndf+HC+pYVFlWqwc=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"

object-keys@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/object-keys/download/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object.assign@^4.1.4, object.assign@^4.1.7:
  version "4.1.7"
  resolved "http://r.npm.sankuai.com/object.assign/download/object.assign-4.1.7.tgz#8c14ca1a424c6a561b0bb2a22f66f5049a945d3d"
  integrity sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"
    has-symbols "^1.1.0"
    object-keys "^1.1.1"

object.entries@^1.1.9:
  version "1.1.9"
  resolved "http://r.npm.sankuai.com/object.entries/download/object.entries-1.1.9.tgz#e4770a6a1444afb61bd39f984018b5bede25f8b3"
  integrity sha1-5HcKahREr7Yb05+YQBi1vt4l+LM=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-object-atoms "^1.1.1"

object.fromentries@^2.0.8:
  version "2.0.8"
  resolved "http://r.npm.sankuai.com/object.fromentries/download/object.fromentries-2.0.8.tgz#f7195d8a9b97bd95cbc1999ea939ecd1a2b00c65"
  integrity sha1-9xldipuXvZXLwZmeqTns0aKwDGU=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.values@^1.1.6, object.values@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/object.values/download/object.values-1.2.1.tgz#deed520a50809ff7f75a7cfd4bc64c7a038c6216"
  integrity sha1-3u1SClCAn/f3Wnz9S8ZMegOMYhY=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

once@^1.3.0, once@^1.3.1, once@^1.4.0:
  version "1.4.0"
  resolved "http://r.npm.sankuai.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-2.0.1.tgz#067428230fd67443b2794b22bba528b6867962d4"
  integrity sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=
  dependencies:
    mimic-fn "^1.0.0"

onetime@^5.1.0:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/onetime/download/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

open@^6.1.0:
  version "6.4.0"
  resolved "http://r.npm.sankuai.com/open/download/open-6.4.0.tgz#5c13e96d0dc894686164f18965ecfe889ecfc8a9"
  integrity sha1-XBPpbQ3IlGhhZPGJZez+iJ7PyKk=
  dependencies:
    is-wsl "^1.1.0"

optionator@^0.9.3:
  version "0.9.4"
  resolved "http://r.npm.sankuai.com/optionator/download/optionator-0.9.4.tgz#7ea1c1a5d91d764fb282139c88fe11e182a3a734"
  integrity sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

ora@^5.4.0, ora@^5.4.1:
  version "5.4.1"
  resolved "http://r.npm.sankuai.com/ora/download/ora-5.4.1.tgz#1b2678426af4ac4a509008e5e4ac9e9959db9e18"
  integrity sha1-GyZ4Qmr0rEpQkAjl5KyemVnbnhg=
  dependencies:
    bl "^4.1.0"
    chalk "^4.1.0"
    cli-cursor "^3.1.0"
    cli-spinners "^2.5.0"
    is-interactive "^1.0.0"
    is-unicode-supported "^0.1.0"
    log-symbols "^4.1.0"
    strip-ansi "^6.0.0"
    wcwidth "^1.0.1"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/os-tmpdir/download/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

own-keys@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/own-keys/download/own-keys-1.0.1.tgz#e4006910a2bf913585289676eebd6f390cf51358"
  integrity sha1-5ABpEKK/kTWFKJZ27r1vOQz1E1g=
  dependencies:
    get-intrinsic "^1.2.6"
    object-keys "^1.1.1"
    safe-push-apply "^1.0.0"

owner@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/owner/download/owner-0.1.0.tgz#14d91146b445a110dd44ec23b5ba4af6c3cdbd64"
  integrity sha1-FNkRRrRFoRDdROwjtbpK9sPNvWQ=

p-cancelable@^2.0.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/p-cancelable/download/p-cancelable-2.1.1.tgz#aab7fbd416582fa32a3db49859c122487c5ed2cf"
  integrity sha1-qrf71BZYL6MqPbSYWcEiSHxe0s8=

p-limit@^3.0.2:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/p-limit/download/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/p-locate/download/p-locate-5.0.0.tgz#83c8315c6785005e3bd021839411c9e110e6d834"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

package-json-from-dist@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/package-json-from-dist/download/package-json-from-dist-1.0.1.tgz#4f1471a010827a86f94cfd9b0727e36d267de505"
  integrity sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=

parchment@^1.1.2, parchment@^1.1.4:
  version "1.1.4"
  resolved "http://r.npm.sankuai.com/parchment/download/parchment-1.1.4.tgz#aeded7ab938fe921d4c34bc339ce1168bc2ffde5"
  integrity sha1-rt7Xq5OP6SHUw0vDOc4RaLwv/eU=

parchment@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/parchment/download/parchment-3.0.0.tgz#2e3a4ada454e1206ae76ea7afcb50e9fb517e7d6"
  integrity sha1-LjpK2kVOEgaudup6/LUOn7UX59Y=

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-node-version@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/parse-node-version/download/parse-node-version-1.0.1.tgz#e2b5dbede00e7fa9bc363607f53327e8b073189b"
  integrity sha1-4rXb7eAOf6m8NjYH9TMn6LBzGJs=

parse5-htmlparser2-tree-adapter@^7.0.0:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/parse5-htmlparser2-tree-adapter/download/parse5-htmlparser2-tree-adapter-7.1.0.tgz#b5a806548ed893a43e24ccb42fbb78069311e81b"
  integrity sha1-tagGVI7Yk6Q+JMy0L7t4BpMR6Bs=
  dependencies:
    domhandler "^5.0.3"
    parse5 "^7.0.0"

parse5@^7.0.0:
  version "7.2.1"
  resolved "http://r.npm.sankuai.com/parse5/download/parse5-7.2.1.tgz#8928f55915e6125f430cc44309765bf17556a33a"
  integrity sha1-iSj1WRXmEl9DDMRDCXZb8XVWozo=
  dependencies:
    entities "^4.5.0"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.1.0:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-scurry@^1.11.1:
  version "1.11.1"
  resolved "http://r.npm.sankuai.com/path-scurry/download/path-scurry-1.11.1.tgz#7960a668888594a0720b12a911d1a742ab9f11d2"
  integrity sha1-eWCmaIiFlKByCxKpEdGnQqufEdI=
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-to-regexp@^1.7.0:
  version "1.9.0"
  resolved "http://r.npm.sankuai.com/path-to-regexp/download/path-to-regexp-1.9.0.tgz#5dc0753acbf8521ca2e0f137b4578b917b10cf24"
  integrity sha1-XcB1Osv4Uhyi4PE3tFeLkXsQzyQ=
  dependencies:
    isarray "0.0.1"

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/path-type/download/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pdfast@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/pdfast/download/pdfast-0.2.0.tgz#8cbc556e1bf2522177787c0de2e0d4373ba885c9"
  integrity sha1-jLxVbhvyUiF3eHwN4uDUNzuohck=

performance-now@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/performance-now/download/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picocolors@^1.0.0, picocolors@^1.1.0, picocolors@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/picocolors/download/picocolors-1.1.1.tgz#3d321af3eab939b083c8f929a1d12cda81c26b6b"
  integrity sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

picomatch@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-4.0.2.tgz#77c742931e8f3b8820946c76cd0c1f13730d1dab"
  integrity sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=

pify@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/pify/download/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/pify/download/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pirates@^4.0.1:
  version "4.0.7"
  resolved "http://r.npm.sankuai.com/pirates/download/pirates-4.0.7.tgz#643b4a18c4257c8a65104b73f3049ce9a0a15e22"
  integrity sha1-ZDtKGMQlfIplEEtz8wSc6aChXiI=

possible-typed-array-names@^1.0.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/possible-typed-array-names/download/possible-typed-array-names-1.1.0.tgz#93e3582bc0e5426586d9d07b79ee40fc841de4ae"
  integrity sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=

postcss-import@^15.1.0:
  version "15.1.0"
  resolved "http://r.npm.sankuai.com/postcss-import/download/postcss-import-15.1.0.tgz#41c64ed8cc0e23735a9698b3249ffdbf704adc70"
  integrity sha1-QcZO2MwOI3NalpizJJ/9v3BK3HA=
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-js@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/postcss-js/download/postcss-js-4.0.1.tgz#61598186f3703bab052f1c4f7d805f3991bee9d2"
  integrity sha1-YVmBhvNwO6sFLxxPfYBfOZG+6dI=
  dependencies:
    camelcase-css "^2.0.1"

postcss-load-config@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/postcss-load-config/download/postcss-load-config-4.0.2.tgz#7159dcf626118d33e299f485d6afe4aff7c4a3e3"
  integrity sha1-cVnc9iYRjTPimfSF1q/kr/fEo+M=
  dependencies:
    lilconfig "^3.0.0"
    yaml "^2.3.4"

postcss-load-config@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/postcss-load-config/download/postcss-load-config-6.0.1.tgz#6fd7dcd8ae89badcf1b2d644489cbabf83aa8096"
  integrity sha1-b9fc2K6JutzxstZESJy6v4OqgJY=
  dependencies:
    lilconfig "^3.1.1"

postcss-nested@^6.2.0:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/postcss-nested/download/postcss-nested-6.2.0.tgz#4c2d22ab5f20b9cb61e2c5c5915950784d068131"
  integrity sha1-TC0iq18gucth4sXFkVlQeE0GgTE=
  dependencies:
    postcss-selector-parser "^6.1.1"

postcss-selector-parser@^6.1.1, postcss-selector-parser@^6.1.2:
  version "6.1.2"
  resolved "http://r.npm.sankuai.com/postcss-selector-parser/download/postcss-selector-parser-6.1.2.tgz#27ecb41fb0e3b6ba7a1ec84fff347f734c7929de"
  integrity sha1-J+y0H7Djtrp6HshP/zR/c0x5Kd4=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.0.0:
  version "4.2.0"
  resolved "http://r.npm.sankuai.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz#723c09920836ba6d3e5af019f92bc0971c02e514"
  integrity sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=

postcss@^8.4.27:
  version "8.4.49"
  resolved "http://r.npm.sankuai.com/postcss/download/postcss-8.4.49.tgz#4ea479048ab059ab3ae61d082190fabfd994fe19"
  integrity sha1-TqR5BIqwWas65h0IIZD6v9mU/hk=
  dependencies:
    nanoid "^3.3.7"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

postcss@^8.4.47:
  version "8.5.3"
  resolved "http://r.npm.sankuai.com/postcss/download/postcss-8.5.3.tgz#1463b6f1c7fb16fe258736cba29a2de35237eafb"
  integrity sha1-FGO28cf7Fv4lhzbLopot41I36vs=
  dependencies:
    nanoid "^3.3.8"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

preact@^10.5.13:
  version "10.25.3"
  resolved "http://r.npm.sankuai.com/preact/download/preact-10.25.3.tgz#22dfb072b088dda9a2bc6d4ca41bf46b588d325e"
  integrity sha1-It+wcrCI3amivG1MpBv0a1iNMl4=

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/prelude-ls/download/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/prettier-linter-helpers/download/prettier-linter-helpers-1.0.0.tgz#d23d41fe1375646de2d0104d3454a3008802cf7b"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier@^1.15.2:
  version "1.19.1"
  resolved "http://r.npm.sankuai.com/prettier/download/prettier-1.19.1.tgz#f7d7f5ff8a9cd872a7be4ca142095956a60797cb"
  integrity sha1-99f1/4qc2HKnvkyhQglZVqYHl8s=

prettier@^2.7.1:
  version "2.8.8"
  resolved "http://r.npm.sankuai.com/prettier/download/prettier-2.8.8.tgz#e8c5d7e98a4305ffe3de2e1fc4aca1a71c28b1da"
  integrity sha1-6MXX6YpDBf/j3i4fxKyhpxwosdo=

prismjs@^1.23.0:
  version "1.29.0"
  resolved "http://r.npm.sankuai.com/prismjs/download/prismjs-1.29.0.tgz#f113555a8fa9b57c35e637bba27509dcf802dd12"
  integrity sha1-8RNVWo+ptXw15je7onUJ3PgC3RI=

prop-types@^15.5.10, prop-types@^15.5.8, prop-types@^15.6.1, prop-types@^15.6.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "http://r.npm.sankuai.com/prop-types/download/prop-types-15.8.1.tgz#67d87bf1a694f48435cf332c24af10214a3140b5"
  integrity sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

properties-parser@^0.3.1:
  version "0.3.1"
  resolved "http://r.npm.sankuai.com/properties-parser/download/properties-parser-0.3.1.tgz#1316e9539ffbfd93845e369b211022abd478771a"
  integrity sha1-ExbpU5/7/ZOEXjabIRAiq9R4dxo=
  dependencies:
    string.prototype.codepointat "^0.2.0"

proxy-from-env@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/proxy-from-env/download/proxy-from-env-1.1.0.tgz#e102f16ca355424865755d2c9e8ea4f24d58c3e2"
  integrity sha1-4QLxbKNVQkhldV0sno6k8k1Yw+I=

prr@~1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/prr/download/prr-1.0.1.tgz#d3fc114ba06995a45ec6893f484ceb1d78f5f476"
  integrity sha1-0/wRS6BplaRexok/SEzrHXj19HY=

psl@^1.1.28:
  version "1.15.0"
  resolved "http://r.npm.sankuai.com/psl/download/psl-1.15.0.tgz#bdace31896f1d97cec6a79e8224898ce93d974c6"
  integrity sha1-vazjGJbx2XzsannoIkiYzpPZdMY=
  dependencies:
    punycode "^2.3.1"

pump@^3.0.0:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/pump/download/pump-3.0.2.tgz#836f3edd6bc2ee599256c924ffe0d88573ddcbf8"
  integrity sha1-g28+3WvC7lmSVskk/+DYhXPdy/g=
  dependencies:
    end-of-stream "^1.1.0"
    once "^1.3.1"

punycode.js@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/punycode.js/download/punycode.js-2.3.1.tgz#6b53e56ad75588234e79f4affa90972c7dd8cdb7"
  integrity sha1-a1PlatdViCNOefSv+pCXLH3Yzbc=

punycode@^2.1.0, punycode@^2.1.1, punycode@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

q@^1.5.0:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/q/download/q-1.5.1.tgz#7e32f75b41381291d04611f1bf14109ac00651d7"
  integrity sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=

qs@^2.4.1:
  version "2.4.2"
  resolved "http://r.npm.sankuai.com/qs/download/qs-2.4.2.tgz#f7ce788e5777df0b5010da7f7c4e73ba32470f5a"
  integrity sha1-9854jld33wtQENp/fE5zujJHD1o=

qs@^6.11.0:
  version "6.13.1"
  resolved "http://r.npm.sankuai.com/qs/download/qs-6.13.1.tgz#3ce5fc72bd3a8171b85c99b93c65dd20b7d1b16e"
  integrity sha1-POX8cr06gXG4XJm5PGXdILfRsW4=
  dependencies:
    side-channel "^1.0.6"

qs@~6.5.2:
  version "6.5.3"
  resolved "http://r.npm.sankuai.com/qs/download/qs-6.5.3.tgz#3aeeffc91967ef6e35c0e488ef46fb296ab76aad"
  integrity sha1-Ou7/yRln7241wOSI70b7KWq3aq0=

query-string@8:
  version "8.2.0"
  resolved "http://r.npm.sankuai.com/query-string/download/query-string-8.2.0.tgz#f0b0ef6caa85f525dbdb745a67d3f8c08d71cc6b"
  integrity sha1-8LDvbKqF9SXb23RaZ9P4wI1xzGs=
  dependencies:
    decode-uri-component "^0.4.1"
    filter-obj "^5.1.0"
    split-on-first "^3.0.0"

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/queue-microtask/download/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

quick-lru@^5.1.1:
  version "5.1.1"
  resolved "http://r.npm.sankuai.com/quick-lru/download/quick-lru-5.1.1.tgz#366493e6b3e42a3a6885e2e99d18f80fb7a8c932"
  integrity sha1-NmST5rPkKjpoheLpnRj4D7eoyTI=

quickselect@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/quickselect/download/quickselect-2.0.0.tgz#f19680a486a5eefb581303e023e98faaf25dd018"
  integrity sha1-8ZaApIal7vtYEwPgI+mPqvJd0Bg=

quill-delta@^3.6.2:
  version "3.6.3"
  resolved "http://r.npm.sankuai.com/quill-delta/download/quill-delta-3.6.3.tgz#b19fd2b89412301c60e1ff213d8d860eac0f1032"
  integrity sha1-sZ/SuJQSMBxg4f8hPY2GDqwPEDI=
  dependencies:
    deep-equal "^1.0.1"
    extend "^3.0.2"
    fast-diff "1.1.2"

quill-delta@^5.1.0:
  version "5.1.0"
  resolved "http://r.npm.sankuai.com/quill-delta/download/quill-delta-5.1.0.tgz#1c4bc08f7c8e5cc4bdc88a15a1a70c1cc72d2b48"
  integrity sha1-HEvAj3yOXMS9yIoVoacMHMctK0g=
  dependencies:
    fast-diff "^1.3.0"
    lodash.clonedeep "^4.5.0"
    lodash.isequal "^4.5.0"

quill-image-resize-module-react@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/quill-image-resize-module-react/download/quill-image-resize-module-react-3.0.0.tgz#dec32cecd175fcdc52bfd184206174fdd111a864"
  integrity sha1-3sMs7NF1/NxSv9GEIGF0/dERqGQ=
  dependencies:
    lodash "^4.17.4"
    quill "^1.2.2"
    raw-loader "^0.5.1"

quill-mention@latest:
  version "6.0.2"
  resolved "http://r.npm.sankuai.com/quill-mention/download/quill-mention-6.0.2.tgz#fa6b5ead033d63372cd9bc004d26ff200db23cfa"
  integrity sha1-+mterQM9Yzcs2bwATSb/IA2yPPo=
  dependencies:
    quill "^2.0.2"

quill@^1.2.2, quill@^1.3.7:
  version "1.3.7"
  resolved "http://r.npm.sankuai.com/quill/download/quill-1.3.7.tgz#da5b2f3a2c470e932340cdbf3668c9f21f9286e8"
  integrity sha1-2lsvOixHDpMjQM2/NmjJ8h+Shug=
  dependencies:
    clone "^2.1.1"
    deep-equal "^1.0.1"
    eventemitter3 "^2.0.3"
    extend "^3.0.2"
    parchment "^1.1.4"
    quill-delta "^3.6.2"

quill@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/quill/download/quill-2.0.3.tgz#752765a31d5a535cdc5717dc49d4e50099365eb1"
  integrity sha1-dSdlox1aU1zcVxfcSdTlAJk2XrE=
  dependencies:
    eventemitter3 "^5.0.1"
    lodash-es "^4.17.21"
    parchment "^3.0.0"
    quill-delta "^5.1.0"

raf-schd@^4.0.3:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/raf-schd/download/raf-schd-4.0.3.tgz#5d6c34ef46f8b2a0e880a8fcdb743efc5bfdbc1a"
  integrity sha1-XWw070b4sqDogKj823Q+/Fv9vBo=

randombytes@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/randombytes/download/randombytes-2.1.0.tgz#df6f84372f0270dc65cdf6291349ab7a473d4f2a"
  integrity sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=
  dependencies:
    safe-buffer "^5.1.0"

raw-loader@^0.5.1:
  version "0.5.1"
  resolved "http://r.npm.sankuai.com/raw-loader/download/raw-loader-0.5.1.tgz#0c3d0beaed8a01c966d9787bf778281252a979aa"
  integrity sha1-DD0L6u2KAclm2Xh793goElKpeao=

rbush@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/rbush/download/rbush-3.0.1.tgz#5fafa8a79b3b9afdfe5008403a720cc1de882ecf"
  integrity sha1-X6+op5s7mv3+UAhAOnIMwd6ILs8=
  dependencies:
    quickselect "^2.0.0"

rc-cascader@~3.30.0:
  version "3.30.0"
  resolved "http://r.npm.sankuai.com/rc-cascader/download/rc-cascader-3.30.0.tgz#da3e35cadcc00c58c62a6757eca6c7147ff94ea8"
  integrity sha1-2j41ytzADFjGKmdX7KbHFH/5Tqg=
  dependencies:
    "@babel/runtime" "^7.25.7"
    classnames "^2.3.1"
    rc-select "~14.16.2"
    rc-tree "~5.10.1"
    rc-util "^5.43.0"

rc-checkbox@~3.3.0:
  version "3.3.0"
  resolved "http://r.npm.sankuai.com/rc-checkbox/download/rc-checkbox-3.3.0.tgz#0ffcb65ab78c7d2fcd1a0d6554af36786516bd02"
  integrity sha1-D/y2WreMfS/NGg1lVK82eGUWvQI=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.3.2"
    rc-util "^5.25.2"

rc-collapse@~3.9.0:
  version "3.9.0"
  resolved "http://r.npm.sankuai.com/rc-collapse/download/rc-collapse-3.9.0.tgz#972404ce7724e1c9d1d2476543e1175404a36806"
  integrity sha1-lyQEznck4cnR0kdlQ+EXVASjaAY=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.3.4"
    rc-util "^5.27.0"

rc-dialog@~9.6.0:
  version "9.6.0"
  resolved "http://r.npm.sankuai.com/rc-dialog/download/rc-dialog-9.6.0.tgz#dc7a255c6ad1cb56021c3a61c7de86ee88c7c371"
  integrity sha1-3HolXGrRy1YCHDphx96G7ojHw3E=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/portal" "^1.0.0-8"
    classnames "^2.2.6"
    rc-motion "^2.3.0"
    rc-util "^5.21.0"

rc-drawer@~7.2.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/rc-drawer/download/rc-drawer-7.2.0.tgz#8d7de2f1fd52f3ac5a25f54afbb8ac14c62e5663"
  integrity sha1-jX3i8f1S86xaJfVK+7isFMYuVmM=
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@rc-component/portal" "^1.1.1"
    classnames "^2.2.6"
    rc-motion "^2.6.1"
    rc-util "^5.38.1"

rc-dropdown@~4.2.0, rc-dropdown@~4.2.1:
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/rc-dropdown/download/rc-dropdown-4.2.1.tgz#44729eb2a4272e0353d31ac060da21e606accb1c"
  integrity sha1-RHKesqQnLgNT0xrAYNoh5gasyxw=
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.2.6"
    rc-util "^5.44.1"

rc-field-form@~1.38.2:
  version "1.38.2"
  resolved "http://r.npm.sankuai.com/rc-field-form/download/rc-field-form-1.38.2.tgz#1eafac98eb84d47dc3b55de98ed50751d9852dd2"
  integrity sha1-Hq+smOuE1H3DtV3pjtUHUdmFLdI=
  dependencies:
    "@babel/runtime" "^7.18.0"
    async-validator "^4.1.0"
    rc-util "^5.32.2"

rc-field-form@~2.7.0:
  version "2.7.0"
  resolved "http://r.npm.sankuai.com/rc-field-form/download/rc-field-form-2.7.0.tgz#22413e793f35bfc1f35b0ec462774d7277f5a399"
  integrity sha1-IkE+eT81v8HzWw7EYndNcnf1o5k=
  dependencies:
    "@babel/runtime" "^7.18.0"
    "@rc-component/async-validator" "^5.0.3"
    rc-util "^5.32.2"

rc-image@~7.11.0:
  version "7.11.0"
  resolved "http://r.npm.sankuai.com/rc-image/download/rc-image-7.11.0.tgz#18c77ea557a6fdbe26856c688a9aace1505c0e77"
  integrity sha1-GMd+pVem/b4mhWxoipqs4VBcDnc=
  dependencies:
    "@babel/runtime" "^7.11.2"
    "@rc-component/portal" "^1.0.2"
    classnames "^2.2.6"
    rc-dialog "~9.6.0"
    rc-motion "^2.6.2"
    rc-util "^5.34.1"

rc-input-number@~9.3.0:
  version "9.3.0"
  resolved "http://r.npm.sankuai.com/rc-input-number/download/rc-input-number-9.3.0.tgz#3403c1071fcb9dbf91073faddd80ea83bd3974df"
  integrity sha1-NAPBBx/Lnb+RBz+t3YDqg705dN8=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/mini-decimal" "^1.0.1"
    classnames "^2.2.5"
    rc-input "~1.6.0"
    rc-util "^5.40.1"

rc-input@~1.6.0, rc-input@~1.6.4:
  version "1.6.4"
  resolved "http://r.npm.sankuai.com/rc-input/download/rc-input-1.6.4.tgz#08d91460f6b75b3fa5294154e89775784c233129"
  integrity sha1-CNkUYPa3Wz+lKUFU6Jd1eEwjMSk=
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-util "^5.18.1"

rc-mentions@~2.17.0:
  version "2.17.0"
  resolved "http://r.npm.sankuai.com/rc-mentions/download/rc-mentions-2.17.0.tgz#d16dd5c8e4db87862c1007f7195b0aea9247cdcd"
  integrity sha1-0W3VyOTbh4YsEAf3GVsK6pJHzc0=
  dependencies:
    "@babel/runtime" "^7.22.5"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.2.6"
    rc-input "~1.6.0"
    rc-menu "~9.16.0"
    rc-textarea "~1.8.0"
    rc-util "^5.34.1"

rc-menu@~9.16.0:
  version "9.16.0"
  resolved "http://r.npm.sankuai.com/rc-menu/download/rc-menu-9.16.0.tgz#53647f60f513bfa09bfc1accbd96a8df24900121"
  integrity sha1-U2R/YPUTv6Cb/BrMvZao3ySQASE=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/trigger" "^2.0.0"
    classnames "2.x"
    rc-motion "^2.4.3"
    rc-overflow "^1.3.1"
    rc-util "^5.27.0"

rc-motion@^2.0.0, rc-motion@^2.0.1, rc-motion@^2.3.0, rc-motion@^2.3.4, rc-motion@^2.4.3, rc-motion@^2.4.4, rc-motion@^2.6.1, rc-motion@^2.6.2, rc-motion@^2.9.0, rc-motion@^2.9.2, rc-motion@^2.9.5:
  version "2.9.5"
  resolved "http://r.npm.sankuai.com/rc-motion/download/rc-motion-2.9.5.tgz#12c6ead4fd355f94f00de9bb4f15df576d677e0c"
  integrity sha1-Esbq1P01X5TwDem7TxXfV21nfgw=
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-util "^5.44.0"

rc-notification@~5.6.2:
  version "5.6.2"
  resolved "http://r.npm.sankuai.com/rc-notification/download/rc-notification-5.6.2.tgz#8525b32d49dd96ec974acae61d1d1eabde61463a"
  integrity sha1-hSWzLUndluyXSsrmHR0eq95hRjo=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.9.0"
    rc-util "^5.20.1"

rc-overflow@^1.3.1, rc-overflow@^1.3.2:
  version "1.3.2"
  resolved "http://r.npm.sankuai.com/rc-overflow/download/rc-overflow-1.3.2.tgz#72ee49e85a1308d8d4e3bd53285dc1f3e0bcce2c"
  integrity sha1-cu5J6FoTCNjU471TKF3B8+C8ziw=
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-resize-observer "^1.0.0"
    rc-util "^5.37.0"

rc-pagination@~5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/rc-pagination/download/rc-pagination-5.0.0.tgz#7633e1f0ff372ad78c03e86bcef78b660374d196"
  integrity sha1-djPh8P83KteMA+hrzveLZgN00ZY=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.3.2"
    rc-util "^5.38.0"

rc-picker@~4.8.3:
  version "4.8.3"
  resolved "http://r.npm.sankuai.com/rc-picker/download/rc-picker-4.8.3.tgz#06cffd5a2201fc8d274e12f7ee32ea8ba6f3f60f"
  integrity sha1-Bs/9WiIB/I0nThL37jLqi6bz9g8=
  dependencies:
    "@babel/runtime" "^7.24.7"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.2.1"
    rc-overflow "^1.3.2"
    rc-resize-observer "^1.4.0"
    rc-util "^5.43.0"

rc-progress@^3.5.1:
  version "3.5.1"
  resolved "http://r.npm.sankuai.com/rc-progress/download/rc-progress-3.5.1.tgz#a3cdfd2fe04eb5c3d43fa1c69e7dd70c73b102ae"
  integrity sha1-o839L+BOtcPUP6HGnn3XDHOxAq4=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-util "^5.16.1"

rc-progress@~3.2.1:
  version "3.2.4"
  resolved "http://r.npm.sankuai.com/rc-progress/download/rc-progress-3.2.4.tgz#4036acdae2566438545bc4df2203248babaf7549"
  integrity sha1-QDas2uJWZDhUW8TfIgMki6uvdUk=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-util "^5.16.1"

rc-progress@~4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/rc-progress/download/rc-progress-4.0.0.tgz#5382147d9add33d3a5fbd264001373df6440e126"
  integrity sha1-U4IUfZrdM9Ol+9JkABNz32RA4SY=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.6"
    rc-util "^5.16.1"

rc-rate@~2.13.0:
  version "2.13.0"
  resolved "http://r.npm.sankuai.com/rc-rate/download/rc-rate-2.13.0.tgz#642f591ccf55c3a5d84d8d212caf1f7951d203a8"
  integrity sha1-ZC9ZHM9Vw6XYTY0hLK8feVHSA6g=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.0.1"

rc-resize-observer@^1.0.0, rc-resize-observer@^1.1.0, rc-resize-observer@^1.3.1, rc-resize-observer@^1.4.0, rc-resize-observer@^1.4.3:
  version "1.4.3"
  resolved "http://r.npm.sankuai.com/rc-resize-observer/download/rc-resize-observer-1.4.3.tgz#4fd41fa561ba51362b5155a07c35d7c89a1ea569"
  integrity sha1-T9QfpWG6UTYrUVWgfDXXyJoepWk=
  dependencies:
    "@babel/runtime" "^7.20.7"
    classnames "^2.2.1"
    rc-util "^5.44.1"
    resize-observer-polyfill "^1.5.1"

rc-segmented@~2.5.0:
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/rc-segmented/download/rc-segmented-2.5.0.tgz#3b5423adf57459345c77c39c7581fde786a16c11"
  integrity sha1-O1QjrfV0WTRcd8OcdYH954ahbBE=
  dependencies:
    "@babel/runtime" "^7.11.1"
    classnames "^2.2.1"
    rc-motion "^2.4.4"
    rc-util "^5.17.0"

rc-select@~14.16.2, rc-select@~14.16.4:
  version "14.16.4"
  resolved "http://r.npm.sankuai.com/rc-select/download/rc-select-14.16.4.tgz#a98840c4cfb96e263c750e59334ea0a2862e04fc"
  integrity sha1-qYhAxM+5biY8dQ5ZM06gooYuBPw=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/trigger" "^2.1.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-overflow "^1.3.1"
    rc-util "^5.16.1"
    rc-virtual-list "^3.5.2"

rc-slider@~11.1.7:
  version "11.1.7"
  resolved "http://r.npm.sankuai.com/rc-slider/download/rc-slider-11.1.7.tgz#3de333b1ec84d53a7bda2f816bb4779423628f09"
  integrity sha1-PeMzseyE1Tp72i+Ba7R3lCNijwk=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.5"
    rc-util "^5.36.0"

rc-steps@~6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/rc-steps/download/rc-steps-6.0.1.tgz#c2136cd0087733f6d509209a84a5c80dc29a274d"
  integrity sha1-whNs0Ah3M/bVCSCahKXIDcKaJ00=
  dependencies:
    "@babel/runtime" "^7.16.7"
    classnames "^2.2.3"
    rc-util "^5.16.1"

rc-switch@~4.1.0:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/rc-switch/download/rc-switch-4.1.0.tgz#f37d81b4e0c5afd1274fd85367b17306bf25e7d7"
  integrity sha1-832BtODFr9EnT9hTZ7FzBr8l59c=
  dependencies:
    "@babel/runtime" "^7.21.0"
    classnames "^2.2.1"
    rc-util "^5.30.0"

rc-table@^7.48.1:
  version "7.50.5"
  resolved "http://r.npm.sankuai.com/rc-table/download/rc-table-7.50.5.tgz#d0649d2fd902ca90dc20924c27a2e38523ce3625"
  integrity sha1-0GSdL9kCypDcIJJMJ6LjhSPONiU=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/context" "^1.4.0"
    classnames "^2.2.5"
    rc-resize-observer "^1.1.0"
    rc-util "^5.44.3"
    rc-virtual-list "^3.14.2"

rc-table@~7.49.0:
  version "7.49.0"
  resolved "http://r.npm.sankuai.com/rc-table/download/rc-table-7.49.0.tgz#f5a4880d9527d2c9e42f5f721b5423e7a1ca475b"
  integrity sha1-9aSIDZUn0snkL19yG1Qj56HKR1s=
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/context" "^1.4.0"
    classnames "^2.2.5"
    rc-resize-observer "^1.1.0"
    rc-util "^5.41.0"
    rc-virtual-list "^3.14.2"

rc-tabs@~15.4.0:
  version "15.4.0"
  resolved "http://r.npm.sankuai.com/rc-tabs/download/rc-tabs-15.4.0.tgz#a829cabcb33f93525b548010f5bbf91dee7ac1d6"
  integrity sha1-qCnKvLM/k1JbVIAQ9bv5He56wdY=
  dependencies:
    "@babel/runtime" "^7.11.2"
    classnames "2.x"
    rc-dropdown "~4.2.0"
    rc-menu "~9.16.0"
    rc-motion "^2.6.2"
    rc-resize-observer "^1.0.0"
    rc-util "^5.34.1"

rc-textarea@~1.8.0, rc-textarea@~1.8.2:
  version "1.8.2"
  resolved "http://r.npm.sankuai.com/rc-textarea/download/rc-textarea-1.8.2.tgz#57a6847304551c1883fc3fb0c5076d587f70bf7f"
  integrity sha1-V6aEcwRVHBiD/D+wxQdtWH9wv38=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "^2.2.1"
    rc-input "~1.6.0"
    rc-resize-observer "^1.0.0"
    rc-util "^5.27.0"

rc-tooltip@~6.2.1:
  version "6.2.1"
  resolved "http://r.npm.sankuai.com/rc-tooltip/download/rc-tooltip-6.2.1.tgz#9a8f0335c86443a0c20c2557933205f645a381b7"
  integrity sha1-mo8DNchkQ6DCDCVXkzIF9kWjgbc=
  dependencies:
    "@babel/runtime" "^7.11.2"
    "@rc-component/trigger" "^2.0.0"
    classnames "^2.3.1"

rc-tree-select@~5.24.5:
  version "5.24.5"
  resolved "http://r.npm.sankuai.com/rc-tree-select/download/rc-tree-select-5.24.5.tgz#a1bf85c7d5e4979880cfb0748bb6bab937ed3483"
  integrity sha1-ob+Fx9Xkl5iAz7B0i7a6uTftNIM=
  dependencies:
    "@babel/runtime" "^7.25.7"
    classnames "2.x"
    rc-select "~14.16.2"
    rc-tree "~5.10.1"
    rc-util "^5.43.0"

rc-tree@~5.10.1:
  version "5.10.1"
  resolved "http://r.npm.sankuai.com/rc-tree/download/rc-tree-5.10.1.tgz#8807614c54aaa39edc05392f0f5982b609d95255"
  integrity sha1-iAdhTFSqo57cBTkvD1mCtgnZUlU=
  dependencies:
    "@babel/runtime" "^7.10.1"
    classnames "2.x"
    rc-motion "^2.0.1"
    rc-util "^5.16.1"
    rc-virtual-list "^3.5.1"

rc-upload@~4.8.1:
  version "4.8.1"
  resolved "http://r.npm.sankuai.com/rc-upload/download/rc-upload-4.8.1.tgz#ac55f2bc101b95b52a6e47f3c18f0f55b54e16d2"
  integrity sha1-rFXyvBAblbUqbkfzwY8PVbVOFtI=
  dependencies:
    "@babel/runtime" "^7.18.3"
    classnames "^2.2.5"
    rc-util "^5.2.0"

rc-util@^5.0.1, rc-util@^5.16.1, rc-util@^5.17.0, rc-util@^5.18.1, rc-util@^5.2.0, rc-util@^5.20.1, rc-util@^5.21.0, rc-util@^5.24.4, rc-util@^5.25.2, rc-util@^5.27.0, rc-util@^5.30.0, rc-util@^5.31.1, rc-util@^5.32.2, rc-util@^5.34.1, rc-util@^5.35.0, rc-util@^5.36.0, rc-util@^5.37.0, rc-util@^5.38.0, rc-util@^5.38.1, rc-util@^5.40.1, rc-util@^5.41.0, rc-util@^5.43.0, rc-util@^5.44.0, rc-util@^5.44.1, rc-util@^5.44.2:
  version "5.44.3"
  resolved "http://r.npm.sankuai.com/rc-util/download/rc-util-5.44.3.tgz#9eca5039906446113c4032859f88c15234547961"
  integrity sha1-nspQOZBkRhE8QDKFn4jBUjRUeWE=
  dependencies:
    "@babel/runtime" "^7.18.3"
    react-is "^18.2.0"

rc-util@^5.44.3:
  version "5.44.4"
  resolved "http://r.npm.sankuai.com/rc-util/download/rc-util-5.44.4.tgz#89ee9037683cca01cd60f1a6bbda761457dd6ba5"
  integrity sha1-ie6QN2g8ygHNYPGmu9p2FFfda6U=
  dependencies:
    "@babel/runtime" "^7.18.3"
    react-is "^18.2.0"

rc-virtual-list@^3.14.2, rc-virtual-list@^3.14.8, rc-virtual-list@^3.5.1, rc-virtual-list@^3.5.2:
  version "3.16.1"
  resolved "http://r.npm.sankuai.com/rc-virtual-list/download/rc-virtual-list-3.16.1.tgz#073d75cc0295497cdd9a35d6f5d1b71b4f35233e"
  integrity sha1-Bz11zAKVSXzdmjXW9dG3G081Iz4=
  dependencies:
    "@babel/runtime" "^7.20.0"
    classnames "^2.2.6"
    rc-resize-observer "^1.0.0"
    rc-util "^5.36.0"

react-click-outside@^3.0.1:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/react-click-outside/download/react-click-outside-3.0.1.tgz#6e77e84d2f17afaaac26dbad743cbbf909f5e24c"
  integrity sha1-bnfoTS8Xr6qsJtutdDy7+Qn14kw=
  dependencies:
    hoist-non-react-statics "^2.1.1"

react-dom@^18.2.0:
  version "18.3.1"
  resolved "http://r.npm.sankuai.com/react-dom/download/react-dom-18.3.1.tgz#c2265d79511b57d479b3dd3fdfa51536494c5cb4"
  integrity sha1-wiZdeVEbV9R5s90/36UVNklMXLQ=
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.2"

react-drag-listview@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/react-drag-listview/download/react-drag-listview-2.0.0.tgz#b8e7ec5f980ecbbf3abb85f50db0b03cd764edbf"
  integrity sha1-uOfsX5gOy786u4X1DbCwPNdk7b8=
  dependencies:
    babel-runtime "^6.26.0"
    prop-types "^15.5.8"

react-fast-compare@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/react-fast-compare/download/react-fast-compare-2.0.4.tgz#e84b4d455b0fec113e0402c329352715196f81f9"
  integrity sha1-6EtNRVsP7BE+BALDKTUnFRlvgfk=

react-fast-compare@^3.0.1, react-fast-compare@^3.2.2:
  version "3.2.2"
  resolved "http://r.npm.sankuai.com/react-fast-compare/download/react-fast-compare-3.2.2.tgz#929a97a532304ce9fee4bcae44234f1ce2c21d49"
  integrity sha1-kpqXpTIwTOn+5LyuRCNPHOLCHUk=

react-is@^16.13.1, react-is@^16.7.0:
  version "16.13.1"
  resolved "http://r.npm.sankuai.com/react-is/download/react-is-16.13.1.tgz#789729a4dc36de2999dc156dd6c1d9c18cea56a4"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-is@^18.2.0:
  version "18.3.1"
  resolved "http://r.npm.sankuai.com/react-is/download/react-is-18.3.1.tgz#e83557dc12eae63a99e003a46388b1dcbb44db7e"
  integrity sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=

react-lifecycles-compat@^3.0.4:
  version "3.0.4"
  resolved "http://r.npm.sankuai.com/react-lifecycles-compat/download/react-lifecycles-compat-3.0.4.tgz#4f1a273afdfc8f3488a8c516bfda78f872352362"
  integrity sha1-TxonOv38jzSIqMUWv9p4+HI1I2I=

react-load-script@0.0.6:
  version "0.0.6"
  resolved "http://r.npm.sankuai.com/react-load-script/download/react-load-script-0.0.6.tgz#db6851236aaa25bb622677a2eb51dad4f8d2c258"
  integrity sha1-22hRI2qqJbtiJnei61Ha1PjSwlg=

react-popper@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/react-popper/download/react-popper-2.3.0.tgz#17891c620e1320dce318bad9fede46a5f71c70ba"
  integrity sha1-F4kcYg4TINzjGLrZ/t5GpfcccLo=
  dependencies:
    react-fast-compare "^3.0.1"
    warning "^4.0.2"

react-quill@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/react-quill/download/react-quill-2.0.0.tgz#67a0100f58f96a246af240c9fa6841b363b3e017"
  integrity sha1-Z6AQD1j5aiRq8kDJ+mhBs2Oz4Bc=
  dependencies:
    "@types/quill" "^1.3.10"
    lodash "^4.17.4"
    quill "^1.3.7"

react-redux@5.0.6:
  version "5.0.6"
  resolved "http://r.npm.sankuai.com/react-redux/download/react-redux-5.0.6.tgz#23ed3a4f986359d68b5212eaaa681e60d6574946"
  integrity sha1-I+06T5hjWdaLUhLqqmgeYNZXSUY=
  dependencies:
    hoist-non-react-statics "^2.2.1"
    invariant "^2.0.0"
    lodash "^4.2.0"
    lodash-es "^4.2.0"
    loose-envify "^1.1.0"
    prop-types "^15.5.10"

react-remove-scroll-bar@^2.3.7:
  version "2.3.8"
  resolved "http://r.npm.sankuai.com/react-remove-scroll-bar/download/react-remove-scroll-bar-2.3.8.tgz#99c20f908ee467b385b68a3469b4a3e750012223"
  integrity sha1-mcIPkI7kZ7OFtoo0abSj51ABIiM=
  dependencies:
    react-style-singleton "^2.2.2"
    tslib "^2.0.0"

react-remove-scroll@^2.6.3:
  version "2.6.3"
  resolved "http://r.npm.sankuai.com/react-remove-scroll/download/react-remove-scroll-2.6.3.tgz#df02cde56d5f2731e058531f8ffd7f9adec91ac2"
  integrity sha1-3wLN5W1fJzHgWFMfj/1/mt7JGsI=
  dependencies:
    react-remove-scroll-bar "^2.3.7"
    react-style-singleton "^2.2.3"
    tslib "^2.1.0"
    use-callback-ref "^1.3.3"
    use-sidecar "^1.1.3"

react-router-dom@^4.3.1:
  version "4.3.1"
  resolved "http://r.npm.sankuai.com/react-router-dom/download/react-router-dom-4.3.1.tgz#4c2619fc24c4fa87c9fd18f4fb4a43fe63fbd5c6"
  integrity sha1-TCYZ/CTE+ofJ/Rj0+0pD/mP71cY=
  dependencies:
    history "^4.7.2"
    invariant "^2.2.4"
    loose-envify "^1.3.1"
    prop-types "^15.6.1"
    react-router "^4.3.1"
    warning "^4.0.1"

react-router@^4.3.1:
  version "4.3.1"
  resolved "http://r.npm.sankuai.com/react-router/download/react-router-4.3.1.tgz#aada4aef14c809cb2e686b05cee4742234506c4e"
  integrity sha1-qtpK7xTICcsuaGsFzuR0IjRQbE4=
  dependencies:
    history "^4.7.2"
    hoist-non-react-statics "^2.5.0"
    invariant "^2.2.4"
    loose-envify "^1.3.1"
    path-to-regexp "^1.7.0"
    prop-types "^15.6.1"
    warning "^4.0.1"

react-slick@^0.30.2:
  version "0.30.3"
  resolved "http://r.npm.sankuai.com/react-slick/download/react-slick-0.30.3.tgz#3af5846fcbc04c681f8ba92f48881a0f78124a27"
  integrity sha1-OvWEb8vATGgfi6kvSIgaD3gSSic=
  dependencies:
    classnames "^2.2.5"
    enquire.js "^2.1.6"
    json2mq "^0.2.0"
    lodash.debounce "^4.0.8"
    resize-observer-polyfill "^1.5.0"

react-style-singleton@^2.2.2, react-style-singleton@^2.2.3:
  version "2.2.3"
  resolved "http://r.npm.sankuai.com/react-style-singleton/download/react-style-singleton-2.2.3.tgz#4265608be69a4d70cfe3047f2c6c88b2c3ace388"
  integrity sha1-QmVgi+aaTXDP4wR/LGyIssOs44g=
  dependencies:
    get-nonce "^1.0.0"
    tslib "^2.0.0"

react-transition-group@^2.5.3:
  version "2.9.0"
  resolved "http://r.npm.sankuai.com/react-transition-group/download/react-transition-group-2.9.0.tgz#df9cdb025796211151a436c69a8f3b97b5b07c8d"
  integrity sha1-35zbAleWIRFRpDbGmo87l7WwfI0=
  dependencies:
    dom-helpers "^3.4.0"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"
    react-lifecycles-compat "^3.0.4"

react-window@^1.8.8:
  version "1.8.11"
  resolved "http://r.npm.sankuai.com/react-window/download/react-window-1.8.11.tgz#a857b48fa85bd77042d59cc460964ff2e0648525"
  integrity sha1-qFe0j6hb13BC1ZzEYJZP8uBkhSU=
  dependencies:
    "@babel/runtime" "^7.0.0"
    memoize-one ">=3.1.1 <6"

react@^18.2.0:
  version "18.3.1"
  resolved "http://r.npm.sankuai.com/react/download/react-18.3.1.tgz#49ab892009c53933625bd16b2533fc754cab2891"
  integrity sha1-SauJIAnFOTNiW9FrJTP8dUyrKJE=
  dependencies:
    loose-envify "^1.1.0"

reactcss@^1.2.0:
  version "1.2.3"
  resolved "http://r.npm.sankuai.com/reactcss/download/reactcss-1.2.3.tgz#c00013875e557b1cf0dfd9a368a1c3dab3b548dd"
  integrity sha1-wAATh15Vexzw39mjaKHD2rO1SN0=
  dependencies:
    lodash "^4.0.1"

reactflow@^11.11.4:
  version "11.11.4"
  resolved "http://r.npm.sankuai.com/reactflow/download/reactflow-11.11.4.tgz#e3593e313420542caed81aecbd73fb9bc6576653"
  integrity sha1-41k+MTQgVCyu2BrsvXP7m8ZXZlM=
  dependencies:
    "@reactflow/background" "11.3.14"
    "@reactflow/controls" "11.2.14"
    "@reactflow/core" "11.11.4"
    "@reactflow/minimap" "11.7.14"
    "@reactflow/node-resizer" "2.2.14"
    "@reactflow/node-toolbar" "1.3.14"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/read-cache/download/read-cache-1.0.0.tgz#e664ef31161166c9751cdbe8dbcf86b5fb58f774"
  integrity sha1-5mTvMRYRZsl1HNvo28+GtftY93Q=
  dependencies:
    pify "^2.3.0"

readable-stream@^3.4.0:
  version "3.6.2"
  resolved "http://r.npm.sankuai.com/readable-stream/download/readable-stream-3.6.2.tgz#56a9b36ea965c00c5a93ef31eb111a0f11056967"
  integrity sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=
  dependencies:
    inherits "^2.0.3"
    string_decoder "^1.1.1"
    util-deprecate "^1.0.1"

readdirp@^4.0.1:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/readdirp/download/readdirp-4.0.2.tgz#388fccb8b75665da3abffe2d8f8ed59fe74c230a"
  integrity sha1-OI/MuLdWZdo6v/4tj47Vn+dMIwo=

readdirp@~3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/readdirp/download/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "http://r.npm.sankuai.com/rechoir/download/rechoir-0.6.2.tgz#85204b54dba82d5742e28c96756ef43af50e3384"
  integrity sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=
  dependencies:
    resolve "^1.1.6"

redux-thunk@^2.3.0:
  version "2.4.2"
  resolved "http://r.npm.sankuai.com/redux-thunk/download/redux-thunk-2.4.2.tgz#b9d05d11994b99f7a91ea223e8b04cf0afa5ef3b"
  integrity sha1-udBdEZlLmfepHqIj6LBM8K+l7zs=

redux@^4.0.5:
  version "4.2.1"
  resolved "http://r.npm.sankuai.com/redux/download/redux-4.2.1.tgz#c08f4306826c49b5e9dc901dee0452ea8fce6197"
  integrity sha1-wI9DBoJsSbXp3JAd7gRS6o/OYZc=
  dependencies:
    "@babel/runtime" "^7.9.2"

reflect.getprototypeof@^1.0.6, reflect.getprototypeof@^1.0.9:
  version "1.0.10"
  resolved "http://r.npm.sankuai.com/reflect.getprototypeof/download/reflect.getprototypeof-1.0.10.tgz#c629219e78a3316d8b604c765ef68996964e7bf9"
  integrity sha1-xikhnnijMW2LYEx2XvaJlpZOe/k=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.1"
    which-builtin-type "^1.2.1"

regenerate-unicode-properties@^10.2.0:
  version "10.2.0"
  resolved "http://r.npm.sankuai.com/regenerate-unicode-properties/download/regenerate-unicode-properties-10.2.0.tgz#626e39df8c372338ea9b8028d1f99dc3fd9c3db0"
  integrity sha1-Ym4534w3Izjqm4Ao0fmdw/2cPbA=
  dependencies:
    regenerate "^1.4.2"

regenerate@^1.4.2:
  version "1.4.2"
  resolved "http://r.npm.sankuai.com/regenerate/download/regenerate-1.4.2.tgz#b9346d8827e8f5a32f7ba29637d398b69014848a"
  integrity sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=

regenerator-runtime@^0.11.0:
  version "0.11.1"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.11.1.tgz#be05ad7f9bf7d22e056f9726cee5017fbf19e2e9"
  integrity sha1-vgWtf5v30i4Fb5cmzuUBf78Z4uk=

regenerator-runtime@^0.13.4:
  version "0.13.11"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.13.11.tgz#f6dca3e7ceec20590d07ada785636a90cdca17f9"
  integrity sha1-9tyj587sIFkNB62nhWNqkM3KF/k=

regenerator-runtime@^0.14.0:
  version "0.14.1"
  resolved "http://r.npm.sankuai.com/regenerator-runtime/download/regenerator-runtime-0.14.1.tgz#356ade10263f685dda125100cd862c1db895327f"
  integrity sha1-NWreECY/aF3aElEAzYYsHbiVMn8=

regenerator-transform@^0.15.2:
  version "0.15.2"
  resolved "http://r.npm.sankuai.com/regenerator-transform/download/regenerator-transform-0.15.2.tgz#5bbae58b522098ebdf09bca2f83838929001c7a4"
  integrity sha1-W7rli1IgmOvfCbyi+Dg4kpABx6Q=
  dependencies:
    "@babel/runtime" "^7.8.4"

regexp.prototype.flags@^1.5.1:
  version "1.5.3"
  resolved "http://r.npm.sankuai.com/regexp.prototype.flags/download/regexp.prototype.flags-1.5.3.tgz#b3ae40b1d2499b8350ab2c3fe6ef3845d3a96f42"
  integrity sha1-s65AsdJJm4NQqyw/5u84RdOpb0I=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    set-function-name "^2.0.2"

regexp.prototype.flags@^1.5.3:
  version "1.5.4"
  resolved "http://r.npm.sankuai.com/regexp.prototype.flags/download/regexp.prototype.flags-1.5.4.tgz#1ad6c62d44a259007e55b3970e00f746efbcaa19"
  integrity sha1-GtbGLUSiWQB+VbOXDgD3Ru+8qhk=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    set-function-name "^2.0.2"

regexpu-core@^6.2.0:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/regexpu-core/download/regexpu-core-6.2.0.tgz#0e5190d79e542bf294955dccabae04d3c7d53826"
  integrity sha1-DlGQ155UK/KUlV3Mq64E08fVOCY=
  dependencies:
    regenerate "^1.4.2"
    regenerate-unicode-properties "^10.2.0"
    regjsgen "^0.8.0"
    regjsparser "^0.12.0"
    unicode-match-property-ecmascript "^2.0.0"
    unicode-match-property-value-ecmascript "^2.1.0"

regjsgen@^0.8.0:
  version "0.8.0"
  resolved "http://r.npm.sankuai.com/regjsgen/download/regjsgen-0.8.0.tgz#df23ff26e0c5b300a6470cad160a9d090c3a37ab"
  integrity sha1-3yP/JuDFswCmRwytFgqdCQw6N6s=

regjsparser@^0.12.0:
  version "0.12.0"
  resolved "http://r.npm.sankuai.com/regjsparser/download/regjsparser-0.12.0.tgz#0e846df6c6530586429377de56e0475583b088dc"
  integrity sha1-DoRt9sZTBYZCk3feVuBHVYOwiNw=
  dependencies:
    jsesc "~3.0.2"

repeat-string@^1.5.2:
  version "1.6.1"
  resolved "http://r.npm.sankuai.com/repeat-string/download/repeat-string-1.6.1.tgz#8dcae470e1c88abc2d600fff4a776286da75e637"
  integrity sha1-jcrkcOHIirwtYA//Sndihtp15jc=

request@*, request@^2.67.0, request@^2.88.2:
  version "2.88.2"
  resolved "http://r.npm.sankuai.com/request/download/request-2.88.2.tgz#d73c918731cb5a87da047e207234146f664d12b3"
  integrity sha1-1zyRhzHLWofaBH4gcjQUb2ZNErM=
  dependencies:
    aws-sign2 "~0.7.0"
    aws4 "^1.8.0"
    caseless "~0.12.0"
    combined-stream "~1.0.6"
    extend "~3.0.2"
    forever-agent "~0.6.1"
    form-data "~2.3.2"
    har-validator "~5.1.3"
    http-signature "~1.2.0"
    is-typedarray "~1.0.0"
    isstream "~0.1.2"
    json-stringify-safe "~5.0.1"
    mime-types "~2.1.19"
    oauth-sign "~0.9.0"
    performance-now "^2.1.0"
    qs "~6.5.2"
    safe-buffer "^5.1.2"
    tough-cookie "~2.5.0"
    tunnel-agent "^0.6.0"
    uuid "^3.3.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/require-directory/download/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

resize-observer-polyfill@^1.5.0, resize-observer-polyfill@^1.5.1:
  version "1.5.1"
  resolved "http://r.npm.sankuai.com/resize-observer-polyfill/download/resize-observer-polyfill-1.5.1.tgz#0e9020dd3d21024458d4ebd27e23e40269810464"
  integrity sha1-DpAg3T0hAkRY1OvSfiPkAmmBBGQ=

resolve-alpn@^1.0.0:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/resolve-alpn/download/resolve-alpn-1.2.1.tgz#b7adbdac3546aaaec20b45e7d8265927072726f9"
  integrity sha1-t629rDVGqq7CC0Xn2CZZJwcnJvk=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/resolve-from/download/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-pathname@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/resolve-pathname/download/resolve-pathname-3.0.0.tgz#99d02224d3cf263689becbb393bc560313025dcd"
  integrity sha1-mdAiJNPPJjaJvsuzk7xWAxMCXc0=

resolve@^1.1.6, resolve@^1.1.7, resolve@^1.14.2, resolve@^1.22.8, resolve@~1.22.6:
  version "1.22.10"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-1.22.10.tgz#b663e83ffb09bbf2386944736baae803029b8b39"
  integrity sha1-tmPoP/sJu/I4aURza6roAwKbizk=
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.5:
  version "2.0.0-next.5"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-2.0.0-next.5.tgz#6b0ec3107e671e52b68cd068ef327173b90dc03c"
  integrity sha1-aw7DEH5nHlK2jNBo7zJxc7kNwDw=
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

responselike@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/responselike/download/responselike-2.0.1.tgz#9a0bc8fdc252f3fb1cca68b016591059ba1422bc"
  integrity sha1-mgvI/cJS8/scymiwFlkQWboUIrw=
  dependencies:
    lowercase-keys "^2.0.0"

restore-cursor@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-2.0.0.tgz#9f7ee287f82fd326d4fd162923d62129eee0dfaf"
  integrity sha1-n37ih/gv0ybU/RYpI9YhKe7g368=
  dependencies:
    onetime "^2.0.0"
    signal-exit "^3.0.2"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/restore-cursor/download/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

retry@^0.10.1:
  version "0.10.1"
  resolved "http://r.npm.sankuai.com/retry/download/retry-0.10.1.tgz#e76388d217992c252750241d3d3956fed98d8ff4"
  integrity sha1-52OI0heZLCUnUCQdPTlW/tmNj/Q=

retry@^0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/retry/download/retry-0.6.1.tgz#fdc90eed943fde11b893554b8cc63d0e899ba918"
  integrity sha1-/ckO7ZQ/3hG4k1VLjMY9DombqRg=

reusify@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/reusify/download/reusify-1.0.4.tgz#90da382b1e126efc02146e90845a88db12925d76"
  integrity sha1-kNo4Kx4SbvwCFG6QhFqI2xKSXXY=

right-align@^0.1.1:
  version "0.1.3"
  resolved "http://r.npm.sankuai.com/right-align/download/right-align-0.1.3.tgz#61339b722fe6a3515689210d24e14c96148613ef"
  integrity sha1-YTObci/mo1FWiSENJOFMlhSGE+8=
  dependencies:
    align-text "^0.1.1"

rimraf@^2.6.3:
  version "2.7.1"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-2.7.1.tgz#35797f13a7fdadc566142c29d4f07ccad483e3ec"
  integrity sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=
  dependencies:
    glob "^7.1.3"

rimraf@^3.0.2:
  version "3.0.2"
  resolved "http://r.npm.sankuai.com/rimraf/download/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

rollup-plugin-terser@^7.0.2:
  version "7.0.2"
  resolved "http://r.npm.sankuai.com/rollup-plugin-terser/download/rollup-plugin-terser-7.0.2.tgz#e8fbba4869981b2dc35ae7e8a502d5c6c04d324d"
  integrity sha1-6Pu6SGmYGy3DWufopQLVxsBNMk0=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    jest-worker "^26.2.1"
    serialize-javascript "^4.0.0"
    terser "^5.0.0"

rollup@^0.25.8:
  version "0.25.8"
  resolved "http://r.npm.sankuai.com/rollup/download/rollup-0.25.8.tgz#bf6ce83b87510d163446eeaa577ed6a6fc5835e0"
  integrity sha1-v2zoO4dRDRY0Ru6qV37WpvxYNeA=
  dependencies:
    chalk "^1.1.1"
    minimist "^1.2.0"
    source-map-support "^0.3.2"

rollup@^3.27.1:
  version "3.29.5"
  resolved "http://r.npm.sankuai.com/rollup/download/rollup-3.29.5.tgz#8a2e477a758b520fb78daf04bca4c522c1da8a54"
  integrity sha1-ii5HenWLUg+3ja8EvKTFIsHailQ=
  optionalDependencies:
    fsevents "~2.3.2"

rollup@^4.34.8:
  version "4.40.0"
  resolved "http://r.npm.sankuai.com/rollup/download/rollup-4.40.0.tgz#13742a615f423ccba457554f006873d5a4de1920"
  integrity sha1-E3QqYV9CPMukV1VPAGhz1aTeGSA=
  dependencies:
    "@types/estree" "1.0.7"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.40.0"
    "@rollup/rollup-android-arm64" "4.40.0"
    "@rollup/rollup-darwin-arm64" "4.40.0"
    "@rollup/rollup-darwin-x64" "4.40.0"
    "@rollup/rollup-freebsd-arm64" "4.40.0"
    "@rollup/rollup-freebsd-x64" "4.40.0"
    "@rollup/rollup-linux-arm-gnueabihf" "4.40.0"
    "@rollup/rollup-linux-arm-musleabihf" "4.40.0"
    "@rollup/rollup-linux-arm64-gnu" "4.40.0"
    "@rollup/rollup-linux-arm64-musl" "4.40.0"
    "@rollup/rollup-linux-loongarch64-gnu" "4.40.0"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.40.0"
    "@rollup/rollup-linux-riscv64-gnu" "4.40.0"
    "@rollup/rollup-linux-riscv64-musl" "4.40.0"
    "@rollup/rollup-linux-s390x-gnu" "4.40.0"
    "@rollup/rollup-linux-x64-gnu" "4.40.0"
    "@rollup/rollup-linux-x64-musl" "4.40.0"
    "@rollup/rollup-win32-arm64-msvc" "4.40.0"
    "@rollup/rollup-win32-ia32-msvc" "4.40.0"
    "@rollup/rollup-win32-x64-msvc" "4.40.0"
    fsevents "~2.3.2"

run-async@^2.2.0, run-async@^2.4.0:
  version "2.4.1"
  resolved "http://r.npm.sankuai.com/run-async/download/run-async-2.4.1.tgz#8440eccf99ea3e70bd409d49aab88e10c189a455"
  integrity sha1-hEDsz5nqPnC9QJ1JqriOEMGJpFU=

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/run-parallel/download/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

rw@1, rw@^1.3.2:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/rw/download/rw-1.3.3.tgz#3f862dfa91ab766b14885ef4d01124bfda074fb4"
  integrity sha1-P4Yt+pGrdmsUiF700BEkv9oHT7Q=

rxjs@^6.4.0:
  version "6.6.7"
  resolved "http://r.npm.sankuai.com/rxjs/download/rxjs-6.6.7.tgz#90ac018acabf491bf65044235d5863c4dab804c9"
  integrity sha1-kKwBisq/SRv2UEQjXVhjxNq4BMk=
  dependencies:
    tslib "^1.9.0"

rxjs@^7.5.5:
  version "7.8.1"
  resolved "http://r.npm.sankuai.com/rxjs/download/rxjs-7.8.1.tgz#6f6f3d99ea8044291efd92e7c7fcf562c4057543"
  integrity sha1-b289meqARCke/ZLnx/z1YsQFdUM=
  dependencies:
    tslib "^2.1.0"

safe-array-concat@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/safe-array-concat/download/safe-array-concat-1.1.3.tgz#c9e54ec4f603b0bbb8e7e5007a5ee7aecd1538c3"
  integrity sha1-yeVOxPYDsLu45+UAel7nrs0VOMM=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    has-symbols "^1.1.0"
    isarray "^2.0.5"

safe-buffer@^5.0.1, safe-buffer@^5.1.0, safe-buffer@^5.1.2, safe-buffer@~5.2.0:
  version "5.2.1"
  resolved "http://r.npm.sankuai.com/safe-buffer/download/safe-buffer-5.2.1.tgz#1eaf9fa9bdb1fdd4ec75f58f9cdb4e6b7827eec6"
  integrity sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=

safe-push-apply@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/safe-push-apply/download/safe-push-apply-1.0.0.tgz#01850e981c1602d398c85081f360e4e6d03d27f5"
  integrity sha1-AYUOmBwWAtOYyFCB82Dk5tA9J/U=
  dependencies:
    es-errors "^1.3.0"
    isarray "^2.0.5"

safe-regex-test@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/safe-regex-test/download/safe-regex-test-1.1.0.tgz#7f87dfb67a3150782eaaf18583ff5d1711ac10c1"
  integrity sha1-f4fftnoxUHguqvGFg/9dFxGsEME=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

"safer-buffer@>= 2.1.2 < 3", "safer-buffer@>= 2.1.2 < 3.0.0", safer-buffer@^2.0.2, safer-buffer@^2.1.0, safer-buffer@~2.1.0:
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sass@^1.63.2:
  version "1.83.0"
  resolved "http://r.npm.sankuai.com/sass/download/sass-1.83.0.tgz#e36842c0b88a94ed336fd16249b878a0541d536f"
  integrity sha1-42hCwLiKlO0zb9FiSbh4oFQdU28=
  dependencies:
    chokidar "^4.0.0"
    immutable "^5.0.2"
    source-map-js ">=0.6.2 <2.0.0"
  optionalDependencies:
    "@parcel/watcher" "^2.4.1"

sax@0.4.2:
  version "0.4.2"
  resolved "http://r.npm.sankuai.com/sax/download/sax-0.4.2.tgz#39f3b601733d6bec97105b242a2a40fd6978ac3c"
  integrity sha1-OfO2AXM9a+yXEFskKipA/Wl4rDw=

sax@>=0.6.0, sax@^1.2.4:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/sax/download/sax-1.4.1.tgz#44cc8988377f126304d3b3fc1010c733b929ef0f"
  integrity sha1-RMyJiDd/EmME07P8EBDHM7kp7w8=

scheduler@^0.23.2:
  version "0.23.2"
  resolved "http://r.npm.sankuai.com/scheduler/download/scheduler-0.23.2.tgz#414ba64a3b282892e944cf2108ecc078d115cdc3"
  integrity sha1-QUumSjsoKJLpRM8hCOzAeNEVzcM=
  dependencies:
    loose-envify "^1.1.0"

screenfull@^5.0.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/screenfull/download/screenfull-5.2.0.tgz#6533d524d30621fc1283b9692146f3f13a93d1ba"
  integrity sha1-ZTPVJNMGIfwSg7lpIUbz8TqT0bo=

scroll-into-view-if-needed@^2.2.28, scroll-into-view-if-needed@^2.2.31:
  version "2.2.31"
  resolved "http://r.npm.sankuai.com/scroll-into-view-if-needed/download/scroll-into-view-if-needed-2.2.31.tgz#d3c482959dc483e37962d1521254e3295d0d1587"
  integrity sha1-08SClZ3Eg+N5YtFSElTjKV0NFYc=
  dependencies:
    compute-scroll-into-view "^1.0.20"

scroll-into-view-if-needed@^3.1.0:
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/scroll-into-view-if-needed/download/scroll-into-view-if-needed-3.1.0.tgz#fa9524518c799b45a2ef6bbffb92bcad0296d01f"
  integrity sha1-+pUkUYx5m0Wi72u/+5K8rQKW0B8=
  dependencies:
    compute-scroll-into-view "^3.0.2"

sdk-base@^3.6.0:
  version "3.6.0"
  resolved "http://r.npm.sankuai.com/sdk-base/download/sdk-base-3.6.0.tgz#7cee1fa3a81fdc61b2c20d1130c8765f8d90e588"
  integrity sha1-fO4fo6gf3GGywg0RMMh2X42Q5Yg=
  dependencies:
    await-event "^2.1.0"
    await-first "^1.0.0"
    co "^4.6.0"
    is-type-of "^1.2.1"

semver@^5.6.0:
  version "5.7.2"
  resolved "http://r.npm.sankuai.com/semver/download/semver-5.7.2.tgz#48d55db737c3287cd4835e17fa13feace1c41ef8"
  integrity sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=

semver@^6.1.2, semver@^6.3.1:
  version "6.3.1"
  resolved "http://r.npm.sankuai.com/semver/download/semver-6.3.1.tgz#556d2ef8689146e46dcea4bfdd095f3434dffcb4"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.3.7:
  version "7.6.3"
  resolved "http://r.npm.sankuai.com/semver/download/semver-7.6.3.tgz#980f7b5550bc175fb4dc09403085627f9eb33143"
  integrity sha1-mA97VVC8F1+03AlAMIVif56zMUM=

serialize-javascript@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/serialize-javascript/download/serialize-javascript-4.0.0.tgz#b525e1238489a5ecfc42afacc3fe99e666f4b1aa"
  integrity sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=
  dependencies:
    randombytes "^2.1.0"

set-function-length@^1.2.2:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/set-function-length/download/set-function-length-1.2.2.tgz#aac72314198eaed975cf77b2c3b6b880695e5449"
  integrity sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/set-function-name/download/set-function-name-2.0.2.tgz#16a705c5a0dc2f5e638ca96d8a8cd4e1c2b90985"
  integrity sha1-FqcFxaDcL15jjKltiozU4cK5CYU=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

set-proto@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/set-proto/download/set-proto-1.0.0.tgz#0760dbcff30b2d7e801fd6e19983e56da337565e"
  integrity sha1-B2Dbz/MLLX6AH9bhmYPlbaM3Vl4=
  dependencies:
    dunder-proto "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shelljs@0.8.4:
  version "0.8.4"
  resolved "http://r.npm.sankuai.com/shelljs/download/shelljs-0.8.4.tgz#de7684feeb767f8716b326078a8a00875890e3c2"
  integrity sha1-3naE/ut2f4cWsyYHiooAh1iQ48I=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

shelljs@0.8.5:
  version "0.8.5"
  resolved "http://r.npm.sankuai.com/shelljs/download/shelljs-0.8.5.tgz#de055408d8361bed66c669d2f000538ced8ee20c"
  integrity sha1-3gVUCNg2G+1mxmnS8ABTjO2O4gw=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

side-channel-list@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/side-channel-list/download/side-channel-list-1.0.0.tgz#10cb5984263115d3b7a0e336591e290a830af8ad"
  integrity sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/side-channel-map/download/side-channel-map-1.0.1.tgz#d6bb6b37902c6fef5174e5f533fab4c732a26f42"
  integrity sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/side-channel-weakmap/download/side-channel-weakmap-1.0.2.tgz#11dda19d5368e40ce9ec2bdc1fb0ecbc0790ecea"
  integrity sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.0.6, side-channel@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/side-channel/download/side-channel-1.1.0.tgz#c3fcff9c4da932784873335ec9765fa94ff66bc9"
  integrity sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

signal-exit@^3.0.2:
  version "3.0.7"
  resolved "http://r.npm.sankuai.com/signal-exit/download/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

signal-exit@^4.0.1:
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/signal-exit/download/signal-exit-4.1.0.tgz#952188c1cbd546070e2dd20d0f41c0ae0530cb04"
  integrity sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=

simple-swizzle@^0.2.2:
  version "0.2.2"
  resolved "http://r.npm.sankuai.com/simple-swizzle/download/simple-swizzle-0.2.2.tgz#a4da6b635ffcccca33f70d17cb92592de95e557a"
  integrity sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=
  dependencies:
    is-arrayish "^0.3.1"

size-sensor@^1.0.1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/size-sensor/download/size-sensor-1.0.2.tgz#b8f8da029683cf2b4e22f12bf8b8f0a1145e8471"
  integrity sha1-uPjaApaDzytOIvEr+LjwoRRehHE=

slash@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/slash/download/slash-2.0.0.tgz#de552851a1759df3a8f206535442f5ec4ddeab44"
  integrity sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=

slash@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/slash/download/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slate-history@^0.66.0:
  version "0.66.0"
  resolved "http://r.npm.sankuai.com/slate-history/download/slate-history-0.66.0.tgz#ac63fddb903098ceb4c944433e3f75fe63acf940"
  integrity sha1-rGP925AwmM60yURDPj91/mOs+UA=
  dependencies:
    is-plain-object "^5.0.0"

slate@^0.72.0:
  version "0.72.8"
  resolved "http://r.npm.sankuai.com/slate/download/slate-0.72.8.tgz#5a018edf24e45448655293a68bfbcf563aa5ba81"
  integrity sha1-WgGO3yTkVEhlUpOmi/vPVjqluoE=
  dependencies:
    immer "^9.0.6"
    is-plain-object "^5.0.0"
    tiny-warning "^1.0.3"

snabbdom@^3.1.0:
  version "3.6.2"
  resolved "http://r.npm.sankuai.com/snabbdom/download/snabbdom-3.6.2.tgz#57dd66878f6320497fa7f67941df356a045c75a1"
  integrity sha1-V91mh49jIEl/p/Z5Qd81agRcdaE=

snappyjs@^0.5.0:
  version "0.5.0"
  resolved "http://r.npm.sankuai.com/snappyjs/download/snappyjs-0.5.0.tgz#2600e75a50f0799c79b055c3df1b7f7008045838"
  integrity sha1-JgDnWlDweZx5sFXD3xt/cAgEWDg=

"source-map-js@>=0.6.2 <2.0.0", source-map-js@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/source-map-js/download/source-map-js-1.2.1.tgz#1ce5650fddd87abc099eda37dcff024c2667ae46"
  integrity sha1-HOVlD93YerwJnto33P8CTCZnrkY=

source-map-support@^0.3.2:
  version "0.3.3"
  resolved "http://r.npm.sankuai.com/source-map-support/download/source-map-support-0.3.3.tgz#34900977d5ba3f07c7757ee72e73bb1a9b53754f"
  integrity sha1-NJAJd9W6PwfHdX7nLnO7GptTdU8=
  dependencies:
    source-map "0.1.32"

source-map-support@~0.5.20:
  version "0.5.21"
  resolved "http://r.npm.sankuai.com/source-map-support/download/source-map-support-0.5.21.tgz#04fe7c7f9e1ed2d662233c28cb2b35b9f63f6e4f"
  integrity sha1-BP58f54e0tZiIzwoyys1ufY/bk8=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@0.1.32:
  version "0.1.32"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.1.32.tgz#c8b6c167797ba4740a8ea33252162ff08591b266"
  integrity sha1-yLbBZ3l7pHQKjqMyUhYv8IWRsmY=
  dependencies:
    amdefine ">=0.0.4"

source-map@0.8.0-beta.0:
  version "0.8.0-beta.0"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.8.0-beta.0.tgz#d4c1bb42c3f7ee925f005927ba10709e0d1d1f11"
  integrity sha1-1MG7QsP37pJfAFknuhBwng0dHxE=
  dependencies:
    whatwg-url "^7.0.0"

source-map@^0.6.0, source-map@~0.6.0:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

source-map@~0.5.1:
  version "0.5.7"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.5.7.tgz#8a039d2d1021d22d1ea14c80d8ea468ba2ef3fcc"
  integrity sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=

split-on-first@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/split-on-first/download/split-on-first-3.0.0.tgz#f04959c9ea8101b9b0bbf35a61b9ebea784a23e7"
  integrity sha1-8ElZyeqBAbmwu/NaYbnr6nhKI+c=

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/sprintf-js/download/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

sshpk@^1.7.0:
  version "1.18.0"
  resolved "http://r.npm.sankuai.com/sshpk/download/sshpk-1.18.0.tgz#1663e55cddf4d688b86a46b77f0d5fe363aba028"
  integrity sha1-FmPlXN301oi4aka3fw1f42OroCg=
  dependencies:
    asn1 "~0.2.3"
    assert-plus "^1.0.0"
    bcrypt-pbkdf "^1.0.0"
    dashdash "^1.12.0"
    ecc-jsbn "~0.1.1"
    getpass "^0.1.1"
    jsbn "~0.1.0"
    safer-buffer "^2.0.2"
    tweetnacl "~0.14.0"

ssr-window@^3.0.0-alpha.1:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/ssr-window/download/ssr-window-3.0.0.tgz#fd5b82801638943e0cc704c4691801435af7ac37"
  integrity sha1-/VuCgBY4lD4MxwTEaRgBQ1r3rDc=

string-convert@^0.2.0:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/string-convert/download/string-convert-0.2.1.tgz#6982cc3049fbb4cd85f8b24568b9d9bf39eeff97"
  integrity sha1-aYLMMEn7tM2F+LJFaLnZvznu/5c=

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^2.1.0:
  version "2.1.1"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-2.1.1.tgz#ab93f27a8dc13d28cac815c462143a6d9012ae9e"
  integrity sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=
  dependencies:
    is-fullwidth-code-point "^2.0.0"
    strip-ansi "^4.0.0"

string-width@^4.1.0, string-width@^4.2.0:
  version "4.2.3"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  resolved "http://r.npm.sankuai.com/string-width/download/string-width-5.1.2.tgz#14f8daec6d81e7221d2a357e668cab73bdbca794"
  integrity sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string.prototype.codepointat@^0.2.0:
  version "0.2.1"
  resolved "http://r.npm.sankuai.com/string.prototype.codepointat/download/string.prototype.codepointat-0.2.1.tgz#004ad44c8afc727527b108cd462b4d971cd469bc"
  integrity sha1-AErUTIr8cnUnsQjNRitNlxzUabw=

string.prototype.matchall@^4.0.12:
  version "4.0.12"
  resolved "http://r.npm.sankuai.com/string.prototype.matchall/download/string.prototype.matchall-4.0.12.tgz#6c88740e49ad4956b1332a911e949583a275d4c0"
  integrity sha1-bIh0DkmtSVaxMyqRHpSVg6J11MA=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    regexp.prototype.flags "^1.5.3"
    set-function-name "^2.0.2"
    side-channel "^1.1.0"

string.prototype.repeat@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/string.prototype.repeat/download/string.prototype.repeat-1.0.0.tgz#e90872ee0308b29435aa26275f6e1b762daee01a"
  integrity sha1-6Qhy7gMIspQ1qiYnX24bdi2u4Bo=
  dependencies:
    define-properties "^1.1.3"
    es-abstract "^1.17.5"

string.prototype.trim@^1.2.10, string.prototype.trim@~1.2.8:
  version "1.2.10"
  resolved "http://r.npm.sankuai.com/string.prototype.trim/download/string.prototype.trim-1.2.10.tgz#40b2dd5ee94c959b4dcfb1d65ce72e90da480c81"
  integrity sha1-QLLdXulMlZtNz7HWXOcukNpIDIE=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-data-property "^1.1.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-object-atoms "^1.0.0"
    has-property-descriptors "^1.0.2"

string.prototype.trimend@^1.0.9:
  version "1.0.9"
  resolved "http://r.npm.sankuai.com/string.prototype.trimend/download/string.prototype.trimend-1.0.9.tgz#62e2731272cd285041b36596054e9f66569b6942"
  integrity sha1-YuJzEnLNKFBBs2WWBU6fZlabaUI=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/string.prototype.trimstart/download/string.prototype.trimstart-1.0.8.tgz#7ee834dda8c7c17eff3118472bb35bfedaa34dde"
  integrity sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string_decoder@^1.1.1:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/string_decoder/download/string_decoder-1.3.0.tgz#42f114594a46cf1a8e30b0a84f56c78c3edac21e"
  integrity sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=
  dependencies:
    safe-buffer "~5.2.0"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-3.0.1.tgz#6a385fb8853d952d5ff05d0e8aaf94278dc63dcf"
  integrity sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=
  dependencies:
    ansi-regex "^2.0.0"

strip-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-4.0.0.tgz#a8479022eb1ac368a871389b635262c505ee368f"
  integrity sha1-qEeQIusaw2iocTibY1JixQXuNo8=
  dependencies:
    ansi-regex "^3.0.0"

strip-ansi@^5.1.0:
  version "5.2.0"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-5.2.0.tgz#8c9a536feb6afc962bdfa5b104a5091c1ad9c0ae"
  integrity sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=
  dependencies:
    ansi-regex "^4.1.0"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/strip-ansi/download/strip-ansi-7.1.0.tgz#d5b6568ca689d8561370b0707685d22434faff45"
  integrity sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=
  dependencies:
    ansi-regex "^6.0.1"

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

strnum@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/strnum/download/strnum-1.0.5.tgz#5c4e829fe15ad4ff0d20c3db5ac97b73c9b072db"
  integrity sha1-XE6Cn+Fa1P8NIMPbWsl7c8mwcts=

strnum@^1.1.1:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/strnum/download/strnum-1.1.2.tgz#57bca4fbaa6f271081715dbc9ed7cee5493e28e4"
  integrity sha1-V7yk+6pvJxCBcV28ntfO5Uk+KOQ=

stylis@^4.3.4:
  version "4.3.4"
  resolved "http://r.npm.sankuai.com/stylis/download/stylis-4.3.4.tgz#ca5c6c4a35c4784e4e93a2a24dc4e9fa075250a4"
  integrity sha1-ylxsSjXEeE5Ok6KiTcTp+gdSUKQ=

sucrase@^3.35.0:
  version "3.35.0"
  resolved "http://r.npm.sankuai.com/sucrase/download/sucrase-3.35.0.tgz#57f17a3d7e19b36d8995f06679d121be914ae263"
  integrity sha1-V/F6PX4Zs22JlfBmedEhvpFK4mM=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    commander "^4.0.0"
    glob "^10.3.10"
    lines-and-columns "^1.1.6"
    mz "^2.7.0"
    pirates "^4.0.1"
    ts-interface-checker "^0.1.9"

supports-color@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-2.0.0.tgz#535d045ce6b6363fa40117084629995e9df324c7"
  integrity sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=

supports-color@^5.3.0:
  version "5.5.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-5.5.0.tgz#e2e69a44ac8772f78a1ec0b35b689df6530efc8f"
  integrity sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=
  dependencies:
    has-flag "^3.0.0"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/supports-color/download/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

svg-path-parser@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/svg-path-parser/download/svg-path-parser-1.1.0.tgz#e16b4b39df0d2b0d39e8347db79fdda1453a6046"
  integrity sha1-4WtLOd8NKw056DR9t5/doUU6YEY=

tailwind-merge@^2.2.1:
  version "2.6.0"
  resolved "http://r.npm.sankuai.com/tailwind-merge/download/tailwind-merge-2.6.0.tgz#ac5fb7e227910c038d458f396b7400d93a3142d5"
  integrity sha1-rF+34ieRDAONRY85a3QA2ToxQtU=

tailwindcss-animate@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/tailwindcss-animate/download/tailwindcss-animate-1.0.7.tgz#318b692c4c42676cc9e67b19b78775742388bef4"
  integrity sha1-MYtpLExCZ2zJ5nsZt4d1dCOIvvQ=

tailwindcss@^3.4.4:
  version "3.4.17"
  resolved "http://r.npm.sankuai.com/tailwindcss/download/tailwindcss-3.4.17.tgz#ae8406c0f96696a631c790768ff319d46d5e5a63"
  integrity sha1-roQGwPlmlqYxx5B2j/MZ1G1eWmM=
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    arg "^5.0.2"
    chokidar "^3.6.0"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.3.2"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    jiti "^1.21.6"
    lilconfig "^3.1.3"
    micromatch "^4.0.8"
    normalize-path "^3.0.0"
    object-hash "^3.0.0"
    picocolors "^1.1.1"
    postcss "^8.4.47"
    postcss-import "^15.1.0"
    postcss-js "^4.0.1"
    postcss-load-config "^4.0.2"
    postcss-nested "^6.2.0"
    postcss-selector-parser "^6.1.2"
    resolve "^1.22.8"
    sucrase "^3.35.0"

tape@^4.5.1:
  version "4.17.0"
  resolved "http://r.npm.sankuai.com/tape/download/tape-4.17.0.tgz#de89f3671ddc5dad178d04c28dc6b0183f42268e"
  integrity sha1-3onzZx3cXa0XjQTCjcawGD9CJo4=
  dependencies:
    "@ljharb/resumer" "~0.0.1"
    "@ljharb/through" "~2.3.9"
    call-bind "~1.0.2"
    deep-equal "~1.1.1"
    defined "~1.0.1"
    dotignore "~0.1.2"
    for-each "~0.3.3"
    glob "~7.2.3"
    has "~1.0.3"
    inherits "~2.0.4"
    is-regex "~1.1.4"
    minimist "~1.2.8"
    mock-property "~1.0.0"
    object-inspect "~1.12.3"
    resolve "~1.22.6"
    string.prototype.trim "~1.2.8"

terser@^5.0.0:
  version "5.37.0"
  resolved "http://r.npm.sankuai.com/terser/download/terser-5.37.0.tgz#38aa66d1cfc43d0638fab54e43ff8a4f72a21ba3"
  integrity sha1-OKpm0c/EPQY4+rVOQ/+KT3KiG6M=
  dependencies:
    "@jridgewell/source-map" "^0.3.3"
    acorn "^8.8.2"
    commander "^2.20.0"
    source-map-support "~0.5.20"

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/thenify-all/download/thenify-all-1.6.0.tgz#1a1918d402d8fc3f98fbf234db0bcc8cc10e9726"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "http://r.npm.sankuai.com/thenify/download/thenify-3.3.1.tgz#8932e686a4066038a016dd9e2ca46add9838a95f"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

thrift@0.11.0, thrift@^0.11.0:
  version "0.11.0"
  resolved "http://r.npm.sankuai.com/thrift/download/thrift-0.11.0.tgz#256115e4ff87871e12537f4b510bd2b425e13990"
  integrity sha1-JWEV5P+Hhx4SU39LUQvStCXhOZA=
  dependencies:
    node-int64 "^0.4.0"
    q "^1.5.0"
    ws ">= 2.2.3"

throttle-debounce@^5.0.0, throttle-debounce@^5.0.2:
  version "5.0.2"
  resolved "http://r.npm.sankuai.com/throttle-debounce/download/throttle-debounce-5.0.2.tgz#ec5549d84e053f043c9fd0f2a6dd892ff84456b1"
  integrity sha1-7FVJ2E4FPwQ8n9Dypt2JL/hEVrE=

through@^2.3.6:
  version "2.3.8"
  resolved "http://r.npm.sankuai.com/through/download/through-2.3.8.tgz#0dd4c9ffaabc357960b1b724115d7e0e86a2e1f5"
  integrity sha1-DdTJ/6q8NXlgsbckEV1+Doai4fU=

tiny-invariant@^1.0.2:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/tiny-invariant/download/tiny-invariant-1.3.3.tgz#46680b7a873a0d5d10005995eb90a70d74d60127"
  integrity sha1-RmgLeoc6DV0QAFmV65CnDXTWASc=

tiny-warning@^1.0.0, tiny-warning@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/tiny-warning/download/tiny-warning-1.0.3.tgz#94a30db453df4c643d0fd566060d60a875d84754"
  integrity sha1-lKMNtFPfTGQ9D9VmBg1gqHXYR1Q=

tinycolor2@^1.4.1:
  version "1.6.0"
  resolved "http://r.npm.sankuai.com/tinycolor2/download/tinycolor2-1.6.0.tgz#f98007460169b0263b97072c5ae92484ce02d09e"
  integrity sha1-+YAHRgFpsCY7lwcsWukkhM4C0J4=

tinyexec@^0.3.2:
  version "0.3.2"
  resolved "http://r.npm.sankuai.com/tinyexec/download/tinyexec-0.3.2.tgz#941794e657a85e496577995c6eef66f53f42b3d2"
  integrity sha1-lBeU5leoXklld5lcbu9m9T9Cs9I=

tinyglobby@^0.2.11:
  version "0.2.13"
  resolved "http://r.npm.sankuai.com/tinyglobby/download/tinyglobby-0.2.13.tgz#a0e46515ce6cbcd65331537e57484af5a7b2ff7e"
  integrity sha1-oORlFc5svNZTMVN+V0hK9aey/34=
  dependencies:
    fdir "^6.4.4"
    picomatch "^4.0.2"

tmp@^0.0.33:
  version "0.0.33"
  resolved "http://r.npm.sankuai.com/tmp/download/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

toggle-selection@^1.0.3, toggle-selection@^1.0.6:
  version "1.0.6"
  resolved "http://r.npm.sankuai.com/toggle-selection/download/toggle-selection-1.0.6.tgz#6e45b1263f2017fa0acc7d89d78b15b8bf77da32"
  integrity sha1-bkWxJj8gF/oKzH2J14sVuL932jI=

toml@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/toml/download/toml-3.0.0.tgz#342160f1af1904ec9d204d03a5d61222d762c5ee"
  integrity sha1-NCFg8a8ZBOydIE0DpdYSItdixe4=

tough-cookie@~2.5.0:
  version "2.5.0"
  resolved "http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-2.5.0.tgz#cd9fb2a0aa1d5a12b473bd9fb96fa3dcff65ade2"
  integrity sha1-zZ+yoKodWhK0c72fuW+j3P9lreI=
  dependencies:
    psl "^1.1.28"
    punycode "^2.1.1"

tr46@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/tr46/download/tr46-1.0.1.tgz#a8b13fd6bfd2489519674ccde55ba3693b706d09"
  integrity sha1-qLE/1r/SSJUZZ0zN5VujaTtwbQk=
  dependencies:
    punycode "^2.1.0"

tr46@~0.0.3:
  version "0.0.3"
  resolved "http://r.npm.sankuai.com/tr46/download/tr46-0.0.3.tgz#8184fd347dac9cdc185992f3a6622e14b9d9ab6a"
  integrity sha1-gYT9NH2snNwYWZLzpmIuFLnZq2o=

tree-kill@^1.2.2:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/tree-kill/download/tree-kill-1.2.2.tgz#4ca09a9092c88b73a7cdc5e8a01b507b0790a0cc"
  integrity sha1-TKCakJLIi3OnzcXooBtQeweQoMw=

ts-interface-checker@^0.1.9:
  version "0.1.13"
  resolved "http://r.npm.sankuai.com/ts-interface-checker/download/ts-interface-checker-0.1.13.tgz#784fd3d679722bc103b1b4b8030bcddb5db2a699"
  integrity sha1-eE/T1nlyK8EDsbS4AwvN212yppk=

ts-polyfill@^3.0.1:
  version "3.8.2"
  resolved "http://r.npm.sankuai.com/ts-polyfill/download/ts-polyfill-3.8.2.tgz#7375c6b6a4ae074af4f6200ff6c0d32dbbd113cb"
  integrity sha1-c3XGtqSuB0r09iAP9sDTLbvRE8s=
  dependencies:
    core-js "^3.6.4"

tsconfck@^3.0.3:
  version "3.1.4"
  resolved "http://r.npm.sankuai.com/tsconfck/download/tsconfck-3.1.4.tgz#de01a15334962e2feb526824339b51be26712229"
  integrity sha1-3gGhUzSWLi/rUmgkM5tRviZxIik=

tslib@^1.10.0, tslib@^1.8.1, tslib@^1.9.0:
  version "1.14.1"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-1.14.1.tgz#cf2d38bdc34a134bcaf1091c41f6619e2f672d00"
  integrity sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=

tslib@^2.0.0, tslib@^2.0.3, tslib@^2.1.0, tslib@^2.3.0, tslib@^2.3.1, tslib@^2.4.1, tslib@^2.5.3:
  version "2.8.1"
  resolved "http://r.npm.sankuai.com/tslib/download/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

tsup@^8.0.1:
  version "8.4.0"
  resolved "http://r.npm.sankuai.com/tsup/download/tsup-8.4.0.tgz#2fdf537e7abc8f1ccbbbfe4228f16831457d4395"
  integrity sha1-L99Tfnq8jxzLu/5CKPFoMUV9Q5U=
  dependencies:
    bundle-require "^5.1.0"
    cac "^6.7.14"
    chokidar "^4.0.3"
    consola "^3.4.0"
    debug "^4.4.0"
    esbuild "^0.25.0"
    joycon "^3.1.1"
    picocolors "^1.1.1"
    postcss-load-config "^6.0.1"
    resolve-from "^5.0.0"
    rollup "^4.34.8"
    source-map "0.8.0-beta.0"
    sucrase "^3.35.0"
    tinyexec "^0.3.2"
    tinyglobby "^0.2.11"
    tree-kill "^1.2.2"

tsutils@^3.21.0:
  version "3.21.0"
  resolved "http://r.npm.sankuai.com/tsutils/download/tsutils-3.21.0.tgz#b48717d394cea6c1e096983eed58e9d61715b623"
  integrity sha1-tIcX05TOpsHglpg+7Vjp1hcVtiM=
  dependencies:
    tslib "^1.8.1"

tunnel-agent@^0.6.0:
  version "0.6.0"
  resolved "http://r.npm.sankuai.com/tunnel-agent/download/tunnel-agent-0.6.0.tgz#27a5dea06b36b04a0a9966774b290868f0fc40fd"
  integrity sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=
  dependencies:
    safe-buffer "^5.0.1"

tweetnacl@^0.14.3, tweetnacl@~0.14.0:
  version "0.14.5"
  resolved "http://r.npm.sankuai.com/tweetnacl/download/tweetnacl-0.14.5.tgz#5ae68177f192d4456269d108afa93ff8743f4f64"
  integrity sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/type-check/download/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.20.2.tgz#1bf207f4b28f91583666cb5fbd327887301cd5f4"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "http://r.npm.sankuai.com/type-fest/download/type-fest-0.21.3.tgz#d260a24b0198436e133fa26a524a6d65fa3b2e37"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type@^2.7.2:
  version "2.7.3"
  resolved "http://r.npm.sankuai.com/type/download/type-2.7.3.tgz#436981652129285cc3ba94f392886c2637ea0486"
  integrity sha1-Q2mBZSEpKFzDupTzkohsJjfqBIY=

typed-array-buffer@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/typed-array-buffer/download/typed-array-buffer-1.0.3.tgz#a72395450a4869ec033fd549371b47af3a2ee536"
  integrity sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-typed-array "^1.1.14"

typed-array-byte-length@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/typed-array-byte-length/download/typed-array-byte-length-1.0.3.tgz#8407a04f7d78684f3d252aa1a143d2b77b4160ce"
  integrity sha1-hAegT314aE89JSqhoUPSt3tBYM4=
  dependencies:
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.14"

typed-array-byte-offset@^1.0.4:
  version "1.0.4"
  resolved "http://r.npm.sankuai.com/typed-array-byte-offset/download/typed-array-byte-offset-1.0.4.tgz#ae3698b8ec91a8ab945016108aef00d5bff12355"
  integrity sha1-rjaYuOyRqKuUUBYQiu8A1b/xI1U=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.15"
    reflect.getprototypeof "^1.0.9"

typed-array-length@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/typed-array-length/download/typed-array-length-1.0.7.tgz#ee4deff984b64be1e118b0de8c9c877d5ce73d3d"
  integrity sha1-7k3v+YS2S+HhGLDejJyHfVznPT0=
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"
    reflect.getprototypeof "^1.0.6"

typescript@^4.9.3:
  version "4.9.5"
  resolved "http://r.npm.sankuai.com/typescript/download/typescript-4.9.5.tgz#095979f9bcc0d09da324d58d03ce8f8374cbe65a"
  integrity sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo=

uc.micro@^2.0.0, uc.micro@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/uc.micro/download/uc.micro-2.1.0.tgz#f8d3f7d0ec4c3dea35a7e3c8efa4cb8b45c9e7ee"
  integrity sha1-+NP30OxMPeo1p+PI76TLi0XJ5+4=

uglify-js@^2.6.2:
  version "2.8.29"
  resolved "http://r.npm.sankuai.com/uglify-js/download/uglify-js-2.8.29.tgz#29c5733148057bb4e1f75df35b7a9cb72e6a59dd"
  integrity sha1-KcVzMUgFe7Th913zW3qcty5qWd0=
  dependencies:
    source-map "~0.5.1"
    yargs "~3.10.0"
  optionalDependencies:
    uglify-to-browserify "~1.0.0"

uglify-to-browserify@~1.0.0:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/uglify-to-browserify/download/uglify-to-browserify-1.0.2.tgz#6e0924d6bda6b5afe349e39a6d632850a0f882b7"
  integrity sha1-bgkk1r2mta/jSeOabWMoUKD4grc=

unbox-primitive@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/unbox-primitive/download/unbox-primitive-1.1.0.tgz#8d9d2c9edeea8460c7f35033a88867944934d1e2"
  integrity sha1-jZ0snt7qhGDH81AzqIhnlEk00eI=
  dependencies:
    call-bound "^1.0.3"
    has-bigints "^1.0.2"
    has-symbols "^1.1.0"
    which-boxed-primitive "^1.1.1"

undici-types@~6.20.0:
  version "6.20.0"
  resolved "http://r.npm.sankuai.com/undici-types/download/undici-types-6.20.0.tgz#8171bf22c1f588d1554d55bf204bc624af388433"
  integrity sha1-gXG/IsH1iNFVTVW/IEvGJK84hDM=

unicode-canonical-property-names-ecmascript@^2.0.0:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-2.0.1.tgz#cb3173fe47ca743e228216e4a3ddc4c84d628cc2"
  integrity sha1-yzFz/kfKdD4ighbko93EyE1ijMI=

unicode-match-property-ecmascript@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-2.0.0.tgz#54fd16e0ecb167cf04cf1f756bdcc92eba7976c3"
  integrity sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=
  dependencies:
    unicode-canonical-property-names-ecmascript "^2.0.0"
    unicode-property-aliases-ecmascript "^2.0.0"

unicode-match-property-value-ecmascript@^2.1.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-2.2.0.tgz#a0401aee72714598f739b68b104e4fe3a0cb3c71"
  integrity sha1-oEAa7nJxRZj3ObaLEE5P46DLPHE=

unicode-property-aliases-ecmascript@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-2.1.0.tgz#43d41e3be698bd493ef911077c9b131f827e8ccd"
  integrity sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=

universalify@^0.1.0:
  version "0.1.2"
  resolved "http://r.npm.sankuai.com/universalify/download/universalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

unstated-next@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/unstated-next/download/unstated-next-1.1.0.tgz#7bb4911a12fdf3cc8ad3eb11a0b315e4a8685ea8"
  integrity sha1-e7SRGhL988yK0+sRoLMV5KhoXqg=

update-browserslist-db@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/update-browserslist-db/download/update-browserslist-db-1.1.1.tgz#80846fba1d79e82547fb661f8d141e0945755fe5"
  integrity sha1-gIRvuh156CVH+2YfjRQeCUV1X+U=
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.0"

update-browserslist-db@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/update-browserslist-db/download/update-browserslist-db-1.1.3.tgz#348377dd245216f9e7060ff50b15a1b740b75420"
  integrity sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/uri-js/download/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

use-callback-ref@^1.3.3:
  version "1.3.3"
  resolved "http://r.npm.sankuai.com/use-callback-ref/download/use-callback-ref-1.3.3.tgz#98d9fab067075841c5b2c6852090d5d0feabe2bf"
  integrity sha1-mNn6sGcHWEHFssaFIJDV0P6r4r8=
  dependencies:
    tslib "^2.0.0"

use-sidecar@^1.1.3:
  version "1.1.3"
  resolved "http://r.npm.sankuai.com/use-sidecar/download/use-sidecar-1.1.3.tgz#10e7fd897d130b896e2c546c63a5e8233d00efdb"
  integrity sha1-EOf9iX0TC4luLFRsY6XoIz0A79s=
  dependencies:
    detect-node-es "^1.1.0"
    tslib "^2.0.0"

use-sync-external-store@^1.2.2, use-sync-external-store@^1.5.0:
  version "1.5.0"
  resolved "http://r.npm.sankuai.com/use-sync-external-store/download/use-sync-external-store-1.5.0.tgz#55122e2a3edd2a6c106174c27485e0fd59bcfca0"
  integrity sha1-VRIuKj7dKmwQYXTCdIXg/Vm8/KA=

util-deprecate@^1.0.1, util-deprecate@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

uuid@^10.0.0:
  version "10.0.0"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-10.0.0.tgz#5a95aa454e6e002725c79055fd42aaba30ca6294"
  integrity sha1-WpWqRU5uACclx5BV/UKqujDKYpQ=

uuid@^3.3.2:
  version "3.4.0"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

uuid@^9.0.0:
  version "9.0.1"
  resolved "http://r.npm.sankuai.com/uuid/download/uuid-9.0.1.tgz#e188d4c8853cc722220392c424cd637f32293f30"
  integrity sha1-4YjUyIU8xyIiA5LEJM1jfzIpPzA=

value-equal@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/value-equal/download/value-equal-1.0.1.tgz#1e0b794c734c5c0cade179c437d356d931a34d6c"
  integrity sha1-Hgt5THNMXAyt4XnEN9NW2TGjTWw=

verror@1.10.0:
  version "1.10.0"
  resolved "http://r.npm.sankuai.com/verror/download/verror-1.10.0.tgz#3a105ca17053af55d6e270c1f8288682e18da400"
  integrity sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=
  dependencies:
    assert-plus "^1.0.0"
    core-util-is "1.0.2"
    extsprintf "^1.2.0"

vite-plugin-css-injected-by-js@^3.5.1:
  version "3.5.2"
  resolved "http://r.npm.sankuai.com/vite-plugin-css-injected-by-js/download/vite-plugin-css-injected-by-js-3.5.2.tgz#1f75d16ad5c05b6b49bf18018099a189ec2e46ad"
  integrity sha1-H3XRatXAW2tJvxgBgJmhiewuRq0=

vite-plugin-html-template@^1.2.0:
  version "1.2.2"
  resolved "http://r.npm.sankuai.com/vite-plugin-html-template/download/vite-plugin-html-template-1.2.2.tgz#d263c18dcf5f5e54bc74894546fd0ed993191f2f"
  integrity sha1-0mPBjc9fXlS8dIlFRv0O2ZMZHy8=
  dependencies:
    shelljs "0.8.4"

vite-plugin-mpa@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/vite-plugin-mpa/download/vite-plugin-mpa-1.2.0.tgz#65fde4b13633e101adcb88d733669d93bcea6fbf"
  integrity sha1-Zf3ksTYz4QGty4jXM2adk7zqb78=
  dependencies:
    connect-history-api-fallback "1.6.0"
    shelljs "0.8.5"
    yargs "16.2.0"

vite-plugin-qiankun@^1.0.15:
  version "1.0.15"
  resolved "http://r.npm.sankuai.com/vite-plugin-qiankun/download/vite-plugin-qiankun-1.0.15.tgz#862bb6935c50db31536cf322e13f3bf59e1adace"
  integrity sha1-hiu2k1xQ2zFTbPMi4T879Z4a2s4=
  dependencies:
    cheerio "^1.0.0-rc.10"

vite-plugin-top-level-await@^1.4.4:
  version "1.4.4"
  resolved "http://r.npm.sankuai.com/vite-plugin-top-level-await/download/vite-plugin-top-level-await-1.4.4.tgz#4900e06bfb7179de20aaa9b4730d04022a9e259e"
  integrity sha1-SQDga/txed4gqqm0cw0EAiqeJZ4=
  dependencies:
    "@rollup/plugin-virtual" "^3.0.2"
    "@swc/core" "^1.7.0"
    uuid "^10.0.0"

vite-tsconfig-paths@^4.0.5:
  version "4.3.2"
  resolved "http://r.npm.sankuai.com/vite-tsconfig-paths/download/vite-tsconfig-paths-4.3.2.tgz#321f02e4b736a90ff62f9086467faf4e2da857a9"
  integrity sha1-Mh8C5Lc2qQ/2L5CGRn+vTi2oV6k=
  dependencies:
    debug "^4.1.1"
    globrex "^0.1.2"
    tsconfck "^3.0.3"

vite@^4.5.3:
  version "4.5.5"
  resolved "http://r.npm.sankuai.com/vite/download/vite-4.5.5.tgz#639b9feca5c0a3bfe3c60cb630ef28bf219d742e"
  integrity sha1-Y5uf7KXAo7/jxgy2MO8ovyGddC4=
  dependencies:
    esbuild "^0.18.10"
    postcss "^8.4.27"
    rollup "^3.27.1"
  optionalDependencies:
    fsevents "~2.3.2"

warning@^4.0.1, warning@^4.0.2, warning@^4.0.3:
  version "4.0.3"
  resolved "http://r.npm.sankuai.com/warning/download/warning-4.0.3.tgz#16e9e077eb8a86d6af7d64aa1e05fd85b4678ca3"
  integrity sha1-Fungd+uKhtavfWSqHgX9hbRnjKM=
  dependencies:
    loose-envify "^1.0.0"

wcwidth@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/wcwidth/download/wcwidth-1.0.1.tgz#f0b0dcf915bc5ff1528afadb2c0e17b532da2fe8"
  integrity sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=
  dependencies:
    defaults "^1.0.3"

webidl-conversions@^3.0.0:
  version "3.0.1"
  resolved "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-3.0.1.tgz#24534275e2a7bc6be7bc86611cc16ae0a5654871"
  integrity sha1-JFNCdeKnvGvnvIZhHMFq4KVlSHE=

webidl-conversions@^4.0.2:
  version "4.0.2"
  resolved "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-4.0.2.tgz#a855980b1f0b6b359ba1d5d9fb39ae941faa63ad"
  integrity sha1-qFWYCx8LazWbodXZ+zmulB+qY60=

whatwg-fetch@^2.0.4:
  version "2.0.4"
  resolved "http://r.npm.sankuai.com/whatwg-fetch/download/whatwg-fetch-2.0.4.tgz#dde6a5df315f9d39991aa17621853d720b85566f"
  integrity sha1-3eal3zFfnTmZGqF2IYU9cguFVm8=

whatwg-url@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-5.0.0.tgz#966454e8765462e37644d3626f6742ce8b70965d"
  integrity sha1-lmRU6HZUYuN2RNNib2dCzotwll0=
  dependencies:
    tr46 "~0.0.3"
    webidl-conversions "^3.0.0"

whatwg-url@^7.0.0:
  version "7.1.0"
  resolved "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-7.1.0.tgz#c2c492f1eca612988efd3d2266be1b9fc6170d06"
  integrity sha1-wsSS8eymEpiO/T0iZr4bn8YXDQY=
  dependencies:
    lodash.sortby "^4.7.0"
    tr46 "^1.0.1"
    webidl-conversions "^4.0.2"

which-boxed-primitive@^1.1.0, which-boxed-primitive@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/which-boxed-primitive/download/which-boxed-primitive-1.1.1.tgz#d76ec27df7fa165f18d5808374a5fe23c29b176e"
  integrity sha1-127Cfff6Fl8Y1YCDdKX+I8KbF24=
  dependencies:
    is-bigint "^1.1.0"
    is-boolean-object "^1.2.1"
    is-number-object "^1.1.1"
    is-string "^1.1.1"
    is-symbol "^1.1.1"

which-builtin-type@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/which-builtin-type/download/which-builtin-type-1.2.1.tgz#89183da1b4907ab089a6b02029cc5d8d6574270e"
  integrity sha1-iRg9obSQerCJprAgKcxdjWV0Jw4=
  dependencies:
    call-bound "^1.0.2"
    function.prototype.name "^1.1.6"
    has-tostringtag "^1.0.2"
    is-async-function "^2.0.0"
    is-date-object "^1.1.0"
    is-finalizationregistry "^1.1.0"
    is-generator-function "^1.0.10"
    is-regex "^1.2.1"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.1.0"
    which-collection "^1.0.2"
    which-typed-array "^1.1.16"

which-collection@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/which-collection/download/which-collection-1.0.2.tgz#627ef76243920a107e7ce8e96191debe4b16c2a0"
  integrity sha1-Yn73YkOSChB+fOjpYZHevksWwqA=
  dependencies:
    is-map "^2.0.3"
    is-set "^2.0.3"
    is-weakmap "^2.0.2"
    is-weakset "^2.0.3"

which-typed-array@^1.1.16, which-typed-array@^1.1.18:
  version "1.1.19"
  resolved "http://r.npm.sankuai.com/which-typed-array/download/which-typed-array-1.1.19.tgz#df03842e870b6b88e117524a4b364b6fc689f956"
  integrity sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    for-each "^0.3.5"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

which@^2.0.1:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

wildcard@^1.1.0:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/wildcard/download/wildcard-1.1.2.tgz#a7020453084d8cd2efe70ba9d3696263de1710a5"
  integrity sha1-pwIEUwhNjNLv5wup02liY94XEKU=

window-size@0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/window-size/download/window-size-0.1.0.tgz#5438cd2ea93b202efa3a19fe8887aee7c94f9c9d"
  integrity sha1-VDjNLqk7IC76Ohn+iIeu58lPnJ0=

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "http://r.npm.sankuai.com/word-wrap/download/word-wrap-1.2.5.tgz#d2c45c6dd4fbce621a66f136cbe328afd0410b34"
  integrity sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=

wordwrap@0.0.2:
  version "0.0.2"
  resolved "http://r.npm.sankuai.com/wordwrap/download/wordwrap-0.0.2.tgz#b79669bb42ecb409f83d583cad52ca17eaa1643f"
  integrity sha1-t5Zpu0LstAn4PVg8rVLKF+qhZD8=

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^6.0.1:
  version "6.2.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-6.2.0.tgz#e9393ba07102e6c91a3b221478f0257cd2856e53"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "http://r.npm.sankuai.com/wrap-ansi/download/wrap-ansi-8.1.0.tgz#56dc22368ee570face1b49819975d9b9a5ead214"
  integrity sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

"ws@>= 2.2.3":
  version "8.18.1"
  resolved "http://r.npm.sankuai.com/ws/download/ws-8.18.1.tgz#ea131d3784e1dfdff91adb0a4a116b127515e3cb"
  integrity sha1-6hMdN4Th39/5GtsKShFrEnUV48s=

xml2js@0.2.6:
  version "0.2.6"
  resolved "http://r.npm.sankuai.com/xml2js/download/xml2js-0.2.6.tgz#d209c4e4dda1fc9c452141ef41c077f5adfdf6c4"
  integrity sha1-0gnE5N2h/JxFIUHvQcB39a399sQ=
  dependencies:
    sax "0.4.2"

xml2js@^0.4.15:
  version "0.4.23"
  resolved "http://r.npm.sankuai.com/xml2js/download/xml2js-0.4.23.tgz#a0c69516752421eb2ac758ee4d4ccf58843eac66"
  integrity sha1-oMaVFnUkIesqx1juTUzPWIQ+rGY=
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder@0.4.2:
  version "0.4.2"
  resolved "http://r.npm.sankuai.com/xmlbuilder/download/xmlbuilder-0.4.2.tgz#1776d65f3fdbad470a08d8604cdeb1c4e540ff83"
  integrity sha1-F3bWXz/brUcKCNhgTN6xxOVA/4M=

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "http://r.npm.sankuai.com/xmlbuilder/download/xmlbuilder-11.0.1.tgz#be9bae1c8a046e76b31127726347d0ad7002beb3"
  integrity sha1-vpuuHIoEbnazESdyY0fQrXACvrM=

y18n@^5.0.5:
  version "5.0.8"
  resolved "http://r.npm.sankuai.com/y18n/download/y18n-5.0.8.tgz#7f4934d0f7ca8c56f95314939ddcd2dd91ce1d55"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^3.0.2:
  version "3.1.1"
  resolved "http://r.npm.sankuai.com/yallist/download/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yaml@^2.3.4:
  version "2.7.1"
  resolved "http://r.npm.sankuai.com/yaml/download/yaml-2.7.1.tgz#44a247d1b88523855679ac7fa7cda6ed7e135cf6"
  integrity sha1-RKJH0biFI4VWeax/p82m7X4TXPY=

yargs-parser@^20.2.2:
  version "20.2.9"
  resolved "http://r.npm.sankuai.com/yargs-parser/download/yargs-parser-20.2.9.tgz#2eb7dc3b0289718fc295f362753845c41a0c94ee"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs@16.2.0:
  version "16.2.0"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-16.2.0.tgz#1c82bf0f6b6a66eafce7ef30e376f49a12477f66"
  integrity sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yargs@~3.10.0:
  version "3.10.0"
  resolved "http://r.npm.sankuai.com/yargs/download/yargs-3.10.0.tgz#f7ee7bd857dd7c1d2d38c0e74efbd681d1431fd1"
  integrity sha1-9+572FfdfB0tOMDnTvvWgdFDH9E=
  dependencies:
    camelcase "^1.0.2"
    cliui "^2.1.0"
    decamelize "^1.0.0"
    window-size "0.1.0"

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "http://r.npm.sankuai.com/yocto-queue/download/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=

zustand@^4.4.1:
  version "4.5.6"
  resolved "http://r.npm.sankuai.com/zustand/download/zustand-4.5.6.tgz#6857d52af44874a79fb3408c9473f78367255c96"
  integrity sha1-aFfVKvRIdKefs0CMlHP3g2clXJY=
  dependencies:
    use-sync-external-store "^1.2.2"
