import { Select } from 'antd';
import { useEffect, useState } from 'react';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import type { SelectProps } from 'antd';

interface Props extends Omit<SelectProps, 'options'> {
    value?: number;
    onChange?: (value: number) => void;
    onDataChange?: (data: any) => void;
}

const SceneSelect: React.FC<Props> = ({
    value,
    onChange,
    onDataChange,
    ...restProps
}) => {
    const [loading, setLoading] = useState(false);
    const [options, setOptions] = useState<{ label: string; value: number }[]>(
        [],
    );

    const fetchData = async () => {
        try {
            setLoading(true);
            const res = await apiCaller.post(
                '/xianfu/api-v2/dove/mark_detail/get_biz',
                {},
            );
            if (res.code === 0) {
                const options = res.data.map((item) => ({
                    label: item.name,
                    value: item.id
                }));
                setOptions(options);
            }
        } catch (error) {
            console.error('获取场景列表失败:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchData();
    }, []);

    return (
        <Select
            loading={loading}
            options={options}
            value={value}
            onChange={v => {
                onChange?.(v);
                onDataChange?.(options.find(item => item.value === v));
            }}
            placeholder="请选择场景"
            {...restProps}
        />
    );
};

export default SceneSelect;
