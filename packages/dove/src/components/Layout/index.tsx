import { bellwetherLinkParse } from '@mfe/bellwether-route';
import { Breadcrumb } from 'antd';
import React, {
    createContext,
    DependencyList,
    PropsWithChildren,
    useContext,
    useEffect,
    useState,
} from 'react';
import './style.scss';
import { HomeOutlined } from '@ant-design/icons';

export interface BreadCrumbItem {
    href?: string;
    name: string;
}

const LayoutContext = createContext({
    breadcrumbs: [] as BreadCrumbItem[],
    setBreadcrumbs: (_: BreadCrumbItem[]) => {},
});

export const useLayoutContext = () => useContext(LayoutContext);
export const useBreadcrumb = (
    b: BreadCrumbItem[],
    deps: DependencyList = [],
) => {
    const { setBreadcrumbs } = useLayoutContext();

    useEffect(() => {
        setBreadcrumbs(b);
    }, deps);
};

const LayoutProvider = (props: PropsWithChildren<any>) => {
    const [breadcrumbs, setBreadcrumbs] = useState<BreadCrumbItem[]>([]);

    return (
        <LayoutContext.Provider value={{ breadcrumbs, setBreadcrumbs }}>
            {breadcrumbs.length ? (
                <div className="jaguar__breadcrumb">
                    <Breadcrumb separator=">">
                        <Breadcrumb.Item href={bellwetherLinkParse('')}>
                            <HomeOutlined />
                        </Breadcrumb.Item>
                        {breadcrumbs.map(b => (
                            <Breadcrumb.Item
                                href={bellwetherLinkParse(b.href as string)}
                                key={b.name}
                            >
                                {b.name}
                            </Breadcrumb.Item>
                        ))}
                    </Breadcrumb>
                </div>
            ) : null}
            {props.children}
        </LayoutContext.Provider>
    );
};

export default LayoutProvider;
