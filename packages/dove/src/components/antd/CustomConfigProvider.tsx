import 'antd/dist/reset.css';
import 'dayjs/locale/zh-cn';
import '~/assets/global.scss';

import { ConfigProvider, App } from 'antd';
import zhCN from 'antd/es/locale/zh_CN'; // vite 不支持 es之外的，所以要用es或者esm目录下的locale包
import OverridesRoo from '../rooPlus/OverrideRoo';

const CustomConfigProvider = ({ children }) => {
    return (
        <ConfigProvider
            locale={zhCN}
            theme={{
                token: {
                    colorPrimary: '#FFcc33',
                    colorLink: '#FFcc33',
                },
                components: {
                    Button: {
                        colorTextLightSolid: '#222222',
                    },
                },
            }}
        >
            <OverridesRoo>
                <App>{children}</App>
            </OverridesRoo>
        </ConfigProvider>
    );
};

export default CustomConfigProvider;
