import React, { useEffect, useState } from 'react';
import { Select } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import type { SelectProps } from 'antd';

interface ProviderSelectorProps extends Omit<SelectProps, 'options'> {
    value?: string[];
    onChange?: (value: string[]) => void;
    defaultSelectFirst?: boolean;
}

const ProviderSelector: React.FC<ProviderSelectorProps> = ({ 
    value, 
    onChange, 
    defaultSelectFirst = false,
    ...props 
}) => {
    const [providerList, setProviderList] = useState<string[]>([]);
    const [loading, setLoading] = useState(false);

    const fetchProviderList = async () => {
        setLoading(true);
        try {
            // @ts-ignore
            const res: any = await apiCaller.get('/xianfu/api-v2/dove/display/number/pool/provider/query', {});
            if (res.code === 0 && Array.isArray(res.data)) {
                setProviderList(res.data);
                // 如果设置了默认选中第一个且当前没有选中值，则选中第一个选项
                if (defaultSelectFirst && !value && res.data.length > 0) {
                    onChange?.([res.data[0]]);
                }
            }
        } catch (error) {
            console.error('Fetch provider list failed:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchProviderList();
    }, []);

    const options = providerList.map(provider => ({
        label: provider,
        value: provider,
    }));

    return (
        <Select
            {...props}
            loading={loading}
            mode="multiple"
            allowClear
            value={value}
            onChange={onChange}
            options={options}
            placeholder="请选择运营商"
            maxTagCount="responsive"
        />
    );
};

export default ProviderSelector;
