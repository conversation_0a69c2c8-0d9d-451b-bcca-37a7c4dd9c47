import { Select } from 'antd';
import { SelectProps } from 'antd/lib/select';
import React, { useEffect } from 'react';

interface DefaultFirstSelect extends SelectProps {
    options: { label: string; value: string | number }[];
}

const DefaultFirstSelect = (props: DefaultFirstSelect) => {
    useEffect(() => {
        if (props.value) {
            return;
        }

        props.onChange?.(props.options[0].value, props.options[0] as any);
    }, [props.options, props.value]);
    return (
        <Select {...props}>
            {props.options.map(op => (
                <Select.Option key={op.value} value={op.value}>
                    {op.label}
                </Select.Option>
            ))}
        </Select>
    );
};

export default DefaultFirstSelect;
