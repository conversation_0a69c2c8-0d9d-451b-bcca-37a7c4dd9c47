import OrganizationPicker from '@roo/roo-plus/OrganizationPicker';
import { OrganizationPickerProps } from '@roo/roo-plus/OrganizationPicker/interface';

import '@roo/roo/theme/default/index.css';
import '@roo/roo-plus/theme/default/index.css';
import { useEffect, useRef, useState } from 'react';
import { apiCaller } from '@mfe/cc-api-caller-pc';

const prefix = '/xianfu/api/common';

const defaultAPI = {
    pidUrl: `${prefix}/uicomponent/api/orgs/getByPid`,
    searchUrl: `${prefix}/uicomponent/api/orgs/search`,
};

interface CommonItem {
    childrenList: CommonItem[];
    id: number;
}
const Source = '1_4_5_6_12_13_18';
export default (
    props: Omit<OrganizationPickerProps, 'resetDisabled'> & {
        value?: number[];
        onChange?: (v: number[]) => void;
        onInit?: (n: number[]) => void;
    },
) => {
    const [defaultValue, setDefaultValue] = useState<number[] | undefined>(
        undefined,
    );
    const defaultOrgs = useRef<number[]>([]);
    const defaultAllOrgs = useRef<number[]>([]);

    const dig = (item: CommonItem) => {
        defaultAllOrgs.current = [...defaultAllOrgs.current, item.id];
        if (item.childrenList) {
            return item.childrenList.map(dig);
        }

        return item.id;
    };

    const fetchDefaultPid = async (sources: string) => {
        if (props.value) {
            return;
        }
        const res = await apiCaller.get('/uicomponent/api/orgs/getByPid', {
            parentId: '0',
            sources,
        });
        if (res.code !== 0) {
            return;
        }

        let defaultSelect: number[];
        if (!res.data.isHq) {
            const defaultValue = res.data.list.map(dig);
            defaultSelect = defaultValue.flat(Infinity) as number[];
        } else {
            const defaultValue = res.data.list.map(dig);
            defaultSelect = defaultValue.flat(Infinity) as number[];
        }

        defaultOrgs.current = defaultSelect;
        setDefaultValue(defaultSelect);
        onConfirm(defaultSelect, {});
        props.onInit?.(defaultSelect);
    };

    useEffect(() => {
        fetchDefaultPid(Source);
    }, []);
    const onConfirm = (
        // @ts-ignore
        ...args: Parameters<OrganizationPickerProps['onConfirm']>
    ) => {
        const ids = args[0] instanceof Array ? args[0] : [args[0]];
        props.onChange?.(ids);
        props.onConfirm?.(ids, args[1]);
    };

    return (
        <OrganizationPicker
            multiple
            key={Source + defaultValue?.join('-')}
            placeholder="请选择组织结构"
            {...props}
            {...defaultAPI}
            params={{
                ...props.params,
                sources: Source,
            }}
            // filterDisableFn={item =>
            //     defaultAllOrgs.current.includes((item as { id: number }).id)
            // }
            allowClear
            defaultValue={props.value}
            onConfirm={onConfirm}
            // value={props.value}
        />
    );
};
