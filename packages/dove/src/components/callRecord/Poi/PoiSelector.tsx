import React, { useEffect, useState } from 'react';
import { get } from '~/pages/callRecord/util';
import { useDebounceFn } from 'ahooks';
import { message, Select } from 'antd';
const { Option } = Select;

interface Poi {
    id: number;
    name: string;
}

interface PoiSelector {
    value?: Poi;
    multi?: boolean;
    simpleValue?: boolean;
    poiType: number | null;
    bizId?: number | null;
    onChange?: (v: Poi) => void;
}

const PoiSelector = (props: PoiSelector) => {
    const [list, setList] = useState<Poi[]>([]);
    const [value, setValue] = useState('');

    const getList = async (content: string) => {
        let type = 2;
        const numPatrn = /^[0-9]*[1-9][0-9]*$/;
        if (numPatrn.test(content)) {
            type = 1;
        }
        get('/uicomponent/pois', { type, content }, { silent: true }).then(
            res => {
                // 组织结构接口以code 1标志成功
                if (res.code !== 1) {
                    message.error(res.msg);
                    return;
                }
                // @ts-ignore
                setList(res.data || []);
            },
        );
    };

    const { run: debouncedGetList } = useDebounceFn(getList, { wait: 500 });

    const onInputChange = input => {
        setValue(input);
        if (input === '') return;
        debouncedGetList(input);
    };
    useEffect(() => {
        props.value && getList(String(props.value));
    }, []);

    const optionRenderer = option => {
        const label = `${option.name}(${option.id})`;
        const index = label.indexOf(value);
        if (index === -1) {
            return <p style={{ margin: '0' }}>{label}</p>;
        }
        const pre = label.slice(0, index);
        const hit = label.slice(index, index + value.length);
        const post = label.slice(index + value.length);
        return (
            <p style={{ margin: '0' }}>
                {pre}
                <span style={{ color: 'red' }}>{hit}</span>
                {post}
            </p>
        );
    };

    const filterOption = (input, option) => {
        return (
            (option.props.value + '').indexOf(input) !== -1 ||
            ((option.props.label as string) || '')
                .toLowerCase()
                .indexOf(input.toLowerCase()) >= 0
        );
    };

    const shouldCheckPoiType = !!props.bizId;

    return (
        <div className="mis-container">
            <Select
                showSearch
                allowClear
                showArrow={false}
                mode={props.multi ? 'multiple' : undefined}
                placeholder={
                    shouldCheckPoiType
                        ? props.poiType
                            ? '请输入商家名称或id'
                            : '请先选择商家类型'
                        : '请输入商家名称或id'
                }
                disabled={shouldCheckPoiType && !props.poiType}
                filterOption={filterOption}
                onSearch={onInputChange}
                value={props.value}
                onChange={props.onChange}
                style={{ width: '100%' }}
            >
                {list.map(item => (
                    <Option key={item.id} value={item.id} label={item.name}>
                        {optionRenderer(item)}
                    </Option>
                ))}
            </Select>
        </div>
    );
};

export default PoiSelector;
