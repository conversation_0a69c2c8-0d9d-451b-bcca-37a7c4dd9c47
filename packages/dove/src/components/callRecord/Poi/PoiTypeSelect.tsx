import { apiCaller, MethodType } from '@mfe/cc-api-caller-pc';
import { Select } from 'antd';
import React, { useEffect, useState } from 'react';

const { Option } = Select;

interface PoiTypeSelect {
    value?: number;
    onChange?: (id: number | undefined) => void;
    bizId: number | null;
}

type PoiTypes = { poiType: number; poiName: string }[];

const PoiTypeSelect = (props: PoiTypeSelect) => {
    const [types, setTypes] = useState<PoiTypes>([]);

    const fetchPoiType = async () => {
        if (!props.bizId) {
            return;
        }

        const res = await apiCaller.send(
            '/dovecall-web/configure/queryPoiType',
            { bizId: props.bizId },
            {
                method: MethodType.GET,
            },
        );

        if (res.code !== 0) {
            return;
        }
        setTypes(res.data.list);
    };

    const [inited, setInited] = useState(false);
    useEffect(() => {
        if (!inited) {
            return;
        }
        fetchPoiType();
        props.onChange?.(undefined);
    }, [props.bizId]);

    useEffect(() => {
        fetchPoiType();
        setInited(true);
    }, []);

    return (
        <Select
            value={props.value}
            placeholder={props.bizId ? '请选择商家类型' : '请先选择业务线'}
            disabled={!props.bizId}
            onChange={props.onChange}
            style={{ width: '100%' }}
            allowClear
        >
            {types.map(option => (
                <Option value={option.poiType} key={option.poiType}>
                    {option.poiName}
                </Option>
            ))}
        </Select>
    );
};

export default PoiTypeSelect;
