import React, { useEffect, useState, forwardRef } from 'react';
import { message, Select } from 'antd';
import { apiCaller, MethodType, APISpec } from '@mfe/cc-api-caller-pc';
import 'antd/dist/reset.css';
const { Option } = Select;
interface TeamSelect {
    value?: number;
    onChange?: (id: number | undefined) => void;
    disabled?: boolean;
    showAll?: boolean;
    mode?: 'multiple' | 'tags' | undefined;
    extraOption?: { bizId: number; bizName: string };
    optionPath?: string;
    allowClear?: boolean;
    isDefaultChecked?: boolean;
    [key: string]: any;
}
type TeamList =
    APISpec['/dovecall-web/agentCall/queryUserBiz']['response']['bizList'];
const ALL = 0;
const TeamSelect = forwardRef((props: TeamSelect, ref) => {
    const [list, setList] = useState<TeamList>([]);
    const getTeamList = async () => {
        const res = await apiCaller.send(
            // @ts-ignore
            props.optionPath || '/dovecall-web/agentCall/queryUserBiz',
            {},
            {
                method: MethodType.GET,
            },
        );
        if (res.code !== 0) {
            return;
        }
        // @ts-ignore
        const teamList = res.data.bizList || [];
        setList(teamList);
        // 如果说指定了一个不存在的teamId, 那么直接给他清掉，并提示
        if (props.value && !teamList.find(one => one.bizId === props.value)) {
            message.error('当前业务无效！');
            props.onChange?.(undefined);
        }

        // 如果没有指定value且有数据，则默认选中第一条
        if (props.value === undefined && teamList.length > 0 && !props.showAll && props.isDefaultChecked) {
            props.onChange?.(teamList[0].bizId);
        }
    };
    useEffect(() => {
        getTeamList();
    }, []);

    const [forceUpdate, setForceUpdate] = useState(0);
    useEffect(() => {
        setForceUpdate(forceUpdate + 1);
    }, [props.value, list, props.extraOption]);
    const fallback = props.showAll ? ALL : undefined;
    return (
        <Select
            // @ts-ignore
            ref={ref}
            allowClear
            value={props.value || fallback}
            placeholder="请选择业务"
            onChange={props.onChange}
            disabled={props.disabled}
            style={{ width: '100%' }}
            mode={props.mode}
            {...props}
        >
            {props.showAll && (
                <Option value={ALL} key={-1}>
                    全部
                </Option>
            )}
            {[
                ...list,
                // @ts-ignore
                ...(!list.map(v => v.bizId).includes(props.extraOption?.bizId)
                    ? props.extraOption
                        ? [props.extraOption]
                        : []
                    : []),
            ].map(option => (
                <Option value={option.bizId} key={option.bizId}>
                    {option.bizName}
                </Option>
            ))}
        </Select>
    );
});
export default TeamSelect;
