import { ConfigProvider, App } from 'antd';
import '@roo/roo/roo-theme/src/scss/theme/custom.scss';
import '@roo/roo-plus/theme/default/index.css';
import '@src/assets/common.scss';

const LocalProvider = ({ children }) => {
    return (
        <ConfigProvider
            theme={{
                token: {
                    colorPrimary: '#FFDD00',
                    colorLink: '#FF6A00',
                    colorLinkActive: '#FFDD00',
                    colorLinkHover: '#FFDD00',
                },
                components: {
                    Button: {
                        colorPrimary: '#222',
                        colorPrimaryHover: '#222',
                        colorLink: '#FF6A00',
                        colorTextLightSolid: '#ffffff',
                    },
                    Table: {
                        rowSelectedBg: '#F5F6FA',
                        rowHoverBg: '#F5F6FA',
                        rowSelectedHoverBg: '#F5F6FA',
                    },
                },
            }}
        >
            <App>{children}</App>
        </ConfigProvider>
    );
};
export default LocalProvider;
