import { PropsWithChildren, Children, ReactElement } from 'react';
import _ from 'lodash';
import { Col, Row } from 'antd';
import { RowProps } from 'antd/lib';

interface Grid {
    gutter?: number;
    divide?: number;
    limit?: number;
    opItem?: ReactElement;
    visibleMode?: boolean;
    justify?: RowProps['justify'];
}

const AdaptiveGrid = (props: PropsWithChildren<Grid>) => {
    const { gutter = 16, divide = 4, limit, children, opItem } = props;

    const alChildren = Children.toArray(children);

    const els = alChildren.slice(0, limit);

    const span = Math.floor(24 / divide);

    if (!props.visibleMode) {
        const remain =
            divide - (els.length % divide) - (opItem && !limit ? 1 : 0);
        const list =
            remain > 0 ? [...els, ...Array(remain).fill(<span />)] : els;
        const elesWithOp = opItem
            ? limit
                ? [...list.slice(0, -1), opItem]
                : [...list, opItem]
            : list;
        const groups = _.chunk(elesWithOp, divide);
        return (
            <>
                {groups.map((g, index) => (
                    <Row
                        gutter={gutter}
                        justify="space-between"
                        align="middle"
                        style={{ marginBottom: 12 }}
                        key={index}
                    >
                        {g.map((node, i) => (
                            <Col key={i} span={span}>
                                {node}
                            </Col>
                        ))}
                    </Row>
                ))}
            </>
        );
    }

    const groups = _.chunk(alChildren, divide);

    return (
        <>
            {groups.map((g, index) => (
                <Row
                    gutter={gutter}
                    justify={props.justify || 'space-between'}
                    align="middle"
                    style={{
                        marginBottom: 12,
                        display: limit
                            ? index * divide > limit
                                ? 'none'
                                : 'flex'
                            : undefined,
                    }}
                    key={index}
                >
                    {g.map((node, i) => (
                        <Col
                            key={i}
                            span={span}
                            style={{
                                display: limit
                                    ? index * divide + i + 1 > limit
                                        ? 'none'
                                        : 'block'
                                    : 'block',
                            }}
                        >
                            {node}
                        </Col>
                    ))}
                </Row>
            ))}
        </>
    );
};

export default AdaptiveGrid;
