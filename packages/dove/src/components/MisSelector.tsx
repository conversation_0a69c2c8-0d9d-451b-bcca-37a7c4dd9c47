import React, { useState, useEffect } from 'react';
import { Select, SelectProps } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useDebounceFn } from 'ahooks';

interface MisSelectProps extends SelectProps {
    value?: string[];
    onChange?: (value: string[]) => void;
    mode?: 'multiple' | 'tags';
    placeholder?: string;
    mis: boolean;
}

interface MisUser {
    id: string;
    name: string;
    email: string;
}

const MisSelect: React.FC<MisSelectProps> = ({
    value,
    onChange,
    mode,
    placeholder,
    mis,
    ...restProps
}) => {
    const [loading, setLoading] = useState(false);
    const [userList, setUserList] = useState<MisUser[]>([]);

    const fetchUserList = async (searchText: string) => {
        if (!searchText) {
            setUserList([]);
            return;
        }

        setLoading(true);
        try {
            const response: any = await apiCaller.get(
                // @ts-ignore
                '/xianfu/api/common/uicomponent/employs',
                {
                    content: searchText,
                    type: searchText.match(/[\u4e00-\u9fa5]/) ? 2 : 1,
                },
                { silent: true },
            );
            if (response.code === 1 && response.data) {
                setUserList(response.data);
            }
        } catch (error) {
            console.error('Failed to fetch MIS users:', error);
        } finally {
            setLoading(false);
        }
    };

    const fetchUsersByEmails = async (emails: string[]) => {
        if (!emails.length) return;

        setLoading(true);
        const res = emails.map(async email => {
            try {
                const response: any = await apiCaller.get(
                    // @ts-ignore
                    '/xianfu/api/common/uicomponent/employs',
                    {
                        content: email,
                        type: email[0].match(/[\u4e00-\u9fa5]/) ? 2 : 1,
                    },
                    { silent: true },
                );
                if (response.code === 1 && response.data) {
                    return response.data;
                }
            } catch (error) {
                console.error('Failed to fetch MIS users by emails:', error);
            }
        });
        const userList = (await Promise.all(res))
            .filter(Boolean)
            .reduce((acc, user) => [...acc, ...user], []);
        setUserList(userList);
        setLoading(false);
    };

    const { run: handleSearch } = useDebounceFn(
        (searchText: string) => {
            fetchUserList(searchText);
        },
        {
            wait: 300,
        },
    );

    const handleChange = (selectedValue: string[]) => {
        onChange?.(selectedValue);
    };

    useEffect(() => {
        if (userList.length === 0 && value?.length && !restProps.labelInValue) {
            fetchUsersByEmails(value);
        }
    }, [value]);

    return (
        <Select
            mode={mode}
            showSearch
            allowClear
            value={value}
            placeholder={placeholder}
            loading={loading}
            filterOption={false}
            onSearch={handleSearch}
            onChange={handleChange}
            style={{ width: '100%' }}
            options={userList.map(user => ({
                label: user.email ? `${user.name}（${user.email}）` : user.name,
                value: mis ? user.email : user.id,
                key: JSON.stringify(user),
            }))}
            {...restProps}
        />
    );
};

export default MisSelect;
