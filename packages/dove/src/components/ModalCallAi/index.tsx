import { Form, Input, Modal, App } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import SelectAgent from '@src/components/SelectAgent';

interface ModalCallAiProps {
    parameters: string[];
    onSuccess?: () => void;
}

interface FormValues {
    tel: string;
    agentId: number;
    [key: string]: any;
}

const useModalCallAi = ({ parameters, onSuccess }: ModalCallAiProps) => {
    const { modal } = App.useApp();
    const [form] = Form.useForm<FormValues>();

    const showModal = () => {
        modal.confirm({
            title: 'AI 外呼',
            width: 500,
            content: (
                <Form
                    form={form}
                    layout="vertical"
                    initialValues={{
                        tel: '',
                        agentId: undefined,
                        ...parameters.reduce(
                            (acc, param) => ({ ...acc, [param]: '' }),
                            {},
                        ),
                    }}
                >
                    <Form.Item
                        label="电话号码"
                        name="tel"
                        rules={[{ required: true, message: '请输入电话号码' }]}
                    >
                        <Input placeholder="请输入电话号码" />
                    </Form.Item>
                    <Form.Item
                        label="选择 Agent"
                        name="agentId"
                        rules={[{ required: true, message: '请选择 Agent' }]}
                    >
                        <SelectAgent placeholder="请选择 Agent" />
                    </Form.Item>
                    {parameters.map(param => (
                        <Form.Item
                            key={param}
                            label={param}
                            name={param}
                            rules={[
                                { required: true, message: `请输入${param}` },
                            ]}
                        >
                            <Input placeholder={`请输入${param}`} />
                        </Form.Item>
                    ))}
                </Form>
            ),
            onOk: async () => {
                try {
                    const values = await form.validateFields();
                    const res = await apiCaller.post(
                        '/xianfu/api-v2/dove/call/ai/try',
                        {
                            tel: values.tel,
                            agentId: values.agentId as any,
                            placeholder: parameters.reduce(
                                (acc, param) => ({
                                    ...acc,
                                    [param]: values[param],
                                }),
                                {},
                            ),
                        },
                    );

                    if (res.code === 0) {
                        onSuccess?.();
                        return Promise.resolve();
                    }
                    return Promise.reject(new Error(res.message || '请求失败'));
                } catch (error) {
                    console.error('AI 外呼失败:', error);
                    return Promise.reject(error);
                }
            },
        });
    };

    return { showModal };
};

export default useModalCallAi;
