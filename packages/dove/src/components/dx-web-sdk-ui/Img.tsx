import * as React from 'react';
import PropTypes from 'prop-types';

function noop() {}

export interface ImgProps {
    src: string;
    placeholderSrc?: string;
    loadingSrc?: string;
    errorSrc?: string;
    onError?: () => any;
    onLoad?: () => any;
    forwardedRef?: any;
    [propName: string]: any;
}

class Img extends React.Component<ImgProps, any> {
    static propTypes = {
        src: PropTypes.string,
        placeholderSrc: PropTypes.string,
        loadingSrc: PropTypes.string,
        errorSrc: PropTypes.string,
        onError: PropTypes.func,
        onLoad: PropTypes.func,
    };

    constructor(props: ImgProps) {
        super(props);
        this.state = {
            src: props.placeholderSrc || props.src,
        };
    }

    private unmounted: boolean | undefined;

    private dealWithSrc = (forceSrc?: string) => {
        let { src, loadingSrc, errorSrc, onError, onLoad } = this.props;

        if (forceSrc != undefined) {
            src = forceSrc;
        }

        if (loadingSrc) {
            this.setState({
                src: loadingSrc,
            });
        }
        const img = new Image();
        img.onload = () => {
            if (!this.unmounted) {
                this.setState({ src: src });
                onLoad && onLoad();
            }
        };

        img.onerror = () => {
            if (!this.unmounted) {
                if (errorSrc) {
                    this.setState({
                        src: errorSrc,
                    });
                }
                onError && onError();
            }
        };

        img.src = src || ''; //no src will trigger onError,just like browser default

        if (!src) {
            this.setState({
                //no src no default img,just like browser default
                src: '',
            });
        }
    };

    componentDidMount() {
        this.dealWithSrc();
    }

    componentWillReceiveProps(nextProps: ImgProps) {
        if (nextProps.src !== this.props.src) {
            this.dealWithSrc(nextProps.src || '');
        }
    }

    componentDidUpdate() {}

    componentWillUnmount() {
        this.unmounted = true;
    }

    render() {
        const {
            placeholderSrc,
            loadingSrc,
            errorSrc,
            forwardedRef = noop,
            ...props
        } = this.props;
        return (
            <img
                {...props}
                src={this.state.src}
                onError={noop}
                onLoad={noop}
                ref={forwardedRef}
            />
        );
    }
}

export default React.forwardRef((props: ImgProps, ref) => {
    return <Img {...props} forwardedRef={ref} />;
});
