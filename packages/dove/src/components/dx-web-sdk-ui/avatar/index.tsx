import React from 'react';
import Img from '../Img';
import defaultAvatarSrc from '../assets/defaultAvatar.png';

export interface VcardInterface {
    name: string;
    uid?: number;
    pubId?: string;
    accountId?: string;
    avatarUrl: string;
    avatar?: string;
    bigAvatarUrl?: string;
    cid?: number;
    extend?: string;
    gender?: number;
    passport?: string;
    status?: number;
    tenant?: string;
    type?: number;
    ver?: string;
}

export interface AvatarProps {
    vcard: VcardInterface;
    className?: string;
    onClick?: (vcard: VcardInterface) => any
}

export default class Avatar extends React.Component<AvatarProps, any> {
    constructor(props: AvatarProps) {
        super(props);
    }

    onClick = () => {
        const { onClick, vcard } = this.props;
        if (onClick) {
            onClick(vcard);
        }
    }

    render() {
        const src = (this.props.vcard && (this.props.vcard.avatarUrl || this.props.vcard.avatar)) || defaultAvatarSrc;
        const { onClick, vcard, ...props } = this.props;
        return (
            <Img
                src={src}
                placeholderSrc={defaultAvatarSrc}
                loadingSrc={defaultAvatarSrc}
                errorSrc={defaultAvatarSrc}
                onClick={this.onClick}
                {...props}
            />
        );
    }
}

