import './index.scss';
import React, { PropsWithChildren, ReactElement } from 'react';

const defaultTriggerOffset = 100;

function noop() { };

export interface BothwayDynamicLoaderProps {
    triggerOffset?: number;          //偏差值
    loadEarlier?: (...args: any) => PromiseLike<any>;   //向上加载历史
    hasEarlier?: boolean;              //是否还有历史可以继续加载
    loadLatter?: (...args: any) => PromiseLike<any>;  //向下加载更多
    hasLatter?: boolean;           //是否还有更多可以继续加载
    wrapperRef?: (e:HTMLElement) => void;           //返回wrapper元素
    wrapperClassName?: string;
    bodyClassName?: string;
}

export default class BothwayDynamicLoader extends React.Component<
    PropsWithChildren<BothwayDynamicLoaderProps>,
    any,
    any
> {
    static defaultProps = {
        triggerOffset: defaultTriggerOffset,
        hasEarlier: true,
        hasLatter: true,
        loadLatter: noop,
        loadEarlier: noop,
        wrapperClassName: '',
        bodyClassName: '',
    };

    // static propTypes = {
    //     triggerOffset: PropTypes.number,          //偏差值
    //     loadEarlier: PropTypes.func.isRequired,   //向上加载历史
    //     hasEarlier: PropTypes.bool,               //是否还有历史可以继续加载
    //     loadLatter: PropTypes.func.isRequired,    //向下加载更多
    //     hasLatter: PropTypes.bool,                //是否还有更多可以继续加载
    //     wrapperRef: PropTypes.func,               //返回wrapper元素
    //     wrapperClassName: PropTypes.string,
    //     bodyClassName: PropTypes.string,
    //     // loadingComp:PropTypes.object
    // };

    constructor(props: BothwayDynamicLoaderProps) {
        super(props);
        this.state = {
            earlierLoading: false,
            latterLoading: false,
        };
        this._refs = {};
    }

    private _refs: { [refName: string]: HTMLElement | null } = {};

    earlierLoading = false;
    loadEarlier = async () => {
        const { loadEarlier, hasEarlier } = this.props;
        if (hasEarlier && !this.earlierLoading) {
            this.earlierLoading = true;
            this.setState({
                earlierLoading: true,
            });
            loadEarlier && (await loadEarlier());
            this.earlierLoading = false;
            this.setState({
                earlierLoading: false,
            });
        }
    };

    latterLoading = false;
    loadLatter = async () => {
        const { loadLatter, hasLatter } = this.props;
        if (hasLatter && !this.latterLoading) {
            this.latterLoading = true;
            this.setState({
                latterLoading: true,
            });
            loadLatter && (await loadLatter());
            this.latterLoading = false;
            this.setState({
                latterLoading: false,
            });
        }
    };

    // lastScrollTop = 0;

    private onWheel = (
        event: React.WheelEvent<HTMLElement> | { deltaY: number },
    ) => {
        const direction = event.deltaY;

        // if (this.silenceScroll) {
        // this.silenceScroll = false;
        // this.lastScrollTop = this._refs.wrapper.scrollTop;
        // return;
        // }

        // const thisScrollTop = wrapper.scrollTop;
        // //方向，仅在滚动方向加载
        // const direction = thisScrollTop - this.lastScrollTop;
        // this.lastScrollTop = thisScrollTop;

        const {
            triggerOffset = defaultTriggerOffset,
            hasLatter,
            hasEarlier,
        } = this.props;
        if (!(hasEarlier || hasLatter)) {
            return;
        }

        const { wrapper, body } = this._refs;

        if (!wrapper || !body) {
            return;
        }

        if (direction >= 0) {
            //向下,加载更多
            if (
                body.scrollHeight - wrapper.offsetHeight - wrapper.scrollTop <=
                triggerOffset
            ) {
                this.loadLatter();
            }
        } else {
            //向上，加载历史
            if (wrapper.scrollTop <= triggerOffset) {
                this.loadEarlier();
            }
        }
    };

    private touchStartY = 0;
    private touchEndY = 0;

    private onTouchStart = (event: React.TouchEvent<HTMLElement>) => {
        if (event && event.touches[0]) {
            this.touchStartY = event.touches[0].pageY;
        }
    };

    private onTouchMove = (event: React.TouchEvent<HTMLElement>) => {
        if (event && event.touches[0]) {
            this.touchEndY = event.touches[0].pageY;
        }
    };

    private onTouchEnd = () => {
        console.info(this.touchStartY - this.touchEndY);
        const mockEvent = {
            deltaY: this.touchStartY - this.touchEndY,
        };
        this.onWheel(mockEvent);
    };

    // silenceScroll = false;

    //通过该方法设置scrollTop不会触发加载操作
    // silenceSetScrollTop = (scrollTop) => {
    //     const {wrapper} = this._refs;
    //     if (wrapper) {
    //         this.silenceScroll = true;
    //         wrapper.scrollTop = scrollTop;
    //     }
    // };

    render() {
        let {
            wrapperClassName,
            bodyClassName,
            hasEarlier,
            hasLatter,
            children,
            wrapperRef,
        } = this.props;
        const { earlierLoading, latterLoading } = this.state;
        wrapperClassName = `${
            wrapperClassName || ''
        }  bothway-loadmore-wrapper`;
        bodyClassName = `${bodyClassName || ''}  bothway-loadmore-body`;

        let loadEarlierTrigger: ReactElement | null = null;
        let loadLatterTrigger: ReactElement | null = null;
        if (hasEarlier) {
            if (earlierLoading) {
                loadEarlierTrigger = (
                    <div className="bothway-loadmore-loading">
                        <span className="loading-icon"></span>
                        <span className="loading-tip">加载中</span>
                    </div>
                );
            } else {
                loadEarlierTrigger = (
                    <div className="bothway-loadmore-loading">
                        <a onClick={this.loadEarlier}>加载历史</a>
                    </div>
                );
            }
        }

        if (hasLatter) {
            if (latterLoading) {
                loadLatterTrigger = (
                    <div className="bothway-loadmore-loading">
                        <span className="loading-icon"></span>
                        <span className="loading-tip">加载中</span>
                    </div>
                );
            } else {
                loadLatterTrigger = (
                    <div className="bothway-loadmore-loading">
                        <a onClick={this.loadLatter}>加载更多</a>
                    </div>
                );
            }
        }

        return (
            <div
                className={wrapperClassName}
                ref={e => {
                    this._refs.wrapper = e;
                    e && wrapperRef && wrapperRef(e);
                }}
                onWheel={this.onWheel}
                onTouchStart={this.onTouchStart}
                onTouchMove={this.onTouchMove}
                onTouchEnd={this.onTouchEnd}
            >
                <div
                    className={bodyClassName}
                    ref={e => {
                        this._refs.body = e;
                    }}
                >
                    {loadEarlierTrigger}
                    {children}
                    {loadLatterTrigger}
                </div>
            </div>
        );
    }
}
