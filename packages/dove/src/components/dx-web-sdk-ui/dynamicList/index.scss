.bothway-loadmore-wrapper {
  height: 100%;
  overflow-y: auto;

  .bothway-loadmore-loading {
    text-align: center;
    height: 30px;
    line-height: 30px;
    color:rgba(0,0,0,.38);
    font-size: 14px;
    .loading-icon{
      display: inline-block;
      background-image: url(./loading.png);
      background-size: cover;
      background-repeat: no-repeat;
      animation: 0.85s loadingCircle steps(8) infinite;
      width:16px;
      height: 16px;
      vertical-align: middle;
    }

    .loading-tip{
      display: inline-block;
      vertical-align: middle;
      color:rgba(0, 0, 0, 0.38);
      margin-left:10px;
    }
  }
}

@keyframes loadingCircle {
  0% {
    transform-origin: 50% 50%;
    transform: rotate(0deg);
  }
  100% {
    transform-origin: 50% 50%;
    transform: rotate(360deg);
  }
}
