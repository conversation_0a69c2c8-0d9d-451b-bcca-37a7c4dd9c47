.json-viewer {
    background: transparent;
    padding: 12px;
    border-radius: 8px;
    width: 100%;
    box-sizing: border-box;

    .json-viewer-title {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 4px;
    }

    .json-viewer-container {
        background: #fafafa;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        overflow-x: hidden;
        position: relative;
        width: 100%;
        box-sizing: border-box;

        &.error {
            border-color: #ff4d4f;
        }

        .json-textarea {
            width: 100%;
            height: 100%;
            min-height: 100px;
            padding: 8px 12px;
            border: none;
            outline: none;
            background: transparent;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
            resize: none;
            white-space: pre-wrap;
            word-break: break-all;
            overflow-wrap: break-word;
            overflow-x: hidden;
            overflow-y: auto;
            box-sizing: border-box;

            &::placeholder {
                color: #bfbfbf;
            }
        }

        .json-content {
            margin: 0;
            padding: 8px 12px;
            font-family: 'Monaco', 'Men<PERSON>', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
            white-space: pre-wrap;
            word-break: break-all;
            overflow-wrap: break-word;
            overflow-x: hidden;
            overflow-y: auto;
            background: transparent;
            width: 100%;
            box-sizing: border-box;

            // JSON语法高亮样式
            .json-key {
                color: #0451a5;
                font-weight: 500;
            }

            .json-string {
                color: #0a8043;
            }

            .json-number {
                color: #098658;
            }

            .json-boolean {
                color: #0000ff;
                font-weight: 500;
            }

            .json-null {
                color: #0000ff;
                font-weight: 500;
            }

            .json-bracket {
                color: #000000;
                font-weight: 600;
            }

            .json-comma {
                color: #000000;
            }
        }

        .json-error {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: #fff2f0;
            border-top: 1px solid #ffccc7;
            padding: 4px 8px;
            font-size: 11px;
            color: #ff4d4f;
            z-index: 1;
        }
    }

    // 滚动条样式
    .json-viewer-container::-webkit-scrollbar,
    .json-content::-webkit-scrollbar,
    .json-textarea::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }

    .json-viewer-container::-webkit-scrollbar-track,
    .json-content::-webkit-scrollbar-track,
    .json-textarea::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .json-viewer-container::-webkit-scrollbar-thumb,
    .json-content::-webkit-scrollbar-thumb,
    .json-textarea::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
            background: #a8a8a8;
        }
    }

    // 响应式设计
    @media (max-width: 768px) {
        .json-viewer-container {

            .json-textarea,
            .json-display .json-content {
                font-size: 11px;
                padding: 6px 8px;
            }
        }
    }
}