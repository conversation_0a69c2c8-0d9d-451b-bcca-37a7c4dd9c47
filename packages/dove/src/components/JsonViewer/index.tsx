/**
 * JSON数据展示组件 - 类似代码编辑器的效果
 */
import React, { useState, useEffect } from 'react';
import './index.scss';

interface JsonViewerProps {
    data: any | string; // JSON数据或JSON字符串
    title?: string;
    maxHeight?: number;
    onChange?: (data: any) => void;
}

const JsonViewer: React.FC<JsonViewerProps> = ({
    data,
    title,
    maxHeight = 200,
}) => {
    const [jsonString, setJsonString] = useState('');
    const [isValid, setIsValid] = useState(true);
    const [error, setError] = useState('');

    // 校验JSON字符串是否规范
    const isValidJsonString = (str: string): boolean => {
        if (typeof str !== 'string') return false;
        try {
            JSON.parse(str);
            return true;
        } catch {
            return false;
        }
    };

    // 处理数据：如果是字符串则尝试解析，否则直接格式化
    const processData = (
        inputData: any,
    ): { jsonString: string; isValid: boolean; error: string } => {
        try {
            // 如果输入是字符串，尝试解析为JSON
            if (typeof inputData === 'string') {
                if (isValidJsonString(inputData)) {
                    // 是有效的JSON字符串，解析后重新格式化
                    const parsed = JSON.parse(inputData);
                    return {
                        jsonString: JSON.stringify(parsed, null, 2),
                        isValid: true,
                        error: '',
                    };
                } else {
                    // 不是有效的JSON字符串，直接显示原字符串
                    return {
                        jsonString: inputData,
                        isValid: false,
                        error: '不是有效的JSON格式',
                    };
                }
            } else {
                // 不是字符串，直接格式化为JSON
                return {
                    jsonString: JSON.stringify(inputData, null, 2),
                    isValid: true,
                    error: '',
                };
            }
        } catch (err) {
            return {
                jsonString: String(inputData),
                isValid: false,
                error: err instanceof Error ? err.message : '数据处理失败',
            };
        }
    };

    // 初始化和更新JSON字符串
    useEffect(() => {
        const result = processData(data);
        setJsonString(result.jsonString);
        setIsValid(result.isValid);
        setError(result.error);
    }, [data]);

    // 语法高亮处理
    const highlightJson = (jsonStr: string): string => {
        return jsonStr
            .replace(
                /("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+-]?\d+)?)/g,
                match => {
                    let cls = 'json-number';
                    if (/^"/.test(match)) {
                        if (/:$/.test(match)) {
                            cls = 'json-key';
                        } else {
                            cls = 'json-string';
                        }
                    } else if (/true|false/.test(match)) {
                        cls = 'json-boolean';
                    } else if (/null/.test(match)) {
                        cls = 'json-null';
                    }
                    return `<span class="${cls}">${match}</span>`;
                },
            )
            .replace(/([{}])/g, '<span class="json-bracket">$1</span>')
            .replace(/([[\\]])/g, '<span class="json-bracket">$1</span>')
            .replace(/(,)/g, '<span class="json-comma">$1</span>');
    };

    return (
        <div className="json-viewer">
            {title && <div className="json-viewer-title">{title}</div>}
            <div
                className={`json-viewer-container ${!isValid ? 'error' : ''}`}
                style={{ maxHeight: `${maxHeight}px` }}
            >
                <pre
                    className="json-content"
                    dangerouslySetInnerHTML={{
                        __html: highlightJson(jsonString),
                    }}
                />
                {!isValid && error && (
                    <div className="json-error">错误: {error}</div>
                )}
            </div>
        </div>
    );
};

export default JsonViewer;
