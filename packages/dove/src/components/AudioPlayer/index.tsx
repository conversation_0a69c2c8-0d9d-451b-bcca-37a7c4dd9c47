/**
 * 音频播放器
 */
import React, {
    useRef,
    useState,
    forwardRef,
    useImperativeHandle,
} from 'react';
import { Button, Slider } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined } from '@ant-design/icons';
import { formatMillisecond } from '@src/utils';

export interface AudioPlayerRefProps {
    play: (startTime?: number) => void;
}

interface Props {
    audioUrl?: string; // 音频URL
    talkingSeconds?: number; // 通话时长（秒）
}

const AudioPlayer = (props: Props, ref: React.Ref<AudioPlayerRefProps>) => {
    const { audioUrl, talkingSeconds } = props;
    const audioRef = useRef<HTMLAudioElement>(null);
    const [currentTime, setCurrentTime] = useState(0); // 当前播放到的时间点（毫秒）
    const [isPlaying, setIsPlaying] = useState(false); // 是否在播放中

    // 向上暴露内容
    useImperativeHandle(ref, () => ({
        play, // 手动播放方法
    }));

    // 更改播放时间点
    const handleTimeChange = (value: number) => {
        if (audioRef.current) {
            audioRef.current.currentTime = value / 1000;
            setCurrentTime(value);
        }
    };

    // 播放操作
    const play = (startTime?: number) => {
        if (audioRef.current) {
            if (startTime) {
                // 传值了就设置为当前播放时间点
                audioRef.current.currentTime = startTime / 1000;
                setCurrentTime(startTime);
            }
            audioRef.current.play();
            setIsPlaying(true);
        }
    };

    // 更新当前播放时间点
    const handleTimeUpdate = () => {
        if (audioRef.current) {
            setCurrentTime(Math.floor(audioRef.current.currentTime * 1000));
        }
    };

    // 切换播放状态
    const togglePlay = () => {
        if (audioRef.current) {
            if (isPlaying) {
                audioRef.current.pause();
            } else {
                audioRef.current.play();
            }
            setIsPlaying(!isPlaying);
        }
    };

    // 播放结束回调
    const handleAudioEnded = () => {
        setIsPlaying(false);
        setCurrentTime(0);
    };

    return (
        <div>
            <audio
                ref={audioRef}
                src={audioUrl}
                onTimeUpdate={handleTimeUpdate}
                onEnded={handleAudioEnded}
                style={{ display: 'none' }}
            />
            <div
                style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    marginBottom: '8px',
                }}
            >
                <Button
                    type="text"
                    icon={
                        isPlaying ? (
                            <PauseCircleOutlined />
                        ) : (
                            <PlayCircleOutlined />
                        )
                    }
                    onClick={togglePlay}
                >
                    {isPlaying ? '暂停' : '播放'}
                </Button>
                <span style={{ color: '#999' }}>
                    {formatMillisecond(currentTime)} /{' '}
                    {formatMillisecond(
                        talkingSeconds
                            ? talkingSeconds * 1000
                            : audioRef.current?.duration
                            ? Math.floor(audioRef.current.duration * 1000)
                            : 0,
                    )}
                </span>
            </div>

            <Slider
                value={currentTime}
                min={0}
                max={
                    audioRef.current?.duration
                        ? Math.floor(audioRef.current.duration * 1000)
                        : 100
                }
                onChange={handleTimeChange}
                tooltip={{
                    formatter: (value?: number) =>
                        value ? formatMillisecond(value) : '00:00',
                }}
            />
        </div>
    );
};

export default forwardRef<AudioPlayerRefProps, Props>(AudioPlayer);
