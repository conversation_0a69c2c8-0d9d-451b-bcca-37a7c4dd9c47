import { Select } from 'antd';
import { useEffect, useState } from 'react';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import type { SelectProps } from "antd";
import debounce from 'lodash/debounce';

interface SelectTaskTypeProps extends Omit<SelectProps, 'option'> {
    value?: string;
    onChange?: (value: string) => void;
    placeholder?: string;
}

const SelectTaskType: React.FC<SelectTaskTypeProps> = ({
    value,
    onChange,
    placeholder = '请选择任务类型',
    ...props
}) => {
    const [options, setOptions] = useState<{ label: string; value: string }[]>(
        [],
    );
    const [loading, setLoading] = useState(false);

    const fetchTaskTypes = async (searchText?: string) => {
        try {
            setLoading(true);
            const res = await apiCaller.post(
                '/xianfu/api-v2/dove/task/type/search',
                {
                    taskType: searchText || '',
                },
            );

            if (res.code === 0) {
                const taskTypes = res.data || [];
                setOptions(
                    taskTypes.map((type: string) => ({
                        label: type,
                        value: type,
                    })),
                );
            }
        } catch (error) {
            console.error('获取任务类型失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 初始加载
    useEffect(() => {
        fetchTaskTypes();
    }, []);

    // 搜索防抖
    const handleSearch = debounce((value: string) => {
        fetchTaskTypes(value);
    }, 300);

    return (
        <Select
            showSearch
            value={value}
            placeholder={placeholder}
            loading={loading}
            filterOption={false}
            onSearch={handleSearch}
            onChange={onChange}
            options={options}
            style={{ width: '100%' }}
            allowClear
            {...props}
        />
    );
};

export default SelectTaskType;
