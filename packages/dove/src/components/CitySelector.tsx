import axios from 'axios';
import React, { useEffect, useState } from 'react';
import CitySelectorRoo from '@roo/roo-plus/CitySelector';
import '@roo/roo-plus/theme/default/index.css';
import { CitySelector as CitySelectorProps } from '@roo/roo-plus/CitySelector/interface';

// 根据id获取城市信息，入参id为逗号分割的城市id
// 出参为城市名称，以sep分割，该方法主要用于其他地方的回显
export const getCityNameFromId = async (ids: string, sep = '、') => {
    const res = await axios.post(
        '/xianfu/api/permission/wmcrm/v1/gis/city-data/physical-city-option-box-tree-search',
        {
            cityIds: ids.split(','),
            tenanId: 1000008,
        },
    );
    if (res.data.responseStatus?.code !== 0) {
        return '';
    }
    try {
        const json = JSON.parse(res.data.cityTree);
        const ctn: string[] = [];
        const getRes = (arr, prefix, ctn) => {
            arr.forEach(e => {
                let path = e.name;
                if (prefix) {
                    path = prefix + '/' + e.name;
                }
                if (e.children?.length) {
                    getRes(e.children, path, ctn);
                } else {
                    ctn.push(path);
                }
            });
        };
        getRes(json, '', ctn);
        return ctn.join('，');
    } catch (e) {
        console.log(e);
        return '';
    }
};

// 根据父城市id获取城市列表
// outParam cityList 城市列表，入参为城市id，出参为城市名称
const fetchData = async (
    maxLevel,
    option: any = { value: 10000001 },
    callback = data => {},
) => {
    const res = await axios.post(
        '/xianfu/api/permission/wmcrm/v1/gis/city-data/physical-city-option-box-search',
        {
            parentCityId: option.value,
            tenanId: 1000008,
        },
    );
    let data: any[] = [];
    if (res.data.responseStatus.code === 0) {
        data = res.data?.boxCities;
    }
    callback(data);
    return data.map(d => ({
        ...d,
        leaf: d.level === maxLevel ? true : d.leaf,
    }));
};

// 根据城市id列表获取城市树
// outParam cityTree 城市树，入参为城市id，出参为城市名称
// withBrother 是否包含兄弟城市
// 该方法主要用于组件回显
const getCityTree = async (cityIds, withBrother = true) => {
    if (!cityIds.length) {
        return [];
    }
    const res = await axios.post(
        '/xianfu/api/permission/wmcrm/v1/gis/city-data/physical-city-option-box-tree-search',
        {
            cityIds,
            withBrother,
            tenanId: 1000008,
        },
    );
    if (res.data.responseStatus.code === 0) {
        try {
            return JSON.parse(res.data.cityTree);
        } catch (e) {
            return [];
        }
    }
    return [];
};
const CitySelector = ({
    value,
    onChange,
    minWidth = 400,
    maxLevel = 4,
    ...rest
}: Partial<Omit<CitySelectorProps, 'values' | 'onChange'>> & {
    value?: any;
    onChange?: (data: any) => void;
    minWidth?: number;
    maxLevel?: number;
}) => {
    const [options, setOptions] = useState<any[]>([]);
    useEffect(() => {
        (async () => {
            let draft: any[] = await fetchData(maxLevel);
            if (value?.length) {
                const cityTree = await getCityTree(value);
                draft = draft.map(d => {
                    const city = cityTree.find(v => v.cityId === d.cityId);
                    if (city) {
                        return city;
                    }
                    return d;
                });
            }
            setOptions(draft);
        })();
    }, []);
    return (
        <div style={{ minWidth  }}>
            <CitySelectorRoo
                fieldNames={{
                    label: 'name',
                    isLeaf: 'leaf',
                    value: 'cityId',
                }}
                layout="cascade"
                options={options}
                loadData={option => fetchData(maxLevel, option)}
                values={value}
                onChange={onChange}
                {...rest}
            />
        </div>
    );
};
export default CitySelector;
