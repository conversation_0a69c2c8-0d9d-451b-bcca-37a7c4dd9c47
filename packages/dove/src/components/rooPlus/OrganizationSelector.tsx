import OrganizationSelector from '@roo/roo-plus/OrganizationSelector';
import { OrganizationPickerProps } from '@roo/roo-plus/OrganizationPicker/interface';

import '@roo/roo/theme/default/index.css';
import '@roo/roo-plus/theme/default/index.css';
import { useEffect, useRef, useState } from 'react';
const prefix = '/xianfu/api/common';

interface CommonItem {
    childrenList: CommonItem[];
    id: number;
    isSelected: boolean;
    isManager: boolean;
}
const defaultAPI = {
    pidUrl: `${prefix}/uicomponent/api/orgs/getByPid`,
    searchUrl: `${prefix}/uicomponent/api/orgs/search`,
};

// TODO: 初始值选择
export default (
    props: Omit<OrganizationPickerProps, 'resetDisabled'> & {
        value?: number[] | number;
        onChange?: (v: number[] | number) => void;
        onInit?: (n?: number[] | number) => void;
    },
) => {
    const defaultAllOrgs = useRef<Set<number>>(new Set());
    const [defaultValue, setDefaultValue] = useState<number | number[]>();

    const dig = (item: CommonItem) => {
        defaultAllOrgs.current?.add(item.id);
        if (item.childrenList) {
            return item.childrenList.map(dig);
        }

        if (!item.isSelected && !item.isManager) {
            return;
        }

        return item.id;
    };

    // const transformDataList = (item: CommonItem) => {
    //     const childrenList = item.childrenList
    //         ? item.childrenList.map(transformDataList)
    //         : undefined;

    //     return {
    //         ...item,
    //         childrenList,
    //         disabled: defaultAllOrgs.current?.has(item.id),
    //     };
    // };

    const applyDefaultValue = (data: CommonItem[]) => {
        if (!data.length) {
            props.onInit?.(props.multiple ? [] : undefined);
            return;
        }
        const diggedIds = data.map(dig).flat(Infinity).filter(Boolean);

        const hasValue =
            props.value instanceof Array ? props.value.length : props.value;

        if (hasValue) {
            return;
        }
        if (diggedIds.some(v => v < 0)) {
            props.onInit?.(props.multiple ? [] : undefined);
            return;
        }

        const ids = props.multiple ? diggedIds : diggedIds[0];
        setDefaultValue(ids);
        props.onInit?.(ids);
    };

    const onConfirm = (
        // @ts-ignore
        ...args: Parameters<OrganizationPickerProps['onConfirm']>
    ) => {
        const ids = args[0];

        if (typeof ids === 'number' ? !ids : !ids?.length) {
            defaultValue && props.onChange?.(defaultValue);
            return;
        }
        props.onChange?.(ids);
        props.onConfirm?.(ids, args[1]);
    };

    return (
        <OrganizationSelector
            placeholder="请选择组织结构"
            key={
                typeof defaultValue === 'number'
                    ? defaultValue
                    : defaultValue?.join()
            }
            {...props}
            params={{
                ...props.params,
                // @ts-ignore
                backtrackOrgType: props.params?.backtrackOrgType || 3,
                sources:
                    // @ts-ignore
                    props.params?.sources ||
                    '1_4_5_6_10_11_12_13_16_17_18_21_23_35',
            }}
            {...defaultAPI}
            // resetDisabled
            // @ts-ignore
            onMountedFetchFn={applyDefaultValue}
            // transformDataList={dataList => {
            //     // @ts-ignore
            //     return dataList.map(transformDataList);
            // }}
            filterDisableFn={it => {
                const item = it as CommonItem;
                if (item.isSelected || item.isManager) {
                    return false;
                }
                // @ts-ignore
                return !item.editable;
            }}
            defaultValue={defaultValue}
            onConfirm={onConfirm}
        />
    );
};
