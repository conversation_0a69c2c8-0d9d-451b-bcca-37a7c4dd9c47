import { APISpec, apiCaller } from '@mfe/cc-api-caller-pc';
import RooPlusMisSearch from '@roo/roo-plus/MisSearch';
import { MisSearchProps } from '@roo/roo-plus/MisSearch/interface';
import { useEffect, useState } from 'react';

type User = APISpec['/uicomponent/employs']['response'][number];

const MisSearch = (
    props: MisSearchProps & {
        misId?: string;
        onInit?: MisSearchProps['onChange'];
    },
) => {
    const [defaultUser, setDefaultUser] = useState<User>();

    const fetchUser = async (mis?: string) => {
        if (!mis) {
            return;
        }

        const res = await apiCaller.get(
            '/uicomponent/employs',
            {
                content: mis,
                type: '1',
            },
            { silent: true },
        );

        // 是的，这个接口就是这么牛逼
        if (res.code !== 1) {
            return;
        }

        // @ts-ignore
        const users = res.data as User[];
        // 长度为1意为精确搜索并查找到
        if (!users || users.length !== 1) {
            return;
        }
        setDefaultUser(users[0]);
        // @ts-ignore
        props.onInit?.(users[0]);
    };
    const onChange = v => {
        props.onChange?.(v || {});
    };

    useEffect(() => {
        fetchUser(props.misId);
    }, [props.misId]);

    return (
        <RooPlusMisSearch
            url="/xianfu/api/common/uicomponent/employs"
            handleResponseData={res => {
                if (
                    props.url?.endsWith('/uicomponent/employs') &&
                    res.code === 1
                ) {
                    return res.data;
                }
                return res.data;
            }}
            placeholder={'请选择'}
            key={defaultUser?.id}
            // @ts-ignore
            defaultValue={defaultUser}
            {...props}
            onChange={onChange}
        />
    );
};

export default MisSearch;
