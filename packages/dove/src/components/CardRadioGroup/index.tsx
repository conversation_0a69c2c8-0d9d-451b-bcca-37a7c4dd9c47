/**
 * 卡片形式的 RadioGroup
 */
import { RadioGroupProps } from 'antd';
import styles from './index.module.scss';

interface CardRadioGroupProps extends Omit<RadioGroupProps, 'options'> {
    options: { label: string; value: any }[];
}

const CardRadioGroup = (props: CardRadioGroupProps) => {
    const { value, options, onChange, ...restProps } = props;

    return (
        <div className={styles.radioGroup} {...restProps}>
            {Array.isArray(options)
                ? options.map(item => {
                      const option =
                          typeof item === 'string'
                              ? { label: item, value: item }
                              : item;
                      const isSelected = value === option.value;

                      return (
                          <div
                              key={option.value}
                              className={`${styles.cardItem} ${
                                  isSelected ? styles.selected : ''
                              }`}
                              onClick={() => onChange?.(option.value)}
                          >
                              {option.label}
                          </div>
                      );
                  })
                : null}
        </div>
    );
};

export default CardRadioGroup;
