.radioGroup {
    display: flex;
    flex-wrap: wrap;
}

.cardItem {
    display: inline-block;
    padding: 6px 12px;
    margin: 0 8px 0 0;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    color: #333;
    font-size: 14px;
    position: relative;
    overflow: hidden;

    &:hover {
        border-color: #FF6A00;
        color: #FF6A00;
    }

    &.selected {
        border-color: #FF6A00;
        color: #FF6A00;

        &::after {
            content: '';
            position: absolute;
            right: 0;
            bottom: 0;
            width: 0;
            height: 0;
            border-style: solid;
            border-width: 0 0 20px 20px;
            border-color: transparent transparent #FF6A00 transparent;
        }

        &::before {
            content: '✓';
            position: absolute;
            right: 2px;
            bottom: -1px;
            color: #fff;
            font-size: 10px;
            z-index: 1;
        }
    }
}
