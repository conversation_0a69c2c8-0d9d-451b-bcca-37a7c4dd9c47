import React from "react";
import OrganizationSelector from '@roo/roo-plus/OrganizationSelector';
import { OrganizationPickerProps } from "@roo/roo-plus/OrganizationPicker/interface";
import '@roo/roo/theme/default/index.css';
import '@roo/roo-plus/theme/default/index.css';

interface RooOrganizationSelectorProps extends OrganizationPickerProps {
    onChange?: (val: any) => void
}

export default function RooOrganizationSelector({
    onChange = () => { },
    ...props
}: RooOrganizationSelectorProps) {
    const prefix = '/xianfu/api/common';
    return (
        <OrganizationSelector
            params={{
                backtrackOrgType: 3,
                sources: '1_4_5_6_10_11_12_13_16_17_18_21_23',
            }}
            onConfirm={v => onChange(v)}
            pidUrl={`${prefix}/uicomponent/api/orgs/getByPid`}
            searchUrl={`${prefix}/uicomponent/api/orgs/search`}
            placeholder="请选择创建人组织架构"
            {...props}
        />
    )

}