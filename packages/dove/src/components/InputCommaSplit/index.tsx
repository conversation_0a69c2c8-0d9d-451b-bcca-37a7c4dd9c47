import { Form, FormItemProps } from 'antd';
import Input, { InputProps } from 'antd/es/input';
import { useState } from 'react';

interface InputCommaSplit extends Omit<InputProps, 'value' | 'onChange'> {
    value?: number[];
    onChange?: (v: number[]) => void;
    formItemProps?: FormItemProps;
}

const reg = /^\d+(,\d+)*$/;

const InputCommaSplit = (props: InputCommaSplit) => {
    const { value = [], onChange } = props;
    const [error, setError] = useState(false);
    const [localValue, setLocalValue] = useState(value.join(','));

    const onInputChange = e => {
        const input = e.target.value || undefined;
        setLocalValue(input);
        const pass = input ? reg.test(input) : true;

        if (!pass) {
            onChange([]);
            setError(true);
            return;
        }

        onChange(input ? input.split(',').map(Number) : []);
        setError(false);
    };

    return (
        <Form.Item
            {...props.formItemProps}
            validateStatus={error ? 'error' : ''}
            help={
                /* eslint-disable @typescript-eslint/indent */
                error ? '请输入逗号分隔的数字' : undefined
                /* eslint-enable @typescript-eslint/indent */
            }
            style={{ marginBottom: 0 }}
        >
            <Input
                {...props}
                value={localValue}
                onChange={onInputChange}
                role="textbox"
            />
        </Form.Item>
    );
};

export default InputCommaSplit;
