import { useState } from 'react';
import { Select, Space } from 'antd';
import { useRequest } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import type { SelectProps } from 'antd';

interface ProcessParams {
    sceneId?: number | null;
    agentId?: number | null; //新接口使用的是agentId，数据还是sceneId保持上下游不变
    pageNum: number;
    pageSize: number;
    systemId?: number | null;
}

interface ProcessSelectProps extends Omit<SelectProps, 'options'> {
    params?: ProcessParams;
    searchAll?: boolean;
    [key: string]: any;
}

export default function ProcessSelect({
    params,
    searchAll = false,
    ...props
}: ProcessSelectProps) {
    const [processOptions, setProcessOptions] = useState<any[]>([]); // 【调度流程】可选项

    // 获取【调度流程】可选项
    const { loading } = useRequest(
        async () => {
            setProcessOptions([]);
            // 是否获取全量数据
            if (!searchAll && !params?.agentId) return { list: [], total: 0 };
            const preApiPath = '/xianfu/api-v2/ai-infra/schedule/process/query'; //旧接口, 支持全量数据查询
            const apiPath =
                '/xianfu/api-v2/ai-infra/schedule/process/query-by-agent'; //新接口

            const res: any = await apiCaller.post(
                // @ts-ignore
                searchAll ? preApiPath : apiPath,
                params,
            );
            if (res.code === 0) {
                const options = (res?.data?.data || [])
                    .filter(item => item.processStatus === 1)
                    .map(item => ({
                        ...item,
                        value: item.id,
                        label: item.processName,
                    }));
                setProcessOptions(options);
                return {
                    list: res?.data?.data || [],
                    total: res?.data?.total || 0,
                };
            }
            return { list: [], total: 0 };
        },
        {
            refreshDeps: [params, searchAll], // 依赖变化时自动触发请求
            debounceWait: 500,
        },
    );

    return (
        <Select
            allowClear
            options={processOptions}
            showSearch
            loading={loading} // 显示加载状态
            filterOption={(input, option) => {
                const query = String(input).toLowerCase();
                const label = String(option?.label || '').toLowerCase();
                return label.includes(query);
            }}
            placeholder="请选择调度流程"
            popupMatchSelectWidth={false}
            optionRender={option => (
                <Space
                    style={{ display: 'flex', justifyContent: 'space-between' }}
                >
                    <div
                        style={{
                            flex: 1,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            maxWidth: '400px',
                        }}
                    >
                        {option.label}
                    </div>
                    <span style={{ color: '#FF6A00', fontSize: 12 }}>
                        {option?.data?.creatorMis}
                    </span>
                </Space>
            )}
            {...props}
        />
    );
}
