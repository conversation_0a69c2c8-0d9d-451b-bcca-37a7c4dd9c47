import { TaskReachDetailProps } from './types';
import { ReachMethodEnum } from '@src/constants';
import AudioDrawer from './AudioDrawer';
import MassTextDrawer from './MassTextDrawer';

const TaskReachDetail = (props: TaskReachDetailProps) => {
    switch (props.contactType) {
        case ReachMethodEnum.AI_CALL:
        case ReachMethodEnum.TEXT_CALL:
        case ReachMethodEnum.OFFLINE_AUDIO:
            return <AudioDrawer {...props} />;
        case ReachMethodEnum.IM_MASS_SEND:
            return <MassTextDrawer {...props} />;

        default:
            return null;
    }
};

export default TaskReachDetail;
