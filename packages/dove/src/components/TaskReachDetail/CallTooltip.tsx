import React from 'react';
import { Tooltip } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { ReachMethodEnum } from '@src/constants';

interface CallTooltipProps {
    contactType?: number;
}

const CallTooltip: React.FC<CallTooltipProps> = ({ contactType }) => {
    if (contactType !== ReachMethodEnum.AI_CALL) {
        return null;
    }
    return (
        <Tooltip title="置灰部分为在通话中被用户打断而没有说出的内容">
            <QuestionCircleOutlined
                style={{
                    marginLeft: '8px',
                    color: '#999',
                    fontSize: '14px',
                }}
            />
        </Tooltip>
    );
};

export default CallTooltip;
