import { Drawer, Image, Typography } from 'antd';
import { TaskReachDetailProps } from './types';
import './massTextDrawer.scss';

const MassTextDrawer = (props: TaskReachDetailProps) => {
    const { massSendTitle, contactContent } = props;
    // avoid null
    const contactImages = props.contactImages || [];

    return (
        <Drawer
            title="沟通详情"
            open={props.visible}
            onClose={props.onClose}
            placement="right"
            width={500}
            className="mass-drawer"
        >
            <div
                style={{
                    maxWidth: '80%',
                    padding: '8px 12px',
                    borderRadius: '8px',
                    backgroundColor: '#1890ff',
                    marginLeft: 'auto',
                }}
            >
                <Typography.Paragraph
                    style={{
                        color: '#fff',
                    }}
                >
                    标题：{massSendTitle || '-'}
                </Typography.Paragraph>
                <Typography.Paragraph
                    style={{
                        color: '#fff',
                    }}
                >
                    内容：{contactContent}
                </Typography.Paragraph>
                {contactImages.length ? (
                    <>
                        <Typography.Paragraph
                            style={{
                                color: '#fff',
                            }}
                        >
                            图片：
                        </Typography.Paragraph>
                        <Image.PreviewGroup>
                            {contactImages.map(it => (
                                <Image
                                    className="mass-drawer-image"
                                    src={it}
                                    key={it}
                                    width={120}
                                />
                            ))}
                        </Image.PreviewGroup>
                    </>
                ) : null}
            </div>
        </Drawer>
    );
};

export default MassTextDrawer;
