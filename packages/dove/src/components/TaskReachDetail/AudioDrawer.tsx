import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>er, <PERSON>ton, Spin, Result, Image, Flex } from 'antd';
import AudioPlayer, { AudioPlayerRefProps } from '../AudioPlayer';
import AudioTalkWindow from '../AudioTalkWindow';
import CallTooltip from '@src/components/TaskReachDetail/CallTooltip';
import EmptyImage from '@src/assets/img/empty.png';
import { TaskReachDetailProps } from './types';
import './audioDrawer.scss';

export interface DialogueText {
    startTime: number;
    endTime: number;
    speaker: number;
    text: string;
}

export interface TextResponse {
    code: number;
    msg: string;
    data: DialogueText[];
}

const AudioDrawer: React.FC<TaskReachDetailProps> = ({
    visible,
    onClose,
    audioUrl,
    contactId,
    textPath = `${import.meta.env.VITE_API_PREFIX}/doveauto/data/text`,
    talkingSeconds,
    ...props
}) => {
    const [dialogues, setDialogues] = useState<DialogueText[]>([]);
    const audioRef = useRef<AudioPlayerRefProps>(null);

    useEffect(() => {
        if (contactId && visible) {
            fetchDialogues();
        }
    }, [contactId, visible]);

    const [loading, setLoading] = useState(false);
    const [failed, setFailed] = useState(false);
    const [errorMsg, setErrorMsg] = useState('');
    const fetchDialogues = async () => {
        setLoading(true);
        setFailed(false);
        setErrorMsg('');
        try {
            const response = await fetch(`${textPath}?contactId=${contactId}`);
            const result: TextResponse = await response.json();
            if (result.code === 0) {
                setDialogues(result.data);
            } else {
                setFailed(true);
                setErrorMsg(result.msg || '获取对话内容失败');
            }
            setLoading(false);
        } catch (error) {
            setErrorMsg('请求失败');
            setFailed(true);
            setLoading(false);
        }
    };

    const handleBubbleClick = (startTime: number) => {
        if (audioRef.current && startTime) {
            audioRef.current.play(startTime);
        }
    };

    return (
        <Drawer
            title={
                <span>
                    沟通详情
                    <CallTooltip contactType={props?.contactType} />
                </span>
            }
            placement="right"
            width={500}
            onClose={onClose}
            open={visible}
            className="audio-drawer"
        >
            <Spin spinning={loading} style={{ height: '100%' }}>
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        height: '100%',
                    }}
                >
                    <div
                        style={{
                            flex: 1,
                            overflowY: 'auto',
                            marginBottom: '16px',
                            display: 'flex',
                            flexDirection: 'column',
                        }}
                    >
                        {!loading && failed && (
                            <Flex
                                style={{
                                    flex: 1,
                                }}
                                vertical
                                justify="center"
                            >
                                <Result
                                    status={'error'}
                                    icon={
                                        <Image
                                            src={EmptyImage}
                                            preview={false}
                                            width={200}
                                        />
                                    }
                                    subTitle={errorMsg}
                                    extra={
                                        <Button
                                            type="primary"
                                            onClick={fetchDialogues}
                                        >
                                            刷新
                                        </Button>
                                    }
                                />
                            </Flex>
                        )}
                        <AudioTalkWindow
                            talkDatas={dialogues}
                            onBubbleClick={handleBubbleClick}
                        />
                    </div>
                    <div
                        style={{
                            padding: '16px',
                            borderTop: '1px solid #f0f0f0',
                            backgroundColor: '#fff',
                        }}
                    >
                        <AudioPlayer
                            ref={audioRef}
                            audioUrl={audioUrl}
                            talkingSeconds={talkingSeconds}
                        />
                    </div>
                </div>
            </Spin>
        </Drawer>
    );
};

export default AudioDrawer;
