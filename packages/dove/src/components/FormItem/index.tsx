/**
 * 对 antd 的 Form.Item 组件的封装
 * 允许在 children 位置放置渲染函数，从函数中可解构出 value/onChange/disabled 等参数，根据需要（比如页面类型，新建/编辑/查看等）渲染不同的组件，实现编辑态/查看态
 * 原版的 Form.Item 组件，children 位置的函数只能得到表单实例 form，获取 value 等很困难。
 */
import React from 'react';
import { Form } from 'antd';
import { Props } from './types';

const FormItem = (props: Props) => {
    const { children = null, ...restProps } = props;

    return (
        <Form.Item {...restProps}>
            {typeof children === 'function' ? (
                <FormItemContent renderFn={children} />
            ) : (
                children
            )}
        </Form.Item>
    );
};

const FormItemContent = ({
    renderFn,
    ...restProps
}: {
    renderFn: (props: any) => React.ReactNode;
}) => {
    return renderFn(restProps);
};

export default FormItem;
