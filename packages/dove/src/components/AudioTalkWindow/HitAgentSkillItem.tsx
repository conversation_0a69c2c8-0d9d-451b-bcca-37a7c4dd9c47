import JsonViewer from '@src/components/JsonViewer';
import './index.scss';

export type SkillPool = {
    skillId: number; // 技能ID
    skillName: string;
    skillDescription: string;
    apiType: number;
    input: string; // 输入参数，Json字符串
    outPut: string; // 输出参数，Json字符串
};

export default function HitAgentSkillItem({ skill }: { skill?: SkillPool }) {
    return (
        <div className="talk-skill-item">
            <div className="talk-skill-item-title">
                {skill?.skillName || '技能'}
            </div>
            <div className="talk-skill-item-desc">
                {skill?.skillDescription || '技能'}
            </div>
            <div className="talk-skill-item-section">
                <JsonViewer
                    title="输入参数："
                    data={skill?.input || {}}
                    maxHeight={150}
                />
            </div>
            <div className="talk-skill-item-section">
                <JsonViewer
                    title="输出结果："
                    data={skill?.outPut || {}}
                    maxHeight={150}
                />
            </div>
        </div>
    );
}
