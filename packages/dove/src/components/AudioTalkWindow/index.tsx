/**
 * 音频对话文本展示组件
 */
import { useState } from 'react';
import { UpOutlined, DownOutlined } from '@ant-design/icons';
import { formatMillisecond } from '@src/utils';
import HitAgentSkillItem, { SkillPool } from './HitAgentSkillItem';
import './index.scss';

interface Props {
    talkDatas: TalkData[]; // 对话文本数据
    speakerNames?: string[]; // 说话者名称对应
    onBubbleClick?: (startTime: number) => void; // 点击气泡回调
}
interface TalkData {
    [key: string]: any;
    speaker: number; // 说话者身份
    startTime: number; // 说话开始时间
    endTime: number; // 说话结束时间
    text: string; // 说话内容（全量文本）
    realText: string; // 实际说话内容（可能经过处理）
    skillExecuteResult?: SkillPool[]; //命中技能
}

const AudioTalkWindow = (props: Props) => {
    const { talkDatas, speakerNames, onBubbleClick } = props;
    const [expandedSkills, setExpandedSkills] = useState<
        Record<number, boolean>
    >({});

    /**
     * 计算文本置灰的字符数
     * @param text 原始文本
     * @param realText 实际文本
     * @returns 需要置灰的字符数
     */
    const calculateGrayedCharCount = ({ text, realText }: TalkData): number => {
        if (!text) return 0;
        if (!realText) return text.length;
        const grayedCount = text.length - realText.length;
        return Math.max(0, Math.min(grayedCount, text.length));
    };

    /**
     * 渲染带置灰效果的文本
     * @param text 原始文本
     * @param grayedCount 需要置灰的字符数
     * @returns JSX元素
     */
    const renderTextWithGray = (item: TalkData) => {
        const { text } = item;
        const grayedCount = calculateGrayedCharCount(item);
        if (grayedCount <= 0) {
            return <span>{text}</span>;
        }

        if (grayedCount >= text.length) {
            return <span className="grayed-text">{text}</span>;
        }

        const normalText = text.slice(0, text.length - grayedCount);
        const grayedText = text.slice(text.length - grayedCount);

        return (
            <span>
                {normalText}
                <span className="grayed-text">{grayedText}</span>
            </span>
        );
    };

    return (
        <div className="audio-talk-window">
            {Array.isArray(talkDatas)
                ? talkDatas.map((item, index) => (
                      <div
                          key={String(index)}
                          className={`talk-line speaker-${item.speaker}`}
                      >
                          <div className="talk-title-line">
                              {speakerNames && speakerNames[item.speaker] && (
                                  <span className="speaker">
                                      {speakerNames[item.speaker]}
                                  </span>
                              )}
                              <span className="time">
                                  {formatMillisecond(item.startTime)}
                              </span>
                          </div>
                          <div
                              className="talk-line-text"
                              onClick={() => onBubbleClick?.(item.startTime)}
                          >
                              {item.text ? renderTextWithGray(item) : '-'}
                              {/* 命中技能展示 */}
                              {item?.skillExecuteResult?.length ? (
                                  <div className="talk-line-skill">
                                      <div className="talk-line-skill-header">
                                          <span className="talk-line-skill-header-label">
                                              命中
                                              {item.skillExecuteResult.length}
                                              个技能：
                                          </span>
                                          {item.skillExecuteResult?.map(
                                              (skill: SkillPool) => (
                                                  <span
                                                      className="talk-line-skill-header-name"
                                                      key={skill.skillName}
                                                  >
                                                      {skill.skillName}
                                                  </span>
                                              ),
                                          )}
                                      </div>
                                      <div
                                          className="talk-line-skill-toggle"
                                          onClick={() =>
                                              setExpandedSkills(prev => ({
                                                  ...prev,
                                                  [index]: !prev[index],
                                              }))
                                          }
                                      >
                                          {expandedSkills[index] ? (
                                              <>
                                                  收起详情
                                                  <UpOutlined className="talk-line-skill-toggle-icon" />
                                              </>
                                          ) : (
                                              <>
                                                  查看详情
                                                  <DownOutlined className="talk-line-skill-toggle-icon" />
                                              </>
                                          )}
                                      </div>
                                      {expandedSkills[index] && (
                                          <div className="talk-line-skill-content">
                                              {item.skillExecuteResult?.map(
                                                  skill => (
                                                      <HitAgentSkillItem
                                                          skill={skill}
                                                      />
                                                  ),
                                              )}
                                          </div>
                                      )}
                                  </div>
                              ) : null}
                          </div>
                      </div>
                  ))
                : null}
        </div>
    );
};

export default AudioTalkWindow;
