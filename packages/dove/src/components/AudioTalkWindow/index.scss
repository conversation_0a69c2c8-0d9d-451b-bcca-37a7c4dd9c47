.audio-talk-window {
    .talk-line {
        margin-bottom: 16px;
        display: flex;
        flex-direction: column;

        &.speaker-0 {
            align-items: flex-end;

            .talk-line-text {
                background-color: rgb(73, 117, 235);
                color: #fff;
                // background-color: #FFDD00;
                // color: #222;
            }
        }

        &.speaker-1 {
            align-items: flex-start;

            .talk-line-text {
                background-color: rgb(231, 231, 231);
                color: #000;
            }
        }

        .talk-title-line {
            .speaker {
                font-size: 12px;
                color: #000;
                margin-right: 8px;
            }

            .time {
                font-size: 12px;
                color: #999;
            }
        }

        .talk-line-text {
            max-width: 80%;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;

            .grayed-text {
                color: rgb(200, 200, 200);
            }
        }

        .talk-line-skill {
            padding: 8px 0px;
            border-top: 0.5px solid #eee;
            color: #fff;
            margin-top: 10px;

            &-header {
                margin-bottom: 6px;
                display: flex;
                align-items: center;
                flex-wrap: wrap;

                &-label {
                    font-size: 12px;
                    margin-right: 4px;
                }

                &-name {
                    font-size: 12px;
                    color: rgb(87, 136, 122);
                    background: #fff;
                    padding: 4px 6px;
                    border-radius: 12px;
                    margin: 6px 6px 0 0;
                }

            }

            &-toggle {
                font-size: 12px;
                cursor: pointer;
                user-select: none;
                color: rgba(255, 255, 255, 0.8);

                &-icon {
                    margin-left: 6px;
                    font-size: 10px;
                }
            }

            &-content {
                margin-top: 8px;
                display: grid;
                gap: 10px;
            }
        }
    }
}

.talk-skill-item {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 8px 12px;

    &-title {
        font-size: 14px;
        color: #fff;
        margin-bottom: 4px;
    }

    &-desc {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 4px;
    }

    &-section {
        margin-bottom: 12px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .json-viewer {
        padding: 0px;
    }
}