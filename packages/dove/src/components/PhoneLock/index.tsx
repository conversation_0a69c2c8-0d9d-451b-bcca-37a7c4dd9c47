/**
 * 电话号码加密/解密组件
 * 仅前端展示加密
 */
import React, { useState, useEffect } from 'react';
import { Button, Space } from 'antd';
import { encryptMiddleFourDigits } from '@src/utils/utils';

interface Props {
    phoneNumber: string; // 真实电话号码
    btnText?: React.ReactNode; // 解密按钮文案
}

const PhoneLock = (props: Props) => {
    const { phoneNumber, btnText = '解密' } = props;
    const [isLocked, setIsLocked] = useState(true); // 是否加密状态
    const [showStr, setShowStr] = useState('-'); // 展示的电话号码

    // 上层电话号码变化，触发制作展示文案
    useEffect(() => {
        if (!phoneNumber) {
            setShowStr('-');
            setIsLocked(false);
        } else {
            setShowStr(encryptMiddleFourDigits(phoneNumber));
            setIsLocked(true);
        }
    }, [phoneNumber]);

    // 解密事件
    const onUnlock = () => {
        setShowStr(phoneNumber);
        setIsLocked(false);
    };

    return (
        <Space size="small">
            <span>{showStr}</span>
            {isLocked && (
                <Button size="small" type="link" onClick={onUnlock}>
                    {btnText}
                </Button>
            )}
        </Space>
    );
};

export default PhoneLock;
