import React, { useEffect, useState } from 'react';
import { Cascader, CascaderProps } from 'antd';
import axios from 'axios';

interface CityInfo {
    provinceId: number;
    provinceName: string;
    cityId: number;
    cityName: string;
}

interface CitySelectorProps
    extends Omit<CascaderProps, 'multiple' | 'value' | 'defaultValue'> {
    value?: any;
    // onChange?: (value: CityInfo) => void;
    maxLevel?: 1 | 2;
    minWidth?: number;
    multiple?: true;
    onlyLeafValue?: boolean;
}

const CitySelectorAntd: React.FC<CitySelectorProps> = ({
    value,
    onChange,
    maxLevel = 2,
    minWidth,
    placeholder = '请选择城市',
    multiple,
    onlyLeafValue,
    ...rest
}) => {
    const [options, setOptions] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);

    const fetchData = async (parentCityId: number) => {
        setLoading(true);
        try {
            const res = await axios.post(
                '/xianfu/api/permission/wmcrm/v1/gis/city-data/physical-city-option-box-search',
                {
                    parentCityId,
                    tenanId: 1000008,
                },
            );
            if (res.data.responseStatus.code === 0) {
                return res.data.boxCities.map((item: any) => ({
                    value: item.cityId,
                    label: item.name,
                    isLeaf: item.level === maxLevel,
                }));
            }
            return [];
        } finally {
            setLoading(false);
        }
    };

    const loadData = async (selectedOptions: any[]) => {
        const targetOption = selectedOptions[selectedOptions.length - 1];
        targetOption.loading = true;
        const children = await fetchData(targetOption.value);
        targetOption.loading = false;
        targetOption.children = children;
        setOptions([...options]);
    };

    useEffect(() => {
        (async () => {
            const initialOptions = await fetchData(10000001);
            setOptions(initialOptions);
        })();
    }, []);

    return (
        <Cascader
            allowClear={true}
            options={options}
            loadData={loadData}
            onChange={async value => {
                if (!multiple && value?.length >= 1 && onChange) {
                    const [provinceId, cityId] = value;
                    const province = options.find(
                        opt => opt.value === provinceId,
                    );
                    const city = province?.children?.find(
                        child => child.value === cityId,
                    );
                    // @ts-ignore
                    onChange({
                        provinceId,
                        provinceName: province?.label,
                        cityId: maxLevel === 2 ? cityId : -1,
                        cityName: maxLevel === 2 ? city?.label : '全部',
                    });
                    return;
                }
                if (multiple && value?.length >= 1 && onChange) {
                    const res = (
                        await Promise.all(
                            value.map(async val => {
                                const [provinceId, cityId] = val;
                                const province = options.find(
                                    opt => opt.value === provinceId,
                                );
                                if (!province?.children) {
                                    await loadData([province]);
                                }
                                let citys = province?.children?.filter(
                                    child => child.value === cityId,
                                );
                                if (!cityId) {
                                    citys = province?.children;
                                }
                                return (citys || [])?.map(city => ({
                                    provinceId,
                                    provinceName: province?.label,
                                    cityId: maxLevel === 2 ? city.value : -1,
                                    cityName:
                                        maxLevel === 2 ? city?.label : '全部',
                                }));
                            }),
                        )
                    ).reduce((a, b) => [...a, ...b], []);
                    // @ts-ignore
                    onChange(res);
                    return;
                }
                // @ts-ignore
                onChange(undefined);
            }}
            placeholder={placeholder}
            style={{ width: minWidth }}
            loading={loading}
            value={value}
            displayRender={labels => labels.join(' / ')}
            multiple={multiple}
            {...rest}
        />
    );
};

export default CitySelectorAntd;
