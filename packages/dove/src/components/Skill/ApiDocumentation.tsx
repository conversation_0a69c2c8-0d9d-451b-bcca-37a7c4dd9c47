import React, { useState, useMemo } from 'react';
import { Table, Tag, Typography, Space } from 'antd';
import type { ColumnsType } from 'antd/es/table';

const { Text, Paragraph } = Typography;

// 定义数据结构类型
interface ApiField {
    key: string;
    name: string;
    type: string;
    description: string;
    required: boolean;
    defaultValue?: string;
    enumValues?: string[];
    children?: ApiField[];
    level?: number;
}

// 原始参数数据类型
interface ArgumentItem {
    cursor: string;
    name: string;
    required: boolean;
    type: string;
    defaultValue?: string;
    enumValues?: string[];
}

// 组件 Props 类型
interface ApiDocumentationProps {
    request?: ArgumentItem[];
    response?: ArgumentItem[];
    title?: string;
}

// 数据转换函数
function transformArgumentListToApiFields(
    argumentList: ArgumentItem[],
): ApiField[] {
    if (!argumentList || argumentList.length === 0) return [];

    const result: ApiField[] = [];
    const nodeMap = new Map<string, ApiField>();

    // 为每个参数创建节点
    argumentList.forEach((arg, index) => {
        const cursor = arg.cursor.replace(/^\$\.?/, ''); // 移除开头的 $ 或 $.
        const pathParts = cursor.split('.');

        // 为路径中的每一级创建节点
        for (let i = 0; i < pathParts.length; i++) {
            const currentPath = pathParts.slice(0, i + 1).join('.');
            const parentPath = i > 0 ? pathParts.slice(0, i).join('.') : '';
            const fieldName = pathParts[i];

            if (!nodeMap.has(currentPath)) {
                const isLeafNode = i === pathParts.length - 1;

                const node: ApiField = {
                    key: currentPath || `root-${index}`,
                    name: fieldName,
                    type: isLeafNode ? arg.type : 'object',
                    description: isLeafNode ? arg.name : `${fieldName}对象`,
                    required: isLeafNode ? arg.required : true,
                    defaultValue: isLeafNode ? arg.defaultValue : undefined,
                    enumValues: isLeafNode ? arg.enumValues : undefined,
                    level: i,
                    children: [],
                };

                nodeMap.set(currentPath, node);

                // 将节点添加到父节点的children中
                if (parentPath && nodeMap.has(parentPath)) {
                    const parentNode = nodeMap.get(parentPath)!;
                    if (!parentNode.children) {
                        parentNode.children = [];
                    }
                    parentNode.children.push(node);
                } else if (i === 0) {
                    // 根级节点
                    result.push(node);
                }
            }
        }
    });

    // 清理空的children数组
    const cleanupEmptyChildren = (nodes: ApiField[]) => {
        nodes.forEach(node => {
            if (node.children && node.children.length === 0) {
                delete node.children;
            } else if (node.children) {
                cleanupEmptyChildren(node.children);
            }
        });
    };

    cleanupEmptyChildren(result);
    return result;
}

const ApiTable: React.FC<{ data: ApiField[]; title?: string }> = ({
    data,
    title,
}) => {
    const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);

    const columns: ColumnsType<ApiField> = [
        {
            title: '字段名',
            dataIndex: 'name',
            key: 'name',
            width: 200,
            render: (text: string, record: ApiField) => {
                const indent = (record.level || 0) * 20;

                return (
                    <div
                        style={{
                            paddingLeft: indent,
                            display: 'flex',
                            alignItems: 'center',
                        }}
                    >
                        <Text code strong={record.level === 0}>
                            {text}
                        </Text>
                    </div>
                );
            },
        },
        {
            title: '类型',
            dataIndex: 'type',
            key: 'type',
            width: 120,
            render: (type: string) => {
                const getTypeColor = (type: string) => {
                    const colorMap: Record<string, string> = {
                        string: 'blue',
                        number: 'green',
                        boolean: 'orange',
                        object: 'purple',
                        array: 'cyan',
                        enum: 'magenta',
                    };
                    return colorMap[type.split('<')[0]] || 'default';
                };

                return <Tag color={getTypeColor(type)}>{type}</Tag>;
            },
        },
        {
            title: '必填',
            dataIndex: 'required',
            key: 'required',
            width: 80,
            align: 'center',
            render: (required: boolean) => (
                <Tag color={required ? 'red' : 'default'}>
                    {required ? '是' : '否'}
                </Tag>
            ),
        },
        // {
        //     title: '默认值',
        //     dataIndex: 'defaultValue',
        //     key: 'defaultValue',
        //     width: 120,
        //     render: (value?: string) =>
        //         value ? (
        //             <Text code>{value}</Text>
        //         ) : (
        //             <Text type="secondary">-</Text>
        //         ),
        // },
        // {
        //     title: '枚举值',
        //     dataIndex: 'enumValues',
        //     key: 'enumValues',
        //     width: 200,
        //     render: (enumValues?: string[]) =>
        //         enumValues ? (
        //             <Space wrap>
        //                 {enumValues.map(value => (
        //                     <Tag key={value} color="geekblue">
        //                         {value}
        //                     </Tag>
        //                 ))}
        //             </Space>
        //         ) : (
        //             <Text type="secondary">-</Text>
        //         ),
        // },
        {
            title: '描述',
            dataIndex: 'description',
            key: 'description',
            render: (description: string) => (
                <Paragraph
                    style={{ margin: 0, maxWidth: 300 }}
                    ellipsis={{ rows: 2, tooltip: description }}
                >
                    {description}
                </Paragraph>
            ),
        },
    ];

    if (!data || data.length === 0) {
        return (
            <div style={{ marginBottom: 24 }}>
                <Typography.Title level={4} style={{ marginBottom: 16 }}>
                    {title}
                </Typography.Title>
                <div
                    style={{ padding: 20, textAlign: 'center', color: '#999' }}
                >
                    暂无数据
                </div>
            </div>
        );
    }

    return (
        <div style={{ marginBottom: 24 }}>
            <Typography.Title level={4} style={{ marginBottom: 16 }}>
                {title}
            </Typography.Title>
            <Table
                columns={columns}
                dataSource={data}
                pagination={false}
                size="small"
                bordered
                rowKey="key"
                scroll={{ x: 1000 }}
                expandable={{
                    expandedRowKeys,
                    onExpandedRowsChange: setExpandedRowKeys as any,
                    childrenColumnName: 'children',
                    indentSize: 0, // 因为我们在 render 中自定义了缩进
                }}
            />
        </div>
    );
};

// 主组件
const ApiDocumentation: React.FC<ApiDocumentationProps> = ({
    request,
    response,
}) => {
    // 使用 useMemo 优化转换性能
    const requestData = useMemo(() => {
        return request ? transformArgumentListToApiFields(request) : [];
    }, [request]);

    const responseData = useMemo(() => {
        return response ? transformArgumentListToApiFields(response) : [];
    }, [response]);

    return (
        <div>
            {request && request.length > 0 && <ApiTable data={requestData} />}
            {response && response.length > 0 && (
                <ApiTable data={responseData} title="出参信息" />
            )}
            {(!request || request.length === 0) &&
                (!response || response.length === 0) && (
                    <div
                        style={{
                            textAlign: 'center',
                            padding: 40,
                            color: '#999',
                        }}
                    >
                        暂无参数数据
                    </div>
                )}
        </div>
    );
};

export default ApiDocumentation;
