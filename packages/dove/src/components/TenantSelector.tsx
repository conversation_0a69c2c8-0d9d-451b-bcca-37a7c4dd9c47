import React, { useEffect, useState } from 'react';
import { Select } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import type { SelectProps } from 'antd';

interface TenantItem {
    jupiterTenantId: string;
    jupiterTenantName: string;
}

interface TenantSelectorProps extends Omit<SelectProps, 'options'> {
    value?: string;
    onChange?: (value: string) => void;
    defaultSelectFirst?: boolean;
}

const TenantSelector: React.FC<TenantSelectorProps> = ({ 
    value, 
    onChange, 
    defaultSelectFirst = false,
    ...props 
}) => {
    const [tenantList, setTenantList] = useState<TenantItem[]>([]);
    const [loading, setLoading] = useState(false);

    const fetchTenantList = async () => {
        setLoading(true);
        try {
            // @ts-ignore
            const res: any = await apiCaller.get('/xianfu/api-v2/dove/display/number/pool/tenant/query', {});
            if (res.code === 0 && Array.isArray(res.data)) {
                setTenantList(res.data);
                // 如果设置了默认选中第一个且当前没有选中值，则选中第一个选项
                if (defaultSelectFirst && !value && res.data.length > 0) {
                    const firstTenantId = res.data[0].jupiterTenantId;
                    onChange?.(firstTenantId);
                }
            }
        } catch (error) {
            console.error('Fetch tenant list failed:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchTenantList();
    }, []);

    const options = tenantList.map(item => ({
        label: item.jupiterTenantName,
        value: item.jupiterTenantId,
    }));

    return (
        <Select
            {...props}
            loading={loading}
            value={value}
            onChange={onChange}
            options={options}
            placeholder="请选择租户"
            maxTagCount="responsive"
        />
    );
};

export default TenantSelector;
