import React, { useState, useEffect } from 'react';
import { Select, SelectProps, Spin } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useDebounceFn } from 'ahooks';

interface SceneTemplateOption {
  label: string;
  value: string;
  key: number | string;
}

interface SceneTemplateSelectProps extends SelectProps {
  value?: string | string[];
  onChange?: (value: string | string[]) => void;
  mode?: 'multiple' | 'tags';
  placeholder?: string;
  allowClear?: boolean;
}

const SceneTemplateSelect: React.FC<SceneTemplateSelectProps> = ({
  value,
  onChange,
  mode,
  placeholder = '请选择场景模板',
  allowClear = true,
  ...restProps
}) => {
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<SceneTemplateOption[]>([]);
  const [keyword, setKeyword] = useState('');
  const [page, setPage] = useState(1);
  const [pageSize] = useState(20);
  const [hasMore, setHasMore] = useState(true);
  const [fetching, setFetching] = useState(false);

  // 获取场景模板列表
  const fetchSceneTemplates = async (
    searchText: string = '',
    currentPage: number = 1,
    isLoadMore: boolean = false
  ) => {
    setFetching(true);
    try {
      const res = await apiCaller.post(
        '/xianfu/api-v2/dove/agent/scene/list' as any,
        {
          page: currentPage,
          pageSize: pageSize,
          keyword: searchText,
        }
      );
      
      if (res.code === 0) {
        const data = Array.isArray(res.data) ? res.data : [];
        const newOptions = data.map((sceneName: string, index: number) => ({
          label: sceneName,
          value: sceneName,
          key: `${currentPage}_${index}`,
        }));
        
        // 如果是加载更多，则合并数据，否则替换数据
        if (isLoadMore) {
          setOptions(prev => [...prev, ...newOptions]);
        } else {
          setOptions(newOptions);
        }

        // 判断是否还有更多数据
        setHasMore(Array.isArray(data) && data.length === pageSize);
      } else {
        console.error('获取场景模板列表失败:', res.msg);
      }
    } catch (error) {
      console.error('获取场景模板列表失败:', error);
    } finally {
      setLoading(false);
      setFetching(false);
    }
  };

  // 初始加载
  useEffect(() => {
    setLoading(true);
    fetchSceneTemplates();
  }, []);

  // 搜索防抖
  const { run: handleSearch } = useDebounceFn(
    (searchText: string) => {
      setKeyword(searchText);
      setPage(1);
      fetchSceneTemplates(searchText, 1);
    },
    {
      wait: 300,
    },
  );

  // 加载更多
  const handlePopupScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { currentTarget } = e;
    if (
      !fetching &&
      hasMore &&
      currentTarget.scrollTop + currentTarget.clientHeight >=
      currentTarget.scrollHeight - 50
    ) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchSceneTemplates(keyword, nextPage, true);
    }
  };

  // 处理选择变更
  const handleChange = (selectedValue: string | string[]) => {
    onChange?.(selectedValue);
  };

  // 下拉菜单内容
  const dropdownRender = (menu: React.ReactElement) => (
    <div>
      {menu}
      {fetching && (
        <div style={{ textAlign: 'center', padding: '8px 0' }}>
          <Spin size="small" />
        </div>
      )}
    </div>
  );

  return (
    <Select
      showSearch
      allowClear={allowClear}
      value={value}
      placeholder={placeholder}
      loading={loading}
      mode={mode}
      filterOption={false}
      onSearch={handleSearch}
      onChange={handleChange}
      style={{ width: '100%' }}
      options={options}
      onPopupScroll={handlePopupScroll}
      dropdownRender={dropdownRender}
      {...restProps}
    />
  );
};

export default SceneTemplateSelect; 