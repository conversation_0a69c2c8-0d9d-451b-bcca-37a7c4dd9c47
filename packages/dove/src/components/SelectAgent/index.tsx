import { Select, Space } from 'antd';
import { useEffect, useState } from 'react';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import type { SelectProps } from 'antd';

interface AgentItem {
    id: number;
    name: string;
    description: string;
    status: number;
    owner?: any;
}

interface SelectAgentProps extends Omit<SelectProps, 'options'> {
    value?: number;
    onChange?: (value: number) => void;
    onDataChange?: (data: any) => void;
    isNewAgentUrl?: boolean;
}

const SelectAgent: React.FC<SelectAgentProps> = ({
    value,
    onChange,
    onDataChange,
    isNewAgentUrl = false, // 是否使用新接口
    ...restProps
}) => {
    const [loading, setLoading] = useState(false);
    const [options, setOptions] = useState<{ label: string; value: number }[]>(
        [],
    );

    const fetchAgents = async () => {
        try {
            setLoading(true);
            const allUrl = '/xianfu/api-v2/dove/agent/query';
            const newUrl = '/xianfu/api-v2/dove/scheduling/query-agent';

            const queryParams: any = {
                page: 1,
                pageSize: 1000,
                status: [1], // 只查询启用状态的 agent
                forUse: true,
            };

            const res = await apiCaller.post(
                // @ts-ignore
                allUrl,
                queryParams,
            );

            let newRes: any = null;
            if (isNewAgentUrl) {
                newRes = await apiCaller.post(
                    // @ts-ignore
                    newUrl,
                    queryParams,
                );
            }

            if (!isNewAgentUrl) {
                if (res.code === 0) {
                    const agentOptions = res.data.data.map((item: AgentItem) => ({
                        ...item,
                        label: item.name,
                        value: item.id,
                        title: item.description || item.name, // 使用 description 作为 tooltip，如果没有则使用 name
                    }));
                    setOptions(agentOptions);
                }
            } else {
                if (res.code === 0 && newRes?.code === 0) {
                    const newOptions: any = [];
                    res.data.data.forEach((item: AgentItem) => {
                        const newItem = newRes.data.data.find(
                            (newItem: AgentItem) => newItem.id === item.id,
                        );
                        if (newItem) {
                            newOptions.push({
                                ...item,
                                label: item.name,
                                value: item.id,
                                title: item.description || item.name, // 使用 description 作为 tooltip，如果没有则使用 name
                            });
                        } else {
                            newOptions.push({
                                ...item,
                                label: item.name,
                                value: item.id,
                                title: item.description || item.name, // 使用 description 作为 tooltip，如果没有则使用 name
                                disabled: true, // 如果新数据中不存在，则添加 disabled 属性
                            });
                        }
                    });
                    setOptions(newOptions);
                }
            }
        } catch (error) {
            console.error('获取 agent 列表失败:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchAgents();
    }, []);

    return (
        <Select
            loading={loading}
            value={value}
            onChange={v => {
                onChange?.(v);
                onDataChange?.(options.find(item => item.value === v));
            }}
            placeholder="请选择 agent"
            popupMatchSelectWidth={false}
            options={options}
            optionRender={(option) => (
                <Space style={{ display: 'flex', justifyContent: 'space-between' }}>
                    {option.label}
                    <span style={{ color: '#FF6A00', fontSize: 12 }}>{option?.data?.owner?.mis}</span>
                </Space>
            )}
            showSearch
            filterOption={(input, option) => {
                if (!input || !option) return true;
                const query = String(input).toLowerCase();
                const label = String(option?.label).toLowerCase();
                const value = String(option?.value).toLowerCase();
                return label.includes(query) || value.includes(query);
            }}
            {...restProps}
        />
    );
};

export default SelectAgent;
