// im 文件需求，id, url, name, format, size, token
import React, { Component, Fragment } from 'react';
import { Button, Upload } from 'antd';
import { FileAddOutlined, UploadOutlined } from '@ant-design/icons';
import './index.scss';
import { apiCaller } from '@mfe/cc-api-caller-pc';

function FileUploaderExcel(props) {
    const { onChange, value } = props;

    const onFileChange = payload => {
        const { file: res } = payload;
        if (res.status === 'remove') {
            if (onChange) {
                onChange('');
            }
            return;
        }

        const { name: _name, type, size } = res;
        const name = '' + Date.now() + '_' + parseInt(Math.random() * 1000);
        if (
            !(
                type ===
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
        ) {
            console.error('请选择 xlsx 格式的文件');
            return;
        }
        if (size >= 20000000) {
            alert('文件大小超出限制，请传 20MB 以内的文件');
            return;
        }
        const uploadRequestUrl = '/impc/mss/token';
        apiCaller
            .send(uploadRequestUrl, {
                contentType: type,
                'Access-Control-Allow-Origin': '*',
            })
            .then(result => {
                if (result.code !== 0) {
                    return;
                }
                const {
                    accessKey,
                    endPoint,
                    policy,
                    prefix,
                    signature,
                    bucketName = 'doveim-public',
                } = result.data;
                var data = new FormData();
                const url = `/doveim-public`;
                data.append('AWSAccessKeyId', accessKey);
                data.append('policy', policy);
                data.append('Signature', signature);
                data.append('key', `${prefix}_${name}`);
                data.append('file', res, name);
                var xhr = new XMLHttpRequest();
                xhr.withCredentials = true;
                xhr.addEventListener('readystatechange', function () {
                    if (this.readyState === 4) {
                        const ExcelUrl = `${endPoint}/v1/mss_b2337692cf044b7aac9164ce94e9f90d/${bucketName}/${prefix}_${name}`;
                        if (onChange) {
                            onChange(ExcelUrl);
                        }
                    }
                });
                // xhr.setRequestHeader('cache', false);
                xhr.open('POST', url);
                xhr.send(data);
            });
    };

    return (
        <Upload
            className="text-sender-uploader"
            beforeUpload={() => {
                return false;
            }}
            onChange={onFileChange}
            headers={{}}
            action={''}
            accept=".xlsx"
            multiple={true}
        >
            {/* <FileAddOutlined className="sender-uploader-icon" /> */}
            <Button
                color="primary"
                icon={<UploadOutlined />}
                variant="outlined"
            >
                上传文件
            </Button>
        </Upload>
    );
}

export default FileUploaderExcel;
