/*通用样式*/
.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/*margin、padding*/
@for $i from 1 through 80 {
    .mt-#{$i} {
        margin-top: #{$i}px;
    }

    .mb-#{$i} {
        margin-bottom: #{$i}px;
    }

    .ml-#{$i} {
        margin-left: #{$i}px;
    }

    .mr-#{$i} {
        margin-right: #{$i}px;
    }

    .pt-#{$i} {
        padding-top: #{$i}px;
    }

    .pb-#{$i} {
        padding-bottom: #{$i}px;
    }

    .pl-#{$i} {
        padding-left: #{$i}px;
    }

    .pr-#{$i} {
        padding-right: #{$i}px;
    }
}

/*font-size*/
@for $i from 10 through 35 {
    .fs-#{$i} {
        font-size: #{$i}px;
    }
}

/*width*/
@for $i from 10 through 30 {
    .w-#{$i*10} {
        width: 10px * $i;
    }
}

/*font-weight*/
$fontWeightArr: 200, 300, 400, 500, 600, 700;

@each $i in $fontWeightArr {
    .fw-#{$i} {
        font-weight:#{$i};
    }
}

/*center*/
.text-center {
    text-align: center;
}

.box-center {
    transform: translate(50%, 50%);
}

/*text*/
.text-wrap {
    white-space: pre-wrap;
}

/*overflow后省略号展示*/
@for $i from 1 through 6 {
    .text-overflow#{$i} {
        overflow: hidden;
        display: -webkit-box; //自适应布局
        -webkit-box-orient: vertical;
        -webkit-line-clamp: #{$i}; //控制行数
    }
}

/*flex*/
.flex {
    display: flex;
}

.flex-row {
    display: flex;
    flex-direction: row;
}

.flex-col {
    display: flex;
    flex-direction: column;
}

.flex-center {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
}

.flex-align {
    display: flex;
    flex-direction: row;
    align-items: center;
}

.flex-align-between {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
}

.flex-center-start {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: flex-start;
}

.flex-around {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
}

.flex-between {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.flex-start-start {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
}

.flex-end {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
}

.flex-between-start {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
}

.flex-between-center {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}


.flex-start-center {
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.flex-wrap {
    display: flex;
    flex-wrap: wrap;
}

.flex-1 {
    flex: 1;
}

.flex-al-end {
    display: flex;
    flex-direction: row;
    align-items: flex-end;
}

.text-al-center {
    text-align: center;
}

.text-vl-center {
    vertical-align: center;
}

/*common colors*/
$errRed: #FF192D;

.err-red {
    color: #{$errRed};
}

$successGreen: #00BF7F;

.success-green {
    color: #{$successGreen};
}

.color6 {
    color: #666666;
}

.color2 {
    color: #222222;
}

.color9 {
    color: #999999;
}

$colorH: #FF6A00;

.hight-light {
    color: #{$colorH};
}

$colorC: #CCCCCC;

.color-c {
    color: #{$colorC};
}

//美团渐变黄背景色
.bkg-mt-yellow {
    background-image: linear-gradient(45deg, #FFE14D, #FFC34D);
}


/*字体*/
.family-pr {
    font-family: 'PingFangSC-Regular';
}

.family-pm {
    font-family: 'PingFangSC-Medium';
}

/*title-style*/
.title-style {
    font-family: 'PingFangSC-Medium';
    font-weight: 500;
    color: #222222;
    font-size: 20px;
}

.word-break-all {
    word-wrap: break-word;
    word-break: break-all;
}

.roo-style-btn {
    background-color: #222;
    color: #fff;
}

.menu-item.redDot {
    position: relative;
    padding-right: 10px;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        // left: 0;
        right: 0;
        display: inline-block;
        width: 8px;
        height: 8px;
        background-color: red;
        border-radius: 50%;
    }

}

.pointer {
    cursor: pointer;
}