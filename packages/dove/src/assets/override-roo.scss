// 引入新版本roo的字体路径（默认以‘~’指代node_modules，如项目有其他配置请修改为对应的路径）
$fonts-dir: '~RooNew/roo-theme/dist/fonts';
// 不同版本的roo使用的icon是同一套，所以需要这里再覆盖一下
$font-icon: 'roo-icon';
$prefix-cls: 'doveCustomRoo';
// @import url('@roo/roo/roo-theme/src/scss/theme/custom.scss');
@import '@roo/roo/roo-theme/src/scss/theme/custom.scss';
@import url('@roo/roo-plus/theme/default/index.css');

input.#{$prefix-cls}-input,
div.#{$prefix-cls}-input,
div.#{$prefix-cls}-input-group.has-icon .#{$prefix-cls}-input:only-of-type {
    &:hover:not([readonly]):not(.readonly):not(.readOnly):not([disabled]):not(
            .disabled
        ):not(.#{$prefix-cls}-input-line) {
        border-color: var(--colorPrimaryHover);
    }
    border-radius: var(--borderRadius);
    height: var(--controlHeight);
    border-color: var(--colorBorder);
}

button.#{$prefix-cls}-btn {
    border-radius: var(--borderRadius);

    &.#{$prefix-cls}-btn-default:hover {
        border-color: var(--colorPrimaryHover);
        color: var(--colorPrimaryHover);
    }

    &.#{$prefix-cls}-btn-primary {
        background-color: var(--colorPrimary);
        border-color: var(--colorPrimaryBorder);
        color: var(--colorBgBase);

        &:hover {
            color: var(--colorBgBase);
            border-color: var(--colorPrimaryHover);
            background-color: var(--colorPrimaryHover);
        }
    }
}

label.#{$prefix-cls}-checkbox input:checked + span.custom-checkbox {
    background-color: var(--colorPrimary);
    border-color: var(--colorPrimaryBorder) !important;
}

label.#{$prefix-cls}-checkbox input:checked:disabled + span.custom-checkbox {
    background-color: var(--colorPrimary);
    border-color: var(--colorPrimaryBorder) !important;
}

.#{$prefix-cls}-checkbox span.half-checked {
    background-color: var(--colorPrimary);
    border-color: var(--colorPrimaryBorder);
}

.#{$prefix-cls}-popup {
    .roo-plus-organizationPicker input.#{$prefix-cls}-input,
    div.#{$prefix-cls}-input,
    div.#{$prefix-cls}-input-group.has-icon .#{$prefix-cls}-input:only-of-type {
        height: unset;
    }
    div.roo-plus-org-panel.#{$prefix-cls}-panel .#{$prefix-cls}-panel-footer {
        padding: 0;
    }

    div.roo-plus-org-panel.#{$prefix-cls}-panel .#{$prefix-cls}-panel-body {
        padding: 0 20px 20px 20px;
    }
}
