import { render } from '@src/module/root';
import Root from './App';
import { useEffect } from 'react';
import { ConfigProvider } from 'antd';

const Wrapper = () => {
    useEffect(() => {
        const defaultTracker = window.LXAnalytics('getTracker');
        defaultTracker('pageView', {}, {}, 'c_waimai_m_m_crm_bi_mspuss76');
    }, []);
    return (
        <ConfigProvider
            theme={{
                token: {
                    // colorPrimary: '#222222',
                    colorLink: '#222222',
                },
                components: {
                    Button: {
                        colorTextLightSolid: '#ffffff',
                        colorPrimary: '#222222',
                        colorPrimaryHover: '#222222',
                    },
                    Table: {
                        rowSelectedBg: '#F5F6FA',
                        rowHoverBg: '#F5F6FA',
                        rowSelectedHoverBg: '#F5F6FA',
                    },
                    Checkbox: {
                        colorPrimary: '#222222',
                        colorPrimaryHover: '#222222',
                    },
                },
            }}
        >
            <Root />
        </ConfigProvider>
    );
};

render(<Wrapper />, 'BD话术模版');
