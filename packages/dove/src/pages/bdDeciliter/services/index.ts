import { apiCaller } from '@mfe/cc-api-caller-pc';
import { getskillMock } from '../mock/dialogMock';
import axios from 'axios';
import { message } from 'antd';
import { API_KEYS, GRANT_TYPE } from '../common/constants/apiKeys';

//获取是否是一线bd
export const getIsFrontLineBd = async () => {
    try {
        const res = await apiCaller.get(
            '/xianfu/api-v2/dovefrontLineOperate' as any,
            {},
        );
        if (res.code === 0) {
            // return false;
            // return true;
            return res.data;
        } else {
            // return false;
            return true;
        }
    } catch (error) {
        console.error('获取是否是一线bd失败:', error);
        return true;
    }
};

export const getLlmOptions = async (params?: { jupiterTenantId?: string }) => {
    try {
        if (!params?.jupiterTenantId) {
            return [];
        }
        const res = await apiCaller.post(
            '/xianfu/api-v2/dove/chat/llm/query' as any,
            {
                tenantId: params?.jupiterTenantId,
            },
        );
        
        if (res.code === 0 && res.data) {
            return res.data.map(item => {
                return {
                    id: item.name,
                    name: item.name,
                    // 确保isFree属性存在，如果后端返回了该属性则使用，否则默认为false
                    isFree: !item.privateApiKeyRequired,
                    ...item,
                };
            });
        } else {
            // 如果API调用失败，返回mock数据
            return [];
        }
    } catch (error) {
        console.error('获取LLM选项失败:', error);
    }
};

export const getVoiceOptions = async (params?: {
    jupiterTenantId?: string;
}) => {
    try {
        if (!params?.jupiterTenantId) {
            return [];
        }
        const res = await apiCaller.post(
            '/xianfu/api-v2/dove/chat/voice/query',
            { tenantId: params?.jupiterTenantId },
        );
        

        if (res.code === 0 && res.data) {
            return res.data;

            // return [
            //     {
            //         id: 'meishuqing',
            //         name: '美书晴',
            //         gender: '女',
            //         description: '温柔亲切的女声，适合客服场景',
            //     },
            //     {
            //         id: 'tuanshutan',
            //         name: '小刚',
            //         gender: '男',
            //         description: '稳重有力的男声，适合商务场景',
            //     },
            //     {
            //         id: 'meishuning',
            //         name: '小云',
            //         gender: '女',
            //         description: '活泼开朗的女声，适合年轻人群',
            //     },
            //     {
            //         id: 'tuanshujin',
            //         name: '小强',
            //         gender: '男',
            //         description: '沉稳专业的男声，适合正式场合',
            //     },
            //     {
            //         id: 'meishubing',
            //         name: '小婷',
            //         gender: '女',
            //         description: '甜美清新的女声，适合亲和力场景',
            //     },
            // ];
        } else {

        }
    } catch (error) {
        console.error('获取语音选项失败:', error);
    }
};

export const getSkillList = async (params: {
    type?: number;
    name?: string;
    status?: number;
    page?: number;
    pageSize?: number;
}) => {
    try {
        const res = (await apiCaller.post(
            '/xianfu/api-v2/dove/skill/query' as any,
            params,
        )) as any;

        const {
            code,
            data: { data, total, page, pageSize },
        } = res;
        if (code === 0) {
            return {
                list: data,
                total,
                page,
                pageSize,
            };
        } else {
            return {
                list: [],
                total: 0,
                page: params.page || 1,
                pageSize: params.pageSize || 10,
            };
        }
    } catch (error) {
        console.error('获取技能列表失败:', error);
        return {
            list: [],
            total: 0,
            page: params.page || 1,
            pageSize: params.pageSize || 10,
        };
    }
};

export const getKnowledgeBaseList = async () => {
    try {
        const res = (await apiCaller.get(
            '/xianfu/api-v2/dove/chat/rag/kb/query' as any,
            {},
        )) as any;

        const { code, data } = res;
        if (code === 0) {
            return {
                list: data || [],
                total: data?.length || 0,
                page: 1,
                pageSize: 10,
            };
        } else {
            return {
                list: [],
                total: 0,
                page: 1,
                pageSize: 10,
            };
        }
    } catch (error) {
        console.error('获取知识库列表失败:', error);
        return {
            list: [],
            total: 0,
            page: 1,
            pageSize: 10,
        };
    }
};

// 知识库mock数据
const getKnowledgeBaseMock = () => {
    return {
        data: [
            {
                id: 1,
                name: '美团知识库',
                type: 1,
                status: 1,
                createTime: '2023-05-01 12:00:00',
            },
            {
                id: 2,
                name: '外卖知识库',
                type: 2,
                status: 1,
                createTime: '2023-05-02 14:30:00',
            },
            {
                id: 3,
                name: '商家服务知识库',
                type: 1,
                status: 0,
                createTime: '2023-05-03 09:15:00',
            },
        ],
        total: 3,
        page: 1,
        pageSize: 10,
    };
};

export const fetchPlaceholders = async (params: {
    name?: string;
    showName?: string;
    status?: number;
    page?: number;
    pageSize?: number;
    objectType?: number;
}) => {
    try {
        const res = await apiCaller.post(
            '/xianfu/api-v2/dove/placeholder/query' as any,
            {
                name: params.name || '',
                showName: params.showName || '',
                status: params.status || 0,
                page: params.page || 1,
                pageSize: params.pageSize || 10,
                objectType: params.objectType || undefined,
            },
        );
        if (res.code === 0) {
            return res.data.data || [];
        } else {
            return [];
        }
    } catch (error) {
        console.error('获取占位符列表失败:', error);
        return {
            list: [],
            total: 0,
            page: params.page || 1,
            pageSize: params.pageSize || 10,
        };
    }
};

export const addTemplate = async (data: any) => {
    try {
        const res = await apiCaller.post(
            '/xianfu/api-v2/dove/agent/create' as any,
            data,
        );

        if (res.code === 0) {
            return {
                success: true,
                data: res.data,
            };
        } else {
            return {
                success: false,
                message: res.message || '新增模板失败',
            };
        }
    } catch (error) {
        console.error('新增模板失败:', error);
        return {
            success: false,
            message: '新增模板请求异常',
        };
    }
};

export const updateTemplate = async (id: string, data: any) => {
    try {
        const res = await apiCaller.post(
            '/xianfu/api-v2/dove/agent/edit' as any,
            {
                id,
                ownerId: data.owner.id,
                ...data,
            },
        );

        return res;
    } catch (error) {
        console.error('更新模板失败:', error);
        return {
            success: false,
            message: '更新模板请求异常',
        };
    }
};

export const getTemplateById = async (id: string) => {
    try {
        const res = await apiCaller.post('/xianfu/api-v2/dove/ids' as any, {
            agentIdList: [id],
        });

        if (res.code === 0 && res.data) {
            return res.data[0];
        } else {
            console.error('获取模板详情失败:', res.message);
            return null;
        }
    } catch (error) {
        console.error('获取模板详情失败:', error);
        return null;
    }
};
export const getObjectType = async () => {
    const res = await apiCaller.post(
        '/xianfu/api-v2/dove/object/type/query',
        {},
    );
    if (res.code !== 0) {
        return [];
    }
    return res.data?.data;
};
// function isValid() {
//     if (!access_token) return false
//     const now = Date.now() / 1000
//     return now - updatedAt < EXPIRE_SECONDS
//   }
export async function getAccessToken() {
    // if (isValid()) {
    //   return access_token
    // }
    return axios
        .post('/xianfu/api-v2/dove/oauth/v2/token', null, {
            params: {
                grant_type: GRANT_TYPE,
                client_id: API_KEYS.CLIENT_ID,
                client_secret: API_KEYS.CLIENT_SECRET,
            },
        })
        .then(data => {
            
            const accessToken = data.data.data?.access_token;
            console.log('accessToken', accessToken);
            return accessToken;
        })
        .catch(err => {
            return Promise.reject(err);
        });
}

export const createAgentText = async (data: {
    agentId: number;
    dynamicParams: {
        [key: string]: string; // 动态参数1, 动态参数2, 动态参数n
    };
}) => {
    try {
        const res = await apiCaller.post(
            '/xianfu/api-v2/dove/chat/try/create',
            data as any,
        );

        return res;
    } catch (error) {
        console.error('创建AI外呼文本失败:', error);
        return null;
    }
};

// 发送用户消息
export const sendChatMessage = async (
    conversationId: number,
    message: string,
) => {
    try {
        // 实际API调用
        const res = await apiCaller.post(
            '/xianfu/api-v2/dove/chat/try/interact',
            {
                conversationId,
                messageList: [
                    {
                        type: 1, // 假设1代表用户消息
                        content: message,
                    },
                ],
            },
        );

        return res;
    } catch (error) {
        console.error('发送消息API错误:', error);
        return null;
    }
};

// 电话测试API
export const phoneTest = async (
    agentId: string,
    phoneNumber: string,
    placeholder: any,
) => {
    try {
        // 调用真实API
        const res = await apiCaller.post('/xianfu/api-v2/dove/call/ai/try', {
            tel: phoneNumber,
            agentId,
            placeholder,
        });

        if (res.code === 0) {
            return res.data;
        } else {
            console.error('电话测试失败:', res.msg);
            return {
                success: false,
                message: res.msg || '电话测试失败',
            };
        }
    } catch (error) {
        console.error('电话测试API错误:', error);
        return {
            success: false,
            message: '电话测试请求异常',
        };
    }
};

export const publishAgent = async (id: string) => {
    const res = await apiCaller.post(
        '/xianfu/api-v2/dove/agent/active' as any,
        {
            id,
        },
    );
    return res;
};

export const getDefaultInitialValue = async () => {
    const res = await apiCaller.get(
        '/xianfu/api-v2/dove/agent/queryDefault' as any,
        {}
    );
    return res;
};

export const getUserName = async () => {
    const res = await apiCaller.get(
        '/xianfu/api-v2/dove/agent/getUserInfo' as any,
        {}
    );
    return res;
};
