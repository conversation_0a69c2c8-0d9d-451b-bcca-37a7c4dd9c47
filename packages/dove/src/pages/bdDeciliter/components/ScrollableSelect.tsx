import React, { useState, useEffect, useCallback } from 'react';
import { Select, Spin, Modal } from 'antd';
import type { SelectProps } from 'antd/es/select';
import { apiCaller } from '@mfe/cc-api-caller-pc';

export interface ScrollableSelectProps<ValueType = any> extends Omit<SelectProps<ValueType>, 'options'> {
  // API路径
  apiPath: string;
  // API调用方法，默认为post
  apiMethod?: 'get' | 'post';
  // 请求参数
  params?: Record<string, any>;
  // 选项显示的字段名
  labelField?: string;
  // 选项值的字段名
  valueField?: string;
  // 每页加载的数量
  pageSize?: number;
  // 是否默认选中第一项（当没有value时）
  defaultFirstSelected?: boolean;
  // 是否需要确认对话框
  needConfirm?: boolean;
  // 确认对话框标题
  confirmTitle?: string;
  // 确认对话框内容
  confirmContent?: string;
}

/**
 * 滚动分页自动加载的Select组件
 * 支持滚动到底部自动加载更多
 */
function ScrollableSelect<ValueType = any>({
  apiPath,
  apiMethod = 'post',
  params = {},
  labelField = 'name',
  valueField = 'name', // 修改默认值为'name'，使label和value都使用场景名称
  pageSize = 20,
  style,
  defaultFirstSelected = false,
  value,
  onChange,
  needConfirm = false,
  confirmTitle = "确认选择",
  confirmContent = "确认选择此项吗？",
  ...restProps
}: ScrollableSelectProps<ValueType>) {
  // 组件状态
  const [options, setOptions] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [page, setPage] = useState<number>(1);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [initialLoadDone, setInitialLoadDone] = useState<boolean>(false);

  // 确认弹窗状态
  const [confirmVisible, setConfirmVisible] = useState<boolean>(false);
  const [pendingValue, setPendingValue] = useState<ValueType | null>(null);
  const [pendingOption, setPendingOption] = useState<any>(null);

  // 检查URL中是否有id参数
  const hasIdInUrl = useCallback(() => {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.has('id');
  }, []);

  // 加载数据
  const loadData = useCallback(async (currentPage: number, replace = false) => {
    if (loading) return;

    setLoading(true);
    try {
      const requestParams = {
        ...params,
        type: 1,
        // newestVersion:1,
        status: [1],
        forUser: true,
        page: currentPage,
        pageSize: pageSize
      };

      let response;
      if (apiMethod === 'get') {
        response = await apiCaller.get(apiPath as any, requestParams);
      } else {
        response = await apiCaller.post(apiPath as any, requestParams);
      }

      if (response.code === 0 && response.data) {
        const newData = response.data.data || [];

        const updatedOptions = replace ? newData : [...options, ...newData];
        setOptions(updatedOptions);
        setHasMore(currentPage * pageSize < (response.data.total || 0));

        // 只在第一次加载完成且无id参数时判断是否需要默认选中第一项
        if (replace && updatedOptions.length > 0 && !hasIdInUrl() && onChange) {
          const firstOption = updatedOptions[0];
          console.log('firstOption', firstOption);
          // 默认选中第一项不需要确认
          onChange(firstOption[valueField] as ValueType, {
            label: firstOption[labelField],
            value: firstOption[valueField],
            isDefaultSelected: true,
            ...firstOption
          });
        }

        setInitialLoadDone(true);
      } else {
        setHasMore(false);
        setInitialLoadDone(true);
      }
    } catch (error) {
      console.error('加载下拉选项失败:', error);
      setHasMore(false);
      setInitialLoadDone(true);
    } finally {
      setLoading(false);
    }
  }, [apiPath, apiMethod, params, pageSize, loading, options, value, onChange, defaultFirstSelected, labelField, valueField, hasIdInUrl]);

  // 初始加载
  useEffect(() => {
    setOptions([]);
    setPage(1);
    setHasMore(true);
    setInitialLoadDone(false);
    loadData(1, true);
  }, [JSON.stringify(params)]);

  // 处理滚动事件
  const handlePopupScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const { target } = e;
    const div = target as HTMLDivElement;

    if (div.scrollTop + div.clientHeight >= div.scrollHeight - 20) { // 滚动到距离底部20px时
      if (hasMore && !loading) {
        const nextPage = page + 1;
        setPage(nextPage);
        loadData(nextPage);
      }
    }
  }, [hasMore, loading, page, loadData]);

  // 处理选择变化
  const handleChange = (newValue: ValueType, option: any) => {
    // 如果是默认选中的或不需要确认，直接调用onChange
    if ((option && (option as any).isDefaultSelected) || !needConfirm) {
      if (onChange) {
        onChange(newValue, option);
      }
    } else {
      // 否则，显示确认弹窗
      setPendingValue(newValue);
      setPendingOption(option);
      setConfirmVisible(true);
    }
  };

  // 确认选择
  const handleConfirm = () => {
    if (pendingValue !== null && pendingOption && onChange) {
      onChange(pendingValue as ValueType, pendingOption);
    }
    setConfirmVisible(false);
  };

  // 取消选择
  const handleCancel = () => {
    setConfirmVisible(false);
  };

  // 转换为Select需要的options格式
  const selectOptions = options.map(item => ({
    label: item[labelField],
    value: item[valueField],
    ...item // 保留原始数据
  }));

  return (
    <>
      <Select
        style={{ width: '100%', ...style }}
        options={selectOptions}
        onPopupScroll={handlePopupScroll}
        loading={loading && options.length === 0}
        showSearch
        value={value}
        onChange={handleChange}
        filterOption={(input, option) =>
          (option?.label as string)?.toLowerCase().includes(input.toLowerCase())
        }
        dropdownRender={(menu) => (
          <div>
            {menu}
            {loading && (
              <div style={{ textAlign: 'center', padding: '8px 0' }}>
                <Spin size="small" />
              </div>
            )}
          </div>
        )}
        {...restProps}
      />

      {/* 确认弹窗 */}
      <Modal
        title={confirmTitle}
        open={confirmVisible}
        onOk={handleConfirm}
        onCancel={handleCancel}
        okText="确定"
        cancelText="取消"
      >
        <p>{confirmContent}</p>
        {pendingOption && (
          <p>选场景模板为: {pendingOption.label}</p>
        )}
      </Modal>
    </>
  );
}

export default ScrollableSelect; 