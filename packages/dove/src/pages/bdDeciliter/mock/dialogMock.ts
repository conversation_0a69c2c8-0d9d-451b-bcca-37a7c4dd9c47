// 消息类型定义
export interface MessageType {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  status: 'success' | 'local' | 'loading' | 'error';
}

// 初始对话数据
export const initialMessages: MessageType[] = [
  {
    id: '1',
    content: '您好，请问是 @商家名称 吗？',
    role: 'assistant',
    status: 'success',
  },
  {
    id: '2',
    content: '是，你是哪位？',
    role: 'user',
    status: 'local',
  },
  {
    id: '3',
    content: '老板您好，我是 #身份定义 ，不好意思占用您2分钟时间为您介绍一下平台的新活动......',
    role: 'assistant',
    status: 'loading',
  }
];

// 不同场景的回复模板
const responseTemplates = {
  // 一般性回复
  default: `感谢您的回复！

**这是我们的平台优势**:
- 更低的佣金费率
- 更多的流量支持
- 更好的客户服务

想了解更多详情，请[点击查看活动详情](https://example.com)`,

  // 询问佣金相关
  commission: `关于佣金问题，我们有以下优惠政策：

**平台佣金政策**:
- 新商家首月佣金低至3%
- 老商家复购优惠2%
- VIP商家专属1%特惠

详细佣金计算方式可以[查看这里](https://example.com/commission)`,

  // 询问活动相关
  activity: `我们最近有以下活动：

1. **新商家入驻礼包**
   - 首月免佣金
   - 获得1000元广告投放券
   
2. **老客户回归计划**
   - 减免平台服务费
   - 专属流量扶持

您对哪个活动更感兴趣呢？`,

  // 拒绝回复
  rejection: `我理解您可能现在比较忙或者暂时没有兴趣。

不过您可以考虑以下几点：
- 我们的活动时间有限，错过可能就没有了
- 已经有很多同行入驻并获得了不错的收益
- 我们可以提供一对一的开店指导

如果您改变主意或有任何问题，随时可以联系我。`,

  // 具体问题
  question: `这是个很好的问题！

关于您提到的具体情况，我们有针对性的解决方案：
1. 针对您所在的行业，我们有特别的扶持政策
2. 根据您的经营规模，可以定制专属方案
3. 我们的技术团队可以提供全程支持

您可以添加我的微信详细咨询：wxid_example`,

  // 结束对话
  ending: `非常感谢您的时间！

如果您后续有任何问题，可以：
- 拨打客服热线：400-XXX-XXXX
- 添加业务微信：wxid_example
- 访问官网了解更多：https://example.com

祝您生意兴隆！`
};

// 关键词匹配表
const keywordMap = {
  '佣金': 'commission',
  '费率': 'commission',
  '活动': 'activity',
  '优惠': 'activity',
  '不': 'rejection',
  '没兴趣': 'rejection',
  '忙': 'rejection',
  '怎么': 'question',
  '如何': 'question',
  '什么': 'question',
  '为什么': 'question',
  '再见': 'ending',
  '谢谢': 'ending'
};

// 根据用户输入获取回复
export const getResponseByUserInput = (input: string): string => {
  // 转为小写便于匹配
  const lowerInput = input.toLowerCase();
  
  // 查找匹配的关键词
  for (const [keyword, template] of Object.entries(keywordMap)) {
    if (lowerInput.includes(keyword)) {
      return responseTemplates[template as keyof typeof responseTemplates];
    }
  }
  
  // 默认回复
  return responseTemplates.default;
};

// 模拟延迟的异步函数
export const getDelayedResponse = (input: string): Promise<string> => {
  return new Promise((resolve) => {
    // 随机延迟500-1500ms
    const delay = Math.floor(Math.random() * 1000) + 500;
    setTimeout(() => {
      resolve(getResponseByUserInput(input));
    }, delay);
  });
}; 


export const getskillMock = () => {
  return {
    page: 1,
    pageSize: 10,
    total: 1,
    data: [
      {
        id: 123,
        type: 1, // COMPANY_CHANNEL(1, "企微渠道"),DOVE_IM_CHANNEL(2, "信鸽IM渠道"),INFO_IDENTIFICATION(3, "信息识别"), CRM_INFO_CHANGE(4, "供应链信息修改")
        skillConfig: {
          name: "天气",
          description: "",
          apiType: 1, // THRIFT(1, "thrift"),HTTP(2, "http"),MCP(3, "mcp")
          params: [{
            name: "321312",
            type: 1, // STRING(1, "String"), INTEGER(2, "Integer"),LONG(3, "Long"), DOUBLE(4, "Double"),OBJECT(5, "Object"), BOOLEAN(6, "Boolean"),  ARRAY_STRING(7, "Array[String]"),ARRAY_INTEGER(8, "Array[Integer]"),ARRAY_LONG(9, "Array[Long]"),ARRAY_DOUBLE(10, "Array[Double]"),ARRAY_OBJECT(11, "Array[Object]"),ARRAY_BOOLEAN(12, "Array[Boolean]")
            description: "",
            required: 1, // 1必须 0可选
            defaultValue: "", // 默认值 JSON/单值
            enumValues: "", // 枚举值 \n分隔
            children: [{}]
          }],
          timeout: 12345,
          result: [{
            name: "",
            type: 1, // 同params
            description: "",
            children: [{}]
          }],
          url: "", // http,mcp
          method: "", // http,thrift
          headers: "", // http
          appkey: "" // thrift
        },
        objectType: "", // WDC_SHOP(-1, "公海门店（未合作）"),MT_SHOP(-2, "到店门店"),WM_SHOP(-3, "外卖门店（已合作）)
        owner: {
          mis: "sunxue08",
          name: "孙雪",
          id: 1
        },
        lastOperator: {
          mis: "sunxue08",
          name: "孙雪",
          id: 1
        },
        status: 1, // 1生效，2失效
        createTime: 1697392000000,
        updateTime: 1697392000000
      }
    ]
  }
}