/* 前线BD意向标签配置表单样式 */
.intention-label-config.disabled-form {
  position: relative;
  pointer-events: none;
  opacity: 0.7;
  
  /* 添加说明文字 */
  &::before {
    content: '当前内容不允许编辑';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    padding: 8px 16px;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    border-radius: 4px;
    font-size: 14px;
  }
  
  /* 覆盖按钮、输入框等交互元素的禁用样式 */
  .ant-input, 
  .ant-select, 
  .ant-select-selector, 
  .ant-btn,
  .ql-editor,
  .rich-editor-wrapper {
    background-color: #f5f5f5 !important;
    color: #bbb !important;
    cursor: not-allowed !important;
    border-color: #e0e0e0 !important;
  }
} 