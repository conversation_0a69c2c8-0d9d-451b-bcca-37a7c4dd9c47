.keyword-extract-container {
    .form-items-container {
        position: relative;

        .form-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            // background-color: rgba(255, 255, 255, 0.1);
            background-color: transparent;
            backdrop-filter: blur(0.5px);
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 6px;
            cursor: not-allowed;


            .overlay-text {
                color: #999;
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 16px;
                white-space: nowrap;
            }
        }

        // 当有遮罩层时，表单项保持可见但略微变暗
        &:has(.form-overlay) {

            .ant-form-item,
            .roo-form-item {}
        }
    }

    .keyword-extract-checkbox {
        margin-bottom: 16px;
    }
}