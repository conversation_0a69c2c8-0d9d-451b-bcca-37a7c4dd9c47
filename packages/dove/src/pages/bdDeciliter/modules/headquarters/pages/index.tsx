import { useState, useEffect, useRef, useContext } from 'react';
import { Form, Checkbox, Select, Button, Tooltip, App, Modal } from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { BDDeciliterContext } from '../../../context';

// 导入工具函数
import type { FormInstance as AntdFormInstance } from 'antd/lib/form';
import type { FormInstance as RooFormInstance } from '@roo/roo/FormPro/hooks/useForm';
import useAgentType, { AgentTypeEnum } from '@src/hooks/useAgentType';
import {
    getSkillList,
    getKnowledgeBaseList,
    getLlmOptions,
    getVoiceOptions,
    getObjectType,
} from '../../../services';
import { FormPro, Input, Selector, Tabs } from '@roo/roo';
import VoiceSelector from '../../../common/components/VoiceSelector';
import PromptConfig from '../../../common/components/PromptConfig';
import ParamConfig from '../../../common/components/ParamConfig';
import OrgnazitionPickerRoo from '@src/components/callRecord/OrgnazitionPickerRoo';

import KeywordExtract from './KeywordExtract';
import IntentionConfig from './IntentionConfig';
const { Pane: TabPane } = Tabs;

// 身份类型
const AuthTypeEnum = {
    FRONT_LINE: 1, // 一线使用
    BACK_LINE: 2, // 后线运营使用
} as const;

interface HeadQuartersFormProps {
    form: AntdFormInstance | RooFormInstance<any> | any;
    id?: string;
}

// Prompt验证规则函数
const validatePrompt =
    (templateType: number | undefined) => (_: any, value: string) => {
        console.log('validatePrompt', templateType, value);
        // 如果是总部应用(type为2)，则不需要校验
        if (templateType === 2 || !templateType) {
            return Promise.resolve();
        }

        if (!value) {
            return Promise.reject(new Error('请输入Prompt'));
        }

        const requiredTags = [
            '#{身份信息}',
            '#{对话流程}',
            '#{知识点}',
            '#{约束}',
        ];
        const missingTags = requiredTags.filter(tag => !value.includes(tag));

        if (missingTags.length > 0) {
            return Promise.reject(
                new Error(`Prompt必须包含 ${missingTags.join(', ')}`),
            );
        }

        // 已移除@{xxx}标记的校验

        return Promise.resolve();
    };

const HeadQuartersForm: React.FC<HeadQuartersFormProps> = ({ form, id }) => {
    const { isFrontLine } = useContext(BDDeciliterContext);
    const [value, setValue] = useState('tab1');
    const [skillList, setSkillList] = useState<any[]>([]);
    const [knowledgeBaseList, setKnowledgeBaseList] = useState<any[]>([]);
    const [llmOptions, setLlmOptions] = useState<any[]>([]);
    const [voiceOptions, setVoiceOptions] = useState<any[]>([]);
    const [ObjectOptions, setObjectOptions] = useState<any[]>([]);
    const [isLoadingLlm, setIsLoadingLlm] = useState(false);
    const [isLoadingVoice, setIsLoadingVoice] = useState(false);
    const { agentTypeList } = useAgentType();
    // 存储最终提交的租户ID（失焦后的值）
    const [submittedTenantId, setSubmittedTenantId] = useState<string>('');
    // 标记是否已经处理过初始化租户ID
    const initialIdProcessed = useRef(false);

    // 添加状态来跟踪是否显示计费APPID
    // const [showMainAppId, setShowMainAppId] = useState(true);

    // 添加状态来跟踪是否显示计费APPID
    const [showIntentionAppId, setShowIntentionAppId] = useState(true);

    // 添加状态来跟踪是否显示意向标签计费APPID
    const [showIntentionLlmAppId, setShowIntentionLlmAppId] = useState(true);

    // 监听意向标签模型选择变化
    const selectedLlmName = Form.useWatch('llmName', form);
    const selectedIntentionLlmName = Form.useWatch(
        ['intention', 'llmName'],
        form,
    );

    useEffect(() => {
        if (selectedIntentionLlmName && llmOptions.length > 0) {
            const selectedModel = llmOptions.find(
                model => model.id === selectedIntentionLlmName,
            );
            console.log('意向标签选中模型:', selectedModel);
            // 如果模型包含isFree字段且为true，则隐藏计费APPID
            setShowIntentionLlmAppId(
                !(selectedModel && selectedModel.isFree === true),
            );
        } else {
            setShowIntentionLlmAppId(true);
        }
        if (selectedLlmName && llmOptions.length > 0) {
            const selectedModel = llmOptions.find(
                model => model.id === selectedLlmName,
            );
            console.log('意向标签选中模型:', selectedModel);
            // 如果模型包含isFree字段且为true，则隐藏计费APPID
            setShowIntentionAppId(
                !(selectedModel && selectedModel.isFree === true),
            );
        } else {
            setShowIntentionAppId(true);
        }
    }, [selectedIntentionLlmName, selectedLlmName, llmOptions]);

    // 使用Form.useWatch监听字段变化
    const templateType = Form.useWatch('type', form);

    // 根据条件过滤选项，当在总部环境时不显示城市应用选项
    const filteredTypeOptions = agentTypeList
        .filter(i => i.code !== AgentTypeEnum.FRIDAY)
        .filter(
            option =>
                // 如果是一线角色(isFrontLine为true)，显示所有选项
                // 如果当前选中的模板类型为3，也显示选项3
                // 如果是总部角色(isFrontLine为false或未定义)，不显示城市应用选项(value为3)
                isFrontLine === true ||
                templateType === 3 ||
                option.value !== 3,
        );

    // 监听权限配置变化
    const authType = Form.useWatch(['auth', 'type'], form);
    // 监听木星租户ID变化
    const jupiterTenantId = Form.useWatch(
        ['agentChannel', 0, 'jupiterTenantId'],
        form,
    );

    // 处理租户ID输入框失焦事件
    const handleTenantIdBlur = () => {
        if (jupiterTenantId !== submittedTenantId) {
            setSubmittedTenantId(jupiterTenantId || '');
        }
    };

    // 处理表单回显时的租户ID
    useEffect(() => {
        // 当jupiterTenantId有值且还没有处理过初始化，则自动设置submittedTenantId
        if (
            jupiterTenantId &&
            !initialIdProcessed.current &&
            !submittedTenantId
        ) {
            setSubmittedTenantId(jupiterTenantId);
            initialIdProcessed.current = true;
        }
    }, [jupiterTenantId, submittedTenantId]);

    // 当模板类型变化为场景模板时，自动设置权限配置为一线可用
    useEffect(() => {
        if (templateType === 1) {
            form.setFieldValue(['auth', 'type'], AuthTypeEnum.FRONT_LINE);
        } else if (templateType === 2) {
            // form.setFieldValue(['auth', 'type'], AuthTypeEnum.FRONT_LINE);
        }
    }, [templateType, form]);

    useEffect(() => {
        // 获取基础选项数据（不依赖租户ID的数据）
        const fetchBasicData = async () => {
            try {
                const [skillRes, knowledgeRes, objectRes] = await Promise.all([
                    getSkillList({ page: 1, pageSize: 100 }),
                    getKnowledgeBaseList(),
                    getObjectType(),
                ]);

                // 处理技能列表数据
                const skillOptions = skillRes.list.map(item => ({
                    label: item.skillConfig?.showName || '未命名技能',
                    value: item.id,
                }));
                setSkillList(skillOptions);

                // 处理知识库列表数据
                const knowledgeBaseOptions = knowledgeRes.list.map(item => ({
                    label: item.name || '未命名知识库',
                    value: item.id,
                }));
                setKnowledgeBaseList(knowledgeBaseOptions);

                // 设置其他选项数据
                setObjectOptions(objectRes || []);
            } catch (error) {
                console.error('获取选项数据失败:', error);
            }
        };

        fetchBasicData();
    }, []);

    // 监听submittedTenantId变化，获取依赖租户ID的数据
    useEffect(() => {
        // 只有当提交的租户ID有值时才获取依赖的数据
        if (submittedTenantId) {
            const fetchDependentData = async () => {
                try {
                    // 设置加载状态
                    setIsLoadingLlm(true);
                    setIsLoadingVoice(true);

                    // 并行获取依赖租户ID的数据
                    const [llmRes, voiceRes] = await Promise.all([
                        getLlmOptions({ jupiterTenantId: submittedTenantId }),
                        getVoiceOptions({ jupiterTenantId: submittedTenantId }),
                    ]);

                    // 设置大模型和音色选项数据
                    setLlmOptions(llmRes || []);
                    setVoiceOptions(voiceRes || []);
                } catch (error) {
                    console.error('获取大模型和音色数据失败:', error);
                } finally {
                    // 清除加载状态
                    setIsLoadingLlm(false);
                    setIsLoadingVoice(false);
                }
            };

            fetchDependentData();
        } else {
            // 当提交的租户ID为空时，清空依赖的数据
            setLlmOptions([]);
            setVoiceOptions([]);
        }
    }, [submittedTenantId]);

    // 弹窗表单实例，放在组件顶层
    const [modalForm] = Form.useForm();

    const handleOpenModelParamsConfig = () => {
        // 回显
        const maxRecall = form.getFieldValue(['modelConfig', 'maxRecall']);
        const simThreshold = form.getFieldValue([
            'modelConfig',
            'simThreshold',
        ]);
        modalForm.setFieldsValue({
            maxRecall,
            simThreshold,
        });

        Modal.confirm({
            title: '输入输出配置',
            icon: null,
            width: 600,
            content: (
                <Form
                    form={modalForm}
                    layout="vertical"
                    style={{ marginTop: 24 }}
                >
                    <Form.Item
                        label="FAQ最大召回数量"
                        name="maxRecall"
                        initialValue={'5'}
                        rules={[
                            {
                                pattern: /^[0-9]+(\.[0-9]+)?$/,
                                message: '请输入大于等于0的数字',
                            },
                        ]}
                    >
                        <Input placeholder="请输入" />
                    </Form.Item>
                    <Form.Item
                        label="FAQ相似度阈值"
                        name="simThreshold"
                        rules={[
                            {
                                pattern: /^[0-9]+(\.[0-9]+)?$/,
                                message: '请输入大于等于0的数字',
                            },
                        ]}
                    >
                        <Input placeholder="请输入" />
                    </Form.Item>
                </Form>
            ),
            onOk: async () => {
                try {
                    const values = await modalForm.validateFields();
                    form.setFieldValue(
                        ['modelConfig', 'maxRecall'],
                        values.maxRecall,
                    );
                    form.setFieldValue(
                        ['modelConfig', 'simThreshold'],
                        values.simThreshold,
                    );
                } catch (e) {
                    return Promise.reject();
                }
            },
            okText: '保存',
            cancelText: '关闭',
        });
    };

    return (
        <>
            <div className="bd-deciliter-header-tabs-container">
                <Tabs
                    value={value}
                    onChange={activeKey => setValue(activeKey as string)}
                    type="fill"
                >
                    <TabPane label="基础配置" name="tab1" forceRender>
                        <div className="basic-config">
                            <FormPro.Item
                                label="模板名称"
                                name="name"
                                rules={[
                                    {
                                        required: true,
                                        message: '请输入模板名称',
                                    },
                                ]}
                            >
                                <Input
                                    placeholder="请输入模板名称"
                                    style={{ width: '100%' }}
                                />
                            </FormPro.Item>
                            <FormPro.Item
                                name={['modelConfig', 'maxRecall']}
                                noStyle
                            />
                            <FormPro.Item
                                name={['modelConfig', 'simThreshold']}
                                noStyle
                            />
                            <FormPro.Item
                                label={
                                    <span>
                                        模板类型
                                        <span style={{ marginLeft: 4 }}>
                                            <Tooltip
                                                title={
                                                    <span>
                                                        总部应用：配置外呼模板
                                                        <br />
                                                        场景模板：为一线配置可自主修改的模板
                                                    </span>
                                                }
                                            >
                                                <QuestionCircleOutlined
                                                    style={{ color: '#999' }}
                                                />
                                            </Tooltip>
                                        </span>
                                    </span>
                                }
                                name="type"
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择模板类型',
                                    },
                                ]}
                            >
                                <Selector
                                    placeholder="请选择模板类型"
                                    options={filteredTypeOptions}
                                    style={{ width: '100%' }}
                                />
                            </FormPro.Item>
                            <FormPro.Item
                                label="木星租户ID"
                                name={['agentChannel', 0, 'jupiterTenantId']}
                                rules={[
                                    {
                                        required: true,
                                        message: '请输入木星租户ID',
                                    },
                                ]}
                            >
                                <Input
                                    placeholder="请输入木星租户ID"
                                    style={{ width: '100%' }}
                                    onBlur={handleTenantIdBlur}
                                />
                            </FormPro.Item>
                            <FormPro.Item
                                label="路由点ID"
                                name={['agentChannel', 0, 'routePoint']}
                                rules={[
                                    {
                                        required: true,
                                        message: '请输入路由点ID',
                                    },
                                ]}
                            >
                                <Input
                                    placeholder="请输入路由点ID"
                                    style={{ width: '100%' }}
                                />
                            </FormPro.Item>
                            <FormPro.Item
                                label="大模型"
                                name="llmName"
                                rules={[
                                    { required: true, message: '请选择模型' },
                                ]}
                                extra={
                                    !submittedTenantId ? (
                                        '请先填写并离开木星租户ID输入框'
                                    ) : (
                                        <Button
                                            type="link"
                                            style={{
                                                marginLeft: 8,
                                                padding: 0,
                                                height: 22,
                                                lineHeight: '22px',
                                                color: '#ff6a00',
                                            }}
                                            onClick={
                                                handleOpenModelParamsConfig
                                            }
                                        >
                                            模型参数配置
                                        </Button>
                                    )
                                }
                            >
                                <Selector
                                    placeholder={
                                        !submittedTenantId
                                            ? '请先填写并离开木星租户ID输入框'
                                            : '请选择模型'
                                    }
                                    options={llmOptions}
                                    style={{ width: 240 }}
                                    fieldNames={{ label: 'name', value: 'id' }}
                                    disabled={
                                        !submittedTenantId || isLoadingLlm
                                    }
                                />
                            </FormPro.Item>
                            {showIntentionAppId && (
                                <FormPro.Item
                                    label="计费APPID"
                                    name="llmAppId"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入计费APPID',
                                        },
                                    ]}
                                >
                                    <Input
                                        placeholder="请输入APPID"
                                        style={{ width: '100%' }}
                                    />
                                </FormPro.Item>
                            )}
                            <FormPro.Item
                                label="音色"
                                name={['agentChannel', 0, 'timbreId']}
                                rules={[
                                    { required: true, message: '请选择音色' },
                                ]}
                                extra={
                                    !submittedTenantId
                                        ? '请先填写并离开木星租户ID输入框'
                                        : null
                                }
                            >
                                <VoiceSelector
                                    options={voiceOptions}
                                    style={{ width: '100%' }}
                                    disabled={
                                        !submittedTenantId || isLoadingVoice
                                    }
                                    placeholder={
                                        !submittedTenantId
                                            ? '请先填写并离开木星租户ID输入框'
                                            : '请选择音色'
                                    }
                                />
                            </FormPro.Item>
                            {templateType === 1 && (
                                <FormPro.Item
                                    label="场景名称"
                                    name="sceneName"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入场景名称',
                                        },
                                    ]}
                                >
                                    <Input
                                        placeholder="请输入场景名称"
                                        style={{ width: '100%' }}
                                    />
                                </FormPro.Item>
                            )}

                            <FormPro.Item
                                name="systemPrompt"
                                initialValue={{ prompt: '', inputParams: {} }}
                                noStyle
                                dependencies={['type']}
                            >
                                {templateType === 1 && (
                                    <FormPro.Item
                                        label="参数配置"
                                        name={['systemPrompt', 'inputParams']}
                                        grid24
                                        labelCol={{ span: 3 }}
                                        wrapperCol={{ span: 21 }}
                                    >
                                        <ParamConfig form={form} />
                                    </FormPro.Item>
                                )}

                                <FormPro.Item
                                    label="开场白"
                                    name={'prologue'}
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入开场白',
                                        },
                                    ]}
                                >
                                    <PromptConfig
                                        form={form}
                                        isShowTitle={false}
                                        className="prologue-editor"
                                    />
                                </FormPro.Item>
                                <FormPro.Item
                                    label="Prompt"
                                    name={['systemPrompt', 'prompt']}
                                    rules={[
                                        {
                                            validator:
                                                validatePrompt(templateType),
                                        },
                                    ]}
                                >
                                    <PromptConfig
                                        form={form}
                                        value={form.getFieldValue([
                                            'systemPrompt',
                                            'prompt',
                                        ])}
                                        onChange={value =>
                                            form.setFieldValue(
                                                ['systemPrompt', 'prompt'],
                                                value,
                                            )
                                        }
                                        typeValue={templateType}
                                        templateId={id}
                                        isPrompt={true}
                                        title={
                                            templateType === 2
                                                ? '输入"空格@"添加通用参数（商家名称、地址等）；输入"空格@{自定义内容}"添加自定义参数（用于系统对接）'
                                                : templateType === 1
                                                ? '输入"空格@"添加通用参数（商家名称、地址等）；输入"空格#"插入一线可编辑prompt（如身份信息、对话流程）；输入"空格@{自定义内容}"添加自定义参数（用于系统对接）'
                                                : undefined
                                        }
                                    />
                                </FormPro.Item>
                            </FormPro.Item>

                            <FormPro.Item
                                label="触达对象"
                                name="objectType"
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择触达对象',
                                    },
                                ]}
                            >
                                <Select
                                    placeholder="请选择应用领域"
                                    style={{ width: '100%' }}
                                    options={ObjectOptions}
                                    fieldNames={{
                                        label: 'name',
                                        value: 'objectType',
                                    }}
                                />
                            </FormPro.Item>

                            {/* 总部和一线都显示 并且根据场景模板显示一线可修改 */}
                            <FormPro.Item
                                label="关联技能"
                                className="skill-config"
                                grid24
                                labelCol={{ span: 3 }}
                                wrapperCol={{ span: 21 }}
                            >
                                <div className="skill-config-container">
                                    <FormPro.Item
                                        name={['skill', 'ids']}
                                        noStyle
                                    >
                                        <Select
                                            allowClear
                                            mode="multiple"
                                            style={{ flex: 1, width: '100%' }}
                                            placeholder="请选择关联技能"
                                            options={skillList}
                                        />
                                    </FormPro.Item>
                                    {templateType === 1 && (
                                        <FormPro.Item
                                            name={[
                                                'skill',
                                                'allowFrontLineChange',
                                            ]}
                                            valuePropName="checked"
                                            wrapperCol={{ span: 11 }}
                                            initialValue={true}
                                        >
                                            <Checkbox
                                                style={{
                                                    marginLeft: '10px',
                                                    minWidth: '120px',
                                                }}
                                            >
                                                一线可修改
                                            </Checkbox>
                                        </FormPro.Item>
                                    )}
                                </div>
                            </FormPro.Item>
                            <FormPro.Item
                                label="关联知识库"
                                className="skill-config"
                            >
                                <div className="skill-config-container">
                                    <FormPro.Item
                                        name={['knowledgeBase', 'ids']}
                                        noStyle
                                    >
                                        <Select
                                            mode="multiple"
                                            style={{ flex: 1, width: '100%' }}
                                            placeholder="请选择关联知识库"
                                            options={knowledgeBaseList}
                                        />
                                    </FormPro.Item>
                                    {templateType === 1 && (
                                        <FormPro.Item
                                            name={[
                                                'knowledgeBase',
                                                'allowFrontLineChange',
                                            ]}
                                            valuePropName="checked"
                                            initialValue={true}
                                        >
                                            <Checkbox
                                                style={{
                                                    marginLeft: '10px',
                                                    minWidth: '120px',
                                                }}
                                            >
                                                一线可修改
                                            </Checkbox>
                                        </FormPro.Item>
                                    )}
                                </div>
                            </FormPro.Item>

                            <FormPro.Item
                                label="权限配置"
                                name={['auth', 'type']}
                                rules={[
                                    {
                                        required: true,
                                        message: '请选择权限配置',
                                    },
                                ]}
                            >
                                <Select
                                    placeholder="请选择权限配置"
                                    allowClear
                                    style={{ width: '100%' }}
                                    options={[
                                        {
                                            label: '一线可用',
                                            value: AuthTypeEnum.FRONT_LINE,
                                        },
                                        {
                                            label: '一线不可用',
                                            value: AuthTypeEnum.BACK_LINE,
                                        },
                                    ]}
                                />
                            </FormPro.Item>

                            {authType === AuthTypeEnum.FRONT_LINE && (
                                <FormPro.Item
                                    label="组织架构权限"
                                    name={['auth', 'org']}
                                    rules={[
                                        {
                                            required: true,
                                            message: '请选择组织架构权限',
                                        },
                                    ]}
                                    initialValue={false}
                                >
                                    <OrgnazitionPickerRoo />
                                </FormPro.Item>
                            )}
                        </div>
                    </TabPane>
                    <TabPane label="意向标签配置" name="tab2" forceRender>
                        <IntentionConfig
                            form={form}
                            llmOptions={llmOptions}
                            showIntentionLlmAppId={showIntentionLlmAppId}
                            submittedTenantId={submittedTenantId}
                            isLoadingLlm={isLoadingLlm}
                        />
                    </TabPane>
                    <TabPane label="关键信息提取" name="tab3" forceRender>
                        <KeywordExtract
                            form={form}
                            llmOptions={llmOptions}
                            showIntentionLlmAppId={showIntentionLlmAppId}
                        />
                    </TabPane>
                </Tabs>
            </div>
        </>
    );
};

export default HeadQuartersForm;
