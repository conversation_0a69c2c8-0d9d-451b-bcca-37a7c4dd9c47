import { FormPro, Input, Selector } from '@roo/roo';
import { Checkbox, Input as AntInput } from 'antd';
import { useUpdateEffect } from 'ahooks';
import IntentionLabelContainer from '../../../common/components/IntentionLabelContainer';
import './KeywordExtract.scss';

export default function KeywordExtract(params: any) {
    const {
        form,
        llmOptions,
        showIntentionLlmAppId = true,
        allowBDChangeConfig,
    } = params;

    // 监听enableKeywordExtract的值
    const enableKeywordExtract = FormPro.useWatch(
        ['keyword', 'enableKeywordExtract'],
        form,
    );

    // 监听关键信息提取的allowFrontLineChange变化
    const keywordAllowFrontLineChange = FormPro.useWatch(
        ['keyword', 'allowFrontLineChange'],
        form,
    );

    // 处理关键信息提取allowFrontLineChange变化
    useUpdateEffect(() => {
        if (keywordAllowFrontLineChange === true) {
            const elementList =
                form.getFieldValue(['keyword', 'elementList']) || [];
            if (elementList.length > 0) {
                // 更新所有elementList项的allowFrontLineChange为true
                const updatedElementList = elementList.map((item: any) => ({
                    ...item,
                    allowFrontLineChange: true,
                }));

                form.setFieldValue(
                    ['keyword', 'elementList'],
                    updatedElementList,
                );
                console.log(
                    '关键信息提取allowFrontLineChange设为true，已同步更新所有elementList项',
                );
            }
        }
    }, [keywordAllowFrontLineChange, form]);

    return (
        <div className="intention-label-config basic-config keyword-extract-container">
            <FormPro.Item
                name={['keyword', 'enableKeywordExtract']}
                valuePropName="checked"
                label=" "
                colon={false}
                initialValue={false}
                className="keyword-extract-checkbox"
            >
                <Checkbox>
                    <span>启用该功能</span>
                </Checkbox>
            </FormPro.Item>
            <FormPro.Item
                name={['keyword', 'allowFrontLineChange']}
                valuePropName="checked"
                label=" "
                colon={false}
                initialValue={false}
            >
                <Checkbox disabled={!enableKeywordExtract}>
                    <span>一线可修改</span>
                </Checkbox>
            </FormPro.Item>
            <div className="form-items-container">
                {!enableKeywordExtract && (
                    <div className="form-overlay">
                        <div className="overlay-text">请先启用该功能</div>
                    </div>
                )}
                <FormPro.Item
                    label="大模型"
                    name={['keyword', 'llmName']}
                    rules={[{ required: true, message: '请选择模型' }]}
                >
                    <Selector
                        fieldNames={{ label: 'name', value: 'id' }}
                        placeholder="请选择模型"
                        options={llmOptions}
                        style={{ width: '100%' }}
                    />
                </FormPro.Item>
                {showIntentionLlmAppId && (
                    <FormPro.Item
                        label="计费APPID"
                        name={['keyword', 'llmAppId']}
                        rules={[
                            {
                                required: true,
                                message: '请输入计费APPID',
                            },
                        ]}
                    >
                        <Input placeholder="请输入计费APPID"></Input>
                    </FormPro.Item>
                )}
                <FormPro.Item
                    label="Prompt"
                    name={['keyword', 'systemPrompt']}
                    rules={[{ required: true, message: '请输入prompt' }]}
                >
                    <AntInput.TextArea style={{ minHeight: 300 }} />
                </FormPro.Item>
                <FormPro.Item
                    label="意向标签配置"
                    rules={[
                        {
                            required: true,
                            message: '请选择意向标签配置',
                        },
                    ]}
                >
                    <IntentionLabelContainer
                        name={['keyword', 'elementList']}
                        form={form}
                        formFilters={[
                            'keyName',
                            'paramName',
                            'dataType',
                            'tagPrompt',
                        ]}
                        allowBDChangeConfig={allowBDChangeConfig}
                    />
                </FormPro.Item>
            </div>
        </div>
    );
}
