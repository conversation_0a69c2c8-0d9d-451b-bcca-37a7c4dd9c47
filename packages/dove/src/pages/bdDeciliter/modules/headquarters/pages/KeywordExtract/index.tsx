import { FormPro, Input, Selector } from '@roo/roo';
import { Checkbox, Input as AntInput } from 'antd';
import { useUpdateEffect } from 'ahooks';
import IntentionLabelContainer from '../../../../common/components/IntentionLabelContainer';
import './index.scss';

export default function KeywordExtract(params: any) {
    const { form, llmOptions, showIntentionLlmAppId = true } = params;

    // 监听关键信息提取的enable的值
    const enableKeywordExtract = FormPro.useWatch(
        ['KeywordExtractConfigDto', 'enable'],
        form,
    );

    // 监听关键信息提取的allowFrontLineChange变化
    const keywordAllowFrontLineChange = FormPro.useWatch(
        ['KeywordExtractConfigDto', 'allowFrontLineChange'],
        form,
    );

    // 处理关键信息提取allowFrontLineChange变化
    useUpdateEffect(() => {
        const elementList =
            form.getFieldValue([
                'KeywordExtractConfigDto',
                'KeywordExtractParamConfigDto',
            ]) || [];
        if (elementList.length > 0) {
            const updatedElementList = elementList.map((item: any) => ({
                ...item,
                allowFrontLineChange: keywordAllowFrontLineChange,
            }));

            form.setFieldValue(
                ['KeywordExtractConfigDto', 'KeywordExtractParamConfigDto'],
                updatedElementList,
            );
        }
    }, [keywordAllowFrontLineChange]);

    return (
        <div className="intention-label-config basic-config keyword-extract-container">
            <FormPro.Item
                name={['KeywordExtractConfigDto', 'enable']}
                valuePropName="checked"
                label=" "
                colon={false}
                initialValue={false}
                className="keyword-extract-checkbox"
            >
                <Checkbox>
                    <span>启用该功能</span>
                </Checkbox>
            </FormPro.Item>
            <FormPro.Item
                name={['KeywordExtractConfigDto', 'allowFrontLineChange']}
                valuePropName="checked"
                label=" "
                colon={false}
                initialValue={false}
            >
                <Checkbox disabled={!enableKeywordExtract}>
                    <span>一线可修改</span>
                </Checkbox>
            </FormPro.Item>
            <div className="form-items-container">
                {!enableKeywordExtract && (
                    <div className="form-overlay">
                        <div className="overlay-text">请先启用该功能</div>
                    </div>
                )}
                <FormPro.Item
                    label="大模型"
                    name={['KeywordExtractConfigDto', 'llmName']}
                    rules={[
                        {
                            required: enableKeywordExtract,
                            message: '请选择模型',
                        },
                    ]}
                >
                    <Selector
                        fieldNames={{ label: 'name', value: 'id' }}
                        placeholder="请选择模型"
                        options={llmOptions}
                        style={{ width: '100%' }}
                    />
                </FormPro.Item>
                {showIntentionLlmAppId && (
                    <FormPro.Item
                        label="计费APPID"
                        name={['KeywordExtractConfigDto', 'llmAppId']}
                        rules={[
                            {
                                required: enableKeywordExtract,
                                message: '请输入计费APPID',
                            },
                        ]}
                    >
                        <Input placeholder="请输入计费APPID"></Input>
                    </FormPro.Item>
                )}
                <FormPro.Item
                    label="Prompt"
                    name={['KeywordExtractConfigDto', 'systemPrompt']}
                    rules={[
                        {
                            required: enableKeywordExtract,
                            message: '请输入prompt',
                        },
                    ]}
                >
                    <AntInput.TextArea style={{ minHeight: 300 }} />
                </FormPro.Item>
                <FormPro.Item
                    label="具体所需信息"
                    rules={[
                        {
                            required: true,
                            message: '请选择具体所需信息',
                        },
                    ]}
                >
                    <IntentionLabelContainer
                        name={[
                            'KeywordExtractConfigDto',
                            'KeywordExtractParamConfigDto',
                        ]}
                        form={form}
                        formFilters={[
                            'keyName',
                            'paramName',
                            'dataType',
                            'tagPrompt',
                        ]}
                        allowBDChangeConfig={keywordAllowFrontLineChange}
                        allBtnText="新增所需抽取信息"
                    />
                </FormPro.Item>
            </div>
        </div>
    );
}
