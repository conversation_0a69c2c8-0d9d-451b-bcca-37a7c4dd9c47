.bd-deciliter-container {
    .custom-checkbox {
        background-color: #222222 !important;
    }

    label.doveCustomRoo-checkbox input:checked+span.custom-checkbox {
        border-color: #222222 !important;
    }

    padding: 20px;
    // background-color: #f5f5f5;
    min-height: 100vh;
    display: flex;
    gap: 24px;

    .bd-deciliter-header-tabs-container {
        // margin-bottom: 24px;
        // width: 70%;
        flex: 1;

        .doveCustomRoo-tabs-content {
            padding: 0;
        }

        .skill-config-container {
            display: flex;
            align-items: center;
        }

        .skill-config {
            .doveCustomRoo-form-pro-item {
                margin-bottom: 0;
            }
        }

        .basic-config {
            padding-top: 24px;
            position: relative;

            .prologue-editor {
                // height: 40px;
                margin-top: 0;
                padding: 0;

                .quill-editor-container .quill-editor {
                    padding: 5px;
                    height: 100%;
                    width: 100%;
                    background-color: #fff;

                    .ql-editor {
                        width: 100%;
                        height: 100%;
                    }
                }
            }

            .dynamic-form-list {
                padding: 10px;
            }

            .doveCustomRoo-form-pro-item-label {
                min-width: 100px;
            }
        }
    }

    .bd-deciliter-header-dialog-flow {
        // width: 30%;
        min-width: 350px;
        max-width: 350px;
    }

    .bd-deciliter-form {
        max-width: 1200px;
        margin: 0 auto;
        background-color: #fff;
        padding: 24px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);

        .config-card {
            margin-bottom: 24px;

            .ant-card-head {
                background-color: #fafafa;
                border-bottom: 1px solid #f0f0f0;
            }

            .ant-form-item {
                margin-bottom: 16px;
            }
        }

        .dialog-flow-item,
        .knowledge-item,
        .constraint-item {
            padding: 16px;
            background-color: #fafafa;
            border-radius: 4px;
            margin-bottom: 16px;

            .config-options {
                display: flex;
                align-items: center;
                margin-top: 8px;

                .anticon-minus-circle {
                    color: #ff4d4f;
                    font-size: 16px;
                    cursor: pointer;
                    margin-left: 16px;

                    &:hover {
                        color: #ff7875;
                    }
                }
            }
        }

        .form-actions {
            display: flex;
            justify-content: center;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid #f0f0f0;

            .ant-btn {
                min-width: 120px;
            }
        }
    }

    .param-options {
        margin-bottom: 16px;
    }

    .dialog-flow-item,
    .knowledge-item,
    .constraint-item {
        width: 100%;
        margin-bottom: 16px;

        .ant-form-item {
            flex: 1;
            margin-bottom: 0;
        }
    }
}

.bd-deciliter-footer {
    position: sticky;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16px;
    background-color: #fff;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);

    // justify-content: space-between;
    // align-items: center;
    .ant-btn {
        min-width: 120px;
    }
}

.keyword-extract-checkbox {
    position: absolute;
    top: -10px;
}

.container-fluid {
    padding: 0 !important;
}

.page-content {
    padding: 0 !important;
}

body>.page-content,
body>.page-container>.page-content,
body>.right-side,
.migarating_bd>.wrapper>.col-xs-10,
#main-container,
#xf-bellwether-main-content,
#J-wrapper-content,
.row-offcanvas-left>.right-side {
    padding: 0 !important;
}