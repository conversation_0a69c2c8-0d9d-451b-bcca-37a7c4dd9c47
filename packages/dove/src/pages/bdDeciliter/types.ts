export interface FormData {
    // 基础配置
    model: string;
    voice: string;
    templateType: string;
    templateName: string;

    // 参数配置
    identityDefinition: string;
    openingRemarks: string;

    // 对话流程
    dialogFlows: Array<{
        content: string;
        required: boolean;
        editable: boolean;
    }>;

    // 知识点
    knowledgePoints: Array<{
        content: string;
        required: boolean;
        editable: boolean;
    }>;

    // 约束
    constraints: Array<{
        content: string;
        required: boolean;
        editable: boolean;
    }>;

    // 关联配置
    associatedSkills: string[];
    associatedKnowledgeBases: string[];
}

export interface ConfigItem {
    content: string;
    required: boolean;
    editable: boolean;
}

export interface SelectOption {
    label: string;
    value: string;
}

// 模型选项
export const MODEL_OPTIONS: SelectOption[] = [
    { label: 'Deepseek-r1-friday-64k', value: 'deepseek' },
];

// 音色选项
export const VOICE_OPTIONS: SelectOption[] = [
    { label: '小美', value: 'xiaomei' },
];

// 模板类型选项
export const TEMPLATE_TYPE_OPTIONS: SelectOption[] = [
    { label: '场景模板', value: 'scene' },
];

// 技能选项
export const SKILL_OPTIONS: SelectOption[] = [
    { label: '向商家发送签约短信', value: 'sms' },
    { label: '向商家发送IM消息', value: 'im' },
];

// 知识库选项
export const KNOWLEDGE_BASE_OPTIONS: SelectOption[] = [
    { label: '餐饮品类知识库', value: 'food' },
    { label: '外卖知识库', value: 'waimai' },
];

// 定义选项类型
export interface LlmOption {
    name: string;
    description: string;
}

export interface VoiceOption {
    id: string;
    name: string;
    gender: string;
    description: string;
}

// 表单参数类型
export interface InputParam {
    name: string;
    requiredFrontLineChange?: boolean;
    allowFrontLineChange?: boolean;
    content: string | string[];
}

// 表单数据处理中使用的类型
export interface FormData {
    name?: string;
    systemPrompt?: {
        inputParams: InputParam[];
    };
    intention?: {
        allowFrontLineChange?: boolean;
        [key: string]: any;
    };
    organizationPermission?: any;
    skillAllowEdit?: boolean;
    knowledgeBaseAllowEdit?: boolean;
    [key: string]: any;
}
