import { MentionItem } from '../components/PromptConfig';
import { FormData, InputParam } from '../../types';
import Delta from 'quill-delta';

/**
 * 将inputParams数组转换为对象格式
 * @param inputParamsArray 输入参数数组
 * @returns 以name为键的对象
 */
export const convertInputParamsToObject = (
    inputParamsArray: InputParam[],
): Record<string, InputParam> => {
    // 初始化结果对象
    const result: Record<string, InputParam> = {};

    // 遍历数组
    inputParamsArray.forEach(item => {
        // 确保item有name属性
        if (item && item.name) {
            // 使用name作为键，将整个对象作为值
            result[item.name] = item;
        }
    });

    return result;
};

/**
 * 将文本中的#{xxx}和@{xxx}转换为对应的mention标签，并处理换行符
 * @param text 原始HTML文本
 * @returns 转换后的HTML文本
 */

export const transformTopicList = (data: any): MentionItem[] => {
    if (!data) return [];

    // 将对象的键转换为MentionItem数组
    return Object.keys(data || {}).map((key, index) => ({
        id: index + 1,
        value: key,
    }));
};

export function transformTextToDelta(
    text: string,
    users: any[] = [],
    topics: any[] = [],
): Delta {
    if (!text) return new Delta();

    const delta = new Delta();

    // 正则：@{xxx}、#{xxx}
    const mentionReg = /(@|#)\{([^}]+)\}/g;

    let lastIndex = 0;
    let match: RegExpExecArray | null;

    while ((match = mentionReg.exec(text)) !== null) {
        // 1. 普通文本
        if (match.index > lastIndex) {
            delta.insert(text.slice(lastIndex, match.index));
        }
        // 2. mention
        const denotationChar = match[1];
        const value = match[2];
        delta.insert({
            mention: {
                denotationChar,
                value,
                id: Math.floor(Math.random() * 1000),
                // 可扩展id等属性
            },
        });
        lastIndex = mentionReg.lastIndex;
    }

    // 3. 剩余文本
    if (lastIndex < text.length) {
        delta.insert(text.slice(lastIndex));
    }

    // 4. 处理换行符，拆分为多段
    // Quill 的 Delta 支持 \n 作为段落分隔
    // 这里简单处理：将所有 \n 替换为 '\n'
    // 如果需要每行一个段落，可以进一步处理

    // 这里直接返回 delta，Quill 会自动处理 \n
    return delta;
}

export const transformTextToMentions = (
    text: string,
    users: any[] = [],
    topics: any[] = [],
): string => {
    if (!text) return text;

    // 转换@{xxx}格式
    let transformedText = text.replace(/@{([^}]+)}/g, (match, username) => {
        return `<span class="mention" data-index="0" data-denotation-char="@" data-id="${Math.floor(
            Math.random() * 1000,
        )}" data-value="${username}"><span contenteditable="false">@${username}</span></span>`;
    });

    // 转换#{xxx}格式
    transformedText = transformedText.replace(
        /#{([^}]+)}/g,
        (match, tagname) => {
            return `<span class="mention" data-index="0" data-denotation-char="#" data-id="${Math.floor(
                Math.random() * 1000,
            )}" data-value="${tagname}"><span contenteditable="false">#${tagname}</span></span>`;
        },
    );

    // 处理换行符，将\n转换为HTML段落
    if (transformedText.includes('\n')) {
        // 按换行符分割文本
        const lines = transformedText.split('\n');
        // 用<p>标签包裹每一行，并重新连接
        transformedText = lines
            .map(line => (line.trim() === '' ? '<p></p>' : `<p>${line}</p>`))
            .join('');
    } else if (!transformedText.startsWith('<p>')) {
        // 如果没有换行，但也不是以<p>开头的，也用<p>包裹
        transformedText = `<p>${transformedText}</p>`;
    }

    return transformedText;
};

/**
 * 将mention标签转换回原始的#{xxx}和@{xxx}格式，并处理HTML换行为\n
 * @param html 包含mention标签的HTML文本
 * @returns 转换后的原始格式文本
 */
export const transformMentionsToText = (
    html: string,
    users: any[] = [],
    topics: any[] = [],
): string => {
    if (!html) return html;

    // 转换@mention标签为@{xxx}格式
    let originalText = html.replace(
        /<span class="mention"[^>]*data-denotation-char="@"[^>]*data-value="([^"]+)"[^>]*>.*?<\/span>/g,
        (match, value) => {
            return `@{${value}}`;
        },
    );

    // 转换#mention标签为#{xxx}格式
    originalText = originalText.replace(
        /<span class="mention"[^>]*data-denotation-char="#"[^>]*data-value="([^"]+)"[^>]*>.*?<\/span>/g,
        (match, value) => {
            return `#{${value}}`;
        },
    );

    // 移除可能残留的</span>标签
    originalText = originalText.replace(/<\/span>/g, '');

    // 首先处理空行标签，将<p><br></p>转换为单个换行符
    originalText = originalText.replace(/<p><br><\/p>/g, '\n');

    // 处理HTML换行为\n，移除所有空的<p>标签
    originalText = originalText.replace(/<p><\/p>/g, '');

    // 将</p><p>转换为换行符
    originalText = originalText.replace(/<\/p>\s*<p>/g, '\n');

    // 移除所有剩余的<p>和</p>标签
    originalText = originalText.replace(/<\/?p>/g, '');

    // 连续的多个换行符压缩为两个换行符（保留段落感但避免过多空行）
    originalText = originalText.replace(/\n{3,}/g, '\n\n');

    // 移除任何可能残留的HTML标签
    originalText = originalText.replace(/<[^>]*>/g, '');

    return originalText;
};

export const ensureCorrectLineBreaks = (str: string): string => {
    if (typeof str !== 'string') return '';
    // 替换所有 "/n" 为 "\n"
    return str.replace(/\/n/g, '\n');
};

// 辅助函数：将数组转换为换行符分隔的字符串
export const convertArrayToString = (array: any[] | undefined): string => {
    if (!array || !Array.isArray(array)) return '';

    // 从数组中提取content字段值并用换行符连接
    return array
        .map(item => {
            // 确保item.content中的换行符是正确的
            const content = item.content || '';
            return typeof content === 'string'
                ? ensureCorrectLineBreaks(content)
                : content;
        })
        .filter(content => content.trim() !== '')
        .join('\n');
};

/**
 * 处理表单数据的函数，将API返回的数据转换为表单可用的格式
 * @param res API返回的原始数据
 * @returns 处理后的表单数据
 */
export const processFormData = (res: FormData): FormData => {
    // 处理对话流程、知识点和约束中的content，将其转换为数组
    if (res.systemPrompt && res.systemPrompt.inputParams) {
        const { inputParams } = res.systemPrompt;

        // 遍历inputParams数组，处理特定名称的项目
        if (Array.isArray(inputParams)) {
            // 将数组转换为对象格式
            const inputParamsObj: Record<string, InputParam> = {};

            // 检查是否存在必要的项目
            const hasDialogFlow = inputParams.some(
                item => item.name === '对话流程',
            );
            const hasKnowledgePoints = inputParams.some(
                item => item.name === '知识点',
            );
            const hasConstraints = inputParams.some(
                item => item.name === '约束',
            );

            // 如果不存在，添加默认项
            if (!hasDialogFlow) {
                inputParamsObj['对话流程'] = {
                    name: '对话流程',
                    content: [''],
                    allowFrontLineChange: true,
                    requiredFrontLineChange: true,
                };
            }

            if (!hasKnowledgePoints) {
                inputParamsObj['知识点'] = {
                    name: '知识点',
                    content: [''],
                    allowFrontLineChange: true,
                    requiredFrontLineChange: false,
                };
            }

            if (!hasConstraints) {
                inputParamsObj['约束'] = {
                    name: '约束',
                    content: [''],
                    allowFrontLineChange: true,
                    requiredFrontLineChange: false,
                };
            }

            // 遍历数组，将每个项目转换为对象的属性
            inputParams.forEach(item => {
                if (item.name) {
                    // 检查是否是对话流程、知识点或约束
                    if (
                        (item.name === '对话流程' ||
                            item.name === '知识点' ||
                            item.name === '约束') &&
                        typeof item.content === 'string'
                    ) {
                        // 将字符串按换行符分割成数组，并过滤掉空行
                        const contentArray = item.content
                            .split('\n')
                            .map(line => line.trim().replace(/\t/g, '\n'))
                            .filter(line => line !== '');

                        // 设置为数组格式
                        inputParamsObj[item.name] = {
                            ...item,
                            content:
                                contentArray.length > 0 ? contentArray : [''],
                            allowFrontLineChange:
                                item.allowFrontLineChange !== undefined
                                    ? item.allowFrontLineChange
                                    : true,
                            requiredFrontLineChange:
                                item.requiredFrontLineChange !== undefined
                                    ? item.requiredFrontLineChange
                                    : true,
                        };
                    } else {
                        // 对于其他类型的项目，保持不变
                        inputParamsObj[item.name] = item;
                    }
                }
            });

            // 更新处理后的inputParams
            // 将Record对象转换回数组格式，以符合表单期望的类型
            res.systemPrompt.inputParams = Object.values(inputParamsObj);
        }
        console.log('处理后的inputParams:', res.systemPrompt.inputParams);
    }

    // 确保 intention 对象结构正确
    if (res.intention) {
        // 确保 allowFrontLineChange 为布尔值
        if (res.intention.allowFrontLineChange !== undefined) {
            res.intention.allowFrontLineChange =
                !!res.intention.allowFrontLineChange;
        }
    }

    return res;
};

// 辅助函数：将换行符分隔的字符串转换为数组
export const convertStringToArray = (str: string): any[] => {
    if (!str) return [{ content: '' }]; // 空数据时返回一个带空content的对象

    // 处理可能错误的换行符
    const correctedStr = ensureCorrectLineBreaks(str);

    // 将字符串按换行符分割，并为每一行创建一个带content字段的对象
    return correctedStr
        .split('\n')
        .map(line => line.trim().replace(/\t/g, '\n'))
        .filter(line => line !== '')
        .map(line => ({ content: line }));
};

export const enum Environment {
    DEV = 'dev',
    TEST = 'test',
    ST = 'st',
    PRORD = 'prod',
}
export const getEnvironment = () => {
    const host = location.host;

    // 检查是否为本地环境（localhost或IP地址）
    if (
        host.includes('localhost') ||
        /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}(:\d+)?$/.test(host)
    ) {
        return Environment.DEV;
    }
    const matchResult =
        host.match(/ocrm.waimai.([a-z]+).sankuai.com/) ||
        host.match(/igate.waimai.([a-z]+).sankuai.com/);
    const env: Environment[] = [
        Environment.DEV,
        Environment.TEST,
        Environment.ST,
    ];

    if (!matchResult || !env.includes(matchResult[1] as Environment)) {
        return Environment.PRORD;
    }

    return matchResult[1];
};

export const getOcrmHost = () => {
    const env = getEnvironment();

    switch (env) {
        case Environment.PRORD:
            return 'https://wm.ocrm.meituan.com';
        default:
            return `https://wm-ocrm.waimai.${env}.sankuai.com`;
    }
};

// 处理系统提示输入参数，将特定字段的字符串内容转换为数组格式
export const processInputParams = (data: any): any => {
    if (data.systemPrompt?.inputParams) {
        // 定义需要特殊处理的字段
        const specialFields = ['对话流程', '知识点', '约束'];

        // 先处理存在的字段
        Object.entries(data.systemPrompt.inputParams).forEach(
            ([key, value]: [string, any]) => {
                // 检查是否是特殊字段
                const isSpecialField = specialFields.includes(key);

                // 如果值为空或undefined且是特殊字段，设置默认值
                if ((!value || (value && !value.content)) && isSpecialField) {
                    data.systemPrompt.inputParams[key] = {
                        ...(value || {}),
                        name: key,
                        content: [''],
                    };
                    return;
                }

                // 如果不为空且是特殊字段，需要转换格式
                if (value && isSpecialField) {
                    if (typeof value.content === 'string') {
                        // 将字符串转换为数组
                        value.content = value.content
                            .split('\n')
                            .map(item => item.trim().replace(/\t/g, '\n'))
                            .filter(Boolean);

                        // 确保即使过滤后为空也设置一个默认值
                        if (value.content.length === 0) {
                            value.content = [''];
                        }
                    } else if (
                        !Array.isArray(value.content) ||
                        value.content.length === 0
                    ) {
                        // 如果不是数组或者是空数组，设置默认值
                        value.content = [''];
                    }
                }
            },
        );

        // 确保特殊字段在对象中存在，即使原始数据中没有
        specialFields.forEach(field => {
            if (!data.systemPrompt.inputParams[field]) {
                data.systemPrompt.inputParams[field] = {
                    name: field,
                    content: [''],
                };
            }
        });
    }

    return data;
};
