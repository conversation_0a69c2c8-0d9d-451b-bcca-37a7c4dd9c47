import React, { useState, useEffect, useRef } from 'react';
import { Selector } from '@roo/roo';
import { SoundOutlined, LoadingOutlined, PauseOutlined } from '@ant-design/icons';
import { Button, message, Select } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import './styles/VoiceSelector.scss';
import { getAccessToken } from '../../services';
import axios from 'axios';

// 定义组件接口
interface VoiceSelectorProps {
  value?: string;
  onChange?: (value: string) => void;
  options: Array<{ id: string; name: string;[key: string]: any }>;
  placeholder?: string;
  style?: React.CSSProperties;
  disabled?: boolean;
  rules?: any[];
}


const VoiceSelector: React.FC<VoiceSelectorProps> = ({
  value,
  onChange,
  options,
  placeholder = '请选择音色',
  style = { width: '100%' },
  disabled = false,
}) => {
  const [playingId, setPlayingId] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [accessToken, setAccessToken] = useState<string>('');
  const audioRef = useRef<HTMLAudioElement | null>(null);

  // 当组件卸载或播放新音频时，停止当前播放
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  // 处理音色播放
  const handlePlay = async (voiceId: string, voiceName: string, e: React.MouseEvent) => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发Selector选择

    try {
      // 如果正在播放，则停止
      if (playingId === voiceId) {
        if (audioRef.current) {
          audioRef.current.pause();
          audioRef.current = null;
        }
        setPlayingId(null);
        return;
      }

      // 如果有其他音频正在播放，先停止
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }

      // 设置播放状态
      setPlayingId(voiceId);
      setLoading(true);
      // 示例文本数组，随机选择一个播放
      const sampleTexts = [
        "<speak>。。。哎，老板您好，我这边美团的业务助理，呃...是石锅烤肉拌饭的老板是吗？</speak>",
      ];

      // 随机选择一个示例文本
      // const randomIndex = Math.floor(Math.random() * sampleTexts.length);
      const sampleText = sampleTexts[0];

      try {
        // 只有在第一次调用时获取token，后续使用已存储的token
        let token = accessToken;
        if (!token) {
          token = await getAccessToken();
          setAccessToken(token);
        }

        // 使用apiCaller调用TTS API，处理内部认证
        const postData = {
          text: sampleText,
          voice_name: voiceId,
          speed: 50,
          volume: 50,
          sample_rate: 24000,
          audio_format: "wav", // 修改为wav格式，浏览器兼容性更好
          enable_extra_volume: 0,
        };


        const response = await axios.post('/xianfu/api-v2/dove/tts/v1/synthesis', postData, {
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'SessionId': "f2ccef1a-f0e2-4184-a42b-9564729e49f22",
            'Token': token,
          },
          responseType: 'blob' // 添加responseType设置为blob
        });

        if (response.status !== 200) {
          throw new Error(`TTS请求失败: ${response.status}`);
        }

        // 获取响应的blob数据
        const audioBlob = response.data;
        console.log('音频数据类型:', audioBlob.type); // 添加日志检查blob类型

        // 创建一个带有正确MIME类型的URL
        const audioUrl = URL.createObjectURL(
          new Blob([audioBlob], { type: 'audio/wav' })
        );

        const audio = new Audio(audioUrl);
        audioRef.current = audio;

        // 设置音频播放结束事件
        audio.onended = () => {
          setPlayingId(null);
          URL.revokeObjectURL(audioUrl);
        };

        // 设置音频错误事件
        audio.onerror = (e) => {
          console.error('音频播放错误:', e);
          message.error('音频播放失败');
          setPlayingId(null);
          setLoading(false);
          URL.revokeObjectURL(audioUrl);
        };

        // 播放音频
        await audio.play();
        setLoading(false);
      } catch (error) {
        console.error('API请求失败:', error);
        message.error('获取音频失败');
        setPlayingId(null);
        setLoading(false);
      }
    } catch (error) {
      console.error('播放音频失败:', error);
      message.error('试听失败，请稍后再试');
      setPlayingId(null);
      setLoading(false);
    }
  };

  // 自定义渲染选项
  const renderOption = (option: any) => {
    const isPlaying = playingId === option.id;

    return (
      <div className="voice-selector-option">
        <span className="voice-name" title={option.name || option.label}>{option.name || option.label}</span>
        <Button
          type="text"
          size="small"
          className="play-button"
          icon={loading && isPlaying ? <LoadingOutlined /> : isPlaying ? <PauseOutlined /> : <SoundOutlined />}
          onClick={(e) => handlePlay(option.id || option.value, option.name || option.label, e)}
        />
      </div>
    );
  };

  // 自定义标签渲染
  const renderTag = (props: any) => {
    const { item, onClose } = props;
    const voiceId = item.id || item.value;
    const voiceName = item.name || item.label;
    const isPlaying = playingId === voiceId;

    return (
      <div className="voice-selector-tag">
        <span className="voice-tag-name">{voiceName}</span>
        <Button
          type="text"
          size="small"
          className="play-button"
          icon={loading && isPlaying ? <LoadingOutlined /> : isPlaying ? <PauseOutlined /> : <SoundOutlined />}
          onClick={(e) => handlePlay(voiceId, voiceName, e)}
        />
      </div>
    );
  };

  // 确保监听选项数据变更
  useEffect(() => {
    if (options && options.length > 0) {
      console.log('VoiceSelector options:', options);
    }
  }, [options]);

  return (
    <div className="voice-selector-container">
      <Select
        placeholder={placeholder}
        options={options}
        fieldNames={{ label: 'name', value: 'id' }}
        style={style}
        value={value}
        onChange={onChange}
        disabled={disabled}
        optionRender={(option) => renderOption(option.data)}
        tagRender={renderTag}
        popupClassName="voice-selector-popup"
        allowClear
      />
    </div>
  );
};

export default VoiceSelector;