import React, { useEffect, useRef } from 'react';
import { FormPro, Input, Panel, CheckBox } from '@roo/roo';
import ParamCard from './ParamCard';
import './styles/ParamConfig.scss';
import DynamicFormList from './DynamicFormList';

// 定义每个参数项的接口
interface ParamItem {
    name: string;
    content: string | string[] | any; // 扩展类型定义，接受更广泛的内容类型
    allowFrontLineChange?: boolean;
    requiredFrontLineChange?: boolean;
}

// 定义参数对象的接口
interface ParamObject {
    [key: string]: ParamItem;
}

interface ParamConfigProps {
    form?: any;
    value?: ParamObject; // 更改为对象类型
    onChange?: (value: ParamObject) => void;
    isFrontLine?: boolean;
}

const ParamConfig: React.FC<ParamConfigProps> = ({ form, value, onChange, isFrontLine }) => {

    return (
        <div className="param-config">
            <FormPro.Item
                noStyle
                shouldUpdate={(prevValues: any, currentValues: any) => {
                    return JSON.stringify(prevValues?.systemPrompt?.inputParams) !==
                        JSON.stringify(currentValues?.systemPrompt?.inputParams);
                }}
                // grid24
                // labelCol={{ span: 3 }}
                // wrapperCol={{ span: 21 }}
            >
                {() => {
                    return (
                        <>
                            <div className="param-item">
                                <FormPro.Item name={['systemPrompt', 'inputParams', "身份信息"]}
                                    wrapperCol={{ span: 24 }}
                                    grid24
                                    labelCol={{ span: 40}}
                                    rules={[{ required: isFrontLine, message: '请输入身份信息' }]}
                                >
                                    <ParamCard
                                        title="身份信息"
                                        name={['systemPrompt', 'inputParams', "身份信息"]}
                                        form={form}
                                    >
                                        <Input.Textarea
                                            placeholder="请输入身份信息"
                                            autoSize={{ minRows: 3, maxRows: 6 }}
                                            style={{ width: '100%' }}
                                        />
                                    </ParamCard>
                                </FormPro.Item>
                            </div>

                            <FormPro.Item
                                style={{ width: '100%' }}
                                wrapperCol={{ span: 24 }}
                                name={['systemPrompt', 'inputParams', '对话流程']}
                                rules={[{ required: isFrontLine, message: '请输入对话流程' }]}
                            >
                                <ParamCard
                                    title="对话流程"
                                    name={['systemPrompt', 'inputParams', '对话流程']}
                                    form={form}
                                    noFormItem={true}
                                >
                                    <DynamicFormList
                                        name={['systemPrompt', 'inputParams', '对话流程']}
                                        form={form}
                                        required={true}
                                        isFrontLine={isFrontLine}
                                    />
                                </ParamCard>
                            </FormPro.Item>

                            <FormPro.Item
                                style={{ width: '100%' }}
                                wrapperCol={{ span: 24 }}
                                name={['systemPrompt', 'inputParams', '知识点']}
                            >
                                <ParamCard
                                    title="知识点"
                                    name={['systemPrompt', 'inputParams', '知识点']}
                                    form={form}
                                    noFormItem={true}
                                >
                                    <DynamicFormList
                                        name={['systemPrompt', 'inputParams', '知识点']}
                                        form={form}
                                        required={false}
                                        isFrontLine={isFrontLine}
                                    />
                                </ParamCard>
                            </FormPro.Item>

                            <FormPro.Item
                                style={{ width: '100%' }}
                                wrapperCol={{ span: 24 }}
                                name={['systemPrompt', 'inputParams', '约束']}
                            // rules={[{ validator: validateListItems }]}
                            >
                                <ParamCard
                                    title="约束"
                                    name={['systemPrompt', 'inputParams', '约束']}
                                    form={form}
                                >
                                    <DynamicFormList
                                        name={['systemPrompt', 'inputParams', '约束']}
                                        form={form}
                                        isFrontLine={isFrontLine}
                                    />
                                </ParamCard>
                            </FormPro.Item>
                        </>
                    )
                }}
            </FormPro.Item>
        </div>
    );
};

export default ParamConfig;
