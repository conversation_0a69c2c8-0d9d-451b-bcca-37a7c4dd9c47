import { useState, useEffect } from 'react';
import Delta from 'quill-delta';
import RichTextEditor from './RichTextEditor/index';
import './styles/PromptConfig.scss';
import { transformTextToDelta } from '../utils';

interface MentionItem {
    id: number;
    value: string;
    avatar?: string;
    description?: string;
}

interface LablePromptConfigProps {
    form?: any;
    value?: string;
    onChange?: (value: string) => void;
    lableList?: MentionItem[];
}

// 用户数据

const LablePromptConfig: React.FC<LablePromptConfigProps> = ({
    form,
    value,
    onChange,
    lableList = [],
}) => {
    // 只保留一个状态，避免状态不同步问题
    const [editorContent, setEditorContent] = useState<
        Delta | string | undefined
    >(value ? transformTextToDelta(value) : undefined);

    // 只在初始化时设置一次默认值，避免每次value变化都重新设置
    useEffect(() => {
        if (value && !editorContent) {
            setEditorContent(transformTextToDelta(value));
        }
    }, [value, editorContent]);

    const handleChange = (content: {
        delta: Delta;
        html: string;
        text: string;
    }) => {
        const { delta, text } = content;
        // 直接更新本地状态，保持富文本格式
        setEditorContent(delta);

        // 调用外部onChange，但转换回原始格式
        if (onChange) {
            // const originalFormatText = transformMentionsToText(
            //     text,
            //     [],
            //     lableList,
            // );
            onChange(text);
        }
    };

    const source = (
        searchTerm: string,
        renderList: (values: MentionItem[], searchTerm: string) => void,
        mentionChar: string,
    ) => {
        let values: MentionItem[] = [];
        if (mentionChar == '#') {
            const newLableList =
                form
                    .getFieldValue('intention')
                    ?.elementList?.filter((item: any) => item?.tag)
                    .map((item: any, index: number) => ({
                        id: index + 1,
                        value: item.tag,
                    })) || [];
            values = newLableList;
        } else {
            values = lableList;
        }

        if (searchTerm.length === 0) {
            renderList(values, searchTerm);
        } else {
            const matches = values.filter(item =>
                item.value.toLowerCase().includes(searchTerm.toLowerCase()),
            );
            renderList(matches, searchTerm);
        }
    };
    return (
        <div className="rich-editor-wrapper">
            <RichTextEditor
                defaultValue={editorContent}
                onChange={handleChange}
                placeholder="请输入内容..."
                mentionDenotationChars={['#', '@']}
                source={source}
            />
        </div>
    );
};

export default LablePromptConfig;
