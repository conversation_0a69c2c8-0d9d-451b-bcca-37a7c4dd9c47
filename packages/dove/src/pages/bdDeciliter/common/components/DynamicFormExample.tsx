import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Button } from '@roo/roo';
import { Card } from 'antd';
import DynamicFormList from './DynamicFormList';
import './styles/DynamicFormExample.scss';

const DynamicFormExample: React.FC = () => {
  const [form] = FormPro.useForm();
  const [dialogFlowData, setDialogFlowData] = useState({
    name: "对话流程",
    content: [
      "自我介绍",
      "了解商家需求",
      "提供解决方案",
      "总结沟通内容"
    ],
    allowFrontLineChange: true,
    requiredFrontLineChange: true
  });

  const handleFormChange = (values: any) => {
    console.log('表单变更:', values);
    setDialogFlowData({
      ...dialogFlowData,
      content: values
    });
  };

  const handleAllowChangeToggle = () => {
    setDialogFlowData({
      ...dialogFlowData,
      allowFrontLineChange: !dialogFlowData.allowFrontLineChange
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('表单提交:', values);
      const formattedDialogFlow = {
        ...dialogFlowData,
        content: values.dialogFlow
      };
      console.log('格式化后的对话流程:', formattedDialogFlow);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Card className="dynamic-form-example-card">
      <h2 className="example-title">对话流程配置</h2>
      <div className="example-content">
        <FormPro form={form} grid24 labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <div className="example-controls">
            <Button 
              type={dialogFlowData.allowFrontLineChange ? "brand" : "hollow"}
              onClick={handleAllowChangeToggle}
              style={{ marginBottom: '16px' }}
            >
              {dialogFlowData.allowFrontLineChange ? "允许编辑" : "禁止编辑"}
            </Button>
          </div>
          
          <DynamicFormList
            name="dialogFlow"
            value={dialogFlowData.content}
            onChange={handleFormChange}
            allowFrontLineChange={dialogFlowData.allowFrontLineChange}
            requiredFrontLineChange={dialogFlowData.requiredFrontLineChange}
            title="对话流程"
          />
          
          <div className="form-actions">
            <Button type="hollow">取消</Button>
            <Button type="brand" onClick={handleSubmit}>保存</Button>
          </div>
        </FormPro>
      </div>
      
      <div className="example-preview">
        <h3>预览区</h3>
        <div className="preview-content">
          <h4>{dialogFlowData.name}</h4>
          <ul className="flow-steps">
            {dialogFlowData.content.map((step, index) => (
              <li key={index} className="flow-step">
                <span className="step-number">{index + 1}</span>
                <span className="step-content">{step}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </Card>
  );
};

export default DynamicFormExample; 