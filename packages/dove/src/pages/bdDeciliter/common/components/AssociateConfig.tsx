import React from 'react';
import { Form, Select, Card } from 'antd';
import { SKILL_OPTIONS, KNOWLEDGE_BASE_OPTIONS } from '../../types';

const AssociateConfig: React.FC = () => {
    return (
        <Card title="关联配置" className="config-card">
            <Form.Item
                label="关联技能"
                name="associatedSkills"
                rules={[{ required: true, message: '请选择关联技能' }]}
            >
                <Select
                    mode="multiple"
                    placeholder="请选择关联技能"
                    options={SKILL_OPTIONS}
                />
            </Form.Item>

            <Form.Item
                label="关联知识库"
                name="associatedKnowledgeBases"
                rules={[{ required: true, message: '请选择关联知识库' }]}
            >
                <Select
                    mode="multiple"
                    placeholder="请选择关联知识库"
                    options={KNOWLEDGE_BASE_OPTIONS}
                />
            </Form.Item>
        </Card>
    );
};

export default AssociateConfig;
