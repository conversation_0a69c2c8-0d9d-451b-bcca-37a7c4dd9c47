import { useState, useEffect, useRef } from 'react';
import Delta from 'quill-delta';
import { Card } from 'antd';
import RichTextEditor from './RichTextEditor/index';
import './styles/PromptConfig.scss';
import { transformTextToDelta, transformTopicList } from '../utils';
import { fetchPlaceholders } from '../../services';
import { DEFAULT_HEAD_PROMPT, DEFAULT_PROMPT_SCENE } from '../../App';

export interface MentionItem {
    id: number;
    value: string;
    avatar?: string;
    description?: string;
}

interface PromptConfigProps {
    form?: any;
    value?: string;
    onChange?: (value: string) => void;
    isShowTitle?: boolean;
    style?: React.CSSProperties;
    className?: string;
    typeValue?: string | number;
    templateId?: string | null;
    isPrompt?: boolean;
    title?: string;
}

const PromptConfig: React.FC<PromptConfigProps> = ({
    form,
    value,
    onChange,
    isShowTitle = true,
    style,
    className,
    typeValue,
    templateId,
    isPrompt = false,
    title,
}) => {
    // 只保留一个状态，避免状态不同步问题
    const [placeholderList, setPlaceholderList] = useState<MentionItem[]>([]);
    const [topicList, setTopicList] = useState<MentionItem[]>([]);
    const [editorContent, setEditorContent] = useState<Delta | string>(
        value ? transformTextToDelta(value) : '',
    );

    // 设置默认 prompt
    useEffect(() => {
        if (!isPrompt) return;
        if (templateId) return;
        if (typeValue) {
            const defaultPrompt =
                typeValue === 2 ? DEFAULT_HEAD_PROMPT : DEFAULT_PROMPT_SCENE;

            setEditorContent(transformTextToDelta(defaultPrompt));
            if (onChange) {
                onChange(defaultPrompt);
            }
        }
    }, [typeValue, templateId]);

    // 初始化时设置默认值
    useEffect(() => {
        const fetchPlaceholderList = async () => {
            const res = await fetchPlaceholders({
                name: '',
                page: 1,
                pageSize: 100,
                status: 1,
            });
            const placeholderList = res.map((item: any) => {
                return {
                    id: item.id,
                    value: item.showName,
                    ...item,
                };
            });
            setPlaceholderList(placeholderList);
        };

        fetchPlaceholderList();
    }, [form]);

    // 处理value和typeValue变化
    useEffect(() => {
        // typeValue变化时，强制更新富文本内容

        // 普通value更新的情况，只在特定条件下更新
        if (value && !editorContent) {
            const transformedContent = transformTextToDelta(value);

            setEditorContent(transformedContent);
        }
    }, [value]);

    const handleChange = (content: {
        delta: Delta;
        html: string;
        text: string;
    }) => {
        const { html, text } = content;
        // 更新编辑器内容

        setEditorContent(html);

        // 调用外部onChange，但转换回原始格式
        if (onChange) {
            onChange(text);
        }
    };

    const handleBlur = (content: { delta: Delta; html: string }) => {
        const { delta } = content;
        if (delta) {
            setEditorContent(delta);
        }
    };

    const source = async (
        searchTerm: string,
        renderList: (values: MentionItem[], searchTerm: string) => void,
        mentionChar: string,
    ) => {
        let values: MentionItem[] = [];
        if (mentionChar === '@') {
            values = placeholderList;
        } else {
            const topicList =
                transformTopicList(
                    form?.getFieldValue(['systemPrompt', 'inputParams']),
                ) || [];
            if (topicList.length > 0) {
                values = topicList;
            } else {
                values = [];
            }
        }

        if (searchTerm.length === 0) {
            renderList(values, searchTerm);
        } else {
            const matches = values.filter(item =>
                item.value.toLowerCase().includes(searchTerm.toLowerCase()),
            );
            renderList(matches, searchTerm);
        }
    };

    return (
        <div
            className={`rich-editor-wrapper ${className}`}
            key={placeholderList.length + topicList.length}
            style={style}
        >
            {isShowTitle && (
                <span className="editor-tips">
                    {title || '输入"@"添加通用参数；输入"#"添加配置参数'}
                </span>
            )}
            <RichTextEditor
                defaultValue={editorContent}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder="请输入内容..."
                mentionDenotationChars={['@', '#']}
                source={source}
            />
        </div>
    );
};

export default PromptConfig;
