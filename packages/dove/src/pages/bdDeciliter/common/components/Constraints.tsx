import React from 'react';
import { Form, Input, Card, Button, Space, Checkbox } from 'antd';
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';

const { TextArea } = Input;

const Constraints: React.FC = () => {
    return (
        <Card title="约束" className="config-card">
            <Form.List name="constraints">
                {(fields, { add, remove }) => (
                    <>
                        {fields.map(({ key, name, ...restField }) => (
                            <div key={key} className="constraint-item">
                                <Form.Item
                                    {...restField}
                                    name={[name, 'content']}
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入约束内容',
                                        },
                                    ]}
                                >
                                    <TextArea
                                        placeholder="请输入约束内容"
                                        autoSize={{ minRows: 3, maxRows: 6 }}
                                    />
                                </Form.Item>
                                <Space className="config-options">
                                    <Form.Item
                                        {...restField}
                                        name={[name, 'required']}
                                        valuePropName="checked"
                                    >
                                        <Checkbox>一线必填</Checkbox>
                                    </Form.Item>
                                    <Form.Item
                                        {...restField}
                                        name={[name, 'editable']}
                                        valuePropName="checked"
                                    >
                                        <Checkbox>一线可修改</Checkbox>
                                    </Form.Item>
                                    <MinusCircleOutlined
                                        onClick={() => remove(name)}
                                    />
                                </Space>
                            </div>
                        ))}
                        <Form.Item>
                            <Button
                                type="dashed"
                                onClick={() =>
                                    add({
                                        content: '',
                                        required: false,
                                        editable: false,
                                    })
                                }
                                block
                                icon={<PlusOutlined />}
                            >
                                添加约束
                            </Button>
                        </Form.Item>
                    </>
                )}
            </Form.List>
        </Card>
    );
};

export default Constraints;
