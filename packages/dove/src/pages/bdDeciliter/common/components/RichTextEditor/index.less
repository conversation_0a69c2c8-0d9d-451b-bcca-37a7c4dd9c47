.rich-text-editor {
    .quill {
        // 编辑器容器样式
        border: 1px solid #e8e8e8;
        border-radius: 4px;

        .ql-toolbar {
            // 工具栏样式
            border-bottom: 1px solid #e8e8e8;
            background-color: #fafafa;
            padding: 8px;

            .ql-formats {
                margin-right: 15px;
            }

            button {
                &:hover {
                    color: #1890ff;
                }

                &.ql-active {
                    color: #1890ff;
                }
            }
        }

        .ql-container {
            // 内容区域样式
            font-size: 14px;
            line-height: 1.5;
            min-height: 120px;

            .ql-editor {
                min-height: 120px;

                p {
                    margin-bottom: 8px;
                }

                &.ql-blank::before {
                    font-style: normal;
                    color: #bfbfbf;
                }

                // @用户标签样式
                .mention {
                    color: #1890ff;
                    background-color: #e6f7ff;
                    padding: 2px 4px;
                    border-radius: 3px;
                    border: 1px solid #91d5ff;
                    margin: 0 2px;
                }

                // #话题标签样式
                .hashtag {
                    color: #722ed1;
                    background-color: #f9f0ff;
                    padding: 2px 4px;
                    border-radius: 3px;
                    border: 1px solid #d3adf7;
                    margin: 0 2px;
                }

                // 自定义触发标签样式
                .custom-trigger {
                    color: #fa8c16;
                    background-color: #fff7e6;
                    padding: 2px 4px;
                    border-radius: 3px;
                    border: 1px solid #ffd591;
                    margin: 0 2px;
                }
            }
        }
    }

    position: relative;
    margin-bottom: 20px;

    .trigger-hints {
        margin-bottom: 8px;
        color: #888;
        font-size: 12px;

        .trigger-hint {
            background-color: #f0f0f0;
            padding: 2px 4px;
            border-radius: 3px;
            color: #1890ff;
            font-weight: bold;
            margin: 0 2px;
        }
    }

    // 优化mention列表容器样式
    .ql-mention-list-container {
        width: 200px;
        background: white;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    // 优化mention列表样式
    .ql-mention-list {
        list-style: none;
        margin: 0;
        padding: 4px 0;
        max-height: 250px;
        overflow-y: auto;

        .ql-mention-list-item {
            padding: 8px 12px;
            cursor: pointer;

            &:hover {
                background-color: #f5f5f5;
            }

            &.selected {
                background-color: #e6f7ff;
                color: #1890ff;
            }
        }
    }
}

// 编辑器操作区域和预览样式
.rich-editor-wrapper {
    margin-top: 10px;
    .editor-tips {

        font-weight: 400;
        color: #999999;
        font-family: PingFang SC;
        font-size: 12px;
        line-height: 18px;
        text-align: left;
    }
    .editor-actions {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-top: 16px;
        border: 1px solid #e8e8e8;

        .editor-preview {
            margin-top: 16px;

            h4 {
                font-size: 16px;
                margin-bottom: 8px;
                color: #333;
            }

            .preview-content {
                padding: 12px;
                border: 1px dashed #e8e8e8;
                border-radius: 4px;
                min-height: 100px;
                background-color: #fafafa;
            }
        }
    }

    .editor-instructions {
        margin-top: 16px;

        h4 {
            font-size: 16px;
            margin-bottom: 8px;
            color: #333;
        }

        ul {
            margin-left: 20px;

            li {
                margin-bottom: 6px;

                code {
                    background-color: #f0f0f0;
                    padding: 2px 4px;
                    border-radius: 3px;
                    color: #1890ff;
                    font-family: monospace;
                }
            }
        }
    }
}

.ql-mention-list-container {
    background-color: #fff;
    border: 1px solid #f0f0f0;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(30, 30, 30, 0.08);
    overflow: auto;
    width: 270px;
    z-index: 9001;
}
.ql-mention-loading {
    font-size: 16px;
    line-height: 44px;
    padding: 0 20px;
    vertical-align: middle;
}
.ql-mention-list {
    list-style: none;
    margin: 0;
    overflow: hidden;
    padding: 0;
}
.ql-mention-list-item {
    cursor: pointer;
    font-size: 16px;
    line-height: 44px;
    padding: 0 20px;
    vertical-align: middle;
}
.ql-mention-list-item.disabled {
    cursor: auto;
}
.ql-mention-list-item.selected {
    background-color: #d3e1eb;
    text-decoration: none;
}
.mention {
    background-color: #d3e1eb;
    border-radius: 6px;
    height: 24px;
    margin-right: 2px;
    padding: 3px 0;
    user-select: all;
    width: 65px;
}
.mention > span {
    margin: 0 3px;
}
