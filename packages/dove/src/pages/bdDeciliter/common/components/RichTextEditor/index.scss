.quill-editor-container {
    position: relative;

    .quill-editor {
        padding: 10px;
        height: 300px;
        border: 1px solid #e8e8e8;
        border-radius: 4px;
        background: #fff;

        .ql-container {
            border: none;
            height: 100%;

            &.ql-snow {
                border: none;
            }
        }

        .ql-editor {
            padding: 0;
            font-size: 14px;
            line-height: 1.6;
            height: 100%;

            &:focus {
                outline: none;
            }

            p {
                margin: 0;
            }

            &.ql-blank::before {
                font-style: normal;
                color: #bfbfbf;
                font-size: 14px;
                position: absolute;
                pointer-events: none;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI',
                    Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
            }
        }
    }
}

// 提及下拉菜单容器
.mention-container {
    position: absolute;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 8px 0;
    width: 280px !important;
    max-height: 320px !important;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 1000;

    // 美化滚动条
    &::-webkit-scrollbar {
        width: 4px;
        height: 4px;
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;

        &:hover {
            background: #a8a8a8;
        }
    }

    &::-webkit-scrollbar-corner {
        background: #f1f1f1;
    }
}

// 提及选项样式
.mention-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;

    &:hover {
        background-color: rgba(0, 0, 0, 0.03);
    }

    &.selected {
        background-color: rgba(0, 0, 0, 0.06);

        &::after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background-color: #fe2c55;
        }
    }

    // 头像样式
    .mention-item-avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        margin-right: 12px;
        object-fit: cover;
        border: 1px solid rgba(0, 0, 0, 0.06);
    }

    // 信息区域
    .mention-item-info {
        flex: 1;
        min-width: 0; // 防止文本溢出

        .mention-item-name {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            margin-bottom: 4px;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .mention-item-desc {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            line-height: 1.2;
        }
    }
}

// 已插入的提及样式（选中后的标签）
.mention {
    &[data-denotation-char='@'] {
        background: #edf6ff;
        font-weight: 400;
        color: #198cff;
        font-family: PingFang SC;
        font-size: 12px;
        line-height: 16px;
        text-align: left;

        &:hover {
            background: #ebebeb;
        }
    }

    &[data-denotation-char='#'] {
        background: #EBFAF4;
        color: #02BE7E;

        &:hover {
            background: #bae7ff;
        }
    }
}

.rich-editor-wrapper {
    .editor-actions {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-top: 16px;

        .editor-preview {
            margin-top: 16px;

            h4 {
                font-size: 16px;
                margin-bottom: 8px;
                color: #333;
            }

            .preview-content {
                padding: 12px;
                border: 1px dashed #e8e8e8;
                border-radius: 4px;
                min-height: 100px;
                background-color: #fafafa;
            }
        }
    }
}
