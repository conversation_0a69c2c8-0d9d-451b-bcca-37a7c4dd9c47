/* eslint-disable react-hooks/exhaustive-deps */
import React, { useEffect, useRef, useState } from 'react';
import 'quill/dist/quill.snow.css'; // 添加 Quill 主题样式
import 'quill-mention/dist/quill.mention.css'; // 引入 mention 插件样件
import 'quill-mention/autoregister';
import Quill from 'quill';
import ReactQuill from 'react-quill';
import type Delta from 'quill-delta';
import './index.scss';

interface MentionItem {
    id: number;
    value: string;
    [key: string]: any;
}

interface QuillEditorProps {
    onChange?: (content: { delta: Delta; html: string; text: string }) => void;
    defaultValue?: string | Delta;
    placeholder?: string;
    className?: string;
    theme?: string;
    mentionUsers?: MentionItem[]; // 改为可选
    mentionTags?: MentionItem[]; // 改为可选
    mentionDenotationChars?: string[]; // 添加可选属性
    source?: (
        searchTerm: string,
        renderList: (values: MentionItem[], searchTerm: string) => void,
        mentionChar: string,
    ) => void; // 添加可选的source函数
    onBlur?: (content: { delta: Delta; html: string; text: string }) => void; // 添加 onBlur 回调函数属性
}

const QuillEditor: React.FC<QuillEditorProps> = ({
    onChange,
    defaultValue = '',
    placeholder = '请输入内容...',
    className = '',
    theme = 'snow',
    mentionUsers = [], // 设置默认值为空数组
    mentionTags = [], // 设置默认值为空数组
    mentionDenotationChars = ['@', '#'], // 添加默认值
    source, // 添加source参数
    onBlur, // 添加 onBlur 参数
}) => {
    const editorRef = useRef<HTMLDivElement>(null);
    const quillInstanceRef = useRef<Quill | null>(null);

    // 初始化Quill实例，只在组件挂载时执行一次
    useEffect(() => {
        if (!editorRef.current || quillInstanceRef.current) return;

        // 初始化 Quill 实例
        const quill = new Quill(editorRef.current, {
            theme: 'snow', // 确保使用 snow 主题
            placeholder: placeholder, // 设置 placeholder
            bounds: editorRef.current, // 设置编辑器边界
            modules: {
                mention: {
                    placeholder: placeholder || '输入@提及用户...',
                    spaceAfterInsert: true,
                    allowedChars: /^[A-Za-z\u4e00-\u9fa5\s]*$/,
                    mentionDenotationChars: mentionDenotationChars, // 使用传入的参数
                    source:
                        source ||
                        function (
                            searchTerm: string,
                            renderList: (
                                values: MentionItem[],
                                searchTerm: string,
                            ) => void,
                            mentionChar: string,
                        ) {
                            let values: MentionItem[] = [];
                            if (mentionChar === '@') {
                                values = mentionUsers;
                            } else {
                                values = mentionTags;
                            }

                            if (searchTerm.length === 0) {
                                renderList(values, searchTerm);
                            } else {
                                const matches = values.filter(item =>
                                    item.value
                                        .toLowerCase()
                                        .includes(searchTerm.toLowerCase()),
                                );
                                renderList(matches, searchTerm);
                            }
                        },
                    fixMentionsToQuill: false,
                    mentionContainerClass: 'mention-container',
                    isolateCharacter: true,
                    listItemClass: 'mention-item',
                    allowInlineMentionChar: true,
                    offsetTop: 10,
                    offsetLeft: 10,
                    defaultMenuOrientation: 'bottom',
                    dataAttributes: ['id', 'value', 'avatar', 'description'],
                },
                toolbar: false,
            },
            formats: ['bold', 'italic', 'mention'], // 允许的格式
        });

        // 自定义 Mention 样式
        const MentionBlot = Quill.import('blots/mention') as any;
        class StyledMentionBlot extends MentionBlot {
            static blotName = 'styled-mention';
            static tagName = 'span';
            static className = 'mention-blot';

            static create(data: { value: string }) {
                const node = super.create();
                node.innerText = `@${data.value}`;
                node.setAttribute('data-value', data.value);
                return node;
            }

            static formats(node: HTMLElement) {
                return { value: node.getAttribute('data-value') };
            }
        }

        Quill.register('formats/styled-mention', StyledMentionBlot);
        // 确保mention格式已注册
        Quill.register('formats/mention', MentionBlot);

        quillInstanceRef.current = quill;

        // 设置初始内容
        if (defaultValue) {
            try {
                if (typeof defaultValue === 'object') {
                    quill.setContents(defaultValue as Delta);
                } else if (typeof defaultValue === 'string') {
                    quill.clipboard.dangerouslyPasteHTML(defaultValue);
                }
            } catch (error) {
                console.error('设置编辑器内容失败:', error);
            }
        }

        const getText = () => {
            const delta = quill.getContents();
            let result = '';
            delta.ops.forEach(op => {
                if (!op.insert) {
                    return;
                }
                if (typeof op.insert === 'string') {
                    result += op.insert;
                } else if (op.insert.mention) {
                    // 你可以自定义 mention 的输出格式
                    // @ts-ignore
                    result += `${op.insert.mention.denotationChar}{${op.insert.mention.value}}`;
                }
            });
            return result;
        };

        const handleTextChange = () => {
            if (quill) {
                const contents = quill.getContents();
                const html = quill.root.innerHTML;
                onChange &&
                    onChange({ delta: contents, html, text: getText() });
            }
        };

        // 添加 blur 事件监听
        const handleBlur = () => {
            const contents = quill.getContents();
            const html = quill.root.innerHTML;
            onBlur && onBlur({ delta: contents, html, text: getText() });
        };

        quill.on('text-change', handleTextChange);
        quill.root.addEventListener('blur', handleBlur); // 添加失去焦点事件监听

        return () => {
            if (quill) {
                quill.off('text-change', handleTextChange);
                quill.root.removeEventListener('blur', handleBlur); // 清理事件监听
                quillInstanceRef.current = null;
            }
        };
    }, []); // 添加 onBlur 到依赖数组

    // 处理defaultValue变化时的内容更新，保留光标位置
    useEffect(() => {
        const quill = quillInstanceRef.current;
        if (!quill || !defaultValue) return;

        // 保存当前光标位置
        const selection = quill.getSelection();

        try {
            // 更新内容
            if (typeof defaultValue === 'object') {
                quill.setContents(defaultValue as Delta);
            } else if (
                typeof defaultValue === 'string' &&
                quill.root.innerHTML !== defaultValue
            ) {
                quill.clipboard.dangerouslyPasteHTML(defaultValue);
            }

            // 如果之前有选择，恢复光标位置
            // if (selection) {
            //   setTimeout(() => {
            //     quill.setSelection(selection);
            //   }, 0);
            // }
        } catch (error) {
            console.error('更新编辑器内容失败:', error);
        }
    }, [defaultValue]);

    return (
        <div className={`quill-editor-container ${className}`}>
            <div ref={editorRef} className="quill-editor"></div>
        </div>
    );
};

export default QuillEditor;
