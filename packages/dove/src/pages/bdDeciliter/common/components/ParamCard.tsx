import './styles/ParamCard.scss';
import { CheckBox, FormPro, Switch } from '@roo/roo';
import { Checkbox } from 'antd';
import { useWatch } from 'antd/es/form/Form';
interface ParamCardProps {
    title: string;
    children?: React.ReactNode;
    name: string[];
    form: any;
    noFormItem?: boolean;
}

const ParamCard: React.FC<ParamCardProps> = ({ title, children, name, form, noFormItem = false }) => {
    const isSceneTemplate = useWatch(['type'], form);
    return (
        <div className="param-card">
            <div className="param-card-header">
                <div className="param-card-header-title">
                    #{title}
                </div>
                {
                    isSceneTemplate === 1 && (
                        <div className="param-card-header-check">
                            <FormPro.Item noStyle shouldUpdate name={[...name, 'requiredFrontLineChange']}
                                // wrapperCol={{ span: 12 }}
                                grid24
                                labelCol={{ span: 4 }}
                                wrapperCol={{ span: 20 }}
                                style={{ marginBottom: 0 }}
                                valuePropName="checked"
                                initialValue={true}
                            >
                                <Checkbox >
                                    一线必填
                                </Checkbox>
                            </FormPro.Item>
                            <FormPro.Item noStyle shouldUpdate name={[...name, 'allowFrontLineChange']}
                                grid24
                                labelCol={{ span: 4 }}
                                wrapperCol={{ span: 20 }}
                                style={{ marginBottom: 0 }}
                                valuePropName="checked"
                                initialValue={true}
                            >
                                <Checkbox >
                                    一线可修改
                                </Checkbox>
                            </FormPro.Item>
                        </div>
                    )
                }
            </div>

            {
                !noFormItem && (
                    <FormPro.Item noStyle shouldUpdate name={[...name, 'content']}
                        rules={[{ required: true, message: '请输入参数配置' }]}
                    >
                        {children}
                    </FormPro.Item>
                )
            }
            {
                noFormItem && (
                    children
                )
            }
        </div>
    );
}

export default ParamCard;