import React, { useEffect } from 'react';
import { FormPro, Input, Button } from '@roo/roo';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import './styles/DynamicFormList.scss';
import deleteIcon from '../../assets/image/del-icon.svg';
import { Textarea } from '@roo/roo/Input';

interface DynamicFormListProps {
    isFrontLine?: boolean; // 是否是前线
    name: string | string[];  // 支持字符串或字符串数组
    form?: any;  // 父级表单实例属性
    required?: boolean; // 是否必填
    onChange?: (value: any[]) => void;
    value?: any[]; // 外部传入的值
    allowFrontLineChange?: boolean; // 是否允许前线修改
    requiredFrontLineChange?: boolean; // 是否要求前线修改
    title?: string; // 标题
}

const DynamicFormList: React.FC<DynamicFormListProps> = ({
    name,
    form,
    title,
    required,
    isFrontLine
}) => {
    // 如果有父级表单就使用父级表单，否则创建新的表单实例

    useEffect(() => {
        if (form && Array.isArray(name)) {
            // 获取当前字段的值
            const fieldValue = form.getFieldValue(name) || {};

            // 检查并设置默认值
            if (fieldValue.allowFrontLineChange === undefined) {
                form.setFields([{
                    name: [...name, 'allowFrontLineChange'],
                    value: true
                }]);
            }

            if (fieldValue.requiredFrontLineChange === undefined) {
                form.setFields([{
                    name: [...name, 'requiredFrontLineChange'],
                    value: true
                }]);
            }
        }
    }, [name]);

    // 判断是否允许前线修改
    const isAllowFrontLineChange = () => {
        // 如果不是前线人员，直接返回true（允许修改）
        if (!isFrontLine) {
            return true;
        }
        // 如果是前线人员，则根据表单中的allowFrontLineChange值决定是否允许修改
        return form?.getFieldValue([...name, 'allowFrontLineChange']);
    };

    // 处理换行符的函数
    const handleTextareaChange = (e: React.ChangeEvent<HTMLTextAreaElement>, fieldName: number) => {
        // const value = e.target.value.replace(/\n/g, ' '); // 将换行符替换为两个空格
        form.setFieldValue([...name, 'content', fieldName], e.target.value);
    };


    return (
        <div className="dynamic-form-list">
            {title && <div className="dynamic-form-list-title">{title}</div>}
            <FormPro.List
                name={[...name, 'content']}
                initialValue={['']} // 设置初始值为包含一个空字符串的数组，实现默认显示一行
            >
                {(fields, { add, remove }) => {
                    return (
                        <>
                            {fields.map(({ key, name: fieldName, ...restField }, index) => (
                                <div key={key} className="form-item-container">
                                    <span className="dynamic-form-list-index">{index + 1}</span>
                                    <FormPro.Item
                                        {...restField}
                                        name={[fieldName]}
                                        rules={[
                                            {
                                                required: isFrontLine
                                                    ? form?.getFieldValue([...name, 'requiredFrontLineChange']) || required
                                                    : required,
                                                message: '请输入内容',
                                            },
                                        ]}
                                        className="content-form-item"
                                        wrapperCol={{ span: 24 }}
                                        grid24
                                    >
                                        <Textarea
                                            placeholder="请输入对话步骤内容"
                                            style={{ width: '100%' }}
                                            disabled={!isAllowFrontLineChange()}
                                            autoSize={{ minRows: 1, maxRows: 6 }}
                                            onChange={(e) => handleTextareaChange(e, fieldName)}
                                        />
                                    </FormPro.Item>

                                    <img
                                        src={deleteIcon}
                                        className={`dynamic-delete-button ${fields.length === 1 || !isAllowFrontLineChange() ? 'disabled' : ''}`}
                                        alt="删除"
                                        onClick={() => fields.length > 1 && isAllowFrontLineChange() ? remove(fieldName) : null}
                                        style={{
                                            opacity: fields.length === 1 || !isAllowFrontLineChange() ? 0.5 : 1,
                                            cursor: fields.length === 1 || !isAllowFrontLineChange() ? 'not-allowed' : 'pointer'
                                        }}
                                    />
                                </div>
                            ))}
                            <div className="form-item-container">
                                <span className="dynamic-form-list-index" style={{ visibility: 'hidden' }}></span>
                                <FormPro.Item className="content-form-item" wrapperCol={{ span: 23 }}>
                                    <Button
                                        type="hollow"
                                        disabled={!isAllowFrontLineChange()}
                                        onClick={() => {
                                            // 添加新项并确保相关字段有默认值
                                            add('');
                                        }}
                                        style={{ width: '100%' }}
                                    >
                                        <PlusOutlined style={{ marginRight: '8px' }} />添加流程
                                    </Button>
                                </FormPro.Item>
                            </div>
                        </>
                    );
                }}
            </FormPro.List>
        </div>
    );
};

export default DynamicFormList;
