import React, { useState, useEffect, useRef, useContext } from 'react';
import { message, Typography, Modal, Input, Form, Button } from 'antd';
import { Bubble, Sender } from '@ant-design/x';
import type { BubbleProps } from '@ant-design/x';
import markdownit from 'markdown-it';
import brandIcon from '../../assets/image/brandIcon.png';
import './styles/DialogFlow.scss';
import bg from '../../assets/image/bgimage.png';
import phoneIcon from '../../assets/image/phone-icon.svg';
import refreshIcon from '../../assets/image/refresh-icon.svg';
import gary_refresh_icon from '../../assets/image/gary-refresh-icon.svg';
import gary_phone_icon from '../../assets/image/gray-phone-icon.svg';
import { MessageType } from '../../mock/dialogMock';
import type { FormInstance as AntdFormInstance } from 'antd/lib/form';
import type { FormInstance as RooFormInstance } from '@roo/roo/FormPro/hooks/useForm';
import { createAgentText, sendChatMessage, phoneTest } from '../../services';
import { BDDeciliterContext } from '../../context';

// 获取URL中的参数
const getUrlParam = (name: string): string | null => {
    const searchParams = new URLSearchParams(window.location.search);
    return searchParams.get(name);
};

// 创建markdown解析器
const md = markdownit({ html: true, breaks: true });

// 将@和#标签转换为HTML格式
const formatSpecialTags = (content: string) => {
    return content
        .replace(/@\{([^}]+)\}/g, '<span class="mention">@$1</span>')
        .replace(/#\{([^}]+)\}/g, '<span class="tag">#$1</span>');
};

// 渲染Markdown内容
const renderMarkdown: BubbleProps['messageRender'] = content => (
    <Typography>
        <div
            dangerouslySetInnerHTML={{
                __html: md.render(formatSpecialTags(content)),
            }}
        />
    </Typography>
);

// 定义组件props接口，接受任何类型的表单实例
interface DialogFlowProps {
    form: AntdFormInstance | RooFormInstance<any> | any;
    templateId?: string | number; // 添加模板ID属性用于监听变化
}

// 增加API消息类型接口
export interface ApiMessageType {
    type: number;
    content: string;
}

export interface ToolCallType {
    name: string;
    description?: string;
    request: any;
    response: string;
}

export interface KnowledgeCallType {
    name: string;
    recall: string[];
}

export interface ChatResponseType {
    conversationId: number;
    messages: ApiMessageType[];
    tools?: ToolCallType[];
    knowledge?: KnowledgeCallType[];
}

// 表单字段接口
interface FormField {
    name: string;
    label: string;
}

const DialogFlow: React.FC<DialogFlowProps> = ({ form, templateId }) => {
    // 消息的角色定义
    const { isFrontLine } = useContext(BDDeciliterContext);
    const roles = {
        user: {
            // 用户角色配置
            placement: 'end' as const, // 右侧显示
            bubbleStyle: {
                backgroundColor: '#333333',
                color: '#ffffff',
                borderRadius: '12px',
                padding: '10px 14px',
                maxWidth: '80%',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            },
            containerStyle: {
                justifyContent: 'flex-end',
                display: 'flex',
                width: '100%',
            },
            // 为用户消息添加自定义类名
            classNames: {
                avatar: 'user-avatar',
                content: 'user-content',
                container: 'user-container',
            },
            // 添加用户角色的自定义渲染器
            messageRender: (content: string) => (
                <Typography style={{ color: '#ffffff' }}>
                    <div
                        dangerouslySetInnerHTML={{
                            __html: md
                                .render(formatSpecialTags(content))
                                .replace(/<a /g, '<a style="color: #61dafb" '),
                        }}
                    />
                </Typography>
            ),
        },
        assistant: {
            // 机器人角色配置
            placement: 'start' as const, // 左侧显示
            bubbleStyle: {
                backgroundColor: '#FFFFFF',
                color: '#333333',
                border: '1px solid #e8e8e8',
                borderRadius: '8px',
                padding: '8px 12px',
                maxWidth: '80%',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
                display: 'flex',
                flexDirection: 'column',
                flexWrap: 'nowrap',
            },
            containerStyle: {
                justifyContent: 'flex-start',
                display: 'flex',
                width: '100%',
            },
            // 为机器人消息添加自定义类名
            classNames: {
                avatar: 'assistant-avatar',
                content: 'assistant-content',
                container: 'assistant-container',
            },
            messageRender: renderMarkdown,
        },
    };

    const [messages, setMessages] = useState<MessageType[]>([]);
    const [inputValue, setInputValue] = useState('');
    const [isTyping, setIsTyping] = useState(false); // 控制是否有消息正在输入
    const [isActivated, setIsActivated] = useState(false); // 控制组件是否已激活
    const [isModalVisible, setIsModalVisible] = useState(false); // 控制模拟弹窗是否可见
    const [isPhoneModalVisible, setIsPhoneModalVisible] = useState(false); // 控制电话测试弹窗是否可见
    const [merchantForm] = Form.useForm(); // 创建商家信息表单实例
    const [phoneForm] = Form.useForm(); // 创建电话测试表单实例
    const [conversationId, setConversationId] = useState<number | null>(null); // 存储会话ID
    const [agentId, setAgentId] = useState<string | null>(null); // 存储代理ID
    const [currentResponse, setCurrentResponse] =
        useState<ChatResponseType | null>(null); // 存储当前响应
    const [formFields, setFormFields] = useState<FormField[]>([]); // 默认表单字段
    const [lastPhoneNumber, setLastPhoneNumber] = useState<string>(''); // 保存上次使用的电话号码
    const [currentDynamicParams, setCurrentDynamicParams] = useState<
        Record<string, any>
    >({}); // 保存当前使用的动态参数
    const [isSubmitting, setIsSubmitting] = useState(false); // 控制提交按钮的加载状态
    const [isPhoneTesting, setIsPhoneTesting] = useState(false); // 控制电话测试按钮的加载状态
    const inputRef = useRef<any>(null); // 添加inputRef用于聚焦Sender组件
    const prevSceneNameRef = useRef<string | undefined>(); // 记录上一次的场景名称

    // 使用useWatch监听场景名称变化
    const sceneName = Form.useWatch('sceneName', form);

    // 监听场景名称变化
    useEffect(() => {
        if (!isFrontLine || !sceneName) {
            return;
        }

        // 忽略首次加载
        if (prevSceneNameRef.current === undefined) {
            prevSceneNameRef.current = sceneName;
            return;
        }

        // 只有当场景名称真正变化时执行重置
        if (prevSceneNameRef.current !== sceneName) {
            console.log(
                '场景模板变化:',
                prevSceneNameRef.current,
                '->',
                sceneName,
            );
            // 更新ref值
            prevSceneNameRef.current = sceneName;

            // 如果已经激活，显示提示并重置对话
            if (isActivated) {
                message.info('场景模板已更改，对话已重置');
                // 重置对话状态
                setMessages([]);
                setConversationId(null);
                setCurrentResponse(null);
                setIsActivated(false);

                // 延迟一下获取新的表单字段，但不自动创建会话
                setTimeout(() => {
                    // 获取新的表单字段
                    const newFormFields = getFormFields();
                    setFormFields(newFormFields);

                    // 不再自动创建会话，用户需要点击"开始模拟"按钮
                }, 300);
            }
        }
    }, [sceneName, isFrontLine, isActivated, agentId]);

    // 组件卸载时清理引用
    useEffect(() => {
        return () => {
            prevSceneNameRef.current = undefined;
        };
    }, []);

    // 初始化组件，获取URL参数
    useEffect(() => {
        const id = getUrlParam('id');
        if (id) {
            setAgentId(id);
        }
    }, [getUrlParam('id')]);

    // 辅助函数：从字符串中提取@{xxx}格式的动态参数
    const extractDynamicParams = (text: string): string[] => {
        if (!text || typeof text !== 'string') {
            return [];
        }

        // 使用正则表达式提取所有@{xxx}格式的内容
        const regex = /@\{([^}]+)\}/g;
        const matches: string[] = [];
        let match;

        while ((match = regex.exec(text)) !== null) {
            // match[1]是括号中的内容
            if (match[1]) {
                matches.push(match[1]);
            }
        }

        return matches;
    };

    // 从formValues字符串中提取@{xxx}格式的动态参数
    const getFormFields = (): FormField[] => {
        if (!form || !form.getFieldsValue) {
            return [];
        }

        const formValues = form.getFieldsValue();
        console.log('获取表单值:', formValues);

        // 从systemPrompt.prompt中提取
        const promptValues = formValues.systemPrompt?.prompt;
        const promptMatches = extractDynamicParams(promptValues);

        // 从prologue中提取
        const prologueValues = formValues.prologue;
        const prologueMatches = extractDynamicParams(prologueValues);

        // 合并并去重prologueMatches
        const allMatches = [...prologueMatches, ...promptMatches];
        const uniqueMatches = [...new Set(allMatches)];

        console.log('提取到的动态参数:', uniqueMatches);

        if (uniqueMatches.length > 0) {
            // 将提取到的内容转换为表单字段格式
            return uniqueMatches.map(field => ({
                name: field,
                label: field,
            }));
        }

        // 如果没有找到匹配项，返回空数组
        return [];
    };

    // 处理消息发送
    const handleSendMessage = async (value: string) => {
        if (!value.trim() || !conversationId) return;

        // 添加用户消息
        const userMessage: MessageType = {
            id: Date.now().toString(),
            content: value,
            role: 'user',
            status: 'success',
        };

        setMessages(prev => [...prev, userMessage]);
        setInputValue('');
        setIsTyping(true);

        // 创建一个临时消息ID
        const tempId = (Date.now() + 1).toString();

        // 添加一个加载中的消息
        const loadingMessage: MessageType = {
            id: tempId,
            content: '正在输入...',
            role: 'assistant',
            status: 'loading',
        };

        // 先显示加载状态
        setMessages(prev => [...prev, loadingMessage]);

        try {
            // 发送消息到API
            const response = await sendChatMessage(conversationId, value);

            if (response && response.code === 0 && response.data) {
                setCurrentResponse(response.data);
                console.log('收到服务器响应:', response.data);

                // 获取最新的助手消息
                const assistantMessages = response.data.messages
                    .filter((msg: ApiMessageType) => msg.type === 1) // type=1是AI消息
                    .map((msg: ApiMessageType) => msg.content);

                console.log('AI消息内容:', assistantMessages);

                // 如果有助手消息，更新最后一条加载中的消息
                if (assistantMessages.length > 0) {
                    // 使用最后一条助手消息替换loading消息
                    setMessages(prev =>
                        prev.map(msg =>
                            msg.id === tempId
                                ? {
                                      ...msg,
                                      content:
                                          assistantMessages[
                                              assistantMessages.length - 1
                                          ],
                                      status: 'success',
                                  }
                                : msg,
                        ),
                    );
                } else {
                    // 如果没有助手消息，更新为错误状态
                    setMessages(prev =>
                        prev.map(msg =>
                            msg.id === tempId
                                ? {
                                      ...msg,
                                      content: '抱歉，未收到回复。',
                                      status: 'error',
                                  }
                                : msg,
                        ),
                    );
                }
            } else {
                // 处理API错误
                setMessages(prev =>
                    prev.map(msg =>
                        msg.id === tempId
                            ? {
                                  ...msg,
                                  content: '抱歉，出现了一些问题，请稍后再试。',
                                  status: 'error',
                              }
                            : msg,
                    ),
                );
            }
        } catch (error) {
            // 处理异常
            setMessages(prev =>
                prev.map(msg =>
                    msg.id === tempId
                        ? {
                              ...msg,
                              content: '抱歉，出现了一些问题，请稍后再试。',
                              status: 'error',
                          }
                        : msg,
                ),
            );
            console.error('发送消息错误:', error);
        } finally {
            setIsTyping(false);
            // 消息处理完成后自动聚焦输入框
            setTimeout(() => {
                inputRef.current?.focus?.();
            }, 100);
        }
    };

    // 处理输入变化
    const handleInputChange = (value: string) => {
        setInputValue(value);
    };

    // 显示模拟弹窗
    const showSimulationModal = () => {
        // 检查是否有agentId
        if (!agentId) {
            message.warning('请先创建agentId，才能进行通话模拟');
            return; // 如果没有agentId，不打开弹窗
        }

        // 获取动态表单字段
        const newFormFields = getFormFields();
        setFormFields(newFormFields);

        // 如果没有动态参数，直接创建会话而不显示弹窗
        if (newFormFields.length === 0) {
            createConversation({});
            return;
        }

        // 有动态参数时，打开弹窗
        setIsModalVisible(true);
    };

    // 创建会话的函数
    const createConversation = async (dynamicParams: Record<string, any>) => {
        if (!agentId) {
            message.error('未找到有效的agentId');
            return;
        }

        setIsTyping(true);

        // 保存当前使用的动态参数
        setCurrentDynamicParams(dynamicParams);

        try {
            // 创建会话获取会话ID和初始消息
            const createResponse = await createAgentText({
                agentId: parseInt(agentId),
                dynamicParams: dynamicParams, // 表单值已经是对象格式，符合接口要求
            });

            if (
                !createResponse ||
                createResponse.code !== 0 ||
                !createResponse.data
            ) {
                message.error('创建会话失败');
                setIsTyping(false);
                return;
            }

            // 获取会话ID和初始消息
            const {
                conversationId: newConversationId,
                messages: initialMessages,
            } = createResponse.data;
            setConversationId(newConversationId);
            setCurrentResponse(createResponse.data);

            // 激活对话并隐藏模态框
            setIsActivated(true);
            setIsModalVisible(false);

            // 直接使用返回的消息进行渲染
            if (initialMessages && initialMessages.length > 0) {
                console.log('初始消息数据:', initialMessages);
                const newMessages = initialMessages.map((msg, index) => {
                    console.log(
                        `消息${index}:`,
                        msg,
                        '类型:',
                        msg.type,
                        '映射为角色:',
                        msg.type === 1 ? 'assistant' : 'user',
                    );
                    return {
                        id: `api-${Date.now()}-${index}`,
                        content: msg.content,
                        role:
                            msg.type === 1
                                ? ('assistant' as const)
                                : ('user' as const),
                        status: 'success' as const,
                    };
                });

                setMessages(newMessages);
            }
        } catch (error) {
            console.error('创建会话错误:', error);
            message.error('创建会话失败');
        } finally {
            setIsTyping(false);
            // 初始消息加载完成后，自动聚焦输入框
            setTimeout(() => {
                inputRef.current?.focus?.();
            }, 100);
        }
    };

    // 处理模拟弹窗确认
    const handleModalOk = async () => {
        try {
            setIsSubmitting(true); // 设置加载状态为true

            // 如果没有表单字段，则直接使用空对象创建会话
            if (formFields.length === 0) {
                await createConversation({});
                setIsModalVisible(false);
                return;
            }

            // 否则验证表单字段
            const values = await merchantForm.validateFields();
            console.log('商家信息:', values);

            await createConversation(values);

            merchantForm.resetFields();
        } catch (info) {
            console.log('表单验证失败:', info);
        } finally {
            setIsSubmitting(false); // 设置加载状态为false
        }
    };

    // 处理模拟弹窗取消
    const handleModalCancel = () => {
        setIsModalVisible(false);
        merchantForm.resetFields();
    };

    // 处理刷新操作
    const handleRefresh = () => {
        // 重置对话状态，但保留动态参数
        setMessages([]);
        setConversationId(null);
        setCurrentResponse(null);
        setIsActivated(false);
        // 注意：不重置 currentDynamicParams，保留上次使用的动态参数

        // 重新获取表单字段并弹出表单弹窗重新开始
        showSimulationModal();
    };

    // 显示电话测试弹窗
    const showPhoneTestModal = () => {
        if (!isActivated || !conversationId) {
            message.warning('请先完成通话模拟初始化');
            return;
        }

        // 设置默认电话号码（如果有）
        if (lastPhoneNumber) {
            phoneForm.setFieldsValue({ phoneNumber: lastPhoneNumber });
        }

        setIsPhoneModalVisible(true);
    };

    // 处理电话测试弹窗确认
    const handlePhoneTestOk = async () => {
        try {
            setIsPhoneTesting(true); // 设置加载状态为true
            const values = await phoneForm.validateFields();
            if (!conversationId) {
                message.error('会话ID不存在');
                setIsPhoneTesting(false); // 如果验证失败，重置加载状态
                return;
            }

            // 确保 agentId 不为 null
            const currentAgentId = agentId || '';

            // 保存当前使用的电话号码
            setLastPhoneNumber(values.phoneNumber);

            const result = await phoneTest(
                currentAgentId,
                values.phoneNumber,
                currentDynamicParams,
            );
            console.log('result', result);
            if (result && typeof result === 'string') {
                message.success('已拨打电话，请注意接听');
                setIsPhoneModalVisible(false);

                // 只重置电话号码字段
                phoneForm.setFieldsValue({ phoneNumber: '' });
            } else {
                // message.error((result && result.message) || '电话测试失败');
            }
        } catch (info) {
            console.log('手机号验证失败:', info);
        } finally {
            setIsPhoneTesting(false); // 设置加载状态为false
        }
    };

    // 处理电话测试弹窗取消
    const handlePhoneTestCancel = () => {
        setIsPhoneModalVisible(false);
        // 只重置电话号码字段
        phoneForm.setFieldsValue({ phoneNumber: '' });
    };

    // 添加一个效果，在messages更新后总是聚焦输入框
    useEffect(() => {
        if (isActivated && !isTyping && messages.length > 0) {
            setTimeout(() => {
                inputRef.current?.focus?.();
            }, 100);
        }
    }, [messages, isTyping, isActivated]);

    return (
        <div className="dialog-flow-container">
            <div className="dialog-flow-bg-container">
                <img src={bg} alt="bg" className="dialog-flow-bg" />
            </div>
            <div className="dialog-flow-header">
                <div className="dialog-flow-header-title">
                    <img src={brandIcon} alt="brandIcon" />
                    <span>通话模拟</span>
                </div>
                <div className="dialog-flow-header-Icons">
                    <div
                        className="dialog-flow-header-phone"
                        onClick={handleRefresh}
                    >
                        <img
                            src={!isActivated ? gary_refresh_icon : refreshIcon}
                            alt="refreshIcon"
                        />
                        <span
                            className={`dialog-flow-header-phone-text ${
                                !isActivated
                                    ? 'dialog-flow-header-phone-text-disabled'
                                    : ''
                            }`}
                        >
                            刷新
                        </span>
                    </div>
                    <div
                        className="dialog-flow-header-phone"
                        onClick={showPhoneTestModal}
                    >
                        <img
                            src={!isActivated ? gary_phone_icon : phoneIcon}
                            alt="phoneIcon"
                        />
                        <span
                            className={`dialog-flow-header-phone-text ${
                                !isActivated
                                    ? 'dialog-flow-header-phone-text-disabled'
                                    : ''
                            }`}
                        >
                            电话测试
                        </span>
                    </div>
                </div>
            </div>

            <div className="dialog-flow-content">
                {isActivated ? (
                    <div className="dialog-flow-content-list">
                        <Bubble.List
                            roles={roles}
                            style={{ maxHeight: 550 }}
                            items={messages.map(
                                ({ id, content, role, status }) => ({
                                    key: id,
                                    loading: status === 'loading',
                                    role: role,
                                    content: content,
                                }),
                            )}
                        />
                    </div>
                ) : (
                    <div className="dialog-flow-content-not-activated">
                        <div className="dialog-flow-content-not-activated-empty">
                            <span className="dialog-flow-content-not-activated-empty-title">
                                模板填写完成后，可以进行通话模拟
                            </span>
                            <p>
                                <span
                                    className="dialog-flow-content-not-activated-empty-button"
                                    onClick={showSimulationModal}
                                >
                                    开始模拟
                                </span>
                            </p>
                        </div>
                    </div>
                )}{' '}
            </div>

            <div className="dialog-flow-footer">
                <div className="dialog-flow-footer-input">
                    <Sender
                        ref={inputRef}
                        value={inputValue}
                        onChange={handleInputChange}
                        onSubmit={value => {
                            handleSendMessage(value);
                            // message.success('消息发送成功!');
                        }}
                        disabled={isTyping || !isActivated}
                        placeholder="您可以模拟商家，测试实际通话效果"
                    />
                </div>
            </div>

            {/* 商家信息填写弹窗 */}
            <Modal
                title="设置通话模拟参数"
                open={isModalVisible}
                onOk={handleModalOk}
                onCancel={handleModalCancel}
                footer={[
                    <Button key="back" onClick={handleModalCancel}>
                        取消
                    </Button>,
                    <Button
                        key="submit"
                        type="primary"
                        onClick={handleModalOk}
                        loading={isSubmitting}
                    >
                        确定
                    </Button>,
                ]}
            >
                <Form
                    form={merchantForm}
                    layout="horizontal"
                    name="merchantForm"
                >
                    {formFields.length > 0
                        ? formFields.map(field => (
                              <Form.Item
                                  key={field.name}
                                  name={field.name}
                                  label={field.label}
                                  rules={[
                                      {
                                          required: true,
                                          message: `请输入${field.label}`,
                                      },
                                  ]}
                              >
                                  <Input placeholder={`请输入${field.label}`} />
                              </Form.Item>
                          ))
                        : null}
                </Form>
            </Modal>

            {/* 电话测试弹窗 */}
            <Modal
                title="电话测试"
                open={isPhoneModalVisible}
                onOk={handlePhoneTestOk}
                onCancel={handlePhoneTestCancel}
                footer={[
                    <Button key="back" onClick={handlePhoneTestCancel}>
                        取消
                    </Button>,
                    <Button
                        key="submit"
                        type="primary"
                        onClick={handlePhoneTestOk}
                        loading={isPhoneTesting}
                    >
                        拨打
                    </Button>,
                ]}
            >
                <Form
                    form={phoneForm}
                    layout="horizontal"
                    name="phoneForm"
                    initialValues={{ phoneNumber: lastPhoneNumber }}
                >
                    <Form.Item
                        name="phoneNumber"
                        label="手机号码"
                        rules={[
                            { required: true, message: '请输入手机号码' },
                            {
                                pattern: /^1[3-9]\d{9}$/,
                                message: '请输入正确的手机号码',
                            },
                        ]}
                    >
                        <Input placeholder="请输入手机号码" />
                    </Form.Item>
                </Form>
            </Modal>
        </div>
    );
};

export default DialogFlow;
