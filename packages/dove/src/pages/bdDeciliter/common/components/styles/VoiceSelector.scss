.voice-selector-container {
  position: relative;
  width: 100%;

  // 确保下拉项中的内容可以正常显示
  :global(.roo-selector-option) {
    .voice-selector-option {
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      width: 100% !important;
      padding: 8px 16px !important;
      
      .voice-name {
        flex: 1 !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
        font-size: 14px !important;
        color: #333333 !important;
      }
      
      .play-button {
        margin-left: 8px !important;
        flex-shrink: 0 !important;
        
        &:hover {
          color: #1890ff !important;
        }
      }
    }
  }

  // 自定义标签样式
  .voice-selector-tag {
    display: flex !important;
    align-items: center !important;
    background-color: #f5f5f5 !important;
    border: 1px solid #e8e8e8 !important;
    border-radius: 4px !important;
    padding: 0 8px !important;
    margin-right: 4px !important;
    margin-bottom: 4px !important;
    height: 28px !important;
    line-height: 28px !important;
    box-sizing: border-box !important;

    .voice-tag-name {
      max-width: 150px !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
      font-size: 14px !important;
      color: #333333 !important;
    }

    .play-button {
      margin-left: 8px !important;
      padding: 0 4px !important;
      flex-shrink: 0 !important;
      
      &:hover {
        color: #1890ff !important;
      }
    }
  }
}

/* 下拉菜单中的选项样式 */
.voice-selector-popup {
  .voice-selector-option {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    width: 100% !important;
    padding: 8px 16px !important;
    
    .voice-name {
      flex: 1 !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
      font-size: 14px !important;
      color: #333333 !important;
    }
    
    .play-button {
      margin-left: 8px !important;
      flex-shrink: 0 !important;
      
      &:hover {
        color: #1890ff !important;
      }
    }
  }
}

/* 全局样式覆盖 */
:global {
  .roo-selector-popover {
    .voice-selector-option {
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      width: 100% !important;
      padding: 8px 16px !important;
      box-sizing: border-box !important;
      
      .voice-name {
        flex: 1 !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
        font-size: 14px !important;
        color: #333333 !important;
      }
      
      .play-button {
        margin-left: 8px !important;
        flex-shrink: 0 !important;
        
        &:hover {
          color: #1890ff !important;
        }
      }
    }
  }

  // 下拉选项的常见类名
  .roo-selector-option, 
  .roo-selector-item,
  .roo-selector-option-item {
    .voice-selector-option {
      display: flex !important;
      justify-content: space-between !important;
      align-items: center !important;
      width: 100% !important;
      box-sizing: border-box !important;
      
      .voice-name {
        flex: 1 !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
      }
      
      .play-button {
        margin-left: 8px !important;
        flex-shrink: 0 !important;
      }
    }
  }

  // 标签样式全局覆盖
  .roo-selector-tag {
    .voice-selector-tag {
      display: flex !important;
      align-items: center !important;
      height: 24px !important;
      line-height: 24px !important;
      margin-right: 4px !important;
      
      .play-button {
        padding: 0 4px !important;
        height: 24px !important;
        line-height: 24px !important;
      }
    }
  }
} 