.dynamic-form-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    background-color: #F5F6FA;
    
    .dynamic-form-list-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 16px;
        padding-left: 4px;
    }
    
    .form-item-container {
        display: flex;
        align-items: center;
        width: 100%;
        position: relative;
        
        &:not(:last-child)::after {
            // content: '';
            // position: absolute;
            // left: 10px;
            // bottom: -14px;
            // height: 14px;
            // width: 1px;
            // background-color: #e8e8e8;
            // z-index: 1;
        }
    }
    
    .dynamic-form-list-index {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 400;
        color: #666666;
        font-family: PingFang SC;
        font-size: 14px;
        line-height: 20px;
        text-align: center;
        margin-right: 8px;
        min-width: 20px;
        height: 20px;
        width: 20px;
        // border-radius: 50%;
        // background-color: #f5f5f5;
    }
    
    .content-form-item {
        flex: 1;
        margin-bottom: 0;
        margin-right: 8px;
    }
    
    .dynamic-delete-button {
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        font-size: 16px;
        cursor: pointer;
        margin-left: 4px;
        
        &:hover {
            color: #ff4d4f;
        }
    }
}