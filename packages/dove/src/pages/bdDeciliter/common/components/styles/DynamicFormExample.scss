.dynamic-form-example-card {
  margin: 20px;
  border-radius: 8px;
  
  .example-title {
    font-size: 18px;
    font-weight: 500;
    margin-bottom: 24px;
    color: #333;
  }
  
  .example-content {
    margin-bottom: 32px;
    
    .example-controls {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 16px;
    }
    
    .form-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 24px;
      gap: 12px;
    }
  }
  
  .example-preview {
    margin-top: 36px;
    padding: 16px;
    background-color: #f9f9f9;
    border-radius: 8px;
    
    h3 {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 16px;
      color: #333;
    }
    
    .preview-content {
      h4 {
        font-size: 15px;
        font-weight: 500;
        margin-bottom: 12px;
        color: #333;
      }
      
      .flow-steps {
        list-style: none;
        padding: 0;
        margin: 0;
        
        .flow-step {
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          
          .step-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: #f0f0f0;
            color: #666;
            font-size: 12px;
            margin-right: 12px;
          }
          
          .step-content {
            font-size: 14px;
            color: #333;
          }
          
          &:not(:last-child) {
            position: relative;
            
            &:after {
              content: '';
              position: absolute;
              left: 12px;
              top: 24px;
              width: 1px;
              height: 16px;
              background-color: #e8e8e8;
            }
          }
        }
      }
    }
  }
} 