.dialog-flow-container {
    padding: 15px 0px;
    border-radius: 8px;
    background: linear-gradient(181deg, #ffffff 0%, #f5f6fa 100%);
    border: 1px solid #eeeeee;
    // background-image: url('../../image/bg.png');
    // background-image: url('../../image/bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    z-index: 1;
    .dialog-flow-bg-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        .dialog-flow-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 0;
        }
    }

    .dialog-flow-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 20px;
        .dialog-flow-header-Icons {
            display: flex;
            gap: 12px;
            .dialog-flow-header-phone-text {
                cursor: pointer;
            }
            .dialog-flow-header-phone-text-disabled {
                cursor: not-allowed;
            }
        }
        .dialog-flow-header-title {
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;
            z-index: 1;
            img {
                width: 28px;
                height: 28px;
            }
            span {
                color: #222222;
                font-family: PingFang SC;
                font-weight: 600;
                font-size: 16px;
                line-height: 22px;
                text-align: left;
            }
        }
        .dialog-flow-header-phone-text-disabled {
            color: #999999;
            cursor: not-allowed;
        }
        .dialog-flow-header-phone {
            display: flex;
            align-items: center;
            gap: 4px;
            span {
                font-weight: 400;
                color: #ff6a00;
                font-family: PingFang SC;
                font-size: 14px;
                line-height: 20px;
                text-align: center;
            }
            .dialog-flow-header-phone-text-disabled {
                color: #999999;
            }
        }
    }
    .dialog-flow-content {
        min-height: 500px;
        .dialog-flow-content-list {
            height: fit-content;
            padding: 24px 0;
            display: flex;
            flex-direction: column;
            gap: 10px;
            overflow-y: auto;
            .ant-bubble-list{
                overflow: auto;
                padding: 0 20px;
            }
            .user-content {
                background-color: #222222;
                padding: 8px 12px;
                display: flex;
                align-items: center;
                p {
                    margin-bottom: 0px;
                }
            }
            .assistant-content {
                border-radius: 8px;
                background: #ffffff;
                padding: 8px 12px;
                display: flex;
                align-items: center;
                p {
                    margin-bottom: 0px;
                }
            }
            .mention {
                background-color: #ebfaf4;
                font-weight: 400;
                color: #02be7e;
                font-family: PingFang SC;
                font-size: 12px;
                line-height: 16px;
                text-align: left;
                padding: 1px 2px;
                margin-right: 0;
                border-radius: 2px;
            }
            .tag {
                background-color: #edf6ff;
                border-radius: 2px;
                font-weight: 400;
                color: #198cff;
                font-family: PingFang SC;
                font-size: 12px;
                line-height: 16px;
                text-align: left;
                padding: 1px 2px;
                margin-right: 0;
            }

            // 自定义样式标签
            :global {
                .mention {
                    color: #1677ff;
                    font-weight: 500;
                }

                .tag {
                    color: #ff6a00;
                    font-weight: 500;
                }

                // 让markdown内容看起来更好看
                ul,
                ol {
                    padding-left: 20px;
                    margin: 8px 0;
                }

                a {
                    color: #1677ff;
                    text-decoration: none;

                    &:hover {
                        text-decoration: underline;
                    }
                }

                // 用户消息自定义样式
                .user-container {
                    margin-bottom: 12px;
                }

                .user-content {
                    font-weight: 500;
                }

                .user-avatar {
                    background-color: #1890ff;
                    border-radius: 50%;
                    color: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                // 机器人消息自定义样式
                .assistant-container {
                    margin-bottom: 12px;
                }

                .assistant-content {
                    font-weight: 400;
                }

                .assistant-avatar {
                    background-color: #f5f5f5;
                    border-radius: 50%;
                    color: #333;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }
        }
        .dialog-flow-content-not-activated {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 500px;
            text-align: center;
            .dialog-flow-content-not-activated-empty {
                color: #999999;
            }
            .dialog-flow-content-not-activated-empty-button {
                color: #ff6a00;
                cursor: pointer;
            }
            .dialog-flow-content-not-activated-empty-button:hover {
                color: #ff6a00;
            }
        }
    }
    .dialog-flow-footer {
        padding: 0 20px;
        .dialog-flow-footer-input {
            margin-top: 10px;

            // 为输入区域添加样式
            :global {
                .ant-input {
                    border-radius: 20px;
                    padding: 8px 16px;
                }

                .ant-btn {
                    border-radius: 20px;
                }
            }
        }
    }
}

/* 工具调用和知识库调用的样式 */
.tool-calls-container,
.knowledge-call-container {
  margin: 10px 0;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.tool-calls-container h4,
.knowledge-call-container h4 {
  margin-top: 0;
  margin-bottom: 8px;
  color: #333333;
  font-size: 14px;
  font-weight: 600;
}

.tool-call-item {
  margin-bottom: 10px;
  padding: 8px;
  background-color: #ffffff;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.tool-name {
  font-weight: 600;
  margin-bottom: 5px;
  color: #1890ff;
}

.tool-request,
.tool-response {
  font-size: 12px;
  word-break: break-all;
  white-space: pre-wrap;
  margin-bottom: 5px;
}

.knowledge-names {
  margin-bottom: 8px;
  font-size: 13px;
}

.knowledge-recall h5 {
  margin-top: 0;
  margin-bottom: 5px;
  font-size: 13px;
  font-weight: 600;
}

.recall-item {
  background-color: #ffffff;
  padding: 8px;
  margin-bottom: 5px;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
  font-size: 12px;
  word-break: break-all;
  white-space: pre-wrap;
}
