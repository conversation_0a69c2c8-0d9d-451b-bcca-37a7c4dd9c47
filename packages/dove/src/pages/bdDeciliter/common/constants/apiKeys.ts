import { Environment, getEnvironment } from '../utils';

interface ApiKeys {
    CLIENT_ID: string;
    CLIENT_SECRET: string;
}

// 生产环境密钥
const PROD_KEYS: ApiKeys = {
    CLIENT_ID: 'D6cKYRpn/QaxkXngw7kPLz6E4tmGUn58xgr4X9F1LHk=',
    CLIENT_SECRET: '6d6a296ceb1144829868c7827a91db93',
};

// 测试环境密钥
const TEST_KEYS: ApiKeys = {
    CLIENT_ID: 's3nQE5n4p9+aI/TEDI1Hv/wrM/OgyLlAq56Ts8X7YBo=',
    CLIENT_SECRET: 'afd8db558f1d4d44a617d8750ab35ac4',
};

// 根据环境获取密钥
export const getApiKeys = (): ApiKeys => {
    const env = getEnvironment();
    
    switch (env) {
        case Environment.PRORD:
            return PROD_KEYS;
        case Environment.DEV:
        case Environment.TEST:
        case Environment.ST:
        default:
            return TEST_KEYS;
    }
};

// 导出当前环境的密钥
export const API_KEYS = getApiKeys();

// 导出其他常量
export const GRANT_TYPE = 'client_credentials'; 