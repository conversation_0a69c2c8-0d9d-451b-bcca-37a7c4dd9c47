import React, {
    createContext,
    useContext,
    useState,
    ReactNode,
    useEffect,
} from 'react';

// 定义Context的类型
interface FrontLineContextType {
    isFrontLine: boolean;
    setIsFrontLine: (value: boolean) => void;
}

// 创建Context
const FrontLineContext = createContext<FrontLineContextType | undefined>(
    undefined,
);

// Provider组件的Props类型
interface FrontLineProviderProps {
    children: ReactNode;
    defaultValue?: boolean;
}

// Provider组件
export const FrontLineProvider: React.FC<FrontLineProviderProps> = ({
    children,
    defaultValue = true,
}) => {
    const [isFrontLine, setIsFrontLine] = useState<boolean>(defaultValue);

    const value: FrontLineContextType = {
        isFrontLine,
        setIsFrontLine,
    };

    return (
        <FrontLineContext.Provider value={value}>
            {children}
        </FrontLineContext.Provider>
    );
};

// 自定义Hook来使用Context
export const useFrontLine = (): FrontLineContextType => {
    const context = useContext(FrontLineContext);
    if (context === undefined) {
        throw new Error('useFrontLine must be used within a FrontLineProvider');
    }
    return context;
};

// 导出Context（如果需要直接使用）
export { FrontLineContext };
