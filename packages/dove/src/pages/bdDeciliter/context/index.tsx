import { createContext, useState, useEffect } from 'react';
import { getIsFrontLineBd } from '../services';

export const BDDeciliterContext = createContext({
    /**
     * isFrontLine: boolean - 是否是前线(BD)
     */
    isFrontLine: true,
});

/**
 * BDDeciliterProvider 组件用于提供前线状态的上下文
 * @param {React.ReactNode} children - 子组件
 * @returns {JSX.Element} 包裹子组件的上下文提供者
 */
const BDDeciliterProvider = ({ children }) => {
    const [isFrontLine, setIsFrontLine] = useState(true);

    const fetchData = async () => {
        try {
            const result = await getIsFrontLineBd();
            setIsFrontLine(result);
        } catch (error) {
            console.error('获取前线状态失败:', error);
        }
    };

    useEffect(() => {
        fetchData();
    }, []);

    return (
        <BDDeciliterContext.Provider value={{ isFrontLine }}>
            {children}
        </BDDeciliterContext.Provider>
    );
};

export default BDDeciliterProvider;
