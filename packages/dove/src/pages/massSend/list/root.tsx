import AdaptiveGrid from '@src/components/AdaptiveGrid';
import OrganizationSelector from '@src/components/rooPlus/OrganizationSelector';
import {
    Table,
    Form,
    DatePicker,
    Input,
    Select,
    Button,
    Space,
    Typography,
    Row,
} from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useAntdTable } from 'ahooks';
import axios from 'axios';
import { useEnums } from '../hooks';
import dayjs, { Dayjs } from 'dayjs';
import MisSearch from '@src/components/rooPlus/MisSearch';
import { useOpenCancelModal } from '../components/cancelModal';
import { PREFIX } from '../constants';
import { useEffect } from 'react';

export enum SendStatus {
    sending,
    sent,
    partRecalled,
    allRecalled,
}
interface Data {
    senderName: string;
    senderMis: string;
    massSendTaskId: number;
    sentTime: number;
    receiverCount: number;
    themeName: string;
    title: string;
    content: string;
    sendStatus: number;
    auditStatus: number;
    callBackId: number;
}
const getColumns = ({ getStatusDesc, openCancelModal }): ColumnsType<Data> => {
    return [
        {
            title: '群发ID',
            dataIndex: 'massSendTaskId',
        },
        {
            title: '发送时间',
            dataIndex: 'sentTime',
            render: v => dayjs.unix(v).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
            title: '发送BD',
            dataIndex: 'senderMis',
            render: (_, r) =>
                [r.senderName, r.senderMis].filter(Boolean).join('/'),
        },
        {
            title: '发送商家数量',
            dataIndex: 'receiverCount',
        },
        {
            title: '消息模板',
            dataIndex: 'themeName',
            render: v => v || '-',
        },
        {
            title: '消息标题',
            dataIndex: 'title',
            width: '15%',
            render: v => (
                <Typography.Text
                    ellipsis={{ tooltip: v }}
                    style={{ width: 200 }}
                >
                    {v}
                </Typography.Text>
            ),
        },
        {
            title: '消息内容',
            dataIndex: 'content',
            width: '20%',
            render: v => (
                <Typography.Text
                    ellipsis={{ tooltip: v }}
                    style={{ width: 200 }}
                >
                    {v}
                </Typography.Text>
            ),
        },
        {
            title: '审核状态',
            dataIndex: 'auditStatus',
            render: v => getStatusDesc(v, 'auditStatus'),
        },
        {
            title: '发送状态',
            dataIndex: 'sendStatus',
            render: v => getStatusDesc(v, 'sendStatus'),
        },
        {
            title: '操作',
            dataIndex: 'massSendTaskId',
            width: 200,
            render: (v, r) => {
                return (
                    <>
                        <Button
                            type={'link'}
                            onClick={() => {
                                open(
                                    `${
                                        import.meta.env.DEV ? '' : '/page/dove'
                                    }/massSend/detail?massSendId=${v}`,
                                );
                            }}
                        >
                            查看
                        </Button>
                        {[SendStatus.partRecalled, SendStatus.sent].includes(
                            r.sendStatus,
                        ) ? (
                            <>
                                |
                                <Button
                                    type={'link'}
                                    onClick={() => openCancelModal(v)}
                                >
                                    撤回
                                </Button>
                            </>
                        ) : null}
                    </>
                );
            },
        },
    ];
};

const Root = () => {
    const [form] = Form.useForm();
    const { tableProps, search } = useAntdTable(
        async ({ pageSize, current }) => {
            const params = form.getFieldsValue();
            params.pageSize = pageSize;
            params.pageNum = current;

            if (params.time) {
                params.beginTime = (params.time[0] as Dayjs)
                    .startOf('day')
                    .unix();
                params.endTime = (params.time[1] as Dayjs).endOf('day').unix();
                delete params.time;
            }
            if (params.uid) {
                params.uid = params.uid.id;
            }

            const res = await axios.post(`${PREFIX}/page`, params);
            if (res.data.code === 0) {
                return {
                    list: res.data.data.massSendTaskList,
                    total: res.data.data.total,
                };
            }
            return { list: [], total: 0 };
        },
        { form, defaultPageSize: 20 },
    );
    const { submit, reset } = search;
    useEffect(submit, []);
    const { themes, auditStatus, getStatusDesc, sendStatus } = useEnums();
    const openCancelModal = useOpenCancelModal();
    return (
        <>
            <Form
                layout={'vertical'}
                form={form}
                initialValues={{
                    time: [dayjs().add(-1, 'month'), dayjs()],
                }}
            >
                <AdaptiveGrid divide={4}>
                    <Form.Item label={'时间范围'} name="time">
                        <DatePicker.RangePicker
                            style={{ width: '100%' }}
                            allowClear={false}
                        />
                    </Form.Item>
                    <Form.Item label={'组织架构'} name="orgIds">
                        <OrganizationSelector multiple />
                    </Form.Item>
                    <Form.Item label={'发送人'} name="uid">
                        <MisSearch />
                    </Form.Item>
                    <Form.Item label={'模板'} name="themeIds">
                        <Select
                            allowClear
                            mode="multiple"
                            placeholder={'请选择模板'}
                            options={themes}
                            fieldNames={{
                                label: 'themeName',
                                value: 'themeId',
                            }}
                        />
                    </Form.Item>
                    <Form.Item label={'审核状态'} name="auditStatus">
                        <Select
                            allowClear
                            mode="multiple"
                            placeholder={'请选择审核状态'}
                            options={auditStatus}
                        />
                    </Form.Item>
                    <Form.Item label={'发送状态'} name="sendStatus">
                        <Select
                            allowClear
                            mode="multiple"
                            placeholder={'请选择发送状态'}
                            options={sendStatus}
                        />
                    </Form.Item>
                    <Form.Item label={'关键词'} name="keyword">
                        <Input
                            placeholder={'请输入消息内容关键词'}
                            allowClear
                        />
                    </Form.Item>
                    <Form.Item label={'群发ID'} name={'massSendId'}>
                        <Input
                            placeholder={'请输入群发ID'}
                            addonAfter={null}
                            style={{ width: '100%' }}
                        />
                    </Form.Item>
                </AdaptiveGrid>
                <Row justify={'end'} style={{ marginBottom: 20 }}>
                    <Space>
                        <Button onClick={reset}>重置</Button>
                        <Button type={'primary'} onClick={submit}>
                            查询
                        </Button>
                    </Space>
                </Row>
            </Form>
            <Table
                bordered
                rowKey={v => v.massSendTaskId}
                columns={getColumns({ getStatusDesc, openCancelModal })}
                {...tableProps}
            />
        </>
    );
};
export default Root;
