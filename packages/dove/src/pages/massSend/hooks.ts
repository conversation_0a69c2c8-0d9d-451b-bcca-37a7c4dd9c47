import { useRequest } from 'ahooks';
import { App } from 'antd';
import axios from 'axios';
import { PREFIX } from './constants';

interface Enums {
    auditStatus: { label: string; value: number }[];
    sendStatus: { label: string; value: number }[];
    themes: { themeId: number; themeName: string }[];
}
export const useEnums = () => {
    const { data } = useRequest<Enums, any>(
        async () => {
            const res = await axios.get(`${PREFIX}/items`);
            if (res.data.code === 0) {
                return res.data.data || res.data;
            }
            return {};
        },
        { cacheKey: 'enums' },
    );
    return {
        themes: data?.themes,
        sendStatus: data?.sendStatus,
        auditStatus: data?.auditStatus,
        getThemeDesc: id => {
            return data?.themes.find(v => v.themeId === id)?.themeName || '-';
        },
        getStatusDesc: (
            target,
            mode: 'auditStatus' | 'sendStatus' = 'auditStatus',
        ) => {
            return data?.[mode]?.find(v => v.value === target)?.label || '-';
        },
    };
};

interface Data {
    massSendId: number;
    content: string;
    title: string;
    senderUid: number;
    senderMis: string;
    senderName: string;
    sentTime: number;
    sendStatus: number;
    auditStatus: number;
    receiverCount: number;
    pictureUrls: string[];
    receiverPois: {
        poiId: number;
        poiName: string;
        isRecalled: boolean;
    }[];
}

export const useDetail = (targetId?) => {
    return useRequest<Data, any>(async () => {
        const id =
            targetId ||
            new URL(window.location.href).searchParams.get('massSendId');
        if (!id) {
            return null;
        }
        const res = await axios.post(`${PREFIX}/detail`, {
            massSendId: id,
        });
        if (res.data.code === 0) {
            return res.data.data;
        }
        return null;
    });
};
