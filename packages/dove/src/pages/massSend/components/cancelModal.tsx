import {
    App,
    Radio,
    Card,
    Checkbox,
    Input,
    Row,
    Button,
    Space,
    Spin,
    Typography,
    Upload,
    Form,
    message,
} from 'antd';
import { useDetail } from '../hooks';
import { useRef, useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { getPrefix } from '@src/module/request/getPrefix';
import axios from 'axios';
import { PREFIX } from '../constants';
import { useWatch } from 'antd/es/form/Form';

const ModalContent = ({ id, modalIns }) => {
    const [form] = Form.useForm();
    const { data } = useDetail(id);
    const [isCheckbox, setIsCheckbox] = useState(true);
    const checkedList = useWatch('poiIdCheckbox', form);

    if (!data) {
        return <Spin />;
    }
    const options = data?.receiverPois.map(
        ({ poiId, poiName, isRecalled }) => ({
            label: `${poiName}(${poiId})`,
            value: String(poiId),
            disabled: isRecalled,
        }),
    );
    const checkAll = options.length === checkedList?.length;
    const indeterminate =
        checkedList?.length > 0 && checkedList?.length < options.length;
    const onCheckAllChange = e => {
        if (e.target.checked) {
            form.setFieldValue(
                'poiIdCheckbox',
                options.map(v => v.value),
            );
            return;
        }
        form.resetFields(['poiIdCheckbox']);
    };

    const actionUrl = `${getPrefix('/impc/task/upload')}/impc/task/upload`;
    return (
        <Form
            form={form}
            initialValues={{
                poiIdCheckbox: data?.receiverPois
                    .filter(v => v.isRecalled)
                    .map(v => String(v.poiId)),
            }}
        >
            <Typography.Title level={4}>撤回范围选择 二选一</Typography.Title>
            <Space
                direction={'vertical'}
                size={'large'}
                style={{ width: '100%' }}
            >
                <Space direction={'vertical'} style={{ width: '100%' }}>
                    <Radio
                        checked={isCheckbox}
                        onClick={() => setIsCheckbox(true)}
                    >
                        <>选择商家撤回</>
                    </Radio>
                    <Card style={{ width: '100%' }}>
                        <Checkbox
                            disabled={!isCheckbox}
                            indeterminate={indeterminate}
                            onChange={onCheckAllChange}
                            checked={checkAll}
                        >
                            全选
                        </Checkbox>
                        <Form.Item
                            name={'poiIdCheckbox'}
                            rules={
                                isCheckbox
                                    ? [
                                          {
                                              required: true,
                                              message: '请选择商家',
                                          },
                                      ]
                                    : []
                            }
                        >
                            <Checkbox.Group
                                disabled={!isCheckbox}
                                options={options}
                            />
                        </Form.Item>
                    </Card>
                </Space>
                <Space direction={'vertical'} style={{ width: '100%' }}>
                    <Radio
                        checked={!isCheckbox}
                        onClick={() => setIsCheckbox(false)}
                    >
                        导入商家ID撤回
                    </Radio>
                    <Row>
                        <Form.Item
                            name={'poiIdInput'}
                            style={{ width: '100%' }}
                            rules={
                                !isCheckbox
                                    ? [
                                          {
                                              required: true,
                                              message: '请输入商家ID',
                                          },
                                          {
                                              validator: (_, value) => {
                                                  if (!value) {
                                                      // 交给上面的规则校验
                                                      return Promise.resolve();
                                                  }
                                                  if (
                                                      /^(\d+)(,\d+)*$/.test(
                                                          value,
                                                      )
                                                  ) {
                                                      return Promise.resolve();
                                                  }
                                                  return Promise.reject(
                                                      '请输入使用英文逗号分割的商家ID',
                                                  );
                                              },
                                          },
                                      ]
                                    : []
                            }
                        >
                            <Input.TextArea
                                disabled={isCheckbox}
                                placeholder={'请输入商家ID，使用英文逗号分割'}
                            />
                        </Form.Item>
                    </Row>
                </Space>
            </Space>
            <Typography.Title level={4}>撤回原因</Typography.Title>
            <Space
                direction={'vertical'}
                style={{ width: '100%' }}
                size={'small'}
            >
                <Form.Item
                    name={'comment'}
                    style={{ width: '100%' }}
                    rules={[{ required: true, message: '请输入撤回原因' }]}
                >
                    <Input.TextArea placeholder={'请输入撤回原因'} />
                </Form.Item>

                <Form.Item name={'attachedImageUrls'}>
                    <Upload
                        listType="picture-card"
                        accept="image/*"
                        action={actionUrl}
                        // fileList={fileList.images}
                        multiple
                        // onChange={list => handleChange(list, FileType.IMAGE)}
                    >
                        <div>
                            <PlusOutlined />
                            <div className="ant-upload-text">上传图片</div>
                        </div>
                    </Upload>
                </Form.Item>
            </Space>
            <Row justify={'end'}>
                <Space>
                    <Button
                        type={'primary'}
                        onClick={async () => {
                            const values = await form.validateFields();
                            const draftParams = { ...values };
                            if (isCheckbox) {
                                const recalledPoiIds = new Set(
                                    data.receiverPois
                                        .filter(v => v.isRecalled)
                                        .map(v => String(v.poiId)),
                                );
                                const poiIdCheckbox = new Set(
                                    draftParams.poiIdCheckbox,
                                );
                                draftParams.wmPoiIds = Array.from(
                                    poiIdCheckbox.difference(recalledPoiIds),
                                ).map(Number);
                            } else {
                                draftParams.wmPoiIds = draftParams.poiIdInput
                                    .split(',')
                                    .map(v => v.trim())
                                    .filter(Boolean)
                                    .map(Number);
                            }

                            if (!draftParams.wmPoiIds?.length) {
                                return message.error('请输入或者选择商家ID');
                            }
                            delete draftParams.poiIdCheckbox;
                            delete draftParams.poiIdInput;

                            if (draftParams.attachedImageUrls) {
                                draftParams.attachedImageUrls =
                                    draftParams.attachedImageUrls.fileList
                                        .map(v => v.response.data)
                                        .filter(Boolean);
                                if (!draftParams.attachedImageUrls?.length) {
                                    delete draftParams.attachedImageUrls;
                                }
                            }
                            const res = await axios.post(
                                `${PREFIX}/recall/message`,
                                {
                                    massSendId: Number(id),
                                    ...draftParams,
                                },
                            );
                            if (res.data.code === 0) {
                                return modalIns.current?.destroy();
                            }
                            message.error(res.data.message);
                        }}
                    >
                        确认
                    </Button>
                    <Button onClick={modalIns.current?.destroy}>取消</Button>
                </Space>
            </Row>
        </Form>
    );
};
export const useOpenCancelModal = () => {
    const { modal } = App.useApp();
    const modalIns = useRef<any>();
    return id => {
        modalIns.current = modal.confirm({
            icon: null,
            width: 800,
            footer: null,
            title: null,
            closable: true,
            content: <ModalContent id={id} modalIns={modalIns} />,
        });
        return modalIns.current;
    };
};
