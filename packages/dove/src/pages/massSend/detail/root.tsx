import { useRequest } from 'ahooks';
import {
    Table,
    Form,
    DatePicker,
    Input,
    Select,
    InputNumber,
    Image,
    Button,
    Typography,
    Space,
    Card,
    Row,
    Empty,
    Descriptions,
    message,
    Spin,
} from 'antd';
import dayjs from 'dayjs';
import { CheckBox } from '@roo/roo';
import { useDetail, useEnums } from '../hooks';
import { useOpenCancelModal } from '../components/cancelModal';
import { SendStatus } from '../list/root';

const Root = () => {
    const { data } = useDetail();
    const { getStatusDesc } = useEnums();
    const openCancelModal = useOpenCancelModal();
    if (!data) {
        return <Spin />;
    }
    return (
        <Space direction={'vertical'}>
            <Card
                title={
                    <Row justify={'space-between'}>
                        基本信息
                        {[SendStatus.partRecalled, SendStatus.sent].includes(
                            data.sendStatus,
                        ) ? (
                            <Button
                                type={'primary'}
                                onClick={() => {
                                    openCancelModal(
                                        new URL(
                                            window.location.href,
                                        ).searchParams.get('massSendId'),
                                    );
                                }}
                            >
                                撤回
                            </Button>
                        ) : null}
                    </Row>
                }
            >
                <Descriptions column={1}>
                    {[
                        {
                            label: '标题',
                            children: data.title,
                        },
                        {
                            label: '审核状态',
                            children: getStatusDesc(
                                data.auditStatus,
                                'auditStatus',
                            ),
                        },
                        {
                            label: '发送状态',
                            children: getStatusDesc(
                                data.sendStatus,
                                'sendStatus',
                            ),
                        },
                        {
                            label: '发送人',
                            children: data.senderName + '/' + data.senderMis,
                        },
                        {
                            label: '发送时间',
                            children: dayjs
                                .unix(data.sentTime)
                                .format('YYYY-MM-DD HH:mm:ss'),
                        },
                    ].map(v => (
                        <Descriptions.Item label={v.label}>
                            {v.children}
                        </Descriptions.Item>
                    ))}
                </Descriptions>
            </Card>
            <Card title={'消息内容'}>
                <Typography>{data.content}</Typography>
                <Space>
                    {data.pictureUrls.map(v => {
                        return <Image src={v} width={100} />;
                    })}
                </Space>
            </Card>
            <Card title={`商家范围（${data.receiverCount}）`}>
                <Space style={{ flexWrap: 'wrap' }} size={'large'}>
                    {data.receiverPois.map(({ poiId, poiName, isRecalled }) => {
                        return (
                            <div>
                                <CheckBox
                                    checked={isRecalled}
                                    disabled={true}
                                    style={{ marginRight: 8 }}
                                />
                                {poiName}({poiId})
                            </div>
                        );
                    })}
                </Space>
            </Card>
        </Space>
    );
};
export default Root;
