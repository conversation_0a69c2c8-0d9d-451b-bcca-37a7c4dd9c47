# 技能管理系统 PRD

## 1. 产品概述
技能管理系统是一个用于管理和配置各种技能的平台，支持技能的查询、新建、编辑和管理功能。

## 2. 功能模块

### 2.1 技能查询
- **功能描述**：支持按多个维度查询已配置的技能
- **查询条件**：
  - 技能类型（下拉选择）
  - 技能状态（下拉选择）
  - 技能名称（文本输入）
- **查询结果展示**：
  - 序号
  - 技能名称
  - 技能编码
  - 技能类型
  - 技能状态
  - 编辑/开启/关闭（操作按钮）
  - 操作（查看｜删除）

### 2.2 新建技能
- **功能描述**：支持创建新的技能配置
- **表单字段**：
  - 技能信息
    - 技能名称（必填）
    - 技能类型（下拉选择，必填）
    - 当前状态（下拉选择，必填）
    - 描述（文本域）
  - 调用API信息
    - 协议类型（单选：HTTP/THRIFT/PIGEON）
    - 请求地址（文本输入）
    - 超时时间（数字输入）
    - 请求方式（单选：POST/GET）
    - 是否需要APPKEY（下拉选择）
  - 配置入参信息
    - 参数名称
    - 是否必填
    - 参数类型
    - 参数描述
    - 默认值
    - 校验规则
    - 操作
  - 配置出参信息
    - 参数名称
    - 参数类型
    - 参数描述
    - 操作

## 3. 交互说明

### 3.1 页面布局
- 顶部：查询条件区域
- 中部：查询结果列表
- 右上角：新建技能按钮

### 3.2 操作流程
1. **查询流程**：
   - 输入查询条件
   - 点击查询按钮
   - 展示查询结果

2. **新建流程**：
   - 点击新建技能按钮
   - 填写技能信息
   - 配置API信息
   - 配置入参信息
   - 配置出参信息
   - 点击保存

## 4. 权限控制
- 查看权限：允许查看技能列表和详情
- 编辑权限：允许新建和编辑技能
- 删除权限：允许删除技能

## 5. 技术实现

### 5.1 前端技术栈
- React + TypeScript
- Ant Design 组件库
- SCSS 样式处理

## 6. 注意事项
1. 所有表单提交前需要进行数据验证
2. API调用需要做好错误处理和超时处理
3. 页面需要适配不同分辨率
4. 需要添加必要的loading状态
5. 操作需要有相应的成功/失败提示 