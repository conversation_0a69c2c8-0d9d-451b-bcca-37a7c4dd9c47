import { render } from '@src/module/root';
import Root from './App';
import { useEffect } from 'react';
import LocalProvider from '@src/components/LocalProvider';

const Wrapper = () => {
    useEffect(() => {
        const defaultTracker = window.LXAnalytics('getTracker');
        defaultTracker('pageView', {}, {}, 'c_waimai_m_m_crm_bi_mspuss76');
    }, []);

    return (
        <LocalProvider>
            <div className={'noOutline'}>
                <Root />
            </div>
        </LocalProvider>
    );
};

render(<Wrapper />, '沟通agent');
