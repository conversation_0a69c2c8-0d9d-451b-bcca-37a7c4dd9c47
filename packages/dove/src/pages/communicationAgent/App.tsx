import React, { useState } from 'react';
import { Flex, Tabs } from 'antd';
import TaskList from './components/TaskList';
import PoiList from './components/PoiList';
import TaskInfo from './components/TaskInfo';
import TaskStatistic from './components/TaskStatistic';
import { TaskData } from './types';
import './index.scss';

const CommunicationAgentPage: React.FC = () => {
    const [currentTask, setCurrentTask] = useState<TaskData | null>(null); // 当前焦点所在任务
    return (
        <Tabs
            defaultActiveKey="1"
            items={[
                {
                    label: '任务列表',
                    key: '1',
                    children: (
                        <Flex className={'pageContainer'}>
                            <TaskList
                                style={{
                                    width: 340,
                                    borderRight: '1px solid #F0F0F0',
                                }}
                                onSelect={setCurrentTask}
                            />
                            <div
                                className="communication-right"
                                style={{ flex: 1 }}
                            >
                                <TaskInfo currentTask={currentTask} />
                                <TaskStatistic currentTask={currentTask} />
                            </div>
                        </Flex>
                    ),
                },
                {
                    label: '商家列表',
                    key: '2',
                    children: <PoiList />,
                },
            ]}
        />
    );
};

export default CommunicationAgentPage;
