/**
 * 任务统计数据展示组件
 */
import React, { useState, useEffect, useRef } from 'react';
import { Flex, Result, Image, Button, message, Tabs } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import TaskMonitorTab from './TaskMonitorTab';
import TaskListTab from './TaskListTab';
import { useInterval } from 'ahooks';
import noNet from '@src/assets/img/noNet.png';
import { TaskStatusEnum, ReachTypeEnum } from '@src/constants';
import { TaskStatisticData } from '../../types';

const TaskStatistic = props => {
    const {
        currentTask, // 当前焦点所在任务数据
    } = props;
    const hasError = useRef<Record<number, boolean>>({});
    const [statisticData, setStatisticData] =
        useState<TaskStatisticData | null>(null); // 任务统计数据

    // 当前焦点所在任务发生变化，重新获取任务统计数据
    useEffect(() => {
        if (!currentTask || !currentTask.id) {
            setStatisticData(null);
            return;
        }
        fetchTaskStatistic(currentTask.id);
    }, [currentTask]);

    // 定时器，每5秒刷新一次任务统计数据
    useInterval(() => {
        if (
            !currentTask ||
            hasError.current[currentTask.id] ||
            currentTask.taskStatus === TaskStatusEnum.DONE
        ) {
            return;
        }
        fetchTaskStatistic(currentTask.id);
    }, 1000 * 5);

    // 查询任务统计数据
    const fetchTaskStatistic = async (taskId: number) => {
        try {
            const res: any = await apiCaller.post(
                '/xianfu/api-v2/dove/task/statistic',
                { taskId },
            );
            if (res.code !== 0) {
                hasError.current[taskId] = true;
                return;
            }
            setStatisticData(res.data);
            if (!res.data) {
                return;
            }
        } catch (error) {
            console.error('获取统计数据失败:', error);
            message.error('获取统计数据失败');
        }
    };

    // 手动刷新任务统计数据
    const handleRefresh = async () => {
        if (!currentTask) return;
        try {
            await fetchTaskStatistic(currentTask?.id);
            message.success('刷新成功');
        } catch (error) {
            message.error('刷新失败，请重试');
            console.error('刷新失败:', error);
        }
    };

    return (
        <Tabs //这个就是点击一个任务后，会根据它的完成情况展示卡片，如果完成，就会有任务监控和任务列表两个选项，否则，只有一个任务详情
            type="card"
            destroyInactiveTabPane
            defaultActiveKey={
                currentTask?.taskStatus === TaskStatusEnum.INIT ||
                currentTask?.taskStatus === TaskStatusEnum.BAOSHIJIE_AUDITING ||
                currentTask?.taskStatus === TaskStatusEnum.AUDIT
                    ? 'taskList'
                    : 'taskMonitor'
            }
            items={[
                {
                    label: '任务监控',
                    key: 'taskMonitor',
                    children: (
                        <>
                            {statisticData ? (
                                <TaskMonitorTab
                                    key={currentTask?.id}
                                    currentTask={currentTask}
                                    statisticData={statisticData}
                                    handleRefresh={handleRefresh}
                                />
                            ) : (
                                <div
                                    className="flex-col"
                                    style={{ paddingTop: '100px' }}
                                >
                                    {/* 如果任务状态是审核中，待执行，待审核，则展示文案：任务创建中。否则展示暂无数据 */}
                                    {currentTask?.taskStatus ===
                                        TaskStatusEnum.INIT ||
                                    currentTask?.taskStatus ===
                                        TaskStatusEnum.BAOSHIJIE_AUDITING ||
                                    currentTask?.taskStatus ===
                                        TaskStatusEnum.AUDIT ? (
                                        <Result
                                            title="任务创建中，请耐心等待"
                                            icon={
                                                <Image
                                                    width={80}
                                                    src={noNet}
                                                    alt="暂无数据"
                                                    preview={false}
                                                />
                                            }
                                        />
                                    ) : (
                                        <Flex
                                            justify="center"
                                            style={{ flex: 1 }}
                                        >
                                            <Result
                                                title="暂无数据"
                                                icon={
                                                    <Image
                                                        width={80}
                                                        src={noNet}
                                                        alt="暂无数据"
                                                        preview={false}
                                                    />
                                                }
                                            />
                                        </Flex>
                                    )}
                                </div>
                            )}
                        </>
                    ),
                },
                {
                    label:
                        currentTask?.reachType ===
                        ReachTypeEnum.INTELLIGENT_SCHEDULING
                            ? '任务明细'
                            : '任务列表',
                    key: 'taskList',
                    children: (
                        <TaskListTab
                            key={currentTask?.id}
                            currentTask={currentTask}
                            statisticData={statisticData as any}
                        />
                    ),
                },
            ]}
        />
    );
};

export default TaskStatistic;
