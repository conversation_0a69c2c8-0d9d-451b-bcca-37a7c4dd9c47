/**
 * 【任务列表】tab结构
 * 【触达类型】为"智能调度"时
 */
import React from 'react';
import {
    Card,
    Form,
    Input,
    Select,
    DatePicker,
    Row,
    Col,
    Table,
    Space,
    Button,
    Tag,
} from 'antd';
import type { TableColumnType } from 'antd';
import { EyeOutlined } from '@ant-design/icons';
import { useAntdTable } from 'ahooks';
import PhoneLock from '@src/components/PhoneLock';
import CallLogBtn from './CallLogBtn';
import TaskTrackBtn from './TaskTrackBtn';
import TakeoverBtn from './TakeoverBtn';
import dayjs from 'dayjs';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { TaskData, TaskList2Data } from '../../../../types';
import { processStatusOptions } from '@src/constants';
import './index.scss';

interface Props {
    currentTask: TaskData; // 当前任务
}

const TaskList2: React.FC<Props> = ({ currentTask }) => {
    const [form] = Form.useForm();

    // crud查询hook
    const {
        search, // crud 搜索方法（触发form.submit()）
        refresh, // 刷新 crud（不触发form.submit()，使用上一次搜索参数查询）
        tableProps, // Table 参数集
    } = useAntdTable(
        async ({ current: page, pageSize }, formData) => {
            try {
                const res: any = await apiCaller.post(
                    // @ts-ignore
                    '/xianfu/api-v2/dove/scheduling/query',
                    {
                        taskId: [currentTask.id], // 任务ID
                        contactId: formData.contactId
                            ? [formData.contactId]
                            : undefined, // 【子任务ID】
                        contactObjectName:
                            formData.contactObjectName || undefined, // 【门店名称】
                        contactObjectId: formData.contactObjectId
                            ? [formData.contactObjectId]
                            : undefined, // 【门店ID】
                        followPerson: formData.followPerson || undefined, // 【责任人】
                        processStatus: formData.processStatus, // 【流程节点】
                        startTimeMin:
                            formData.startTime && formData.startTime[0]
                                ? dayjs(formData.startTime[0])
                                      .startOf('day')
                                      .format('x')
                                : undefined, // 【触达时间】开始
                        startTimeMax:
                            formData.startTime && formData.startTime[1]
                                ? dayjs(formData.startTime[1])
                                      .endOf('day')
                                      .format('x')
                                : undefined, // 【触达时间】结束
                        phoneNum: formData.phoneNum || undefined, // 【被呼叫电话】
                        pageSize,
                        page,
                    } as any,
                );
                if (res.code !== 0) {
                    return {
                        list: [],
                        total: 0,
                    };
                }
                return {
                    list: res?.data?.data || [],
                    total: res?.data?.total || 0,
                };
            } catch (error) {
                console.error('获取数据失败:', error);
                return {
                    list: [],
                    total: 0,
                };
            }
        },
        {
            defaultPageSize: 20,
            form,
            // manual: true, // 初始化时不查询
        },
    );

    // 表格列配置
    const columns: TableColumnType<TaskList2Data>[] = [
        {
            dataIndex: 'contactId',
            title: '子任务ID',
            render: text => text || '-',
        },
        {
            dataIndex: 'objectId',
            title: '门店信息',
            render: (text, record) => {
                return (
                    <div>
                        <div>{record.objectName || '-'}</div>
                        <div>{record.objectId || '-'}</div>
                        <PhoneLock
                            phoneNumber={record.phoneNum}
                            btnText={<EyeOutlined />}
                        />
                    </div>
                );
            },
        },
        {
            dataIndex: 'processStatus',
            title: '流程节点',
            render: text => {
                const option = processStatusOptions.find(
                    item => item.value === text,
                );
                return option ? (
                    <Tag color={option.color}>{option.label}</Tag>
                ) : (
                    '-'
                );
            },
        },
        {
            dataIndex: 'latestCallTime',
            title: '上次沟通时间',
            render: text =>
                text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '-',
        },
        {
            dataIndex: 'tagName',
            title: '意向标签',
            render: text => text || '-',
        },
        {
            dataIndex: 'summarizing',
            title: '总结归纳',
            render: text => {
                return Array.isArray(text)
                    ? text.map((item, index) => (
                          <div key={String(index)}>{item}</div>
                      ))
                    : '-';
            },
        },
        {
            dataIndex: 'operate',
            width: 150,
            title: '操作',
            render: (text, record) => {
                return (
                    <Space wrap>
                        <CallLogBtn record={record} />
                        <TaskTrackBtn record={record} />
                        <TakeoverBtn
                            record={record}
                            onDone={() => {
                                refresh(); // 刷新crud
                            }}
                        />
                    </Space>
                );
            },
        },
    ];

    return (
        <div className="task-list-tab-2">
            <Card style={{ marginBottom: 16 }}>
                <Form form={form} layout="vertical">
                    <Row gutter={24}>
                        <Col span={6}>
                            <Form.Item name="contactId" label="子任务ID">
                                <Input placeholder="请输入" />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item
                                name="contactObjectName"
                                label="门店名称"
                            >
                                <Input placeholder="请输入" />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item name="contactObjectId" label="门店ID">
                                <Input placeholder="请输入" />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item name="followPerson" label="责任人">
                                <Input placeholder="请输入" />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item name="processStatus" label="流程节点">
                                <Select
                                    mode="multiple"
                                    allowClear
                                    options={processStatusOptions}
                                    placeholder="请选择"
                                />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item name="startTime" label="触达时间">
                                <DatePicker.RangePicker
                                    style={{ width: '100%' }}
                                    allowClear
                                />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item name="phoneNum" label="被呼叫电话">
                                <Input placeholder="请输入" />
                            </Form.Item>
                        </Col>
                        <Col span={6}>
                            <Form.Item label=" ">
                                <Space wrap>
                                    <Button
                                        onClick={() => {
                                            search.reset();
                                        }}
                                    >
                                        重置
                                    </Button>
                                    <Button
                                        type="primary"
                                        onClick={() => {
                                            search.submit();
                                        }}
                                    >
                                        查询
                                    </Button>
                                </Space>
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Card>
            <Table
                {...tableProps}
                rowKey="contactId"
                bordered
                columns={columns}
                pagination={{
                    ...tableProps.pagination,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: total => `共 ${total} 条`,
                }}
            />
        </div>
    );
};

export default TaskList2;
