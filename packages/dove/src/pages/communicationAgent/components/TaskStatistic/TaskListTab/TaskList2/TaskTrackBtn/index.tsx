/**
 * 【任务轨迹】按钮
 */
import React, { useState } from 'react';
import { Button, Drawer } from 'antd';
import { TaskList2Data } from '../../../../../types';

interface Props {
    record: TaskList2Data; // 表格行数据
}

const TaskTrackBtn: React.FC<Props> = props => {
    const { record } = props;
    const [open, setOpen] = useState(false);

    const pageUrl = `${
        import.meta.env.DEV ? '' : '/page/dove'
    }/flowConfig?contactId=${record.contactId}&fromXFIframe=1`;

    return (
        <>
            <Button type="link" onClick={() => setOpen(true)}>
                任务轨迹
            </Button>

            <Drawer
                title="任务轨迹"
                placement="right"
                width="80%"
                onClose={() => setOpen(false)}
                open={open}
                destroyOnHidden
            >
                <iframe
                    src={pageUrl}
                    style={{
                        width: '100%',
                        height: 'calc(100vh - 120px)',
                        border: 'none',
                    }}
                    title="任务轨迹"
                />
            </Drawer>
        </>
    );
};

export default TaskTrackBtn;
