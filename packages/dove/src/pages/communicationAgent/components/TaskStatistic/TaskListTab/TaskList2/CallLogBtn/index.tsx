/**
 * 【沟通记录】按钮
 */
import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Drawer, Spin, Timeline, Space } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import AudioTalkWindow from '@src/components/AudioTalkWindow';
import AudioPlayer, { AudioPlayerRefProps } from '@src/components/AudioPlayer';
import CallTooltip from '@src/components/TaskReachDetail/CallTooltip';
import dayjs from 'dayjs';
import {
    TaskList2Data,
    CallLogData,
    CallLogDetailData,
    CallMessage,
} from '../../../../../types';
import { connectedOptions } from '@src/constants';
import { formatMillisecond } from '@src/utils';
import './index.scss';

interface Props {
    record: TaskList2Data; // 表格行数据
}

const CallLogBtn: React.FC<Props> = props => {
    const { record } = props;
    const audioRef: any = useRef<AudioPlayerRefProps>(null); // audio元素
    const [showDrawer, setShowDrawer] = useState(false); // 是否显示抽屉
    const [callLogListloading, setcallLogListLoading] = useState(false); // 是否在列表loading中
    const [callLogList, setCallLogList] = useState<CallLogData[]>([]); // 沟通记录数据
    const [currentContactId, setCurrentContactId] = useState<number>(-1); // 当前选中的沟通记录id
    const [callLogDetailLoading, setCallLogDetailLoading] = useState(false); // 是否在详情loading中
    const [currentCallLog, setCurrentCallLog] =
        useState<CallLogDetailData | null>(null); // 当前选中的沟通记录数据

    // 当前contactId变化时，获取沟通记录详情数据
    useEffect(() => {
        if (currentContactId) {
            getCallLogData(currentContactId);
        }
    }, [currentContactId]);

    // 【沟通记录】按钮点击
    const handleClick = () => {
        setShowDrawer(true);
        getCallLogListData();
    };

    // 获取沟通记录列表数据
    const getCallLogListData = async () => {
        try {
            setcallLogListLoading(true);
            const res: any = await apiCaller.post(
                // @ts-ignore
                '/xianfu/api-v2/dove/data/object/call-history',
                {
                    objectId: record.objectId, // 对象id
                    objectType: record.objectType, // 对象类型
                },
            );
            if (
                res.code === 0 &&
                Array.isArray(res.data) &&
                res.data.length > 0
            ) {
                setCallLogList(res.data);
                // 默认查第一个详情
                setCurrentContactId(res.data[0].contactId);
            }
        } catch (err) {
            console.log(err);
        } finally {
            setcallLogListLoading(false);
        }
    };

    // 获取单条沟通记录数据
    const getCallLogData = async contactId => {
        try {
            setCallLogDetailLoading(true);
            const res: any = await apiCaller.post(
                // @ts-ignore
                '/xianfu/api-v2/dove/data/object/call-detail',
                {
                    contactId, // 主键id
                },
            );
            if (res.code === 0) {
                setCurrentCallLog(res.data);
            }
        } catch (err) {
            console.log(err);
        } finally {
            setCallLogDetailLoading(false);
        }
    };

    const handleBubbleClick = (startTime: number) => {
        if (audioRef.current) {
            audioRef.current.play(startTime);
        }
    };

    return (
        <>
            <Button type="link" onClick={handleClick}>
                沟通记录
            </Button>
            <Drawer
                size="large"
                title="沟通记录"
                open={showDrawer}
                width="75%"
                onClose={() => {
                    setShowDrawer(false);
                }}
            >
                {callLogListloading ? (
                    <Spin spinning />
                ) : (
                    <div className="call-log-container">
                        <div className="call-log-left">
                            <div className="left-title">沟通时间列表</div>
                            <div className="left-content">
                                {Array.isArray(callLogList) ? (
                                    <Timeline
                                        mode="left"
                                        items={callLogList.map(item => ({
                                            color: '#FFDD00',
                                            position: 'right',
                                            children: (
                                                <div
                                                    className={`call-log-item ${
                                                        currentContactId ===
                                                        item?.contactId
                                                            ? 'selected'
                                                            : ''
                                                    }`}
                                                    onClick={() => {
                                                        setCurrentContactId(
                                                            item.contactId,
                                                        );
                                                    }}
                                                >
                                                    <div className="item-time-line">
                                                        {item.startTime
                                                            ? dayjs(
                                                                  item.startTime,
                                                              ).format(
                                                                  'YYYY-MM-DD HH:mm:ss',
                                                              )
                                                            : '-'}
                                                    </div>
                                                    <div className="item-name-line">
                                                        <div>
                                                            {item.staffName ||
                                                                '-'}
                                                        </div>
                                                        <div>
                                                            {item.callTypeName ||
                                                                '-'}
                                                        </div>
                                                    </div>
                                                    <div
                                                        className="item-status-line"
                                                        style={{
                                                            color: connectedOptions.find(
                                                                option =>
                                                                    option.value ===
                                                                    item.connected,
                                                            )?.color,
                                                        }}
                                                    >
                                                        {connectedOptions.find(
                                                            option =>
                                                                option.value ===
                                                                item.connected,
                                                        )?.label || '-'}
                                                    </div>
                                                </div>
                                            ),
                                        }))}
                                    />
                                ) : null}
                            </div>
                        </div>
                        <div className="call-log-right">
                            <div className="right-title">
                                通话记录
                                <CallTooltip
                                    contactType={currentCallLog?.callType}
                                />
                            </div>
                            {callLogDetailLoading ? (
                                <Spin spinning />
                            ) : currentCallLog ? (
                                <>
                                    <div className="right-header">
                                        <div className="header-title">
                                            <span className="staff-name">
                                                {currentCallLog?.staffName ||
                                                    '-'}
                                            </span>
                                            <span className="call-type-name">
                                                {currentCallLog?.callTypeName ||
                                                    '-'}
                                            </span>
                                        </div>
                                        <div className="header-content">
                                            <div className="content-left">
                                                <Space size="large" wrap>
                                                    <span>
                                                        通话时间：
                                                        {currentCallLog?.startTime
                                                            ? dayjs(
                                                                  currentCallLog?.startTime,
                                                              ).format(
                                                                  'YYYY-MM-DD HH:mm:ss',
                                                              )
                                                            : '-'}
                                                    </span>
                                                    <span>
                                                        通话时长：
                                                        {formatMillisecond(
                                                            currentCallLog?.talkingTimeLen ||
                                                                0,
                                                        )}
                                                    </span>
                                                </Space>
                                            </div>
                                            <div className="content-right">
                                                <div
                                                    className="connected"
                                                    style={{
                                                        color: connectedOptions.find(
                                                            option =>
                                                                option.value ===
                                                                currentCallLog?.connected,
                                                        )?.color,
                                                    }}
                                                >
                                                    {connectedOptions.find(
                                                        option =>
                                                            option.value ===
                                                            currentCallLog?.connected,
                                                    )?.label || '-'}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="right-content">
                                        <AudioTalkWindow
                                            talkDatas={
                                                currentCallLog?.message as CallMessage[]
                                            }
                                            speakerNames={['客服', '商户']}
                                            onBubbleClick={handleBubbleClick}
                                        />
                                    </div>
                                    <div className="right-audio-player">
                                        <AudioPlayer
                                            ref={audioRef}
                                            audioUrl={currentCallLog?.audioUrl}
                                            talkingSeconds={
                                                currentCallLog?.talkingTimeLen
                                                    ? currentCallLog?.talkingTimeLen /
                                                      1000
                                                    : 0
                                            }
                                        />
                                    </div>
                                </>
                            ) : (
                                '暂无数据'
                            )}
                        </div>
                    </div>
                )}
            </Drawer>
        </>
    );
};

export default CallLogBtn;
