.call-log-container {
    height: 100%;
    display: flex;
    overflow: hidden;
    .call-log-left {
        height: 100%;
        width: 350px;
        border-right: 1px solid #e0e0e0;
        padding-right: 10px;
        display: flex;
        flex-direction: column;
        .left-title {
            font-size: 16px;
            font-weight: 600;
            color: #333333;
        }
        .left-content {
            flex: 1;
            overflow-y: auto;
            padding-top: 10px;
            .call-log-item {
                padding: 10px;
                cursor: pointer;
                display: flex;
                flex-direction: column;
                border: 1px solid transparent;
                &.selected {
                    border: 1px solid #FFDD00;
                    background: #FFFDF0;
                    border-radius: 4px;
                }
                .item-tiem-line {
                    color: #666666;
                }
                .item-name-line {
                    display: flex;
                    justify-content: space-between;
                }
                .item-status-line {
                    position: relative;
                    padding-left: 8px;
                    &::before {
                        content: '';
                        position: absolute;
                        left: 0;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 4px;
                        height: 4px;
                        background-color: currentColor;
                        border-radius: 50%;
                    }
                }
            }
        }
    }
    .call-log-right {
        flex: 1;
        padding-left: 10px;
        display: flex;
        flex-direction: column;
        .right-title {
            font-size: 16px;
            font-weight: 600;
            color: #333333;
        }
        .right-header {
            padding: 10px;
            border-bottom: 1px solid #f0f0f0;
            background-color: #F8F8F8;
            .header-title {
                font-size: 14px;
                font-weight: 600;
                .staff-name {
                    margin-right: 20px;
                }
            }
            .header-content {
                display: flex;
                justify-content: space-between;
                .content-left {
                    flex: 1;
                    color: #666;
                }
                .content-right {
                    .connected {
                        position: relative;
                        padding-left: 8px;
                        &::before {
                            content: '';
                            position: absolute;
                            left: 0;
                            top: 50%;
                            transform: translateY(-50%);
                            width: 4px;
                            height: 4px;
                            background-color: currentColor;
                            border-radius: 50%;
                        }
                    }
                }
            }
        }
        .right-content {
            margin-bottom: 10px;
            flex: 1;
            padding: 10px;
            background-color: #F8F8F8;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }
        .right-audio-player {
            padding: 16px;
            border-top: 1px solid #f0f0f0;
            background-color: #fff;
        }
    }
}