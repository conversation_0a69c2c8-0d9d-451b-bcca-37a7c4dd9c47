/**
 * 【任务列表】tab结构
 * 【触达类型】为“单次沟通”时
 */
import React, { useMemo, useState } from 'react';
import { Button, Tabs, message, App } from 'antd';
import TableList from '@src/pages/communicationDetail/components/TableList';
import { ExecutionStatusEnum } from '@src/constants';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { isEqual } from 'lodash';
import { TaskStatisticData } from '../../../../types';
import styles from './index.module.scss';

type TaskStatus = 'completed' | 'inProgress' | 'pending';

const TaskList1: React.FC<{
    currentTask: any;
    statisticData: TaskStatisticData;
}> = ({ currentTask, statisticData }) => {
    const [activeTab, setActiveTab] = useState<string>(
        String(ExecutionStatusEnum.DONE),
    );
    const { modal } = App.useApp();

    const handleExport = async () => {
        const res = await apiCaller.post('/xianfu/api-v2/dove/data/export', {
            // @ts-ignore
            taskId: currentTask.id,
        });
        if (res.code !== 0) {
            return;
        }
        message.success('导出成功');
    };

    const batchIntentionRecognize = task => {
        modal.confirm({
            title: '批量重新输出意向',
            content: '将重新输出意向，输出完成后将通过大象推送，是否确认？',
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
                apiCaller
                    .post('/xianfu/api-v2/dove/agent/batch/intention', {
                        taskId: task.id,
                        taskName: task.taskName,
                    })
                    .catch(() => {
                        message.error('批量重新输出意向失败');
                    });
            },
        });
    };

    // agent任务执行状态
    const items = [
        {
            key: String(ExecutionStatusEnum.DONE),
            label: `已执行${statisticData?.taskProgress?.doneCount || 0}`,
        },
        {
            key: String(ExecutionStatusEnum.DOING),
            label: `执行中${statisticData?.taskProgress?.doingCount || 0}`,
        },
        {
            key: String(ExecutionStatusEnum.WAITING),
            label: `待执行${statisticData?.taskProgress?.waitingCount || 0}`,
        },
        {
            key: String(ExecutionStatusEnum.CANCEL),
            label: `已取消${statisticData?.taskProgress?.cancelCount || 0}`,
        },
    ];

    const searchParams = useMemo(() => {
        return {
            taskId: currentTask?.id,
            executionStatus: [Number(activeTab)],
        };
    }, [currentTask, activeTab]);

    const rankFilters = useMemo(() => {
        if (!statisticData) return [];
        const sTags = statisticData?.successTag.map(item => ({
            text: [item.tagCode, item.tagName].filter(Boolean).join('-'),
            value: item.tagCode,
        }));

        const fTags = statisticData?.failTag
            .filter(it => it.tagCode)
            .map(item => ({
                text: [item.tagCode, item.tagName].filter(Boolean).join('-'),
                value: item.tagCode,
            }));

        return [...sTags, ...fTags];
    }, [statisticData?.successTag, statisticData?.failTag]);

    return (
        <div className={styles.taskListContainer}>
            <div className={styles.header}>
                <Tabs
                    activeKey={activeTab}
                    items={items}
                    onChange={key => setActiveTab(key as TaskStatus)}
                />
                <div className={styles.operationBtn}>
                    <Button
                        onClick={() => batchIntentionRecognize(currentTask)}
                        type="default"
                        className="mr-15"
                    >
                        批量重新输出意向
                    </Button>
                    <Button type="primary" onClick={handleExport}>
                        导出
                    </Button>
                </div>
            </div>
            <TableList
                searchParams={searchParams}
                showPartColumns={true}
                rankFilters={rankFilters}
            />
        </div>
    );
};

export default React.memo(TaskList1, (prevProps, nextProps) => {
    return (
        prevProps.currentTask.id === nextProps.currentTask.id &&
        isEqual(prevProps.statisticData, nextProps.statisticData)
    );
});
