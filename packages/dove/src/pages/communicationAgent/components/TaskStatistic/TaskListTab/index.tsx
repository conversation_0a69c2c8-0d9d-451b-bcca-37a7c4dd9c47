/**
 * 【任务列表】tab结构
 */
import React from 'react';
import TaskList1 from './TaskList1';
import TaskList2 from './TaskList2';
import { TaskStatisticData } from '../../../types';
import { ReachTypeEnum } from '@src/constants';

interface Props {
    currentTask: any;
    statisticData: TaskStatisticData;
}

const TaskListTab: React.FC<Props> = ({ currentTask, statisticData }) => {
    // 渲染函数
    const render = () => {
        if (currentTask?.reachType === ReachTypeEnum.SINGLE_COMMUNICATION) {
            // 【触达类型】为"单次沟通"
            return (
                <TaskList1
                    currentTask={currentTask}
                    statisticData={statisticData}
                />
            );
        }
        if (currentTask?.reachType === ReachTypeEnum.INTELLIGENT_SCHEDULING) {
            // 【触达类型】为"智能调度"
            return <TaskList2 currentTask={currentTask} />;
        }
        // 兜底，当"单次沟通"显示
        return (
            <TaskList1
                currentTask={currentTask}
                statisticData={statisticData}
            />
        );
    };

    return render();
};

export default TaskListTab;
