/**
 * 刷新模块
 */
import React, { useState } from 'react';
import { Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';

interface Props {
    handleRefresh: () => void;
}

const RefreshButton: React.FC<Props> = ({ handleRefresh: _handleRefresh }) => {
    const [loading, setLoading] = useState(false);

    const handleRefresh = async () => {
        try {
            setLoading(true);
            await _handleRefresh();
        } finally {
            setLoading(false);
        }
    };

    return (
        <Button
            type="link"
            style={{
                background: '#F5F6FA',
            }}
            icon={
                <ReloadOutlined
                    style={{
                        color: '#FF6A00',
                    }}
                />
            }
            loading={loading}
            onClick={handleRefresh}
        >
            <span
                style={{
                    color: '#FF6A00',
                }}
            >
                刷新
            </span>
        </Button>
    );
};

export default RefreshButton;
