/**
 * 【任务监控】tab结构
 */
import TaskProgressCard1 from './TaskProgressCard1';
import TaskProgressCard2 from './TaskProgressCard2';
import AIAnalysis from './AIAnalysis';
import { TaskStatisticData, TaskData } from '../../../types';
import { ReachTypeEnum } from '@src/constants';

interface Props {
    currentTask: TaskData | null; // 当前焦点所在任务
    statisticData: TaskStatisticData | null; // 任务统计数据
    handleRefresh: () => void; // 刷新任务统计数据
}

const TaskMonitorTab = (props: Props) => {
    const {
        currentTask, // 当前焦点所在任务
        statisticData, // 任务统计数据
        handleRefresh, // 刷新任务统计数据
    } = props;

    // 渲染函数
    const render = () => {
        if (currentTask?.reachType === ReachTypeEnum.SINGLE_COMMUNICATION) {
            // 【触达类型】为"单次沟通"
            return (
                <div>
                    <TaskProgressCard1
                        statisticData={statisticData}
                        handleRefresh={handleRefresh}
                    />
                    <AIAnalysis
                        successData={statisticData?.successTag}
                        failureData={statisticData?.failTag}
                    />
                </div>
            );
        }
        if (currentTask?.reachType === ReachTypeEnum.INTELLIGENT_SCHEDULING) {
            // 【触达类型】为"智能调度"
            return (
                <div>
                    <TaskProgressCard2
                        statisticData={statisticData}
                        handleRefresh={handleRefresh}
                    />
                    {/* TODO: 当期隐藏，等后端数据支持后放开 */}
                    {/* <AIAnalysis
                        successData={statisticData?.successTag}
                        failureData={statisticData?.failTag}
                    /> */}
                </div>
            );
        }
        // 兜底，当"单次沟通"显示
        return (
            <div>
                <TaskProgressCard1
                    statisticData={statisticData}
                    handleRefresh={handleRefresh}
                />
                <AIAnalysis
                    successData={statisticData?.successTag}
                    failureData={statisticData?.failTag}
                />
            </div>
        );
    };

    return render();
};

export default TaskMonitorTab;
