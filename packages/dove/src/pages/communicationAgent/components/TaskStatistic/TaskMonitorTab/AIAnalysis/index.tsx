/**
 * 【成功意向】【失败原因】分析模块
 */
import React, { useEffect, useRef } from 'react';
import { Row, Col ,Avatar} from 'antd';
import { Chart } from '@antv/g2';
import aiLogo from '@src/assets/img/ai_logo.png';
import { percentFormatter } from '@src/utils';
import './index.scss';
import { aiAvatar } from '@src/constants';

const formatStr = (str = '') =>
    str.length > 10 ? `${str.slice(0, 10)}...` : str;

const AIAnalysis: React.FC<{
    successData: any;
    failureData: any;
}> = ({ successData, failureData }) => {
    const successChartRef = useRef<HTMLDivElement>(null);
    const failureChartRef = useRef<HTMLDivElement>(null);

    const successTotal = successData?.reduce(
        (acc: number, curr: any) => acc + curr.count,
        0,
    );

    const failureTotal = failureData?.reduce(
        (acc: number, curr: any) => acc + curr.count,
        0,
    );

    const sortedSuccessData = successData?.toSorted(
        (a: any, b: any) => b.count - a.count,
    );
    const sortedFailureData = failureData?.toSorted(
        (a: any, b: any) => b.count - a.count,
    );

    useEffect(() => {
        let successChart:any = null;
        let failureChart:any = null;
        if (successChartRef.current) {
            // 创建成功原因图表
             successChart = new Chart({
                container: successChartRef.current,
                autoFit: true,
                height: 400,
            });

            successChart
                .interval()
                .data(sortedSuccessData)
                .transform({ type: 'stackY' })
                .coordinate({ type: 'theta' })
                .encode('y', 'count')
                .encode('color', 'tagName')
                .style('radius', 0.75)
                .tooltip(data => ({
                    name: data.tagName,
                    value: data.count,
                }))
                .legend('color', {
                    crossPadding: 48, // 设置图例和图表之间的间距，单位是像素
                    size: 80,
                })
                .label({
                    text: d =>
                        `${formatStr(d.tagName)}: ${percentFormatter(
                            d.count,
                            successTotal,
                            2,
                        )}%`,
                    position: 'outside',
                    connector: false,
                    transform: [
                        { type: 'exceedAdjust' },
                        { type: 'overlapDodgeY' },
                        { type: 'overflowHide' },
                        { type: 'overlapHide' },
                    ],
                });

            successChart.theme({
                category10: ['#0E75F7', '#00BF7F', '#FCC132'],
            });

            successChart.render();
        }
            // 创建失败原因图表
        if (failureChartRef.current) {
            failureChart = new Chart({
                container: failureChartRef.current,
                autoFit: true,
                height: 400,
            });
            failureChart
                .interval()
                .data(sortedFailureData)
                .transform({ type: 'stackY' })
                .coordinate({ type: 'theta' })
                .encode('y', 'count')
                .encode('color', 'tagName')
                .style('radius', 0.75)
                .legend('color', {
                    crossPadding: 48, // 设置图例和图表之间的间距，单位是像素
                    size: 80,
                })
                .label({
                    text: (d, index) =>
                        index >= 10
                            ? ''
                            : `${formatStr(d.tagName)}: ${(
                                  (d.count / failureTotal) *
                                  100
                              ).toFixed(2)}%`,
                    position: 'outside',
                    // connector: (d, index) => index < 10,
                    connector: false,
                    transform: [
                        { type: 'exceedAdjust' },
                        { type: 'overlapDodgeY' },
                        { type: 'overflowHide' },
                        { type: 'overlapHide' },
                    ],
                })
                .tooltip(data => ({
                    name: data.tagName,
                    value: `${data.count}`,
                }));

            // 设置失败原因图表的配色方案
            failureChart.theme({
                category10: ['#FF6A00', '#8F6EF6', '#EE80FF'],
            });

            failureChart.render();
        }
            // 清理函数
        return () => {
            if (successChart) {
                successChart.destroy();
            }
            if (failureChart) {
                failureChart.destroy();
            }
        };
    }, [successData, failureData]);

    return (
        <div className="ai-analysis">
            <Row gutter={[16, 16]}>
                <Col xs={24} xxl={12}>
                    <div className="custom-card">
                        <div className="card-header">
                            <img
                                src={aiAvatar}
                                alt="AI Logo"
                                className="ai-logo"
                            />
                            
                            {/* <Avatar
            icon={ <img src={aiAvatar}></img>}
            style={{ backgroundColor: 'transparent'  }}
          /> */}
                            <div className="header-text">
                                <span className="primary-text">
                                    帮你智能归纳
                                </span>
                                <span className="secondary-text">成功意向</span>
                            </div>
                        </div>
                        <div className="card-content">
                            {successData?.length > 0 ? (
                                <div ref={successChartRef} />
                            ) : (
                                <div className="no-data">暂无成功数据</div>
                            )}
                        </div>
                    </div>
                </Col>
                <Col xs={24} xxl={12}>
                    <div className="custom-card">
                        <div className="card-header">
                            <img
                                src={aiAvatar}
                                alt="AI Logo"
                                className="ai-logo"
                            />
                            <div className="header-text">
                                <span className="primary-text">
                                    帮你智能归纳
                                </span>
                                <span className="secondary-text">失败原因</span>
                            </div>
                        </div>
                        <div className="card-content">
                            {failureData?.length > 0 ? (
                                <div ref={failureChartRef} />
                            ) : (
                                <div className="no-data">暂无失败数据</div>
                            )}
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
    );
};

export default AIAnalysis;
