.ai-analysis {
    padding: 24px;

    .custom-card {
        position: relative;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        background-image: url('../../../../../assets//img/ai_bg.png');
        background-size: cover;
        background-position: center;

        .card-header {
            display: flex;
            align-items: center;
            padding: 20px 24px;
            
            .ai-logo {
                width: 30px;
                height: 30px;
                margin-left: -8px;
            }

            .header-text {
                display: flex;
                align-items: center;
                gap: 4px;
                
                .primary-text {
                    font-size: 16px;
                    color: #666;
                    font-weight: 500;
                }

                .secondary-text {
                    font-size: 16px;
                    color: #222;
                    font-weight: 500;
                }
            }
        }

        .card-content {
            position: relative;
            padding: 0 24px;
            min-height: 400px;
            
            .no-data {
                position: absolute;
                top: 0;
                left: 24px;
                right: 24px;
                bottom: 5px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #999;
                font-size: 14px;
                background-color: #f5f5f5;
                border-radius: 4px;
            }
        }
    }

}
