/**
 * 任务进度模块
 * 【触达类型】为"智能调度"
 */
import React from 'react';
import { Space } from 'antd';
import AIProgress from '../AIProgress';
import RefreshButton from '../RefreshButton';
import arrowPng from '@src/assets/img/arrow.png';
import { TaskStatisticData } from '../../../../types';
import './index.scss';

// 箭头元素
const ArrowImg = (
    <img className="card-arrow" src={arrowPng} width={40} alt="arrow" />
);

interface Props {
    statisticData: TaskStatisticData | null; // 任务统计数据
    handleRefresh: () => void; // 刷新任务统计数据
}

const TaskProgressCard2: React.FC<Props> = props => {
    const {
        statisticData, // 任务统计数据
        handleRefresh, // 刷新任务统计数据
    } = props;

    const {
        totalCount = 0,
        aiExecutionCount = 0,
        aiDoneCount = 0,
        telesaleExecutionCount = 0,
        telesaleDoneCount = 0,
        bdCount = 0,
    } = statisticData?.scheduleProgress || {};

    return (
        <div className="task-progress-card-2-wrap">
            <div className="card-desc">
                <div className="card-desc-text">
                    各角色流转执行任务，数据实时更新
                </div>
                <RefreshButton handleRefresh={handleRefresh} />
            </div>
            <div className="card-items">
                <div className="card-item-wrap">
                    <h4>任务总数</h4>
                    <div className="card-item-count">
                        {(totalCount || 0).toLocaleString('en-US')}
                    </div>
                    <div>
                        <Space split="-">
                            <span
                                style={{
                                    color: 'rgba(1, 102, 255, 1)',
                                    fontWeight: 600,
                                }}
                            >
                                AI
                            </span>
                            <span
                                style={{
                                    color: 'rgba(0, 191, 127, 1)',
                                    fontWeight: 600,
                                }}
                            >
                                电销
                            </span>
                            <span
                                style={{
                                    color: 'rgba(252, 193, 50, 1)',
                                    fontWeight: 600,
                                }}
                            >
                                BD
                            </span>
                        </Space>
                    </div>
                </div>
                {ArrowImg}
                <div className="card-item-wrap">
                    <h4>AI</h4>
                    <div className="card-item-count">
                        {(
                            aiExecutionCount ||
                            0 + aiDoneCount ||
                            0
                        ).toLocaleString('en-US')}
                    </div>
                    <AIProgress
                        data={
                            [
                                aiExecutionCount
                                    ? {
                                          year: '',
                                          value: aiExecutionCount,
                                          type: '执行中',
                                      }
                                    : undefined,
                                aiDoneCount
                                    ? {
                                          year: '',
                                          value: aiDoneCount,
                                          type: '已完成',
                                      }
                                    : undefined,
                            ].filter(Boolean) as any
                        }
                        colors={[
                            'rgba(1, 102, 255, 1)',
                            'rgba(1, 102, 255, 0.5)',
                        ]}
                    />
                </div>
                {ArrowImg}
                <div className="card-item-wrap">
                    <h4>电销</h4>
                    <div className="card-item-count">
                        {(
                            telesaleExecutionCount ||
                            0 + telesaleDoneCount ||
                            0
                        ).toLocaleString('en-US')}
                    </div>
                    <AIProgress
                        data={
                            [
                                telesaleExecutionCount
                                    ? {
                                          year: '',
                                          value: telesaleExecutionCount,
                                          type: '执行中',
                                      }
                                    : undefined,
                                telesaleDoneCount
                                    ? {
                                          year: '',
                                          value: telesaleDoneCount,
                                          type: '已完成',
                                      }
                                    : undefined,
                            ].filter(Boolean) as any
                        }
                        colors={[
                            'rgba(0, 191, 127, 1)',
                            'rgba(0, 191, 127, 0.5)',
                        ]}
                    />
                </div>
                {ArrowImg}
                <div className="card-item-wrap">
                    <h4>BD</h4>
                    <div className="card-item-count">
                        {(bdCount || 0).toLocaleString('en-US')}
                    </div>
                    <AIProgress
                        data={
                            [
                                bdCount
                                    ? {
                                          year: '',
                                          value: bdCount,
                                          type: '已接管',
                                      }
                                    : undefined,
                            ].filter(Boolean) as any
                        }
                        colors={['rgba(252, 193, 50, 1)']}
                    />
                </div>
            </div>
        </div>
    );
};

export default TaskProgressCard2;
