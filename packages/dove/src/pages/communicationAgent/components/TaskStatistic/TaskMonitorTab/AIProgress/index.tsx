/**
 * 进度条展示组件
 */
import { useEffect, useRef } from 'react';
import { Bar } from '@antv/g2plot';

interface AIProgressProps {
    data: Array<{
        year: string;
        value: number;
        type: string;
    }>;
    colors?: string[];
}

const AIProgress = ({ data, colors }: AIProgressProps) => {
    const containerRef = useRef<HTMLDivElement>(null);
    useEffect(() => {
        if (!containerRef.current) return;
        const stackedBarPlot = new Bar(containerRef.current, {
            data: data,
            isStack: true,
            xField: 'value',
            yField: 'year',
            height: 60,
            seriesField: 'type',
            xAxis: false,
            yAxis: false,
            legend: {
                position: 'bottom',
            },
            label: {
                // 可手动配置 label 数据标签位置
                position: 'middle', // 'left', 'middle', 'right'
                // 可配置附加的布局方法
                layout: [
                    // 柱形图数据标签位置自动调整
                    { type: 'interval-adjust-position' },
                    // 数据标签防遮挡
                    { type: 'interval-hide-overlap' },
                    // 数据标签文颜色自动调整
                    { type: 'adjust-color' },
                ],
            },
            color: colors || ['#198CFF', '#198CFF', '#eee'], // 默认颜色：蓝色、灰色、浅蓝色
        });

        stackedBarPlot.render();

        return () => {
            stackedBarPlot.destroy();
        };
    }, [containerRef.current, data, colors]);

    if (!data) return null;
    return <div ref={containerRef}></div>;
};

export default AIProgress;
