/**
 * 任务进度模块
 * 【触达类型】为"单次沟通"
 */
import React from 'react';
import { Row, Col, Card } from 'antd';
import AIProgress from '../AIProgress';
import RefreshButton from '../RefreshButton';
import { TaskStatisticData } from '../../../../types';
import { percentFormatter } from '@src/utils';
import './index.scss';

interface Props {
    statisticData: TaskStatisticData | null; // 任务统计数据
    handleRefresh: () => void; // 刷新任务统计数据
}

const TaskProgressCard1: React.FC<Props> = props => {
    const {
        statisticData, // 任务统计数据
        handleRefresh, // 刷新任务统计数据
    } = props;

    // 执行进度渲染
    const renderTaskProgress = () => {
        const {
            waitingCount = 0, // 待执行数量
            doingCount = 0, // 进行中数量
            doneCount = 0, // 完成数量
            cancelCount = 0, // 取消数量
        } = statisticData?.taskProgress || {};
        const total = waitingCount + doingCount + doneCount + cancelCount; // 总数量
        return (
            <Card
                title={
                    <span>
                        执行进度{' '}
                        <span className="stats-card-title">
                            已执行/全部任务
                        </span>
                    </span>
                }
                className="statsCard"
            >
                <div className="successRate">
                    <span className="percentage">
                        {percentFormatter(doneCount, total)}
                    </span>
                    <span className="ratio">
                        % {doneCount}/{total}
                    </span>
                </div>
                <AIProgress
                    data={
                        [
                            doneCount
                                ? {
                                      year: '',
                                      value: doneCount,
                                      type: '完成',
                                  }
                                : undefined,
                            waitingCount
                                ? {
                                      year: '',
                                      value: waitingCount,
                                      type: '待执行',
                                  }
                                : undefined,
                            doingCount
                                ? {
                                      year: '',
                                      value: doingCount,
                                      type: '进行中',
                                  }
                                : undefined,
                        ].filter(Boolean) as any
                    }
                    colors={['#198CFF', 'rgba(25, 140, 255, 0.5)', '#eee']}
                />
            </Card>
        );
    };

    // 触达成功率渲染
    const renderContactSuccessRate = () => {
        const contactSuccessCount = statisticData?.contactSuccessCount || 0; // 触达成功数量
        const contactFailCount = statisticData?.contactFailCount || 0; // 触达失败数量
        const contactTotal = contactSuccessCount + contactFailCount; // 触达总数量
        return (
            <Card
                title={
                    <span>
                        触达成功率{' '}
                        <span className="stats-card-title">
                            触达成功/已执行
                        </span>
                    </span>
                }
                className="statsCard"
            >
                <div className="successRate">
                    <span className="percentage">
                        {percentFormatter(contactSuccessCount, contactTotal)}
                    </span>
                    <span className="ratio">
                        % {contactSuccessCount}/{contactTotal}
                    </span>
                </div>
                <AIProgress
                    data={
                        [
                            contactSuccessCount
                                ? {
                                      year: '',
                                      value: contactSuccessCount,
                                      type: '触达成功',
                                  }
                                : undefined,
                            contactFailCount
                                ? {
                                      year: '',
                                      value: contactFailCount,
                                      type: '触达失败',
                                  }
                                : undefined,
                        ].filter(Boolean) as any
                    }
                    colors={['#52C41A', '#FF6A00']}
                />
            </Card>
        );
    };

    // 平均执行时长渲染
    const renderExecutionAvgTime = () => {
        const executionAvgTime = statisticData?.executionAvgTime || 0; // 平均执行时长
        const executionAvgTimeMinutes = Math.floor(executionAvgTime / 60); // 平均执行时长-分钟数
        const executionAvgTimeSeconds = executionAvgTime % 60; // 平均执行时长-秒数
        return (
            <Card title="平均执行时长" className="statsCard">
                <div className="executionTime">
                    <span className="timeValue">{executionAvgTimeMinutes}</span>
                    <span className="timeUnit">分</span>
                    <span className="timeValue">{executionAvgTimeSeconds}</span>
                    <span className="timeUnit">秒</span>
                </div>
            </Card>
        );
    };

    return (
        <div className="task-progress-card-1-wrap">
            <Row gutter={16}>
                <Col span={8}>{renderTaskProgress()}</Col>
                <Col span={8}>{renderContactSuccessRate()}</Col>
                <Col span={5}>{renderExecutionAvgTime()}</Col>
                <Col span={3}>
                    <RefreshButton handleRefresh={handleRefresh} />
                </Col>
            </Row>
        </div>
    );
};

export default TaskProgressCard1;
