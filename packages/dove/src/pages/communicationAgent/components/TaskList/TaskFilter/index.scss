.task-filter {
  padding: 16px;

  &__item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__label {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
  }

  &__button {
    padding: 6px 16px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    border: 1px solid transparent;
    transition: all 0.3s;

    &--clear {
      background: #fff;
      border-color: #d9d9d9;
      color: #666;

      &:hover {
        color: #1890ff;
        border-color: #1890ff;
      }
    }

    &--confirm {
      background: #1890ff;
      color: #fff;
      border-color: #1890ff;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }
  }
} 