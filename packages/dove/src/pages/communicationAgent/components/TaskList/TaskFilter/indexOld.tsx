import React from 'react';
import { Input, DatePicker, Button, Select, Form } from 'antd';
import SelectTaskType from '@src/components/SelectTaskType';
import MisSelect from '@src/components/MisSelector';
import OrganizationSelector from '@roo/roo-plus/OrganizationSelector';
import './index.scss';
import { filterReachMethodOptions } from '@src/constants';

const prefix = '/xianfu/api/common';

interface TaskFilterProps {
    onFilterChange: (filters: any) => void;
    onClear: () => void;
    showFilter: boolean;
}

const TaskFilter: React.FC<TaskFilterProps> = ({
    onFilterChange,
    onClear,
    showFilter,
}) => {
    const [taskType, setTaskType] = React.useState<string>(); // 【任务类型】
    const [reachMethod, setReachMethod] = React.useState<string>(); // 【触达方式】
    const [contactObjectId, setContactObjectId] = React.useState(''); // 【商家ID】
    const [id, setId] = React.useState(''); // 【任务ID】
    const [creator, setCreator] = React.useState(''); // 【创建人】
    const [creatorOrgId, setCreatorOrgId] = React.useState<number | number[]>(); // 【创建人组织架构】
    const [createTimeRange, setCreateTimeRange] = React.useState<[any, any]>([
        null,
        null,
    ]); // 【任务创建时间】
    const [touchTimeRange, setTouchTimeRange] = React.useState<[any, any]>([
        null,
        null,
    ]); // 【触达时间】

    // 【确定】按钮点击事件
    const handleConfirm = () => {
        onFilterChange({
            taskType,
            contactObjectId,
            id,
            reachMethod, //新增的 与后端对接
            creator,
            creatorOrgId,
            createTimeRange,
            // endTimeRange, //新增的 与后端对接
            touchTimeRange,
        });
    };

    // 【清空选择】按钮点击事件
    const handleClear = () => {
        setTaskType(undefined);
        setContactObjectId('');
        setId('');
        setCreator('');
        setCreatorOrgId(undefined);
        setCreateTimeRange([null, null]);
        setTouchTimeRange([null, null]);
        onClear();
    };

    return (
        <div
            className="task-filter"
            style={showFilter ? { display: 'block' } : { display: 'none' }}
        >
            <div className="task-filter__item">
                <div className="task-filter__label">触达方式</div>
                <Select
                    style={{ width: '100%' }}
                    value={reachMethod}
                    options={filterReachMethodOptions}
                    onChange={value => setReachMethod(value)}
                    placeholder="请选择触达方式"
                />
            </div>

            <div className="task-filter__item">
                <div className="task-filter__label">任务类型</div>
                <SelectTaskType
                    value={taskType}
                    onChange={value => setTaskType(value)}
                    placeholder="请选择任务类型"
                />
            </div>
            <div className="task-filter__item">
                <div className="task-filter__label">创建人</div>
                <MisSelect
                    value={creator ? [creator] : undefined}
                    mis={false}
                    mode={'multiple'}
                    onChange={v => setCreator(v[0])}
                    placeholder="请输入创建人"
                />
            </div>
            <div className="task-filter__item">
                <div className="task-filter__label">创建人组织架构</div>
                <OrganizationSelector
                    value={creatorOrgId}
                    multiple
                    params={{
                        backtrackOrgType: 3,
                        sources: '1_4_5_6_10_11_12_13_16_17_18_21_23',
                    }}
                    onConfirm={v => setCreatorOrgId(v)}
                    pidUrl={`${prefix}/uicomponent/api/orgs/getByPid`}
                    searchUrl={`${prefix}/uicomponent/api/orgs/search`}
                    placeholder="请选择创建人组织架构"
                />
            </div>
            <div className="task-filter__item">
                <div className="task-filter__label">商家ID</div>
                <Input
                    value={contactObjectId}
                    onChange={e => setContactObjectId(e.target.value)}
                    placeholder="请输入商家ID"
                />
            </div>

            <div className="task-filter__item">
                <div className="task-filter__label">任务ID</div>
                <Input
                    value={id}
                    onChange={e => setId(e.target.value)}
                    placeholder="请输入任务ID"
                />
            </div>

            <div className="task-filter__item">
                <div className="task-filter__label">任务创建时间</div>
                <DatePicker.RangePicker
                    value={createTimeRange}
                    onChange={dates => setCreateTimeRange(dates as any)}
                    style={{ width: '100%' }}
                />
            </div>

            <div className="task-filter__item">
                <div className="task-filter__label">触达时间</div>
                <DatePicker.RangePicker
                    value={touchTimeRange}
                    onChange={dates => setTouchTimeRange(dates as any)}
                    style={{ width: '100%' }}
                />
            </div>

            <div className="task-filter__actions">
                <Button
                    className="task-filter__button task-filter__button--clear"
                    onClick={handleClear}
                >
                    清空选择
                </Button>
                <Button onClick={handleConfirm} type="primary">
                    确定
                </Button>
            </div>
        </div>
    );
};

export default TaskFilter;
