/**
 * 任务名称搜索Input
 * 增加防抖效果
 */
import React from 'react';
import { Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { debounce } from 'lodash';

const TaskNameInput = props => {
    const { onChange } = props;

    const debouncedChange = debounce(onChange, 600);

    return (
        <Input
            style={{ width: '100%', height: '40px' }}
            prefix={<SearchOutlined />}
            onChange={e => {
                const val = e.target.value;
                debouncedChange(val);
            }}
            placeholder="输入任务名称搜索"
        />
    );
};

export default TaskNameInput;
