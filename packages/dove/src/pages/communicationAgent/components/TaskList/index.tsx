import React, { useState, useEffect, useRef, useLayoutEffect } from 'react';
import { Button, message, Progress, Tabs, Tag } from 'antd';
import { DownOutlined, PlusOutlined } from '@ant-design/icons';
import TaskNameInput from './TaskNameInput';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useLatest } from 'ahooks';
import dayjs from 'dayjs';
import TaskFilter from './TaskFilter';
import useModalCreateTask from './ModalCreateTask';
import { TaskData } from '../../types';
import {
    taskStatusOptions,
    reachTypeOptions,
    ReachTypeEnum,
    reachMethodOptions,
    TaskStatusEnum,
} from '@src/constants';
import { getAgentTaskIdFromUrl } from '@src/pages/communicationAgent/utils';
import TaskProgress from '../TaskProgress';
import './index.scss';

function observeElementHeight(elementId, callback) {
    const element = document.querySelector(elementId);
    if (!element) {
        console.error('Element not found');
        return;
    }

    let lastScrollHeight = element.scrollHeight;

    // 创建 MutationObserver 实例
    const observer = new MutationObserver(() => {
        // 检查 scrollHeight 是否发生变化
        const currentScrollHeight = element.scrollHeight;
        if (currentScrollHeight !== lastScrollHeight) {
            callback({
                oldHeight: lastScrollHeight,
                newHeight: currentScrollHeight,
                element: element,
            });
            lastScrollHeight = currentScrollHeight;
        }
    });

    // 配置观察选项
    const config = {
        attributes: true, // 观察属性变化
        childList: true, // 观察子节点变化
        subtree: true, // 观察所有后代节点
        characterData: true, // 观察文本内容变化
    };

    // 开始观察
    observer.observe(element, config);

    // 返回观察者实例，以便之后可以停止观察
    return observer;
}

// 定义组件Props
interface TaskList2Props {
    style?: React.CSSProperties;
    onSelect?: (task: TaskData) => void;
}

const TaskList: React.FC<TaskList2Props> = ({ style, onSelect }) => {
    const [tasks, setTasks] = useState<TaskData[]>([]);
    const [currentTab, setCurrentTab] = useState<string>('all');
    const [searchText, setSearchText] = useState<string>('');
    const [page, setPage] = useState(1);
    const [loading, setLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const observerRef = useRef<IntersectionObserver | null>(null);
    const loadMoreRef = useRef<HTMLDivElement>(null);
    const [showFilter, setShowFilter] = useState(false);
    const [filters, setFilters] = useState<any>({});
    const filterRef = useRef<HTMLDivElement>(null);
    const [selectedTaskId, setSelectedTaskId] = useState<number | null>(null);
    const [taskProgressVisible, setTaskProgressVisible] = useState(false);

    // 状态标签
    const statusTabs = taskStatusOptions.map(item => ({
        key: item.value,
        label: item.label,
    }));
    // 获取任务列表数据
    const latestSearchText = useLatest(searchText);
    const latestLoading = useLatest(loading);
    const latestFilters = useLatest(filters);

    const fetchTasks = async (isLoadMore = false) => {
        const filters = latestFilters.current;
        if (latestLoading.current || (isLoadMore && !hasMore)) return;

        setLoading(true);
        try {
            const currentPage = isLoadMore ? page + 1 : 1;
            const { createTimeRange, touchTimeRange, ...newFilters } = filters;
            const taskId = getAgentTaskIdFromUrl();
            const params = {
                taskName: latestSearchText.current || undefined, // 任务名称
                ...newFilters,
                taskStatus:
                    currentTab === 'all' ? undefined : [Number(currentTab)], // 任务状态
                taskType: filters.taskType ? filters.taskType : undefined, // 【任务类型】
                contactObjectId: filters.contactObjectId
                    ? [filters.contactObjectId]
                    : undefined, // 【商家ID】
                contactObjectType: filters?.contactObjectType
                    ? [filters.contactObjectType]
                    : undefined, //【商家/对象类型】
                reachType: filters?.reachType ? [filters.reachType] : undefined, //【触达类型】
                id: taskId ? [taskId] : filters.id ? [filters.id] : undefined, // 【任务ID】
                creatorOrgId:
                    filters?.creatorOrgId?.length > 0
                        ? filters.creatorOrgId
                        : undefined, // 【创建人组织架构】
                createTimeMin: createTimeRange?.[0]
                    ? dayjs(createTimeRange[0]).startOf('day').format('x')
                    : undefined, // 【任务创建时间】开始
                createTimeMax: createTimeRange?.[1]
                    ? dayjs(createTimeRange[1]).endOf('day').format('x')
                    : undefined, // 【任务创建时间】结束
                taskStartTimeMin: touchTimeRange?.[0]
                    ? dayjs(touchTimeRange[0]).startOf('day').format('x')
                    : undefined, // 【触达时间】开始
                taskStartTimeMax: touchTimeRange?.[1]
                    ? dayjs(touchTimeRange[1]).endOf('day').format('x')
                    : undefined, // 【触达时间】结束
                page: currentPage,
                pageSize: 10,
            };

            const res = await apiCaller.post(
                '/xianfu/api-v2/dove/task/search',
                params as any,
            );

            if (res.code !== 0) {
                console.error('获取任务列表失败:', res);
                return;
            }

            const { data } = res.data;
            // 确保返回的数据包含 taskStatus 字段
            !isLoadMore && handleTaskSelect(data[0] as any);
            setTasks(prev => (isLoadMore ? [...prev, ...data] : (data as any)));
            setPage(currentPage);
            setHasMore(data.length === 10);
        } catch (error) {
            console.error('获取任务列表失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const openCreateTaskModal = useModalCreateTask(fetchTasks);

    // 设置无限滚动观察器
    const latestHasMore = useLatest(hasMore);
    useEffect(() => {
        observerRef.current = new IntersectionObserver(
            entries => {
                if (entries[0].isIntersecting && latestHasMore.current) {
                    fetchTasks(true);
                }
            },
            { threshold: 0.5 },
        );

        if (loadMoreRef.current) {
            observerRef.current.observe(loadMoreRef.current);
        }

        return () => {
            if (observerRef.current) {
                observerRef.current.disconnect();
            }
        };
    }, [hasMore, page]);

    // 监听搜索文本和筛选条件变化
    useEffect(() => {
        setPage(1);
        setHasMore(true);
        fetchTasks();
    }, [searchText, filters, currentTab]);

    const handleCreateTask = async (): Promise<void> => {
        const success = await openCreateTaskModal();
        if (success) {
            setPage(1);
            setHasMore(true);
            fetchTasks();
        }
    };

    const handleTaskSelect = (task: TaskData) => {
        setSelectedTaskId(task?.id);
        onSelect?.(task);
    };

    const getStatus = (status: number) => {
        return taskStatusOptions.find(
            // @ts-ignore
            item => item.value === status,
        );
    };

    const gotToApproval = (task: TaskData) => {
        if (task?.taskStatus === TaskStatusEnum.AUDIT && task?.bpmUrl) {
            window.open(task?.bpmUrl, '_blank');
            return;
        }
    };

    const [scrollHeight, setScrollHeight] = useState(undefined);
    useLayoutEffect(() => {
        observeElementHeight('.communication-right', ({ newHeight }) =>
            setScrollHeight(newHeight),
        );
    }, []);

    return (
        <div className="task-list" style={{ ...style }}>
            <Button
                onClick={() => setTaskProgressVisible(true)}
                type="link"
                className="show-call-task-progress-btn"
            >
                查看数据
            </Button>
            <div className="task-list__search-filter">
                <div className="task-list__search">
                    <TaskNameInput
                        onChange={value => {
                            setSearchText(value);
                        }}
                    />
                </div>

                <div ref={filterRef}>
                    <Button
                        type="link"
                        onClick={() => setShowFilter(!showFilter)}
                    >
                        筛选 <DownOutlined />
                    </Button>

                    <div className="task-list__filter-panel">
                        <TaskFilter
                            onFilterChange={newFilters => {
                                setFilters(newFilters);
                                setShowFilter(false);
                            }}
                            onClear={() => {
                                setFilters({});
                                setShowFilter(false);
                            }}
                            showFilter={showFilter}
                        />
                    </div>
                </div>
            </div>

            <Tabs
                activeKey={currentTab}
                onChange={setCurrentTab}
                items={[
                    {
                        key: 'all',
                        label: '全部',
                    },
                    ...(statusTabs as any),
                ]}
                className="task-list__tabs"
            />

            <Button
                type="primary"
                icon={<PlusOutlined />}
                className="task-list__create-button"
                onClick={handleCreateTask}
                style={{ height: '40px' }}
            >
                <span className="ant-btn-icon">新建任务</span>
            </Button>

            <div className="task-list__content">
                {tasks.map(task => (
                    <div
                        key={task.id}
                        className={`task-card ${
                            task.taskStatus === 1
                                ? 'task-card--in-progress'
                                : ''
                        } ${
                            selectedTaskId === task.id
                                ? 'task-card--selected'
                                : ''
                        }`}
                        onClick={() => handleTaskSelect(task)}
                    >
                        <div className="task-card__header">
                            <div className="task-card__title">
                                {task.taskName}
                            </div>
                            <div onClick={() => gotToApproval(task)}>
                                <Tag color={getStatus(task.taskStatus)?.color}>
                                    {getStatus(task.taskStatus)?.label || '-'}
                                </Tag>
                            </div>
                        </div>

                        {/* 显示任务id */}
                        <div className="task-card__detail">
                            <div className="task-card__detail-label">
                                任务ID
                            </div>
                            <div className="task-card__detail-value">
                                {task.id}
                            </div>
                        </div>
                        <Progress
                            percent={
                                task.statisticDto?.taskProgress
                                    ? Math.round(
                                          (task.statisticDto.taskProgress
                                              .doneCount /
                                              (task.statisticDto.taskProgress
                                                  .waitingCount +
                                                  task.statisticDto.taskProgress
                                                      .doingCount +
                                                  task.statisticDto.taskProgress
                                                      .doneCount +
                                                  task.statisticDto.taskProgress
                                                      .cancelCount)) *
                                              100,
                                      )
                                    : 0
                            }
                            format={() => {
                                const total = task.statisticDto?.taskProgress
                                    ? task.statisticDto.taskProgress
                                          .waitingCount +
                                      task.statisticDto.taskProgress
                                          .doingCount +
                                      task.statisticDto.taskProgress.doneCount +
                                      task.statisticDto.taskProgress.cancelCount
                                    : 0;
                                const done =
                                    task.statisticDto?.taskProgress
                                        ?.doneCount || 0;
                                const isCompleted = total > 0 && done === total;
                                return `${done}/${total}${
                                    isCompleted ? ' ✅' : ''
                                }`;
                            }}
                            showInfo={true}
                            strokeWidth={12}
                            strokeColor={'#198CFF'}
                        />
                        {/* 增加触达方式字段 */}
                        <div className="task-card__detail">
                            <div className="task-card__detail-label">
                                触达方式
                            </div>
                            <div className="task-card__detail-value">
                                {reachMethodOptions.find(
                                    item =>
                                        item.value ===
                                        task.channelDto?.contactType,
                                )?.label || '-'}
                            </div>
                        </div>

                        <div className="task-card__detail">
                            <div className="task-card__detail-label">
                                任务类型
                            </div>
                            <div className="task-card__detail-value">
                                {task.taskType}
                            </div>
                        </div>

                        <div className="task-card__detail">
                            <div className="task-card__detail-label">
                                任务流程
                            </div>
                            <div className="task-card__detail-value">
                                <span>
                                    {reachTypeOptions.find(
                                        item => item.value === task.reachType,
                                    )?.label || '-'}
                                </span>
                                <span>
                                    {task.reachType ===
                                    ReachTypeEnum.SINGLE_COMMUNICATION
                                        ? `（${
                                              reachMethodOptions.find(
                                                  item =>
                                                      item.value ===
                                                      task?.channelDto
                                                          ?.contactType,
                                              )?.label || ''
                                          }）`
                                        : ''}
                                </span>
                                <span>
                                    {task.reachType ===
                                    ReachTypeEnum.INTELLIGENT_SCHEDULING
                                        ? `（${task.processName || ''}）`
                                        : ''}
                                </span>
                            </div>
                        </div>

                        <div className="task-card__detail">
                            <div className="task-card__detail-label">
                                开始时间
                            </div>
                            <div className="task-card__detail-value">
                                {task.taskWorkStrategy?.startTime
                                    ? dayjs(
                                          task.taskWorkStrategy?.startTime,
                                      ).format('YYYY-MM-DD HH:mm:ss')
                                    : '-'}
                            </div>
                        </div>

                        <div className="task-card__detail">
                            <div className="task-card__detail-label">
                                创建人
                            </div>
                            <div className="task-card__detail-value">
                                {task?.creator?.name || '-'}{' '}
                                {task?.creator?.mis
                                    ? `（${task?.creator?.mis}）`
                                    : ''}
                            </div>
                        </div>

                        <div className="task-card__detail">
                            <div className="task-card__detail-label">
                                创建时间
                            </div>
                            <div className="task-card__detail-value">
                                {dayjs(task.createTime).format(
                                    'YYYY-MM-DD HH:mm:ss',
                                )}
                            </div>
                        </div>
                    </div>
                ))}

                <div ref={loadMoreRef} className="task-list__load-more">
                    {loading && (
                        <div className="task-list__loading">加载中...</div>
                    )}
                    {!hasMore && (
                        <div className="task-list__no-more">没有更多数据了</div>
                    )}
                </div>
            </div>
            <TaskProgress
                visible={taskProgressVisible}
                onClose={() => setTaskProgressVisible(false)}
            />
        </div>
    );
};

export default TaskList;
