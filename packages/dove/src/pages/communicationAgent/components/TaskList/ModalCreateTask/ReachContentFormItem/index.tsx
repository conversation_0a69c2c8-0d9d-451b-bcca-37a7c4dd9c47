/**
 * 【关联agent】表单项
 *  多态组件，根据【触达类型】和【触达方式】的不同，展示不同的表单项
 */
import { useState, useEffect } from 'react';
import { Form, Input, Upload, UploadFile, message, Select, Space } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import SelectAgent from '@src/components/SelectAgent';
import ProcessSelect from '@src/components/ProcessSelect';
import {
    ReachTypeEnum,
    ReachMethodEnum,
    SCHEDULE_FLOW_SYSTEM_ID_101,
} from '@src/constants';
import { getPrefix } from '@src/module/request/getPrefix';

export const getReachContentLabelName = ({ reachType, contactType }) => {
    if (reachType === ReachTypeEnum.SINGLE_COMMUNICATION) {
        // 【触达类型】为"单次沟通"
        const nameMap = {
            [ReachMethodEnum.AI_CALL]: '关联agent', // 【触达方式】为"智能外呼"
            [ReachMethodEnum.IM_MASS_SEND]: '触达内容', // "IM群发"
            [ReachMethodEnum.TEXT_CALL]: '触达内容', // "文本外呼"
        };
        return nameMap[contactType];
    }
    if (reachType === ReachTypeEnum.INTELLIGENT_SCHEDULING) {
        // 【触达类型】为"智能调度"
        return '关联agent';
    }
};

const ReachContentFormItem = props => {
    const {
        form, // 表单实例
        reachType, // 【触达类型】实时数据
        contactType, // 【触达方式】实时数据
        contactTitle, // 【关联agent】标题实时数据
        contactContent, // 【关联agent】文本实时数据
        sceneId, // 【触达类型】为"智能调度"时，【关联agent】选择的场景id 实时数据
        setAgent = (_val: any) => {}, // 设置已选择的agent数据
    } = props;

    const actionUrl = `${getPrefix('/impc/task/upload')}/impc/task/upload`; // 【关联agent】图片上传地址
    const [fileList, setFileList] = useState<any[]>([]); // 【触达方式】是"IM群发"时，【关联agent】图片列表 实时数据
    const [sceneOptions, setSceneOptions] = useState<any[]>([]); // 【场景】可选项

    // 【触达类型】为“智能调度”时，需要获取【场景】可选项
    useEffect(() => {
        if (reachType === ReachTypeEnum.INTELLIGENT_SCHEDULING) {
            getSceneOptions();
        }
    }, [reachType]);

    // 获取【场景】可选项
    const getSceneOptions = async () => {
        try {
            const allUrl = '/xianfu/api-v2/dove/agent/query';
            const queryParams: any = {
                page: 1,
                pageSize: 1000,
                status: [1], // 只查询启用状态的 agent
                forUse: true,
            };

            const res: any = await apiCaller.post(
                // @ts-ignore
                allUrl,
                queryParams,
            );

            if (res.code === 0) {
                // 手动过滤，保留"启用"的场景
                const options = (res?.data?.data || []).map(item => {
                    return {
                        ...item,
                        label: item.name,
                        value: item.id,
                        title: item.description || item.name,
                    };
                });
                setSceneOptions(options);
            }
        } catch (error) {
            console.error('获取数据失败:', error);
        }
    };

    // 【触达内容】图片上传事件
    const handleUploadChange = ({
        fileList: newFileList,
    }: {
        fileList: UploadFile[];
    }) => {
        setFileList(newFileList);
    };

    // 渲染函数
    const render = () => {
        const labelName = getReachContentLabelName({ reachType, contactType });
        if (reachType === ReachTypeEnum.SINGLE_COMMUNICATION) {
            // 【触达类型】为"单次沟通"
            if (contactType === ReachMethodEnum.AI_CALL) {
                // 【触达方式】为"智能外呼"
                return (
                    <Form.Item
                        name="agentId"
                        label={labelName}
                        required
                        rules={[{ required: true, message: '请选择AI外呼' }]}
                    >
                        <SelectAgent onDataChange={setAgent} />
                    </Form.Item>
                );
            }
            if (contactType === ReachMethodEnum.IM_MASS_SEND) {
                // 【触达方式】为"IM群发"
                return (
                    <>
                        <Form.Item
                            name="contactTitle"
                            label={labelName}
                            required
                            rules={[
                                { required: true, message: '请输入关联agent' },
                            ]}
                        >
                            <Input
                                placeholder="请输入标题"
                                maxLength={15}
                                className="im-title"
                            />
                        </Form.Item>
                        <div className="im-count">
                            {contactTitle?.length || 0}
                            /15
                        </div>
                        <Form.Item
                            name="contactContent"
                            label="&nbsp;"
                            colon={false}
                        >
                            <Input.TextArea
                                placeholder="请输入内容"
                                maxLength={300}
                                rows={4}
                                className="im-textarea"
                            />
                        </Form.Item>
                        <div className="im-count">
                            {contactContent?.length || 0}/300
                        </div>
                        <Form.Item name="fileList" label="&nbsp;" colon={false}>
                            <Form.Item
                                name="images"
                                label="&nbsp;"
                                colon={false}
                                className="no-label"
                            >
                                <Upload
                                    action={actionUrl}
                                    listType="picture-card"
                                    onChange={handleUploadChange}
                                    beforeUpload={file => {
                                        const isImage =
                                            file.type.startsWith('image/');
                                        if (!isImage) {
                                            message.error('只能上传图片文件!');
                                        }
                                        const isLt5M =
                                            file.size / 1024 / 1024 < 5;
                                        if (!isLt5M) {
                                            message.error('图片必须小于5MB!');
                                        }
                                        return isImage && isLt5M;
                                    }}
                                    accept="image/*"
                                >
                                    {fileList.length >= 8 ? null : (
                                        <div>
                                            <PlusOutlined />
                                            <div style={{ marginTop: 8 }}>
                                                上传图片
                                            </div>
                                        </div>
                                    )}
                                </Upload>
                            </Form.Item>
                        </Form.Item>
                    </>
                );
            }
            if (contactType === ReachMethodEnum.TEXT_CALL) {
                // 【触达方式】为"文本外呼"
                return (
                    <>
                        <Form.Item
                            name="contactContent"
                            label={labelName}
                            required
                            rules={[
                                { required: true, message: '请输入关联agent' },
                            ]}
                        >
                            <Input.TextArea
                                placeholder="请输入"
                                maxLength={500}
                                rows={4}
                            />
                        </Form.Item>
                        <div className="im-count">
                            {contactContent?.length || 0}/500
                        </div>
                    </>
                );
            }
        }
        if (reachType === ReachTypeEnum.INTELLIGENT_SCHEDULING) {
            // 【触达类型】为"智能调度"
            return (
                <>
                    <Form.Item
                        name="sceneId"
                        label={labelName}
                        required
                        rules={[{ required: true, message: '请选择场景' }]}
                    >
                        <Select
                            allowClear
                            options={sceneOptions}
                            showSearch
                            filterOption={(input, option) => {
                                const query = String(input).toLowerCase();
                                const label = String(
                                    option?.label || '',
                                ).toLowerCase();
                                return label.includes(query);
                            }}
                            onChange={() => {
                                // 数据联动，清空【调度流程】已选值
                                form.setFieldValue('processId', null);
                            }}
                            placeholder="请选择场景"
                        />
                    </Form.Item>
                    <Form.Item
                        name="processId"
                        label="调度流程"
                        required
                        rules={[{ required: true, message: '请选择调度流程' }]}
                    >
                        <ProcessSelect
                            params={{
                                agentId: sceneId,
                                systemId: SCHEDULE_FLOW_SYSTEM_ID_101, // 标记发起方为"任务列表"
                                pageSize: 10000,
                                pageNum: 1,
                            }}
                        />
                    </Form.Item>
                </>
            );
        }
    };

    return render();
};

export default ReachContentFormItem;
