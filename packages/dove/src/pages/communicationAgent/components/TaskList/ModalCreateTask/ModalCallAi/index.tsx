import { Form, Input, App } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';

interface ModalCallAiProps {
    parameters: { name: string; showName: string }[];
    onSuccess?: () => void;
    agentId: number;
}

interface FormValues {
    tel: string;
    agentId: number;
    [key: string]: any;
}

const useModalCallAi = () => {
    const { modal } = App.useApp();
    const [form] = Form.useForm<FormValues>();

    const openModal = ({
        parameters,
        onSuccess,
        agentId,
    }: ModalCallAiProps) => {
        form.resetFields();
        modal.confirm({
            icon: null,
            title: 'AI 外呼',
            width: 500,
            content: (
                <Form form={form} layout="vertical">
                    <Form.Item
                        label="电话号码"
                        name="tel"
                        rules={[{ required: true, message: '请输入电话号码' }]}
                    >
                        <Input placeholder="请输入电话号码" />
                    </Form.Item>
                    {parameters.map(param => (
                        <Form.Item
                            key={param.name}
                            label={param.showName}
                            name={param.name}
                            rules={[
                                {
                                    required: true,
                                    message: `请输入${param.showName}`,
                                },
                            ]}
                        >
                            <Input placeholder={`请输入${param.showName}`} />
                        </Form.Item>
                    ))}
                </Form>
            ),
            onOk: async () => {
                try {
                    const values = await form.validateFields();
                    const res = await apiCaller.post(
                        '/xianfu/api-v2/dove/call/ai/try',
                        {
                            tel: values.tel,
                            // @ts-ignore
                            agentId,
                            placeholder: parameters.reduce(
                                (acc, param) => ({
                                    ...acc,
                                    [param.name]: values[param.name],
                                }),
                                {},
                            ),
                        },
                    );

                    if (res.code === 0) {
                        onSuccess?.();
                        return Promise.resolve();
                    }
                    return Promise.reject(new Error(res.message || '请求失败'));
                } catch (error) {
                    console.error('AI 外呼失败:', error);
                    return Promise.reject(error);
                }
            },
        });
    };

    return { openModal };
};

export default useModalCallAi;
