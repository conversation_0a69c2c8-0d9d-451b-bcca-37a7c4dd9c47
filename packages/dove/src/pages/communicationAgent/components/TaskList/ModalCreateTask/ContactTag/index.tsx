/**
 * 【选择商家】类型为"筛选"时，商家tag选择器
 */
import './index.scss';

interface ContactTagProps {
    value?: number[];
    onChange?: (value: number[]) => void;
    options?: Array<{
        label: string;
        value: any;
    }>;
}

const ContactTag = (props: ContactTagProps) => {
    const { value, onChange, options = [] } = props;

    return (
        <div className="biz-type-list">
            {options.map(option => (
                <div
                    key={option.value}
                    className={`biz-type-item ${
                        value?.includes(option?.value) ? 'selected' : ''
                    }`}
                    style={
                        value?.includes(option?.value)
                            ? {
                                  background: '#FFF6F0',
                                  color: '#FF6A00',
                              }
                            : {
                                  background: '#F5F6FA',
                                  color: '#222',
                              }
                    }
                    onClick={() => {
                        onChange?.(
                            value?.includes(option?.value)
                                ? value?.filter(v => v !== option?.value)
                                : [option?.value],
                        );
                    }}
                >
                    {option.label}
                </div>
            ))}
        </div>
    );
};

export default ContactTag;
