.create-task-form {
    padding: 16px 0;

    .ant-form-item {
        margin-bottom: 24px;

        .ant-form-item-label {
            text-align: right;
            padding-right: 12px;
        }
    }

    .ant-form-item-label {
        >label {
            color: #333;
            font-size: 14px;
            font-weight: 500;
        }
    }

    .ant-input,
    .ant-select-selector,
    .ant-picker {
        border-radius: 4px;

        // &:hover,
        // &:focus {
        //     border-color: #198cff;
        // }
    }

    .ant-input-textarea {
        textarea {
            resize: none;
        }
    }
}

.ant-modal-content {
    .ant-modal-header {
        margin-bottom: 8px;

        .ant-modal-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }
    }

    .ant-modal-footer {
        padding: 16px 24px;
        border-top: 1px solid #f0f0f0;

        .ant-btn {
            height: 36px;
            padding: 0 24px;
            border-radius: 4px;
            font-size: 14px;

            &.ant-btn-primary {
                background-color: #198cff;

                &:hover,
                &:focus {
                    background-color: #40a1ff;
                }

                &:active {
                    background-color: #1472cc;
                }
            }
        }
    }
}

.no-label {
    .ant-form-item-label {
        display: none;
    }
}

.im-count {
    font-size: 12px;
    color: #999;
    text-align: right;
    margin-top: -24px;
    position: relative;
    top: -24px;
    right: 10px;
    z-index: 0;
}