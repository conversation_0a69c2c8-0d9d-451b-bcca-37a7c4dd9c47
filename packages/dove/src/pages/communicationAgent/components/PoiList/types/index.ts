// 基础分页请求接口
export interface PageRequest {
    page: number;
    pageSize: number;
}

// 基础分页响应接口
export interface PageResponse<T> {
    data: T[];
    total: number;
    page: number;
    pageSize: number;
}

// 子任务筛选条件
export interface SubtaskFilter {
    taskId?: string;
    taskName?: string;
    creatorMis?: string[];
    agentIdList?: string[];
    agentName?: string;
    contactType?: string;
    subTaskStatus?: string[];
    bizId?: string[];
    subTaskStartTime?: [any, any]; // dayjs对象
    createTime?: [any, any]; // dayjs对象
    createTimeMax?: number;
    createTimeMin?: number;
    subTaskStartTimeMax?: number;
    subTaskStartTimeMin?: number;
}

// 商家分页查询请求
export interface PoiPageQueryRequest extends PageRequest {
    // 基础字段
    poiId?: string;
    poiName?: string;
    assigneeList?: string[];
    createTimeMin?: number;
    createTimeMax?: number;

    // 展开字段
    poiType?: string;
    cityId?: string;
    subTaskStartTimeMin?: number;
    subTaskStartTimeMax?: number;

    // 任务相关字段
    taskId?: string;
    taskName?: string;
    creatorMis?: string[];

    // Agent相关字段
    agentIdList?: string[];
    agentName?: string;

    // 业务相关字段
    contactType?: string;
    subTaskStatus?: string[];
    bizId?: string[];
}

// 商家基础信息
export interface PoiDto {
    poiId: string;
    poiName: string;
    address?: string;
    phone?: string;
    businessHours?: string;
    category?: string;
    rating?: number;
    imageUrl?: string;
    status?: string;
    createTime?: number;
    updateTime?: number;
}

// 子任务信息
export interface SubtaskDto {
    subtaskId: string;
    taskType: string;
    status: string;
    title: string;
    description?: string;
    createTime: number;
    updateTime?: number;
    agentTaskId?: string;
    priority?: 'HIGH' | 'MEDIUM' | 'LOW';
    estimatedDuration?: number;
    actualDuration?: number;
    assignee?: string;
    tags?: string[];
}

// 子任务分页查询请求
export interface SubtaskPageQueryRequest extends PageRequest {
    poiId: string;
    status?: string[];
    taskType?: string[];
    createTimeMin?: number;
    createTimeMax?: number;
    agentTaskId?: string;
}

// 统计数据
export interface SubtaskStatisticsDTO {
    totalCount: number;
    pendingCount: number;
    inProgressCount: number;
    completedCount: number;
    failedCount: number;
    taskTypeStats: {
        [taskType: string]: {
            total: number;
            pending: number;
            inProgress: number;
            completed: number;
            failed: number;
        };
    };
}

// 筛选表单数据
export interface FilterFormData {
    // 基础字段
    poiId?: string;
    poiName?: string;
    assigneeList?: string[];
    creatorMis?: string[];
    subtaskFilter: SubtaskFilter;
    poiType?: string;
    cityId?: string;
}

// 组件Props类型
export interface PoiFilterProps {
    initialValues?: Partial<PoiPageQueryRequest>;
    onChange: (filters: Partial<PoiPageQueryRequest>) => void;
}

export interface PoiStatisticsProps {
    data?: SubtaskStatisticsDTO;
    loading?: boolean;
}

export interface PoiCardListProps {
    data?: PoiDto[];
    loading?: boolean;
    error?: Error;
    onReload?: () => void;
}

export interface PoiCardProps {
    poi: PoiDto;
}

export interface SubTaskListProps {
    poiId: string;
    visible?: boolean;
}

export interface SubTaskProps {
    subtask: SubtaskDto;
}

// API响应类型
export interface ApiResponse<T> {
    code: number;
    message: string;
    data: T;
    success: boolean;
}

// 任务状态枚举
export enum TaskStatus {
    PENDING = 'PENDING',
    IN_PROGRESS = 'IN_PROGRESS',
    COMPLETED = 'COMPLETED',
    FAILED = 'FAILED',
    CANCELLED = 'CANCELLED',
}

// 任务类型枚举
export enum TaskType {
    DATA_COLLECTION = 'DATA_COLLECTION',
    QUALITY_CHECK = 'QUALITY_CHECK',
    CONTENT_REVIEW = 'CONTENT_REVIEW',
    PHONE_VERIFICATION = 'PHONE_VERIFICATION',
    ADDRESS_VERIFICATION = 'ADDRESS_VERIFICATION',
}
