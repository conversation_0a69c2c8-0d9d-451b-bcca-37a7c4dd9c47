# PRD: AI 协同工作台 - 商家列表视图

## 1. 概述

### 1.1. 背景

当前的 AI 协同工作台以“任务”为核心，在处理多任务交叉触达同一商家的场景时，运营和销售人员难以形成对单个商家的完整认知。为了提升效率，我们需要引入一个以“商家”为核心的视图。

### 1.2. 用户故事

- **作为一名BD/电销/运营人员**，我希望能在一个页面上看到某个商家的所有相关任务和历史沟通记录，以便我能全面了解情况，做出更精准的跟进决策。
- **作为一名团队管理者**，我希望能通过多维度筛选和聚合，快速掌握我团队名下商家的整体任务进展和状态，以便进行有效管理和资源调配。

### 1.3. 功能目标

- **商家聚合视图**: 将原本分散在各个任务中的信息，按商家维度进行聚合展示。
- **高效筛选与定位**: 提供丰富的筛选条件，帮助用户快速定位目标商家。
- **关键指标可视化**: 直观展示核心任务指标，帮助用户快速了解整体状况。
- **清晰的任务详情**: 在商家视图下，清晰地展示每个关联任务的状态、过程和结果。

## 2. 页面功能规格

### 2.1. 页面入口与结构

- **入口**: 在 AI 协同工作台的顶部导航栏，增加“商家列表”Tab，与现有的“任务列表”并列。
- **页面结构**:
    1.  **筛选区 (Filter Section)**: 页面顶部，用于输入和选择过滤条件。
    2.  **统计区 (Statistics Section)**: 筛选区下方，展示关键业务指标的统计卡片。
    3.  **列表区 (List Section)**: 页面主体，展示经过筛选和分页的商家卡片列表。
    4.  **分页区 (Pagination Section)**: 列表区底部，用于列表的翻页导航。

### 2.2. 筛选区 (Filter Section)

使用 Ant Design 的 `Form` 组件构建。所有筛选条件应作为 `PoiPageQueryRequest` 接口的参数。

**默认显示字段**:

| 字段名 | 控件 | `name` 属性 | 描述 |
| :--- | :--- | :--- | :--- |
| 商家ID | `Input` | `poiId` | 精确匹配。 |
| 商家名称 | `Input` | `poiName` | 模糊匹配。 |
| 门店责任人 | `MisSelector` | `assigneeList` | 搜索并选择责任人/认领人。 |
| 任务创建时间 | `DatePicker.RangePicker` | `createTimeMin`, `createTimeMax` | 默认最近一个月。 |
| 任务ID | `Input` | `subtaskFilter.taskId` | 精确匹配。 |
| 任务名称 | `Input` | `subtaskFilter.taskName` | 模糊匹配。 |
| 任务创建人 | `MisSelector` | `subtaskFilter.creatorMis` | 搜索并选择创建人。 |

**“展开”后显示更多字段**:

| 字段名 | 控件 | `name` 属性 | 描述 |
| :--- | :--- | :--- | :--- |
| 商家类型 | `Select` | `poiType` | 选项：外卖门店、公海门店。 |
| 所属城市 | `Select` | `cityId` | 搜索并选择城市。 |
| AgentID | `SelectAgent` | `subtaskFilter.agentIdList` | 支持多选。 |
| Agent名称 | `Input` | `subtaskFilter.agentName` | 模糊匹配。 |
| 触达类型 | `Select` | `subtaskFilter.contactType` | 选项：AI外呼、普通外呼、智能调度。 |
| 任务状态 | `Select` | `subtaskFilter.subTaskStatus` | 选项：待执行、执行中、已完成、已取消。 |
| 触达时间范围 | `DatePicker.RangePicker` | `subTaskStartTimeMin`, `subTaskStartTimeMax` | |
| 业务线/租户 | `TeamSelect` | `subtaskFilter.bizId` | |

**操作按钮**:

- **查询**: `type="primary"`，点击后触发表单 `onFinish` 事件，调用接口刷新列表。
- **重置**: 点击后重置表单所有字段，并重新调用接口。
- **展开/收起**: 控制“更多字段”的显示与隐藏。

### 2.3. 统计区 (Statistics Section)

根据当前筛选条件，调用 `/api/tasks/stats` 接口获取数据并展示。

| 卡片名称 | 数据来源 |
| :--- | :--- |
| 任务总数 | `totalTaskCount` |
| AI任务 | `aiTask.inProgressCount` / `aiTask.completedCount` |
| 电销任务 | `telemarketingTask.inProgressCount` / `telemarketingTask.completedCount` |
| BD任务 | `bdTask.count` |

### 2.4. 列表区 (List Section)

#### 2.4.1. 商家卡片 (`PoiDto`)

列表中的每一项都是一个商家卡片，展示该商家的核心信息。

- **Header**:
    - `logoUrl`: 商家头像。
    - `name`: 商家名称。
    - `id`: 商家ID。
- **Tags**:
    - `category`: 品类。
    - `type`: 合作状态 (已合作/未合作)。
    - `operatingStatus` / `claimStatus`: 营业/认领状态。
- **Body**:
    - `city`, `aor`: 城市与蜂窝。
    - `address`: 详细地址。
    - `assignee` / `claimedBy`: 责任人/认领人。

#### 2.4.2. 商家子任务列表（/xianfu/api-v2/dove/data/poi/subtask）

每个商家卡片下方，是与该商家关联的任务列表，一次最多展示三个，如果接口返回total超过三个，展示一个按钮【查看更多】，这个按钮是一个分页按钮，相当于pageSize为3，点击一次pageNum+1。

- **任务标题**: `taskName` 和 `taskId`。
- **任务状态**: `subTaskStatus`，以标签形式展示。
- **元数据**: 创建人、创建时间、任务类型、关联Agent等。
- **结果**:
    - **失败**: 显示失败原因。
    - **成功**: 显示沟通时长、意向等。
    - **AI总结**: 若有，则显示。
- **操作**:
    - `查看沟通记录`: 打开新弹窗或页面，展示详细记录。
    - `AI跟进进度`: 查看AI任务的详细进展。
    - `接管`: 人工接管。

### 2.5. 分页区 (Pagination Section)

使用 Ant Design 的 `Pagination` 组件。

- `current`: 对应请求参数的 `page`。
- `pageSize`: 对应请求参数的 `pageSize`。
- `total`: 对应响应数据中的 `total`。
- `onChange`: 切换页码或 `pageSize` 时，更新请求参数并重新调用接口。

## 3. 交互逻辑

- **WHEN** 用户在筛选区修改了任何字段 **THEN** 点击“查询”按钮或按回车键，将触发表单提交，并使用表单值作为参数调用商家列表接口。
- **WHEN** 接口成功返回数据 **THEN** 更新列表区和分页区的数据；**IF** 列表数据为空，**THEN** 列表区应显示 Ant Design 的 `Empty` 组件，提示“暂无数据”。
- **WHEN** 接口请求失败 **THEN** 列表区应显示加载失败的状态，并提供“重新加载”的按钮。
- **WHEN** 用户点击商家卡片 **THEN** 展开或收起该商家下的任务摘要列表。
- **WHEN** 用户点击“查看沟通记录” **THEN** 携带 `taskId` 调用沟通记录接口，并在模态框中展示详情。

## 4. 接口定义

### 4.1. 商家列表查询 (`/xianfu/api-v2/dove/data/poi/search`)

- **Method**: `POST`
- **Request Body**: `PoiPageQueryRequest`

```typescript
// 请求体
export interface PoiPageQueryRequest {
    page?: number;
    pageSize?: number;
    poiId?: string;
    cityId?: number;
    poiName?: string;
    poiType?: number;
    assigneeList?: string[];
    sortList?: SortReq[];
    subtaskFilter?: SubtaskFilterRequest;
}

export interface SubtaskFilterRequest {
    bizId?: number;
    taskId?: string;
    taskName?: string;
    agentName?: string;
    creatorMis?: string;
    agentIdList?: string[];
    contactType?: number;
    createTimeMax?: number;
    createTimeMin?: number;
    subTaskStatus?: number;
    subTaskStartTimeMax?: number;
    subTaskStartTimeMin?: number;
}
```

- **Response Body**:

```typescript
// 响应体
export interface PageResponse<T> {
    page: number;
    pageSize: number;
    total: number;
    data: T[];
}

export interface PoiDto {
    id?: string;
    aor?: string;
    city?: string;
    name?: string;
    type?: string;
    address?: string;
    logoUrl?: string;
    assignee?: string;
    category?: string;
    claimedBy?: string;
    claimStatus?: string;
    listingStatus?: string;
    operatingStatus?: string;
}
```

### 4.2. 任务统计 (`/xianfu/api-v2/dove/data/poi/subtask/statistics`)

- **Method**: `POST`
- **Query Params**: 与 `PoiPageQueryRequest` 相同。
- **Response Body**: 
export interface SubtaskStatisticsResponse { 
    /** 响应信息 */ 
    msg?: string; 
    /** 响应码 */ 
    code?: number; 
    /** 响应数据 */ 
    data?: SubtaskStatisticsDTO; 
}

export interface SubtaskStatisticsDTO { 
    /** 任务总数 */ 
    totalTaskCount?: number; 
    /** AI任务统计 */ 
    aiTask?: number; 
    /** 电销任务统计 */ 
    telemarketingTask?: number; 
    /** BD任务统计 */ 
    bdTask?: number;
}

### 4.3. 商家子任务列表 (`/xianfu/api-v2/dove/data/poi/subtask`)

- **Method**: `POST`
请求参数：
/** 子任务筛选请求 */
export interface SubtaskFilterRequest { 
    /** 业务线 ID, 必填 */
    bizId?: number; 
    /** 任务 ID，非必填，精确搜索 */
    taskId?: string; 
    /** 任务名称，非必填，模糊搜索 */
    taskName?: string; 
    /** agent 名称，非必填，模糊搜索 */
    agentName?: string; 
    /** 创建人 mis */
    creatorMis?: string; 
    /** agentId，非必填，支持多个 agent 一起搜索 */
    agentIdList?: string[]; 
    /** 触达类型，非必填 */
    contactType?: number; 
    /** 创建时间最大值 */
    createTimeMax?: number; 
    /** 创建时间最小值 */
    createTimeMin?: number; 
    /** 子任务状态，非必填 */
    subTaskStatus?: number; 
    /** 子任务触达时间最大值 */
    subTaskStartTimeMax?: number; 
    /** 子任务触达时间最小值 */
    subTaskStartTimeMin?: number; 
}
响应参数：

/** 分页信息 */
export interface PageDto_SubTaskDTO { 
    /** 数据 */
    data?: SubTaskDTO[]; 
    /** 页码 */
    page?: number; 
    /** 总数 */
    total?: number; 
    /** 每页数据 */
    pageSize?: number;
}

/** 子任务信息 */
export interface SubTaskDTO { 
    /** 子任务 ID */
    id?: string; 
    /** 被叫号码 */
    tel?: string; 
    /** 沟通意向 */
    rank?: string; 
    /** 业务 ID */
    bizId?: number; 
    /** 任务执行状态 */
    status?: string; 
    /** 主任务 ID */
    taskId?: string; 
    /** 关联 agent ID */
    agentId?: string; 
    /** 业务名称 */
    bizName?: string; 
    /** 创建人 */
    creator?: string; 
    /** 沟通总结 */
    summary?: string; 
    /** 沟通时长 */
    duration?: number; 
    /** 任务名称 */
    taskName?: string; 
    /** 关联 agent 名称 */
    agentName?: string; 
    /** 跟进人 mis */
    followMis?: string; 
    /** 跟进人 uid */
    followUid?: string; 
    /** 调度流程 ID */
    processId?: string; 
    /** 触达时间 */
    reachTime?: number; 
    /** 创建时间 */
    createTime?: number; 
    /** 任务流程类型 */
    contactType?: string; 
    /** 调度流程名称 */
    processName?: string; 
    /** 触达结果 */
    reachStatus?: string; 
    /** 失败原因 */
    failureReason?: string;
}

- **Response Body**: 包含详细沟通记录的对象。

## 5. 验收标准 (Acceptance Criteria)

- [ ] 页面布局与 UI 设计图完全一致。
- [ ] 所有筛选条件均可按预期工作，并能正确反映在接口请求参数中。
- [ ] “查询”和“重置”功能符合预期。
- [ ] 统计卡片的数据能根据筛选条件正确更新。
- [ ] 商家列表能正确分页，并能根据筛选条件展示正确的数据。
- [ ] 商家卡片和任务摘要的数据字段与接口返回一致。
- [ ] “查看沟通记录”等操作能正常工作。
- [ ] 页面在加载、空状态、错误状态下有明确的视觉反馈。