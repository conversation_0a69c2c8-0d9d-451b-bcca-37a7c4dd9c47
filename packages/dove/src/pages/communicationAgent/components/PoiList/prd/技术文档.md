# 技术实现文档：商家列表页面 (PoiList Page)

## 1. 总体组件架构

本页面采用组件化开发模式，结构如下：

```
PoiList (Page)
├── PoiFilter (Component)      // 筛选表单
├── PoiStatistics (Component)  // 任务统计卡片
└── PoiCardList (Component)      // 商家卡片列表
    ├── PoiCard (Component)    // 单个商家卡片
    │   └── SubTaskList (Component) // 子任务列表
    │       └── SubTask (Component) // 单个子任务
    └── Pagination (Antd)      // 分页
```

## 2. 状态管理与数据流

- **核心状态管理**: 在顶层组件 `PoiList` 中，使用 React Hooks (`useState`, `useReducer`) 管理核心状态。
- **数据流**: 单向数据流。`PoiList` 组件负责获取所有数据，并通过 `props` 将数据传递给子组件。
- **API请求**: 使用统一的请求库 apiCaller 进行 API 调用，并在 `PoiList` 组件中处理加载（loading）和错误（error）状态。请求可以使用ahooks当中useRequest，便于管理loading。

## 3. 组件详细规格

### 3.1. `PoiList` (Page Component)

**职责**: 页面主容器，负责状态管理、API 调用和将数据分发给子组件。

**State**:

```typescript
const [filters, setFilters] = useState<PoiPageQueryRequest>({ /* 默认筛选条件 */ });
const [pagination, setPagination] = useState({ page: 1, pageSize: 10 });
const [poiData, setPoiData] = useState<PageResponse<PoiDto> | null>(null);
const [statsData, setStatsData] = useState<SubtaskStatisticsDTO | null>(null);
const [isLoading, setIsLoading] = useState<boolean>(false);
const [error, setError] = useState<any>(null);
```

**Effects**:

- `useEffect(() => { fetchData(); }, [filters, pagination]);`
    - 当 `filters` 或 `pagination` 变化时，触发 `fetchData` 函数。

**Functions**:

- `fetchData()`: 异步函数
    1.  `setIsLoading(true); setError(null);`
    2.  并行调用商家列表接口 (`/data/poi/search`) 和统计接口 (`/data/poi/subtask/statistics`)。
    3.  **On Success**: `setPoiData(response.data); setStatsData(statsResponse.data);`
    4.  **On Error**: `setError(error);`
    5.  **Finally**: `setIsLoading(false);`
- `handleFilterChange(newFilters)`:
    1.  `setFilters(prev => ({ ...prev, ...newFilters }));`
    2.  `setPagination(p => ({ ...p, page: 1 }));` // 回到第一页
- `handlePaginationChange(page, pageSize)`:
    1.  `setPagination({ page, pageSize });`

**Render Logic**:

```jsx
<PoiFilter onChange={handleFilterChange} />
<PoiStatistics data={statsData} />
<PoiCardList 
    data={poiData?.data} 
    loading={isLoading} 
    error={error} 
/>
<Pagination 
    current={pagination.page}
    pageSize={pagination.pageSize}
    total={poiData?.total}
    onChange={handlePaginationChange}
/>
```

### 3.2. `PoiFilter` (Component)

**职责**: 渲染筛选表单，并将表单的变更通知父组件。

**Props**:

```typescript
interface PoiFilterProps {
    onChange: (filters: Partial<PoiPageQueryRequest>) => void;
}
```

**Implementation**:

- 使用 Antd `Form` 组件，`name` 属性严格对应接口字段。
- 使用 `useState` 管理“展开/收起”状态。
- **On Finish**: 调用 `props.onChange(formValues)`。
- **On Reset**: 重置表单并调用 `props.onChange(initialValues)`。

### 3.3. `PoiStatistics` (Component)

**职责**: 展示任务统计数据。

**Props**:

```typescript
interface PoiStatisticsProps {
    data: SubtaskStatisticsDTO | null;
}
```

**Implementation**: 纯展示组件，根据 `props.data` 渲染各个统计卡片。

### 3.4. `PoiCardList` & `PoiCard` (Components)

**职责**: 渲染商家列表。`PoiCard` 负责单个商家的展示。

**Props (`PoiCardList`)**:

```typescript
interface PoiCardListProps {
    data: PoiDto[] | undefined;
    loading: boolean;
    error: any;
}
```

**Implementation (`PoiCardList`)**:

- **If `loading`**: 显示 `Spin` 组件。
- **If `error`**: 显示错误信息和重试按钮。
- **If `!data || data.length === 0`**: 显示 `Empty` 组件。
- **Else**: `data.map(poi => <PoiCard key={poi.id} poi={poi} />)`

**Props (`PoiCard`)**:

```typescript
interface PoiCardProps {
    poi: PoiDto;
}
```

**Implementation (`PoiCard`)**:

- 渲染商家信息（名称、ID、地址等）。
- 包含 `SubTaskList` 组件，并传入 `poi.id`。

### 3.5. `SubTaskList` & `SubTask` (Components)

**职责**: 获取并展示某个商家下的子任务列表。

**Props (`SubTaskList`)**:

```typescript
interface SubTaskListProps {
    poiId: string;
}
```

**State (`SubTaskList`)**:

- `useState` 管理子任务列表数据、分页 (`page`, `pageSize=3`)、加载和错误状态。

**Effects (`SubTaskList`)**:

- `useEffect(() => { fetchSubTasks(); }, [page]);`
    - 调用 `/data/poi/subtask` 接口获取数据。

**Functions (`SubTaskList`)**:

- `handleViewMore()`: `setPage(prev => prev + 1)`。

**Implementation (`SubTaskList`)**:

- 渲染 `SubTask` 组件列表。
- 如果 `total > data.length`，显示“查看更多”按钮，点击时调用 `handleViewMore`。

## 4. API 接口伪代码

```typescript
// api.ts
import { apiCaller } from '@mfe/cc-api-caller-pc';

// 商家列表搜索
async function searchPoi(params: PoiPageQueryRequest): Promise<PageResponse<PoiDto>> {
    return apiCaller.post('/xianfu/api-v2/dove/data/poi/search', params);
}

// 任务统计
async function getSubtaskStatistics(params: PoiPageQueryRequest): Promise<SubtaskStatisticsResponse> {
    return apiCaller.post('/xianfu/api-v2/dove/data/poi/subtask/statistics', params);
}

// 商家子任务列表
async function getSubtasks(params: SubtaskFilterRequest): Promise<PageDto_SubTaskDTO> {
    return apiCaller.post('/xianfu/api-v2/dove/data/poi/subtask', params);
}
```
