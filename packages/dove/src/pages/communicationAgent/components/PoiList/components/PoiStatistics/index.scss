.poi-statistics {
    &__loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    &__main {
        margin-bottom: 16px;

        .ant-col {
            padding: 0 8px;

            &:first-child {
                padding-left: 0;
            }

            &:last-child {
                padding-right: 0;
            }
        }
    }

    &__card {
        text-align: center;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: none;
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        .ant-card-body {
            padding: 20px 16px;
        }

        .ant-statistic-title {
            font-size: 14px;
            color: #8c8c8c;
            margin-bottom: 12px;
            font-weight: 500;
        }

        .ant-statistic-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;

            .ant-statistic-content-value {
                font-size: 28px;
                font-weight: 600;
                line-height: 1.2;
            }

            .ant-statistic-content-suffix {
                font-size: 16px;
                font-weight: 400;
                margin-left: 4px;
            }
        }
    }

    &__task-types {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        border: none;

        .ant-card-head {
            border-bottom: 1px solid #f0f0f0;

            .ant-card-head-title {
                font-size: 16px;
                font-weight: 600;
                color: #262626;
            }
        }

        .ant-card-body {
            padding: 20px;
        }
    }

    &__task-type-card {
        height: 140px;
        border-radius: 6px;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);
        border: 1px solid #f0f0f0;
        transition: all 0.3s ease;

        &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
            border-color: #d9d9d9;
        }

        .ant-card-body {
            padding: 16px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .task-type-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;

            .task-type-name {
                font-weight: 600;
                color: #262626;
                font-size: 14px;
            }

            .task-type-total {
                font-size: 12px;
                color: #8c8c8c;
                background-color: #f5f5f5;
                padding: 2px 8px;
                border-radius: 10px;
            }
        }

        .task-type-details {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .task-type-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 13px;
            line-height: 1.4;

            .label {
                color: #595959;
                font-weight: 400;
            }

            .value {
                font-weight: 600;
                min-width: 20px;
                text-align: right;

                &.pending {
                    color: #faad14;
                }

                &.in-progress {
                    color: #1890ff;
                }

                &.completed {
                    color: #52c41a;
                }

                &.failed {
                    color: #ff4d4f;
                }
            }
        }
    }
}