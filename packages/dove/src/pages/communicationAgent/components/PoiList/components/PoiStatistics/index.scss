.poi-statistics {
    &__loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
    }

    &__main {
        margin-bottom: 16px;
    }

    &__card {
        text-align: center;
        
        .ant-statistic-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .ant-statistic-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
    }

    &__task-types {
        .ant-card-head-title {
            font-size: 16px;
            font-weight: 600;
        }
    }

    &__task-type-card {
        height: 120px;
        
        .ant-card-body {
            padding: 12px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .task-type-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;

            .task-type-name {
                font-weight: 600;
                color: #333;
            }

            .task-type-total {
                font-size: 12px;
                color: #666;
            }
        }

        .task-type-details {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .task-type-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;

            .label {
                color: #666;
            }

            .value {
                font-weight: 500;

                &.pending {
                    color: #faad14;
                }

                &.in-progress {
                    color: #1890ff;
                }

                &.completed {
                    color: #52c41a;
                }

                &.failed {
                    color: #ff4d4f;
                }
            }
        }
    }
}
