import React from 'react';
import { Card, Row, Col, Statistic, Spin, Empty, Button } from 'antd';
import {
    ClockCircleOutlined,
    SyncOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    FileTextOutlined,
    PlusOutlined,
} from '@ant-design/icons';
import type { PoiStatisticsProps } from '../../types';
import './index.scss';

const PoiStatistics: React.FC<PoiStatisticsProps> = ({ data, loading }) => {
    if (loading) {
        return (
            <Card className="poi-statistics">
                <div className="poi-statistics__loading">
                    <Spin size="large" />
                </div>
            </Card>
        );
    }

    if (!data) {
        return (
            <Card className="poi-statistics">
                <Empty
                    description="暂无统计数据"
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                />
            </Card>
        );
    }

    // 统计卡片配置
    const statisticsCards = [
        {
            title: '任务总数',
            value: data.totalCount || 459,
            icon: <FileTextOutlined />,
            color: '#262626',
            suffix: '',
        },
        {
            title: 'AI任务',
            value: data.aiTaskCount || 258,
            icon: <SyncOutlined />,
            color: '#1890ff',
            suffix: '',
            progress: {
                doing: data.aiDoingCount || 38,
                done: data.aiDoneCount || 220,
            },
        },
        {
            title: '电销任务',
            value: data.telesaleTaskCount || 258,
            icon: <CheckCircleOutlined />,
            color: '#52c41a',
            suffix: '',
            progress: {
                doing: data.telesaleDoingCount || 38,
                done: data.telesaleDoneCount || 220,
            },
        },
        {
            title: 'BD任务',
            value: data.bdTaskCount || 43,
            icon: <CloseCircleOutlined />,
            color: '#faad14',
            suffix: '',
        },
    ];

    // 计算完成率
    const completionRate =
        data.totalCount > 0
            ? ((data.completedCount / data.totalCount) * 100).toFixed(1)
            : '0.0';

    return (
        <div className="poi-statistics">
            {/* 主要统计指标 */}
            <Row gutter={16} className="poi-statistics__main">
                {statisticsCards.map((card, index) => (
                    <Col span={4} key={index}>
                        <Card className="poi-statistics__card">
                            <Statistic
                                title={card.title}
                                value={card.value}
                                prefix={
                                    <span style={{ color: card.color }}>
                                        {card.icon}
                                    </span>
                                }
                                suffix={card.suffix}
                                valueStyle={{
                                    color: card.color,
                                    fontSize: '24px',
                                    fontWeight: 'bold',
                                }}
                            />
                        </Card>
                    </Col>
                ))}

                {/* 完成率 */}
                <Col span={4}>
                    <Card className="poi-statistics__card">
                        <Statistic
                            title="完成率"
                            value={completionRate}
                            suffix="%"
                            prefix={
                                <CheckCircleOutlined
                                    style={{ color: '#52c41a' }}
                                />
                            }
                            valueStyle={{
                                color: '#52c41a',
                                fontSize: '24px',
                                fontWeight: 'bold',
                            }}
                        />
                    </Card>
                </Col>
            </Row>

            {/* 任务类型统计 */}
            {data.taskTypeStats &&
                Object.keys(data.taskTypeStats).length > 0 && (
                    <Card
                        title="任务类型统计"
                        className="poi-statistics__task-types"
                        size="small"
                    >
                        <Row gutter={16}>
                            {Object.entries(data.taskTypeStats).map(
                                ([taskType, stats]) => (
                                    <Col span={6} key={taskType}>
                                        <Card className="poi-statistics__task-type-card">
                                            <div className="task-type-header">
                                                <span className="task-type-name">
                                                    {getTaskTypeName(taskType)}
                                                </span>
                                                <span className="task-type-total">
                                                    共 {stats.total} 个
                                                </span>
                                            </div>
                                            <div className="task-type-details">
                                                <div className="task-type-item">
                                                    <span className="label">
                                                        待处理:
                                                    </span>
                                                    <span className="value pending">
                                                        {stats.pending}
                                                    </span>
                                                </div>
                                                <div className="task-type-item">
                                                    <span className="label">
                                                        进行中:
                                                    </span>
                                                    <span className="value in-progress">
                                                        {stats.inProgress}
                                                    </span>
                                                </div>
                                                <div className="task-type-item">
                                                    <span className="label">
                                                        已完成:
                                                    </span>
                                                    <span className="value completed">
                                                        {stats.completed}
                                                    </span>
                                                </div>
                                                <div className="task-type-item">
                                                    <span className="label">
                                                        失败:
                                                    </span>
                                                    <span className="value failed">
                                                        {stats.failed}
                                                    </span>
                                                </div>
                                            </div>
                                        </Card>
                                    </Col>
                                ),
                            )}
                        </Row>
                    </Card>
                )}
        </div>
    );
};

// 获取任务类型中文名称
const getTaskTypeName = (taskType: string): string => {
    const taskTypeMap: Record<string, string> = {
        DATA_COLLECTION: '数据采集',
        QUALITY_CHECK: '质量检查',
        CONTENT_REVIEW: '内容审核',
        PHONE_VERIFICATION: '电话核实',
        ADDRESS_VERIFICATION: '地址核实',
    };
    return taskTypeMap[taskType] || taskType;
};

export default PoiStatistics;
