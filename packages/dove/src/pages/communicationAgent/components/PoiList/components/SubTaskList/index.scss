.subtask-list {
    background-color: #fafafa;
    border-radius: 6px;
    padding: 12px;

    &__loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100px;
    }

    &__pagination {
        display: flex;
        justify-content: center;
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid #f0f0f0;
    }

    .subtask-item {
        background-color: #fff;
        border-radius: 4px;
        margin-bottom: 8px;
        padding: 12px;
        border: 1px solid #f0f0f0;

        &:last-child {
            margin-bottom: 0;
        }

        &:hover {
            border-color: #d9d9d9;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .ant-list-item-meta-title {
            margin-bottom: 8px;
            
            .ant-tag {
                margin-left: 8px;
            }
        }

        .ant-list-item-meta-description {
            color: #666;
            font-size: 12px;
        }

        .ant-list-item-action {
            margin-left: 16px;

            .ant-btn-link {
                padding: 0 4px;
                font-size: 12px;
            }
        }
    }

    .ant-list-item {
        padding: 0;
        border: none;
    }

    .ant-empty {
        padding: 20px 0;
    }

    .ant-alert {
        margin-bottom: 12px;
    }
}
