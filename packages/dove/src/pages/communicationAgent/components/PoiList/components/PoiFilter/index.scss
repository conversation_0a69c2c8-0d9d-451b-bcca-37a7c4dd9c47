.poi-filter {
    border: none;
    box-shadow: none;

    .ant-card-body {
        padding: 24px;
    }

    .ant-form-item {
        margin-bottom: 20px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    .ant-form-item-label {
        font-weight: 500;
        color: #262626;

        >label {
            font-size: 14px;
            line-height: 22px;
        }
    }

    .ant-input,
    .ant-select-selector,
    .ant-picker {
        border-radius: 6px;
        border: 1px solid #d9d9d9;

        &:hover {
            border-color: #40a9ff;
        }

        &:focus,
        &.ant-picker-focused {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
    }

    .ant-btn {
        margin-right: 12px;
        border-radius: 6px;
        height: 36px;
        padding: 0 16px;
        font-weight: 500;

        &:last-child {
            margin-right: 0;
        }

        &.ant-btn-primary {
            background-color: #1890ff;
            border-color: #1890ff;

            &:hover {
                background-color: #40a9ff;
                border-color: #40a9ff;
            }
        }

        &.ant-btn-default {
            color: #595959;
            border-color: #d9d9d9;

            &:hover {
                color: #40a9ff;
                border-color: #40a9ff;
            }
        }
    }

    .ant-btn-link {
        padding-left: 0;
        padding-right: 0;
        color: #1890ff;

        &:hover {
            color: #40a9ff;
        }

        .anticon {
            margin-left: 4px;
        }
    }

    // 表单行间距调整
    .ant-row {
        margin-bottom: 0;

        .ant-col {
            padding-bottom: 4px;
        }
    }

    // 操作按钮区域
    .ant-row:last-child {
        margin-top: 8px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;
    }
}