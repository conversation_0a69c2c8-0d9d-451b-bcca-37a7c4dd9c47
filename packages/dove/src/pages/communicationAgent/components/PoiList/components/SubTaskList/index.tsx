import React, { useState, useEffect } from 'react';
import {
    List,
    Tag,
    Button,
    Space,
    Typography,
    Pagination,
    Spin,
    Empty,
    Alert,
    Modal,
    message,
} from 'antd';
import {
    ClockCircleOutlined,
    SyncOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    ExclamationCircleOutlined,
    ReloadOutlined,
} from '@ant-design/icons';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';
import { getSubtaskList } from '../../services/api';
import type { SubTaskListProps, SubtaskDto } from '../../types';
import './index.scss';

const { Text } = Typography;
const { confirm } = Modal;

const SubTaskList: React.FC<SubTaskListProps> = ({ poiId, visible }) => {
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 5,
    });

    // 获取子任务列表
    const {
        data: subtaskData,
        loading,
        error,
        run: fetchSubtasks,
        refresh,
    } = useRequest(
        () =>
            getSubtaskList({
                poiId,
                ...pagination,
            }),
        {
            manual: true,
            onSuccess: data => {
                console.log('子任务列表获取成功:', data);
            },
            onError: error => {
                console.error('子任务列表获取失败:', error);
                message.error('获取子任务列表失败');
            },
        },
    );

    // 当visible变为true时，获取数据
    useEffect(() => {
        if (visible && poiId) {
            fetchSubtasks();
        }
    }, [visible, poiId, pagination, fetchSubtasks]);

    // 获取状态图标和颜色
    const getStatusConfig = (status: string) => {
        const statusMap: Record<
            string,
            { icon: React.ReactNode; color: string; text: string }
        > = {
            PENDING: {
                icon: <ClockCircleOutlined />,
                color: 'orange',
                text: '待处理',
            },
            IN_PROGRESS: {
                icon: <SyncOutlined spin />,
                color: 'blue',
                text: '进行中',
            },
            COMPLETED: {
                icon: <CheckCircleOutlined />,
                color: 'green',
                text: '已完成',
            },
            FAILED: {
                icon: <CloseCircleOutlined />,
                color: 'red',
                text: '失败',
            },
            CANCELLED: {
                icon: <ExclamationCircleOutlined />,
                color: 'gray',
                text: '已取消',
            },
        };
        return (
            statusMap[status] || { icon: null, color: 'default', text: status }
        );
    };

    // 获取任务类型文本
    const getTaskTypeText = (taskType: string) => {
        const taskTypeMap: Record<string, string> = {
            DATA_COLLECTION: '数据采集',
            QUALITY_CHECK: '质量检查',
            CONTENT_REVIEW: '内容审核',
            PHONE_VERIFICATION: '电话核实',
            ADDRESS_VERIFICATION: '地址核实',
        };
        return taskTypeMap[taskType] || taskType;
    };

    // 处理分页变化
    const handlePaginationChange = (page: number, pageSize?: number) => {
        setPagination({
            page,
            pageSize: pageSize || pagination.pageSize,
        });
    };

    // 渲染任务项
    const renderSubtaskItem = (subtask: SubtaskDto) => {
        const statusConfig = getStatusConfig(subtask.status);

        return (
            <List.Item
                key={subtask.subtaskId}
                className="subtask-item"
                actions={[
                    <Button key="complete" type="link" size="small">
                        完成
                    </Button>,
                    <Button key="fail" type="link" size="small">
                        标记失败
                    </Button>,
                ]}
            >
                <List.Item.Meta
                    title={
                        <Space>
                            <Text strong>{subtask.title}</Text>
                            <Tag
                                icon={statusConfig.icon}
                                color={statusConfig.color}
                            >
                                {statusConfig.text}
                            </Tag>
                            <Tag>{getTaskTypeText(subtask.taskType)}</Tag>
                            {subtask.priority && (
                                <Tag
                                    color={
                                        subtask.priority === 'HIGH'
                                            ? 'red'
                                            : subtask.priority === 'MEDIUM'
                                            ? 'orange'
                                            : 'green'
                                    }
                                >
                                    {subtask.priority === 'HIGH'
                                        ? '高优先级'
                                        : subtask.priority === 'MEDIUM'
                                        ? '中优先级'
                                        : '低优先级'}
                                </Tag>
                            )}
                        </Space>
                    }
                    description={
                        <Space direction="vertical" size="small">
                            {subtask.description && (
                                <Text type="secondary">
                                    {subtask.description}
                                </Text>
                            )}
                            <Space>
                                <Text type="secondary">
                                    创建时间:{' '}
                                    {dayjs(subtask.createTime).format(
                                        'YYYY-MM-DD HH:mm',
                                    )}
                                </Text>
                                {subtask.agentTaskId && (
                                    <Text type="secondary">
                                        Agent任务: {subtask.agentTaskId}
                                    </Text>
                                )}
                                {subtask.assignee && (
                                    <Text type="secondary">
                                        负责人: {subtask.assignee}
                                    </Text>
                                )}
                            </Space>
                        </Space>
                    }
                />
            </List.Item>
        );
    };

    if (!visible) {
        return null;
    }

    if (loading) {
        return (
            <div className="subtask-list__loading">
                <Spin tip="加载子任务中..." />
            </div>
        );
    }

    if (error) {
        return (
            <Alert
                message="子任务加载失败"
                description={error.message}
                type="error"
                showIcon
                action={
                    <Button
                        size="small"
                        icon={<ReloadOutlined />}
                        onClick={() => fetchSubtasks()}
                    >
                        重试
                    </Button>
                }
            />
        );
    }

    if (!subtaskData || subtaskData.data.length === 0) {
        return (
            <Empty
                description="暂无子任务"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
        );
    }

    return (
        <div className="subtask-list">
            <List
                dataSource={subtaskData.data}
                renderItem={renderSubtaskItem}
                size="small"
            />
            {subtaskData.total > pagination.pageSize && (
                <div className="subtask-list__pagination">
                    <Pagination
                        current={pagination.page}
                        pageSize={pagination.pageSize}
                        total={subtaskData.total}
                        size="small"
                        showSizeChanger={false}
                        showQuickJumper={false}
                        showTotal={(total, range) =>
                            `${range[0]}-${range[1]} / ${total}`
                        }
                        onChange={handlePaginationChange}
                    />
                </div>
            )}
        </div>
    );
};

export default SubTaskList;
