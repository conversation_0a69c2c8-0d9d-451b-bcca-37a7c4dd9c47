.poi-card {
    width: 100%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    transition: box-shadow 0.3s ease;

    &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    &__header {
        display: flex;
        align-items: flex-start;
        gap: 16px;
        margin-bottom: 16px;
    }

    &__avatar {
        flex-shrink: 0;
    }

    &__info {
        flex: 1;
        min-width: 0;
    }

    &__title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        .poi-name {
            font-size: 18px;
            color: #333;
        }
    }

    &__meta {
        &-item {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;

            .poi-id {
                font-size: 12px;
            }

            .poi-category {
                font-size: 12px;
            }
        }
    }

    &__rating {
        display: flex;
        align-items: center;
        gap: 4px;
    }

    &__actions {
        flex-shrink: 0;
    }

    &__details {
        margin-bottom: 12px;
    }

    &__detail-item {
        display: flex;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 4px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    &__detail-icon {
        color: #666;
        margin-top: 2px;
        flex-shrink: 0;
    }

    &__detail-text {
        flex: 1;
        margin: 0;
    }

    &__footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 12px;
        border-top: 1px solid #f0f0f0;
        margin-top: 12px;
    }

    &__time {
        font-size: 12px;
    }

    &__subtasks {
        margin-top: 16px;
    }

    .ant-divider {
        margin: 16px 0;
    }
}