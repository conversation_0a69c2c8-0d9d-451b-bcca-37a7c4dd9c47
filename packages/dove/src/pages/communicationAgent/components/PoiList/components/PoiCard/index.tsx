import React, { useState } from 'react';
import {
    Card,
    Avatar,
    Tag,
    Button,
    Space,
    Typography,
    Rate,
    Divider,
} from 'antd';
import {
    ShopOutlined,
    PhoneOutlined,
    EnvironmentOutlined,
    ClockCircleOutlined,
    DownOutlined,
    UpOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import SubTaskList from '../SubTaskList';
import type { PoiCardProps } from '../../types';
import './index.scss';

const { Text, Paragraph } = Typography;

const PoiCard: React.FC<PoiCardProps> = ({ poi }) => {
    const [subtaskVisible, setSubtaskVisible] = useState(false);

    // 获取状态标签颜色
    const getStatusColor = (status?: string) => {
        const statusColorMap: Record<string, string> = {
            ACTIVE: 'green',
            INACTIVE: 'red',
            PENDING: 'orange',
            SUSPENDED: 'gray',
        };
        return statusColorMap[status || ''] || 'default';
    };

    // 获取状态文本
    const getStatusText = (status?: string) => {
        const statusTextMap: Record<string, string> = {
            ACTIVE: '营业中',
            INACTIVE: '已关闭',
            PENDING: '待审核',
            SUSPENDED: '已暂停',
        };
        return statusTextMap[status || ''] || status || '未知';
    };

    // 切换子任务列表显示
    const toggleSubtasks = () => {
        setSubtaskVisible(!subtaskVisible);
    };

    return (
        <Card className="poi-card">
            <div className="poi-card__header">
                <div className="poi-card__avatar">
                    <Avatar
                        size={64}
                        src={poi.imageUrl}
                        icon={<ShopOutlined />}
                    />
                </div>

                <div className="poi-card__info">
                    <div className="poi-card__title">
                        <Text strong className="poi-name">
                            {poi.poiName}
                        </Text>
                        <Tag color={getStatusColor(poi.status)}>
                            {getStatusText(poi.status)}
                        </Tag>
                    </div>

                    <div className="poi-card__meta">
                        <Space direction="vertical" size="small">
                            <div className="poi-card__meta-item">
                                <Text type="secondary" className="poi-id">
                                    ID: {poi.poiId}
                                </Text>
                                {poi.category && (
                                    <Tag className="poi-category">
                                        {poi.category}
                                    </Tag>
                                )}
                            </div>

                            {poi.rating && (
                                <div className="poi-card__rating">
                                    <Rate
                                        disabled
                                        value={poi.rating}
                                        allowHalf
                                        style={{ fontSize: '14px' }}
                                    />
                                    <Text type="secondary">({poi.rating})</Text>
                                </div>
                            )}
                        </Space>
                    </div>
                </div>

                <div className="poi-card__actions">
                    <Button
                        type="primary"
                        size="small"
                        icon={
                            subtaskVisible ? <UpOutlined /> : <DownOutlined />
                        }
                        onClick={toggleSubtasks}
                    >
                        {subtaskVisible ? '收起任务' : '查看任务'}
                    </Button>
                </div>
            </div>

            <div className="poi-card__details">
                <Space
                    direction="vertical"
                    size="small"
                    style={{ width: '100%' }}
                >
                    {poi.address && (
                        <div className="poi-card__detail-item">
                            <EnvironmentOutlined className="poi-card__detail-icon" />
                            <Paragraph
                                ellipsis={{ rows: 1, tooltip: poi.address }}
                                className="poi-card__detail-text"
                            >
                                {poi.address}
                            </Paragraph>
                        </div>
                    )}

                    {poi.phone && (
                        <div className="poi-card__detail-item">
                            <PhoneOutlined className="poi-card__detail-icon" />
                            <Text className="poi-card__detail-text">
                                {poi.phone}
                            </Text>
                        </div>
                    )}

                    {poi.businessHours && (
                        <div className="poi-card__detail-item">
                            <ClockCircleOutlined className="poi-card__detail-icon" />
                            <Text className="poi-card__detail-text">
                                {poi.businessHours}
                            </Text>
                        </div>
                    )}
                </Space>
            </div>

            {poi.createTime && (
                <div className="poi-card__footer">
                    <Text type="secondary" className="poi-card__time">
                        创建时间:{' '}
                        {dayjs(poi.createTime).format('YYYY-MM-DD HH:mm:ss')}
                    </Text>
                    {poi.updateTime && (
                        <Text type="secondary" className="poi-card__time">
                            更新时间:{' '}
                            {dayjs(poi.updateTime).format(
                                'YYYY-MM-DD HH:mm:ss',
                            )}
                        </Text>
                    )}
                </div>
            )}

            {/* 子任务列表 */}
            {subtaskVisible && (
                <>
                    <Divider />
                    <div className="poi-card__subtasks">
                        <SubTaskList
                            poiId={poi.poiId}
                            visible={subtaskVisible}
                        />
                    </div>
                </>
            )}
        </Card>
    );
};

export default PoiCard;
