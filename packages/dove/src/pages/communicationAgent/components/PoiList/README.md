# PoiList 商家列表页面

基于需求描述和技术文档开发的商家列表页面组件。

## 功能特性

-   ✅ 筛选表单（支持展开/收起）
-   ✅ 统计数据展示
-   ✅ 商家卡片列表
-   ✅ 子任务管理
-   ✅ 分页功能
-   ✅ 错误处理和加载状态
-   ✅ 所有时间处理使用 dayjs
-   ✅ 数组管理表单项模式

## 组件结构

```
PoiList (主页面)
├── PoiFilter (筛选组件) - 使用数组管理表单项
├── PoiStatistics (统计组件)
├── PoiCardList (卡片列表)
│   └── PoiCard (单个商家卡片)
│       └── SubTaskList (子任务列表)
└── Pagination (分页)
```

## 筛选表单字段

### 基础字段（默认显示）

-   商家 ID - Input
-   商家名称 - Input
-   门店责任人 - MisSelector
-   任务创建时间 - DatePicker.RangePicker（默认最近一个月）
-   任务 ID - Input
-   任务名称 - Input
-   任务创建人 - MisSelector

### 展开字段

-   商家类型 - Select（外卖门店、公海门店）
-   所属城市 - Input
-   AgentID - SelectAgent
-   Agent 名称 - Input
-   触达类型 - Select（AI 外呼、普通外呼、智能调度）
-   任务状态 - Select（待执行、执行中、已完成、已取消）
-   触达时间范围 - DatePicker.RangePicker
-   业务线/租户 - TeamSelect

## 时间处理

所有时间相关操作都使用 dayjs：

-   默认时间范围设置：`dayjs().subtract(30, 'day')`
-   时间格式化：`dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss')`
-   时间戳转换：`dayjs(date).valueOf()`

## 使用方法

```tsx
import PoiList from '@/pages/communicationAgent/components/PoiList';

function App() {
    return <PoiList />;
}
```

## API 接口

-   `/xianfu/api-v2/dove/data/poi/search` - 商家搜索
-   `/xianfu/api-v2/dove/data/poi/subtask/statistics` - 统计数据
-   `/xianfu/api-v2/dove/data/poi/subtask` - 子任务列表

## 开发状态

✅ 已完成基础组件结构和功能实现
✅ 已完成数组管理表单项模式
✅ 已完成所有时间处理使用 dayjs
⏳ 需要根据 UI 设计图调整样式
⏳ 需要测试 API 集成和数据流
