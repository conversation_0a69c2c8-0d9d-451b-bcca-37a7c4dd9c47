import React, { useState, useEffect } from 'react';
import { useRequest, useUpdateEffect } from 'ahooks';
import { Pagination } from 'antd';
import PoiFilter from './components/PoiFilter';
import PoiStatistics from './components/PoiStatistics';
import PoiCardList from './components/PoiCardList';
import { searchPoi, getSubtaskStatistics } from './services/api';
import type { PoiPageQueryRequest } from './types';
import './index.scss';

const PoiList: React.FC = () => {
    // 状态管理
    const [filters, setFilters] = useState<PoiPageQueryRequest>({
        page: 1,
        pageSize: 10,
    });
    const [pagination, setPagination] = useState({
        page: 1,
        pageSize: 10,
    });

    // 商家列表数据请求
    const {
        data: poiData,
        loading: poiLoading,
        error: poiError,
        run: fetchPoiData,
    } = useRequest(
        (extra = {}) => searchPoi({ ...filters, ...pagination, ...extra }),
        {
            manual: true,
            refreshDeps: [filters, pagination],
            debounceWait: 500,
            onSuccess: data => {
                console.log('商家列表数据获取成功:', data);
            },
            onError: error => {
                console.error('商家列表数据获取失败:', error);
            },
        },
    );

    // 统计数据请求
    const {
        data: statsData,
        loading: statsLoading,
        run: fetchStatsData,
    } = useRequest(() => getSubtaskStatistics(filters), {
        manual: true,
        refreshDeps: [filters],
        debounceWait: 500,
        onSuccess: data => {
            console.log('统计数据获取成功:', data);
        },
        onError: error => {
            console.error('统计数据获取失败:', error);
        },
    });

    // 处理筛选条件变化
    const handleFilterChange = (newFilters: Partial<PoiPageQueryRequest>) => {
        setFilters(prev => ({ ...prev, page: 1, ...newFilters }));
        // 筛选条件变化时回到第一页
        setPagination(p => ({ ...p, page: 1 }));
    };

    // 处理分页变化
    const handlePaginationChange = (page: number, pageSize?: number) => {
        setPagination({
            page,
            pageSize: pageSize || pagination.pageSize,
        });
        fetchPoiData({
            page,
            pageSize: pageSize || pagination.pageSize,
        });
    };

    // 重新加载数据
    const handleReload = () => {
        fetchPoiData();
        fetchStatsData();
    };

    return (
        <div className="poi-list">
            {/* 筛选区 */}
            <div className="poi-list__filter">
                <PoiFilter
                    initialValues={filters}
                    onChange={handleFilterChange}
                />
            </div>

            {/* 统计区 */}
            <div className="poi-list__statistics">
                <PoiStatistics data={statsData} loading={statsLoading} />
            </div>

            {/* 列表区 */}
            <div className="poi-list__content">
                <PoiCardList
                    data={poiData?.data}
                    loading={poiLoading}
                    error={poiError}
                    onReload={handleReload}
                />
            </div>

            {/* 分页区 */}
            {poiData && poiData.total > 0 && (
                <div className="poi-list__pagination">
                    <Pagination
                        current={pagination.page}
                        pageSize={pagination.pageSize}
                        total={poiData.total}
                        showSizeChanger
                        showQuickJumper
                        showTotal={(total, range) =>
                            `第 ${range[0]}-${range[1]} 条/共 ${total} 条`
                        }
                        onChange={handlePaginationChange}
                        onShowSizeChange={handlePaginationChange}
                    />
                </div>
            )}
        </div>
    );
};

export default PoiList;
