/**
 * 任务进度柱状图展示
 */
import React, { useEffect, useRef } from 'react';
import { Card, Spin, Empty } from 'antd';
import { Bar } from '@antv/g2plot';
import { TaskProgressData } from '../index';

interface TaskProgressContentProps {
    data: TaskProgressData | null;
}

const TaskProgressContent: React.FC<TaskProgressContentProps> = ({ data }) => {
    const chartRef = useRef<HTMLDivElement>(null);
    const plotRef = useRef<Bar | null>(null);

    useEffect(() => {
        if (!data || !chartRef.current) {
            return;
        }

        // 准备图表数据
        const chartData = [
            {
                type: '待执行通话',
                count: data.waitingCnt,
                color: 'rgb(104, 216, 169)', // 绿色
            },
            {
                type: '执行中通话',
                count: data.doingCnt,
                color: 'rgb(104, 150, 249)', // 蓝色
            },
            {
                type: '已执行/失败通话',
                count: data.doneCnt,
                color: '#722ed1', // 紫色
            },
            {
                type: '触达成功通话',
                count: data.reachSuccessCnt,
                color: '#13c2c2', // 青色
            },
            {
                type: '触达失败通话',
                count: data.reachFailureCnt,
                color: '#fa8c16', // 橙色
            },
        ];

        // 销毁之前的图表
        if (plotRef.current) {
            plotRef.current.destroy();
        }

        // 创建新的横向条形图
        const plot = new Bar(chartRef.current, {
            data: chartData,
            xField: 'count', // 横向条形图：数值作为x轴
            yField: 'type', // 横向条形图：类型作为y轴
            seriesField: 'type', // Bar图表使用seriesField而不是colorField
            color: ({ type }) => {
                const item = chartData.find(d => d.type === type);
                return item?.color || '#1890ff';
            },
            barWidthRatio: 0.6, // Bar图表使用barWidthRatio而不是columnWidthRatio
            label: {
                position: 'left', // 横向条形图标签位置在右侧
                style: {
                    fill: '#333',
                    fontSize: 12,
                    // fontWeight: 'bold',
                },
            },
            meta: {
                count: {
                    alias: '任务数量',
                },
                type: {
                    alias: '任务类型',
                },
            },
            tooltip: {
                formatter: datum => {
                    return {
                        name: datum.type,
                        value: `${datum.count} 个`,
                    };
                },
            },
            xAxis: {
                label: {
                    style: {
                        fontSize: 12,
                    },
                },
                grid: {
                    line: {
                        style: {
                            stroke: '#f0f0f0',
                        },
                    },
                },
            },
            yAxis: {
                label: {
                    style: {
                        fontSize: 12,
                    },
                },
            },
            animation: {
                appear: {
                    animation: 'wave-in',
                    duration: 1000,
                },
            },
        });

        plot.render();
        plotRef.current = plot;

        return () => {
            if (plotRef.current) {
                plotRef.current.destroy();
                plotRef.current = null;
            }
        };
    }, [data]);

    // 清理图表
    useEffect(() => {
        return () => {
            if (plotRef.current) {
                plotRef.current.destroy();
            }
        };
    }, []);

    if (!data) {
        return (
            <div className="task-progress-chart">
                <h3>智能外呼任务执行看板</h3>
                <Empty
                    description="暂无数据，请先查询"
                    style={{ padding: '50px 0' }}
                />
            </div>
        );
    }

    return (
        <div className="task-progress-chart">
            <h3>智能外呼任务执行看板</h3>
            <div
                ref={chartRef}
                style={{
                    height: '400px',
                    width: '100%',
                }}
            />
        </div>
    );
};

export default TaskProgressContent;
