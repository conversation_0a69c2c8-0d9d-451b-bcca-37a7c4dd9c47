# 任务进度模块 PRD

## 1. 模块设计

### 1.1 模块功能

任务进度模块用于展示任务进度信息，包括任务名称、进度条、完成状态等信息。

### 1.2 模块结构

任务进度模块由以下组件组成：

-   TaskProgressForm：任务进度信息查询表单。
-   TaskProgressConetent：任务进度信息展示柱状图。

### 1.3 模块交互

任务进度模块与后端接口进行交互，通过查询任务进度信息并展示在柱状图中。

## 2. 模块实现

### 2.1 任务进度信息查询表单

#### 2.1.1 表单字段

-   木星租户 ID：下拉选择框。
-   APPID：下拉选择框，用于选择任务状态。
-   业务线：下拉选择框，复用 src/components/callRecord/TeamSelect。
-   起止时间：日期起止选择器，用于选择任务创建时间范围。
-   组织架构：复用 src/components/RooOrganizationSelector，用于组织架构查询。
    使用 antd 的 Form 组件实现表单

#### 2.1.2 表单交互

-   查询按钮：点击查询按钮后，发送查询任务进度信息的请求。
    请求地址：/dove/taskProgress/query
    请求方法：POST
    请求参数：

```json
{
  "startTime": 时间戳(s), // 开始时间
  "endTime": 时间戳(s), // 结束时间
  "jupiterTenantId: "123234-123e423", // 木星租户id
  "bizId": 5001, // 业务线id
  "orgId": 2132, // 组织id
  "appId": 123234, // appid
}
```

响应结果：

```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "waitingCnt": 1, //待执行任务汇总
        "doingCnt": 1, //执行中任务汇总
        "doneCnt": 1, //当日已执行任务汇总
        "failureCnt": 1, //当日失败任务汇总
        "reachSuccessCnt": 1, //触达成功任务汇总
        "reachFailureCnt": 1 //触达失败任务汇总
    }
}
```

-   重置按钮：点击重置按钮后，清空表单字段。

### 2.2 任务进度信息展示柱状图

-   柱状图：使用@antv/g2 或者@antv/g2plot 实现柱状图。
-   柱状图数据：使用查询任务进度信息的响应结果中的数据。
