.task-progress-drawer {

    .task-progress-container {
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 24px;
    }

    .task-progress-form {
        // background: #fff;
        // border-radius: 8px;
        // padding: 24px;
        // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        position: relative;

        .task-progress-form-container {
            .ant-form-item {
                margin-bottom: 16px;
            }

            .ant-form-item-label {
                font-weight: 500;
                color: #262626;
            }

            .ant-btn {
                height: 32px;
                border-radius: 6px;
                font-weight: 500;
            }

            .ant-btn-primary {
                background: #1890ff;
                border-color: #1890ff;

                &:hover {
                    background: #40a9ff;
                    border-color: #40a9ff;
                }
            }
        }

        .task-progress-form-actions {
            position: absolute;
            right: 0;
            bottom: 30px;
        }
    }

    .task-progress-chart {
        height: 100%;
        border: none;
        box-shadow: none;

        h3 {
            margin-bottom: 15px;
        }

        .ant-card-head {
            background: #fafafa;
            border-bottom: 1px solid #f0f0f0;
            padding: 0 24px;

            .ant-card-head-title {
                font-size: 16px;
                font-weight: 600;
                color: #262626;
                padding: 16px 0;
            }
        }

        .ant-card-body {
            padding: 24px;
            height: calc(100% - 57px);
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
}

// 响应式设计
@media (max-width: 1200px) {
    .task-progress-drawer {
        .ant-drawer {
            width: 100% !important;
        }
    }
}

@media (max-width: 768px) {
    .task-progress-drawer {
        .task-progress-form {
            .ant-col {
                width: 100% !important;
                max-width: 100% !important;
                flex: 0 0 100% !important;
            }
        }
    }
}