/**
 * 任务进度数据查询看板
 */
import React, { useEffect, useState } from 'react';
import { Drawer, Form } from 'antd';
import { useRequest } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import dayjs from 'dayjs';
import TaskProgressForm from './TaskProgressForm';
import TaskProgressContent from './TaskProgressContent';
import './index.scss';

interface TaskProgressProps {
    visible: boolean;
    onClose: () => void;
}

// 任务进度数据类型
export interface TaskProgressData {
    waitingCnt: number; // 待执行任务汇总
    doingCnt: number; // 执行中任务汇总
    doneCnt: number; // 当日已执行任务汇总
    failureCnt: number; // 当日失败任务汇总
    reachSuccessCnt: number; // 触达成功任务汇总
    reachFailureCnt: number; // 触达失败任务汇总
}

// 查询参数类型
export interface TaskProgressQueryParams {
    startTime?: number; // 开始时间戳(s)
    endTime?: number; // 结束时间戳(s)
    jupiterTenantId?: string; // 木星租户id
    bizId?: number; // 业务线id
    orgId?: number; // 组织id
    appId?: number; // appid
}

const TaskProgress: React.FC<TaskProgressProps> = ({ visible, onClose }) => {
    const [form] = Form.useForm();
    const [progressData, setProgressData] = useState<TaskProgressData | null>(
        null,
    );

    // 查询任务进度数据
    const { loading, run: queryTaskProgress } = useRequest(
        async (params: TaskProgressQueryParams) => {
            const res = await apiCaller.post(
                '/xianfu/api-v2/dove/taskProgress/query' as any,
                params,
            );
            if (res.code === 0) {
                setProgressData(res.data as TaskProgressData);
                return res.data;
            }
            return [];
        },
        {
            manual: true,
            onError: error => {
                console.error('查询任务进度失败:', error);
            },
        },
    );

    // 处理查询
    const handleQuery = async () => {
        try {
            const { timeRange, ...values } = await form.validateFields();
            const params: TaskProgressQueryParams = {
                ...values,
                // 转换时间为时间戳(秒)
                startTime: timeRange?.[0]
                    ? dayjs(timeRange[0]).unix()
                    : undefined,
                endTime: timeRange?.[1]
                    ? dayjs(timeRange[1]).unix()
                    : undefined,
            };
            await queryTaskProgress(params);
        } catch (error) {
            console.error('表单验证失败:', error);
        }
    };

    useEffect(() => {
        if (visible) {
            handleQuery();
        }
    }, [visible]);

    // 重置表单
    const handleReset = () => {
        form.resetFields();
    };

    return (
        <Drawer
            title="智能外呼任务数据"
            width={900}
            open={visible}
            onClose={onClose}
            className="task-progress-drawer"
        >
            <div className="task-progress-container">
                {/* 查询表单 */}
                <TaskProgressForm
                    form={form}
                    loading={loading}
                    onQuery={handleQuery}
                    onReset={handleReset}
                />
                {/* 进度图表 */}
                <TaskProgressContent data={progressData} />
            </div>
        </Drawer>
    );
};

export default TaskProgress;
