/**
 * 任务进度查询表单
 */
import React from 'react';
import {
    Form,
    Row,
    Col,
    Button,
    DatePicker,
    Space,
    FormInstance,
    Input,
} from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import TeamSelect from '@src/components/callRecord/TeamSelect';
import RooOrganizationSelector from '@src/components/RooOrganizationSelector';

const { RangePicker } = DatePicker;

interface TaskProgressFormProps {
    form: FormInstance;
    loading: boolean;
    onQuery: () => void;
    onReset: () => void;
}

const TaskProgressForm: React.FC<TaskProgressFormProps> = ({
    form,
    loading,
    onQuery,
    onReset,
}) => {
    return (
        <Form form={form} layout="vertical" className="task-progress-form">
            <Row gutter={16}>
                <Col span={6}>
                    <Form.Item label="木星租户ID" name="jupiterTenantId">
                        <Input placeholder="请输入木星租户ID" allowClear />
                    </Form.Item>
                </Col>
                <Col span={6}>
                    <Form.Item label="业务线" name="bizId">
                        <TeamSelect
                            optionPath="/xianfu/api-v2/dove/staff/biz/query"
                            mode="multiple"
                            maxTagCount="responsive"
                        />
                    </Form.Item>
                </Col>
                <Col span={12}>
                    <Form.Item label="起止时间" name="timeRange">
                        <RangePicker
                            placeholder={['开始时间', '结束时间']}
                            showTime
                            format="YYYY-MM-DD HH:mm:ss"
                            style={{ width: '100%' }}
                        />
                    </Form.Item>
                </Col>
                <Col span={12}>
                    <Form.Item label="组织架构" name="orgId">
                        <RooOrganizationSelector
                            placeholder="请选择组织架构"
                            allowClear
                            multiple
                        />
                    </Form.Item>
                </Col>
            </Row>
            <Row gutter={16}>
                <Space
                    className="task-progress-form-actions"
                    style={{ marginTop: 24 }}
                >
                    <Button icon={<ReloadOutlined />} onClick={onReset}>
                        重置
                    </Button>
                    <Button
                        type="primary"
                        icon={<SearchOutlined />}
                        loading={loading}
                        onClick={onQuery}
                    >
                        查询
                    </Button>
                </Space>
            </Row>
        </Form>
    );
};

export default TaskProgressForm;
