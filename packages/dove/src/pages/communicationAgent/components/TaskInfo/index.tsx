import { useMemo, useState } from 'react';
import { Typography, Row, Col, Tag } from 'antd';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import {
    reachMethodOptions,
    taskStatusOptions,
    ReachTypeName,
    ReachTypeEnum,
} from '@src/constants';
import { TaskData } from '../../types';
import { getReachContentLabelName } from '../TaskList/ModalCreateTask/ReachContentFormItem';
import styles from './index.module.scss';

const { Text } = Typography;

interface TaskInfoProps {
    currentTask: TaskData | null; // 当前焦点所在任务
}

const TaskInfo: React.FC<TaskInfoProps> = props => {
    const {
        currentTask, // 当前焦点所在任务
    } = props;

    if (!currentTask?.id) return null;
    const [showMore, setShowMore] = useState(false);

    const {
        taskName,
        taskType,
        poiDto,
        channelDto,
        taskWorkStrategy,
        creator,
        createTime,
        taskStatus,
        id,
    } = currentTask;

    const status = taskStatusOptions?.find(
        // @ts-ignore
        item => item.value === taskStatus,
    );

    const renderReachTime = time => {
        if (!time.startTime) return '-';
        const startTime = dayjs(time.startTime).format('YYYY-MM-DD HH:mm');
        const endTime = time?.endTime
            ? ` ~ ${dayjs(time.endTime).format('YYYY-MM-DD HH:mm')}`
            : '';
        return startTime + endTime;
    };

    return (
        <div className={styles.taskInfoCard}>
            <div className={styles.header}>
                <Text className={styles.title}>{taskName}</Text>
                <div className={styles.subtitleWrapper}>
                    <Text className={styles.subtitle}>{taskType}</Text>
                    <Tag color={status?.color} className={styles.statusTag}>
                        {status?.label || '-'}
                    </Tag>
                </div>
            </div>
            <Row gutter={[24, 16]} className={styles.content}>
                <Col span={8}>
                    <div className={styles.infoRow}>
                        <span className={styles.label}>任务ID</span>
                        <span>{id}</span>
                    </div>
                </Col>
                <Col span={8}>
                    <div className={styles.infoRow}>
                        <span className={styles.label}>
                            {getReachContentLabelName({
                                ...currentTask,
                                ...channelDto,
                            })}
                        </span>
                        <Typography.Text
                            ellipsis={{
                                tooltip: currentTask?.agent || '-',
                            }}
                        >
                            <span>{currentTask?.agent || '-'}</span>
                        </Typography.Text>
                    </div>
                </Col>
                <Col span={8}>
                    <div className={styles.infoRow}>
                        <span className={styles.label}>商家/对象类型</span>
                        <span>{poiDto?.contactObjectTypeName || '-'}</span>
                    </div>
                </Col>
                <Col span={8}>
                    <div className={styles.infoRow}>
                        <span className={styles.label}>触达方式</span>
                        <span>
                            {
                                reachMethodOptions.find(
                                    v => v.value === channelDto.contactType,
                                )?.label
                            }
                        </span>
                    </div>
                </Col>
                <Col span={8}>
                    <div className={styles.infoRow}>
                        <span className={styles.label}>创建人及mis</span>
                        <span>{`${creator?.name || '-'}  ${
                            creator?.mis ? `（${creator?.mis}）` : ''
                        }`}</span>
                    </div>
                </Col>
                <Col span={8}>
                    <div className={styles.infoRow}>
                        <span className={styles.label}>创建时间</span>
                        <span>
                            {dayjs(createTime).format('YYYY-MM-DD HH:mm:ss')}
                        </span>
                    </div>
                </Col>
                {showMore ? (
                    <>
                        <Col span={8}>
                            <div className={styles.infoRow}>
                                <span className={styles.label}>触达时间</span>
                                <span>{renderReachTime(taskWorkStrategy)}</span>
                            </div>
                        </Col>
                        <Col span={8}>
                            <div className={styles.infoRow}>
                                <span className={styles.label}>
                                    关联agent类型
                                </span>
                                <span>{currentTask?.agentType || '-'}</span>
                            </div>
                        </Col>
                        <Col span={8}>
                            <div className={styles.infoRow}>
                                <span className={styles.label}>触达类型</span>
                                <span>
                                    {ReachTypeName[currentTask?.reachType] ||
                                        '-'}
                                </span>
                            </div>
                        </Col>
                        <Col span={8}>
                            <div className={styles.infoRow}>
                                <span className={styles.label}>触达对象</span>
                                <span>
                                    {poiDto?.kpPriority?.join('、') || '-'}
                                </span>
                            </div>
                        </Col>
                        <Col span={8}>
                            <div className={styles.infoRow}>
                                <span className={styles.label}>
                                    创建人组织架构
                                </span>
                                <span>{creator?.orgName || '-'}</span>
                            </div>
                        </Col>
                        {currentTask?.reachType ===
                            ReachTypeEnum.INTELLIGENT_SCHEDULING && (
                            <Col span={8}>
                                <div className={styles.infoRow}>
                                    <span className={styles.label}>
                                        调度流程名称及ID
                                    </span>
                                    <span>{`${currentTask?.processName}（${currentTask?.processId}）`}</span>
                                </div>
                            </Col>
                        )}
                        <Col span={8}>
                            <div className={styles.infoRow}>
                                <span className={styles.label}>业务线租户</span>
                                <span>{currentTask?.bizName || '-'}</span>
                            </div>
                        </Col>
                        <Col span={24} className={styles.showMore}>
                            <div onClick={() => setShowMore(!showMore)}>
                                收起更多
                                <UpOutlined />
                            </div>
                        </Col>
                    </>
                ) : (
                    <Col span={24} className={styles.showMore}>
                        <div onClick={() => setShowMore(!showMore)}>
                            展开更多
                            <DownOutlined />
                        </div>
                    </Col>
                )}
            </Row>
        </div>
    );
};

export default TaskInfo;
