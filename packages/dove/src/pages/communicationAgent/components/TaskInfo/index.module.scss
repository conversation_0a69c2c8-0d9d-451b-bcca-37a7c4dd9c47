.taskInfoCard {
  :global {
    padding: 11px 16px;
    border-bottom: 1px solid #F0F0F0;
    margin-bottom: 5px;
    padding-top: 0;
  }
}

.header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  gap: 12px;

  .title {
    font-size: 16px;
    font-weight: 500;
    color: #000000;
  }

  .subtitleWrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    
    .subtitle {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.65);
    }
    .smtitle {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.85);
    }

    .statusTag {
      margin: 0;
      font-size: 12px;
      line-height: 20px;
      height: 22px;
    }
  }
}

.content {
  width: 100%;
}

.infoRow {
  display: flex;
  align-items: center;
  font-size: 12px!important;
  
  .label {
    min-width: 48px;
    color: rgba(0, 0, 0, 0.65);
    margin-right: 16px;
    flex-shrink: 0;
  }
  
  :global(.ant-typography) {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 12px !important;
  }
} 

.showMore{
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    font-size: 12px;
}