# 外卖商家招商任务管理系统 PRD

## 1. 产品概述
该系统是一个面向外卖商家招商团队的任务管理平台，用于管理和追踪商家招商进度。

## 2. 功能模块

### 2.1 任务列表页面

#### 2.1.1 顶部功能区
- **搜索功能**
  - 搜索框占位文本：搜索
  - 支持模糊搜索
- **筛选功能**
  - 筛选按钮（可展开/收起）
  - 筛选项包括：
    - 创建人：支持输入框搜索
    - 任务类型：下拉选择框
    - 任务创建时间：日期范围选择器（开始-结束）
    - 触达时间：日期范围选择器（开始-结束）
  - 操作按钮：
    - 清空选择：重置所有筛选条件
    - 确定：应用筛选条件

#### 2.1.2 任务状态分类导航
- **状态分类标签**：
  - 全部（10）
  - 待开始（3）
  - 进行中（2）
  - 已结束（5）
- **标签交互**：
  - 当前选中标签显示下划线
  - 数字实时更新

#### 2.1.3 新建任务
- **新建按钮**
  - 位置：列表顶部
  - 样式：黑色背景，白色"+"图标和文字
  - 文案：新建任务

### 2.2 任务详情列表

#### 2.2.1 列表表头
- 商家名称
- 触达状态
- 沟通结果
- 沟通意向
- 触达时间
- 沟通时长
- 沟通内容
- 操作

#### 2.2.2 列表内容展示
- **商家信息**：
  - 商家名称（如：麦当劳北京）
- **状态标签**：
  - 已触达：绿色背景
  - 触达成功：绿色背景
  - 触达失败：红色背景
- **时间显示**：
  - 格式：YYYY-MM-DD HH:mm:ss
- **沟通时长**：
  - 格式：X分X秒
- **操作按钮**：
  - 详情：橙色文字

#### 2.2.3 分页功能
- 页码导航
- 支持跳转指定页
- 显示总条数
- 每页显示条数选择

### 2.3 任务基本信息

#### 2.3.1 商家信息
- **商家类型**：公海商家
- **关联agent**：新签招商
- **触达方式**：AI 外呼
- **触达时间**：支持时间段设置（如：2025-01-23 09:00 ~ 2025-01-24 09:00）

#### 2.3.2 创建信息
- 创建人
- 创建时间
- 任务状态标识（如：进行中）

### 2.4 任务监控模块
#### 2.4.1 执行进度模块
- **数据展示**
  - 总任务数：显示在进度条右上角，格式为 "已执行数/总任务数"（如：3333/10000）
  - 进度条样式：
    - 已执行部分：蓝色实心
    - 执行中部分：浅蓝色实心
    - 待执行部分：灰色实心
  - 百分比显示：右侧显示总体完成百分比（如：30%）
  - 进度条使用antv实现
  - antv文档：https://g2.antv.antgroup.com/manual/core/mark/interval

#### 2.4.2 触达成功率模块
- **数据展示**
  - 触达数据：显示在进度条右上角，格式为 "触达成功数/已执行数"（如：2500/3333）
  - 进度条样式：
    - 触达成功部分：绿色实心
    - 触达失败部分：橙色实心
  - 百分比显示：右侧显示触达成功率（如：80%）

#### 2.4.3 平均执行时长模块
- **数据展示**
  - 时长格式：X分X秒（如：1分20秒）
  - 刷新机制：
    - 支持手动刷新
    - 每60秒自动刷新一次
  - 展示位置：右侧独立卡片

#### 2.4.4 AI智能分析模块
- **成功原因分析**
  - 展示形式：环形图
  - 数据项：
    - A原因（78%）：蓝色
    - B原因（10%）：黄色
    - C原因（12%）：绿色
  - 交互：鼠标悬浮显示具体百分比和数量

- **失败原因分析**
  - 展示形式：环形图
  - 数据项：
    - A原因（78%）：橙色
    - B原因（10%）：浅橙色
    - C原因（12%）：紫色
  - 交互：鼠标悬浮显示具体百分比和数量

#### 2.4.5 数据更新机制
- 实时数据：执行进度、触达成功率实时更新
- 定时刷新：平均执行时长每分钟更新一次
- AI分析：每10分钟更新一次分析结果

#### 2.4.6 交互要求
- 所有百分比保留2位小数
- 数据加载时显示loading状态
- 刷新按钮点击后有loading效果

### 2.5 AI智能分析
- **帮你智能归纳成功原因**：
  - A原因占比（78%）
  - B原因占比（10%）
  - C原因占比（12%）
  - 以环形图形式展示

- **帮你智能归纳失败原因**：
  - A原因占比（78%）
  - B原因占比（10%）
  - C原因占比（12%）
  - 以环形图形式展示

### 2.6 触达状态分类
- **状态分类**：
  - 已触达（10）
  - 触达中（3）
  - 待触达（2）

## 3. 权限控制
- 创建任务权限
- 查看任务详情权限
- 编辑任务权限

## 4. 数据统计
- 任务完成率统计
- 触达成功率统计
- 商家响应情况统计

## 5. 系统要求
- **状态实时更新**：任务状态需实时反映
- **数据准确性**：确保触达数据准确记录
- **响应时间**：页面加载和操作响应需在3秒内完成

## 6. 其他要求
- 支持数据导出
- 支持批量操作
- 提供操作日志记录

## 7. API
- 搜索任务：/xianfu/api-v2/dove/task/search
- 沟通记录查询：/xianfu/api-v2/dove/data/query
- 任务统计：/xianfu/api-v2/dove/task/statistic
- 创建任务：/xianfu/api-v2/dove/task/create
- 取消任务：/xianfu/api-v2/dove/task/cancel

## 8. API接口类型定义

```typescript
// 1. 任务搜索接口
interface TaskSearchRequest {
  taskName: string; // 任务名称
  creatorUid: number[]; // 创建人ID列表
  taskType: string; // 任务类型
  taskStatus: number[]; // 任务状态列表
  taskStartTimeMin: number; // 任务开始时间最小值
  taskStartTimeMax: number; // 任务开始时间最大值
  createTimeMin: number; // 创建时间最小值
  createTimeMax: number; // 创建时间最大值
  creatorOrgId: number[]; // 创建人组织ID列表
  page: number; // 页码
  pageSize: number; // 每页条数
}

interface TaskSearchResponse {
  page: number;
  pageSize: number;
  total: number;
  data: Array<{
    id: number;
    bizId: number;
    taskName: string;
    taskType: string;
    poiDto: {
      contactObjectType: number;
      contactObjectIdList: number[];
      kpPriority: string[];
      contactTag: number;
    };
    channelDto: {
      contactType: number;
      agentId: number;
      contactContent: string;
    };
    contactTime: {
      type: number;
      startTime: string;
      endTime: string;
    };
    taskWorkStrategy: {
      type: number;
      startTime: number;
      endTime: number;
    };
    creator: {
      mis: string;
      name: string;
      id: number;
    };
    createTime: number;
    updateTime: number;
    statisticDto: TaskStatisticData;
  }>;
}

// 2. 沟通记录查询接口
interface DataQueryRequest {
  bizId: number[]; // 业务ID列表
  orgId: number[]; // 组织ID列表
  taskId: number[]; // 任务ID列表
  staffId: number; // 员工ID
  contactObjectType: number[]; // 联系对象类型列表
  submitTimeMin: number; // 提交时间最小值
  submitTimeMax: number; // 提交时间最大值
  contactType: number[]; // 联系类型列表
  reachStatus: number[]; // 触达状态列表
  startTimeMin: number[]; // 开始时间最小值
  startTimeMax: number[]; // 开始时间最大值
  executionStatus: number[]; // 执行状态列表
  rank: string[]; // 等级列表
  page: number; // 页码
  pageSize: number; // 每页条数
}

interface DataQueryResponse {
  page: number;
  pageSize: number;
  total: number;
  data: Array<{
    contactId: number;
    objectName: string;
    objectType: number;
    contactType: number;
    bizId: number;
    submitTime: number;
    startTime: number;
    talkingTimeLen: number;
    rank: string;
    rankDesc: string;
    audio: string;
    releaseReasonMsg: string;
    staff: {
      mis: string;
      name: string;
      id: number;
    };
    reachStatus: number;
    taskId: number;
    taskName: string;
    contactContent: string;
    executionStatus: number;
    skillExecutionList: Array<{
      skillName: string;
      executed: boolean;
    }>;
  }>;
}

// 3. 任务统计接口
interface TaskStatisticRequest {
  taskId: number; // 任务ID
}

interface TaskStatisticResponse {
  taskProgress: {
    waitingCount: number; // 待执行数量
    doingCount: number; // 执行中数量
    doneCount: number; // 已完成数量
    cancelCount: number; // 已取消数量
  };
  contactSuccessCount: number; // 触达成功数量
  contactFailCount: number; // 触达失败数量
  executionAvgTime: number; // 平均执行时长
  successTag: Array<{
    tagCode: string;
    tagName: string;
    count: number;
  }>;
  failTag: Array<{
    tagName: string;
    count: number;
  }>;
}
```