// 单条任务详情数据类型
export interface TaskData {
    id: number;
    bizId: number;
    bizName: string;
    taskName: string;
    taskType: string;
    reachType: number;
    taskStatus: number;
    processId: number;
    processName: string;
    sceneId: number;
    agent: string; //关联agent（各类下关联agent的统一字段）
    agentType: string; //关联agent类型（各类下的统一字段）
    bpmUrl?: string; //审批链接
    poiDto: {
        contactObjectType: number;
        contactObjectIdList: number[];
        kpPriority: string[];
        contactTag: number;
        contactObjectTypeName: string;
    };
    channelDto: {
        contactType: number;
        agentId: number;
        contactContent: string;
        massSendTitle: string;
        contactImages: string;
        concatAgentTypeName: string;
    };
    taskWorkStrategy: {
        type: number;
        startTime: number;
        endTime: number;
    };
    creator: {
        mis: string;
        name: string;
        id: number;
        orgName: string; //组织架构任务
    };
    createTime: number;
    updateTime: number;
    statisticDto: {
        taskProgress: {
            waitingCount: number;
            doingCount: number;
            doneCount: number;
            cancelCount: number;
        };
        contactSuccessCount: number;
        contactFailCount: number;
        executionAvgTime: number;
        successTag: {
            tagCode: string;
            tagName: string;
            count: number;
        }[];
        failTag: {
            tagCode: string;
            tagName: string;
            count: number;
        }[];
    };
}

// 任务统计数据类型
export interface TaskStatisticData {
    scheduleProgress: {
        totalCount: number; // 总数量
        aiExecutionCount: number; // AI执行中数量
        aiDoneCount: number; // AI已完成数量
        telesaleExecutionCount: number; // 电销执行中数量
        telesaleDoneCount: number; // 电销已完成数量
        bdCount: number; // BD数量
    };
    // 任务执行进度数据
    taskProgress: {
        waitingCount: number; // 待执行 数量
        doingCount: number; // 执行中 数量
        doneCount: number; // 已执行 数量
        cancelCount: number; // 已取消 数量
    };
    contactSuccessCount: number; // 触达成功数量
    contactFailCount: number; // 触达失败数量
    executionAvgTime: number; // 平均执行时长
    // 成功意向数据
    successTag: {
        tagCode: string; // 标签编码
        tagName: string; // 标签名称
        count: number; // 数量
    }[];
    // 失败原因数据
    failTag: {
        tagCode: string;
        tagName: string;
        count: number;
    }[];
}

// 【触达类型】为"智能调度"时，任务明细表格数据类型
export interface TaskList2Data {
    contactId: number;
    objectName: string; // 门店名称
    objectId: number; // 门店ID
    phoneNum: string; // 电话号码
    objectType: number;
    contactType: number;
    bizId: number;
    submitTime: number;
    startTime: number;
    talkingTimeLen: number;
    rank: string;
    rankDesc: string;
    audio: string;
    releaseReasonMsg: string;
    staff: {
        mis: string;
        name: string;
        id: number;
    };
    reachStatus: number;
    taskId: number;
    taskName: string;
    contactContent: string;
    executionStatus: number;
    skillExecutionList: {
        skillName: string;
        executed: boolean;
    }[];
    processStatus: number; // 流程节点
    processStatusName: string;
    latestCallTime: number; // 上次沟通时间
    tag: string; // 意向标签
    tagName: string; // 意向标签名称
    summarizing: string[]; // 总结归纳
    processInstanceId: number;
}

// 沟通记录表格单条数据类型
export interface CallLogData {
    contactId: number; // 主键id
    startTime: number; // 开始时间
    callType: number; // 呼叫类型
    callTypeName: string; // 呼叫类型名称
    connected: number; // 是否接通，0-未接通，1-已接通
    staffName: string; // 呼叫人
    message: CallMessage[]; // 沟通内容
}
// 沟通记录详情数据类型
export interface CallLogDetailData {
    contactId: number; // 主键id
    startTime: number; // 开始时间
    audioUrl: string; // 音频URL
    talkingTimeLen: number; // 沟通时长
    callType: number; // 呼叫类型
    callTypeName: string; // 呼叫类型名称
    connected: number; // 是否接通，0-未接通，1-已接通
    staffName: string; // 呼叫人
    message: CallMessage[]; // 沟通内容
}
// 单次说话数据
export interface CallMessage {
    startTime: number;
    endTime: number;
    speaker: number;
    text: string;
}
