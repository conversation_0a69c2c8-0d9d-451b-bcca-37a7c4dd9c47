import React from 'react';
import moment from 'moment';
import AudioPlayer from '@mtfe/audio-player';
import queryString from 'query-string';
import { Modal as RooModal, Tabs as ATabs, message, Card } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';

const { TabPane } = ATabs;

import { Modal, Tabs, Table, Pagination, Button } from 'antd';

import '@mtfe/audio-player/dist/index.css';

import './index.scss';
import FloatingAudioPlayer from './FloatingAudioPlayer';
import extractTextFromAudio from './extractTextFromAudio';
import AudioDrawer from '../../components/TaskReachDetail/AudioDrawer';

const CALL_STATUS_MAP = {
    READ: 1, // 已读
    UNREAD: 0, // 未读
    CONNECTED: 1, // 已接通
    UNCONNECTED: 0, // 未接通
    WAITING: 3, // 待外呼
    CALLING: 2, // 进行中
    READ_NOT_CALL: 105, // 无需外呼
};

// 测试页面 http://localhost:8808/dove/autoCallShopDetail.html?taskId=480&isBd=false
class AutoCallShopDetail extends React.Component {
    constructor(props) {
        super(props);
        const query = queryString.parse(window.location.search.substr(1));
        this.sendTimeTypeOptions = [
            {
                value: 1,
                name: '外呼前30分钟',
            },
            {
                value: 2,
                name: '外呼后立即发送',
            },
        ];
        this.sendTargetOptions = [
            {
                value: 1,
                name: '全部商家',
            },
            {
                value: 2,
                name: '未接通商家',
            },
            {
                value: 3,
                name: '已接通商家',
            },
        ];

        this.state = {
            loading: false,
            taskId: query.taskId,
            drawerParams: {
                visible: false,
                audioUrl: undefined,
                contactId: undefined,
            },
            currentStatusMap: {
                callStatus: -1,
                imRead: -1,
                intention: -1,
            },
            tabPaneOptions: [
                {
                    label: '外呼状态',
                    key: 'callStatus',
                    options: [
                        {
                            value: -1,
                            name: '全部',
                            initName: '全部',
                        },
                        {
                            value: CALL_STATUS_MAP.CONNECTED,
                            name: '已接通',
                            initName: '已接通',
                        },
                        {
                            value: CALL_STATUS_MAP.UNCONNECTED,
                            name: '未接通',
                            initName: '未接通',
                        },
                        {
                            value: CALL_STATUS_MAP.WAITING,
                            name: '待外呼',
                            initName: '待外呼',
                        },
                        {
                            value: CALL_STATUS_MAP.CALLING,
                            name: '进行中',
                            initName: '进行中',
                        },
                    ],
                },
                {
                    label: '阅读状态',
                    key: 'imRead',
                    options: [
                        {
                            value: -1,
                            name: '全部',
                            initName: '全部',
                        },
                        {
                            value: CALL_STATUS_MAP.READ,
                            name: '已读',
                            initName: '已读',
                        },
                        {
                            value: CALL_STATUS_MAP.UNREAD,
                            name: '未读',
                            initName: '未读',
                        },
                    ],
                },
                {
                    label: '意向度',
                    key: 'intention',
                    options: [
                        {
                            value: -1,
                            name: '全部',
                            initName: '全部',
                        },
                    ],
                },
            ],
            columns: [
                {
                    title: '商家名称',
                    field: 'poiName',
                    render: r =>
                        r.poiId ? `${r.poiName}(${r.poiId})` : r.poiName,
                },
                {
                    title: '外呼时间',
                    field: 'callTime',
                    render: r => r.callTime,
                },
                {
                    title: '通话时长',
                    field: 'callDuration',
                    render: r => r.callDuration,
                },
                {
                    title: '未接原因',
                    field: 'failReason',
                    render: r => (r.callDuration ? '-' : r.failReason),
                },
                {
                    title: 'IM消息阅读状态',
                    field: 'readDesc',
                    render: r => r.readDesc,
                },
                {
                    title: '意向度',
                    field: 'intention',
                    render: r => r.intention,
                },
                {
                    title: '意向标签',
                    field: 'intentionTag',
                    render: r => r.intentionTag,
                },
                {
                    title: '操作',
                    field: 'audioUrl',
                    render: r => {
                        if (r.audioUrl) {
                            return (
                                <div>
                                    <a
                                        style={{ marginRight: 10 }}
                                        onClick={this.handlePlayAudio(
                                            r.audioUrl,
                                            r.contactId,
                                        )}
                                    >
                                        听录音
                                    </a>
                                    {/* <a onClick={this.extractText(r.audioUrl)}>
                                        语音转文字
                                    </a> */}
                                </div>
                            );
                        }

                        return <span> - </span>;
                    },
                },
            ],
            tableList: [],
            pageSize: query.pageSize ? Number(query.pageSize) : 20,
            pageNum: 1,
            totalNum: 0,
            taskInfo: {},
            showTabId: query.showTabId || '1',
            hasSpread: 0,
            audioInfo: {
                src: '',
                isVisible: false,
            },
            extractLoading: false,
        };
    }

    componentWillMount() {
        this.getTaskInfo();
        this.getReadyData();
    }

    componentDidMount() {
        const { taskId } = this.state;
        // 详情页埋点请求
        apiCaller.post('/doveauto/pc/callPoi/w/visitDetail', {
            visitCode: 1,
            taskId,
        });
        setTimeout(() => {
            if (document.getElementById('page-content')) {
                document.getElementById('page-content').style.display = 'block';
            }
        }, 300);
    }

    getReadyData(isGetCallPoiListStat) {
        let { taskId, tabPaneOptions, pageNum, pageSize, currentStatusMap } =
            this.state;
        const requestList = [
            apiCaller.post('/doveauto/pc/callPoi/r/getCallPoiListStat', {
                taskId,
                pageNum,
                pageSize,
                callStatus:
                    currentStatusMap.callStatus === -1 ||
                    currentStatusMap.callStatus === '-1'
                        ? null
                        : Number(currentStatusMap.callStatus),
                imRead:
                    currentStatusMap.imRead === -1 ||
                    currentStatusMap.imRead === '-1'
                        ? null
                        : Number(currentStatusMap.imRead),
                intention:
                    currentStatusMap.intention === -1 ||
                    currentStatusMap.intention === '-1'
                        ? null
                        : currentStatusMap.intention,
            }),
        ];

        if (!isGetCallPoiListStat) {
            requestList.push(
                apiCaller.get('/doveauto/pc/callPoi/r/getIntentionList', {
                    taskId,
                }),
            );
        }

        Promise.all(requestList)
            .then(res => {
                const callNumListRes = (res && res[0]) || {};
                const intentionListRes = (res && res[1]) || {};

                if (intentionListRes.code === 0 && !isGetCallPoiListStat) {
                    const { intentionList = [] } = intentionListRes.data || {};
                    const intentionOptions = intentionList.map(i => ({
                        value: i,
                        name: i,
                        initName: i,
                    }));

                    tabPaneOptions = tabPaneOptions.map(i => {
                        if (i.key === 'intention') {
                            i.options = i.options.concat(intentionOptions);
                        }
                        return i;
                    });
                }

                if (callNumListRes.code === 0) {
                    const {
                        callStatusCountMap = {},
                        imReadCountMap = {},
                        intentionCountMap = {},
                    } = callNumListRes.data || {};

                    tabPaneOptions = tabPaneOptions.map(i => {
                        const countMap =
                            i.key === 'callStatus'
                                ? callStatusCountMap
                                : i.key === 'imRead'
                                ? imReadCountMap
                                : intentionCountMap;

                        i.options = i.options.map(j => {
                            let name = `${j.initName}(${
                                countMap[j.value] || 0
                            })`;
                            if (j.value === -1) {
                                // 全部，累加count
                                let allCount = 0;
                                Object.keys(countMap).forEach(k => {
                                    allCount += countMap[k] || 0;
                                });
                                name = `${j.initName}(${allCount})`;
                            }

                            return {
                                ...j,
                                name,
                            };
                        });

                        return i;
                    });
                }

                this.setState(
                    {
                        tabPaneOptions,
                    },
                    () => {
                        this.onSearch();
                    },
                );
            })
            .catch(e => {});
    }

    getTaskInfo() {
        const { taskId } = this.state;
        apiCaller
            .get('/doveauto/pc/callPoi/r/getCallPoiTaskInfo', { taskId })
            .then(res => {
                if (res && res.code === 0) {
                    const normalName = res.data.poiNameList
                        .map(a => a.wmPoiName)
                        .join(',');
                    const hasSpread = normalName.length > 50 ? 1 : 0;
                    this.setState({
                        taskInfo: res.data || {},
                        hasSpread,
                    });
                }
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
            });
    }

    onSearch() {
        const { taskId, currentStatusMap, pageNum, pageSize } = this.state;
        this.setState({
            loading: true,
        });

        apiCaller
            .post('/doveauto/pc/callPoi/r/getCallPoiListV2', {
                callStatus:
                    currentStatusMap.callStatus === -1 ||
                    currentStatusMap.callStatus === '-1'
                        ? null
                        : Number(currentStatusMap.callStatus),
                imRead:
                    currentStatusMap.imRead === -1 ||
                    currentStatusMap.imRead === '-1'
                        ? null
                        : Number(currentStatusMap.imRead),
                intention:
                    currentStatusMap.intention === -1 ||
                    currentStatusMap.intention === '-1'
                        ? null
                        : currentStatusMap.intention,
                taskId,
                pageSize,
                pageNum,
            })
            .then(res => {
                if (res && res.code === 0) {
                    const data = res.data || {};
                    const total = data.total || 0;

                    this.setState({
                        tableList: data.callPoiListRespV2DtoList || [],
                        totalNum: total,
                        loading: false,
                    });
                } else {
                    this.setState({ loading: false });
                }
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
                this.setState({ loading: false });
            });
    }

    changeState(name, val) {
        this.setState({
            [name]: val,
        });
    }

    renderInfo() {
        const { taskInfo, hasSpread } = this.state;
        let statusName = '';
        if (taskInfo.taskStatus === 0) statusName = '待开始';
        if (taskInfo.taskStatus === 1) statusName = '进行中';
        if (taskInfo.taskStatus === 2) statusName = '已完成';
        if (taskInfo.taskStatus === 5) statusName = '暂停中';
        let normalName = '';
        let ellipsisName = '';
        if (taskInfo.poiNameList) {
            normalName = taskInfo.poiNameList.map(a => a.wmPoiName).join(',');
            if (normalName.length > 50) {
                ellipsisName = `${normalName.substr(0, 46)}...`;
            }
        }

        taskInfo.imItems = (taskInfo.imItems || []).map(i => {
            let sendTimeTypeStr = this.sendTimeTypeOptions.filter(
                j => j.value === i.sendTimeType,
            );
            sendTimeTypeStr = sendTimeTypeStr[0] && sendTimeTypeStr[0].name;
            let sendTargetStr = this.sendTargetOptions.filter(
                j => j.value === i.sendTarget,
            );
            sendTargetStr = sendTargetStr[0] && sendTargetStr[0].name;
            return {
                ...i,
                sendTimeTypeStr,
                sendTargetStr,
            };
        });

        const { callMode } = taskInfo;

        return (
            <div>
                <div className="info-title">外呼消息</div>
                <div className="search-form">
                    <div className="form-item">
                        <span className="form-label">*任务名称：</span>
                        <div className="form-value">
                            {taskInfo.taskName || '-'}
                        </div>
                    </div>
                    <div className="form-item">
                        <span className="form-label">状态：</span>
                        <div className="form-value">{statusName}</div>
                    </div>

                    <div className="form-item">
                        <span className="form-label">*被叫商家明细：</span>
                        {
                            // eslint-disable-next-line no-nested-ternary
                            hasSpread === 1 ? (
                                <div className="form-value">
                                    {ellipsisName}
                                    <span
                                        style={{
                                            color: '#00abe4',
                                            cursor: 'pointer',
                                        }}
                                        onClick={() => {
                                            this.setState({
                                                hasSpread: 2,
                                            });
                                        }}
                                    >
                                        展开
                                    </span>
                                </div>
                            ) : hasSpread === 2 ? (
                                <div className="form-value">
                                    {normalName}
                                    <span
                                        style={{
                                            color: '#00abe4',
                                            cursor: 'pointer',
                                        }}
                                        onClick={() => {
                                            this.setState({
                                                hasSpread: 1,
                                            });
                                        }}
                                    >
                                        收起
                                    </span>
                                </div>
                            ) : (
                                <div className="form-value">{normalName}</div>
                            )
                        }
                    </div>

                    <div className="form-item">
                        <span className="form-label">*被叫数量：</span>
                        <div className="form-value">
                            {taskInfo.callPoiTotal || '-'}
                        </div>
                    </div>

                    <div className="form-item">
                        <span className="form-label">*定时重复任务：</span>
                        <div className="form-value">
                            {taskInfo.repeat ? '是' : '否'}
                        </div>
                    </div>

                    <div className="form-item">
                        <span className="form-label">*重复时间：</span>
                        <div className="form-value">
                            {taskInfo.repeatTime || '-'}
                        </div>
                    </div>

                    <div className="form-item">
                        <span className="form-label">*外呼开始时间：</span>
                        <div className="form-value">
                            {taskInfo.callTime
                                ? moment
                                      .unix(taskInfo.callTime)
                                      .format('YYYY.MM.DD HH:mm')
                                : '-'}
                        </div>
                    </div>
                    <div className="form-item">
                        <span className="form-label">*外呼结束时间：</span>
                        <div className="form-value">
                            {taskInfo.endTime
                                ? moment
                                      .unix(taskInfo.endTime)
                                      .format('YYYY.MM.DD HH:mm')
                                : '-'}
                        </div>
                    </div>
                    <div className="form-item">
                        <span className="form-label">*外呼类型：</span>
                        <div className="form-value">
                            {taskInfo.callTypeName
                                ? taskInfo.callTypeName
                                : '-'}
                        </div>
                    </div>
                    <div className="form-item">
                        <span className="form-label">*创建人：</span>
                        <div className="form-value">
                            {taskInfo.createName || ''}
                        </div>
                    </div>
                    <div className="form-item">
                        <span className="form-label">*创建时间：</span>
                        <div className="form-value">
                            {taskInfo.ctime
                                ? moment
                                      .unix(taskInfo.ctime)
                                      .format('YYYY.MM.DD HH:mm')
                                : '-'}
                        </div>
                    </div>
                    {callMode === 0 && (
                        <div className="form-item">
                            <span className="form-label">*外呼内容：</span>
                            <div className="form-value" style={{ flex: 1 }}>
                                {taskInfo.callText || '-'}
                            </div>
                        </div>
                    )}
                    {callMode === 1 && (
                        <div className="form-item">
                            <span className="form-label">*AI外呼模板：</span>
                            <div className="form-value" style={{ flex: 1 }}>
                                {taskInfo.callTemplateDesc || '-'}
                            </div>
                        </div>
                    )}
                    {taskInfo.mp3Url ? (
                        <div className="form-item">
                            <span className="form-label">*外呼语音：</span>
                            <div className="form-value">
                                <AudioPlayer
                                    src={taskInfo.mp3Url}
                                    extractTextConfig={{
                                        clientId:
                                            'i5FhcvEjZw9FN8pI6WKrsTbe/GvgOk0Qyan0PBUrRAo=',
                                        clientSecret:
                                            '9ebda733af6843b9922f3e6ecf4f6e23',
                                    }}
                                    onExtractText={texts => {
                                        RooModal.success({
                                            title: '对话内容',
                                            width: '40%',
                                            content: (
                                                <div
                                                    style={{
                                                        height: '60vh',
                                                        overflow: 'scroll',
                                                    }}
                                                >
                                                    {texts
                                                        .filter(
                                                            ({ channel }) =>
                                                                channel === 0,
                                                        )
                                                        .map(line => (
                                                            <div>
                                                                <div
                                                                    style={{
                                                                        fontSize:
                                                                            '12px',
                                                                        color: 'grey',
                                                                    }}
                                                                >
                                                                    <span>{`00:00:${parseFloat(
                                                                        line.start,
                                                                    ).toFixed(
                                                                        2,
                                                                    )}`}</span>
                                                                    <span
                                                                        style={{
                                                                            paddingLeft:
                                                                                '20px',
                                                                        }}
                                                                    >
                                                                        {
                                                                            [
                                                                                '讲话人A',
                                                                                '讲话人B',
                                                                            ][
                                                                                line
                                                                                    .channel
                                                                            ]
                                                                        }
                                                                    </span>
                                                                </div>
                                                                <div
                                                                    style={{
                                                                        paddingTop:
                                                                            '4px',
                                                                        paddingBottom:
                                                                            '4px',
                                                                        paddingLeft:
                                                                            '94px',
                                                                    }}
                                                                >
                                                                    <span>
                                                                        {
                                                                            line.text
                                                                        }
                                                                    </span>
                                                                </div>
                                                            </div>
                                                        ))}
                                                </div>
                                            ),
                                            okText: '好的',
                                        });
                                    }}
                                />
                            </div>
                        </div>
                    ) : null}
                    <div className="form-item" />
                </div>
                <div className="row-action">
                    {(taskInfo.taskStatus === 0 || taskInfo.taskStatus === 1) &&
                    taskInfo.repeat ? (
                        <Button
                            style={{ marginRight: '10px' }}
                            className="delete_btn"
                            onClick={() => this.onSuspend()}
                        >
                            暂停
                        </Button>
                    ) : null}
                    {taskInfo.taskStatus === 5 && taskInfo.repeat ? (
                        <Button
                            style={{ marginRight: '10px' }}
                            className="delete_btn"
                            onClick={() => this.onStart()}
                        >
                            开启
                        </Button>
                    ) : null}
                </div>

                <div className="im-wrap split-line">
                    <div className="info-title">信鸽im消息</div>
                    <div className="im-detail">
                        {taskInfo.imItems && taskInfo.imItems.length ? (
                            taskInfo.imItems.map(i => (
                                <div className="im-detail-item">
                                    <div className="im-item-left">
                                        <div className="im-item-time im-item-item">
                                            <span className="im-item-label">
                                                发送时间：
                                            </span>
                                            <span className="im-item-value">
                                                {i.sendTimeTypeStr}
                                            </span>
                                        </div>
                                        <div className="im-item-content im-item-item">
                                            <span className="im-item-label">
                                                发送内容：
                                            </span>
                                            <span className="im-item-value">
                                                {i.imMessage}
                                            </span>
                                        </div>
                                    </div>
                                    <div className="im-item-right">
                                        <div className="im-item-obj im-item-item">
                                            <span className="im-item-label">
                                                发送对象：
                                            </span>
                                            <span className="im-item-value">
                                                {i.sendTargetStr}
                                            </span>
                                        </div>
                                        <div className="im-item-img im-item-item">
                                            <span className="im-item-label">
                                                发送图片：
                                            </span>
                                            {i.picUrl ? (
                                                <img
                                                    onClick={() => {
                                                        RooModal.info({
                                                            title: '查看图片',
                                                            width: '80%',
                                                            centered: true,
                                                            maskClosable: true,
                                                            className:
                                                                'picPreview',
                                                            content: (
                                                                <div>
                                                                    {
                                                                        <img
                                                                            src={
                                                                                i.picUrl
                                                                            }
                                                                            style={{
                                                                                width: '100%',
                                                                            }}
                                                                        />
                                                                    }
                                                                </div>
                                                            ),
                                                            okText: '关闭',
                                                        });
                                                    }}
                                                    src={i.picUrl}
                                                    className="im-img"
                                                />
                                            ) : (
                                                <span className="im-item-value">
                                                    无
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            ))
                        ) : (
                            <div>无</div>
                        )}
                    </div>
                </div>
            </div>
        );
    }

    handleTabsChange(val, type) {
        const { currentStatusMap } = this.state;
        currentStatusMap[type] = val;

        this.setState(
            {
                currentStatusMap,
                pageNum: 1,
            },
            () => {
                this.getReadyData(true); // 搜索项联动，需要重新拉取 getCallPoiListStat 接口
                // this.onSearch();
            },
        );
    }

    extractText = url => () => {
        this.handleCloseAudio();
        this.setState(
            {
                extractLoading: true,
            },
            async () => {
                try {
                    await extractTextFromAudio(url);
                } catch (error) {
                    console.error(error);
                    RooModal.error({
                        title: '音频解析失败',
                    });
                } finally {
                    this.setState({
                        extractLoading: false,
                    });
                }
            },
        );
    };

    renderTable() {
        const {
            loading,
            tableList,
            columns,
            pageNum,
            pageSize,
            totalNum,
            tabPaneOptions,
        } = this.state;

        return (
            <div>
                {tabPaneOptions.map(i => (
                    <div key={i.key} className="tab-item-wrap">
                        <span style={{ width: 70 }}>{i.label}:</span>
                        <ATabs
                            type="card"
                            defaultActiveKey="-1"
                            onChange={val => this.handleTabsChange(val, i.key)}
                        >
                            {i.options.map(j => (
                                <TabPane tab={j.name} key={j.value} />
                            ))}
                        </ATabs>
                    </div>
                ))}
                <div style={{ marginTop: 20 }}>
                    <a style={{ cursor: 'text' }}>
                        注：使用AI电话才会展示意向度和意向标签；A级意向度最高；不同AI外呼模板的意向标签可能会不同
                    </a>
                </div>
                <div className="detail-table">
                    <Table
                        enableBorder
                        enableLoading={loading}
                        dataSource={tableList}
                        columns={columns}
                        loading={loading}
                        pagination={{
                            pageSize,
                            total: totalNum,
                            onChange: (pageNum, pageSize) => {
                                this.setState(
                                    {
                                        pageNum,
                                        loading: true,
                                    },
                                    () => {
                                        this.onSearch();
                                    },
                                );
                            },
                        }}
                    />
                </div>
            </div>
        );
    }

    onSuspend = () => {
        const { taskInfo } = this.state;
        apiCaller
            .post('/doveauto/pc/callPoi/w/taskSuspended', {
                taskId: taskInfo.taskId,
            })
            .then(res => {
                if (res && res.code === 0) {
                    message.success('暂停成功！');
                    window.location.reload();
                }
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
                this.setState({ loading: false });
            });
    };
    onStart = () => {
        const { taskInfo } = this.state;
        apiCaller
            .post('/doveauto/pc/callPoi/w/taskActivate', {
                taskId: taskInfo.taskId,
            })
            .then(res => {
                if (res && res.code === 0) {
                    message.success('开启成功！');
                    window.location.reload();
                }
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
                this.setState({ loading: false });
            });
    };

    handlePlayAudio = (src, contactId) => () => {
        this.setState({
            drawerParams: {
                visible: true,
                audioUrl: src,
                contactId,
            },
            // audioInfo: {
            //     src,
            //     isVisible: true,
            // },
        });
    };

    handleCloseAudio = () => {
        this.setState({
            audioInfo: {
                src: '',
                isVisible: false,
            },
        });
    };

    render = () => {
        const { showTabId, audioInfo, extractLoading } = this.state;
        const crumbItems = [
            {
                text: '信鸽',
            },
            {
                text: '自动外呼商家',
                href: 'autoCallShop.html',
            },
            {
                text: '查看外呼商家任务',
                active: true,
            },
        ];
        return (
            <Card enableHeader={false}>
                <AudioDrawer
                    {...this.state.drawerParams}
                    onClose={() => {
                        this.setState({
                            drawerParams: {
                                visible: false,
                                audioUrl: undefined,
                                contactId: undefined,
                            },
                        });
                    }}
                />
                {/* <Breadcrumb items={crumbItems} /> */}
                <Card enableHeader={false}>
                    <Tabs
                        selectedId={showTabId}
                        activeKey={showTabId}
                        items={[
                            {
                                key: '1',
                                label: '任务详情',
                            },
                            {
                                key: '2',
                                label: '外呼记录',
                            },
                        ]}
                        onChange={selected => {
                            this.setState(
                                {
                                    showTabId: selected,
                                },
                                () => {
                                    if (selected === '2') {
                                        this.onSearch();
                                    }
                                },
                            );
                        }}
                    />
                    {showTabId === '1' ? this.renderInfo() : this.renderTable()}
                </Card>
                <FloatingAudioPlayer
                    src={audioInfo.src}
                    isVisible={audioInfo.isVisible}
                    onClose={this.handleCloseAudio}
                />
                <Modal
                    key="confirm"
                    ref={ref => {
                        this._confirmModal = ref;
                    }}
                />
                <RooModal
                    open={extractLoading}
                    title={null}
                    maskClosable={false}
                    closable={false}
                    footer={null}
                    okButtonProps={{ style: { display: 'none' } }}
                >
                    <div style={{ fontSize: 18, color: 'black' }}>
                        音频解析中, 请稍后...
                    </div>
                </RooModal>
            </Card>
        );
    };
}
export default AutoCallShopDetail;
