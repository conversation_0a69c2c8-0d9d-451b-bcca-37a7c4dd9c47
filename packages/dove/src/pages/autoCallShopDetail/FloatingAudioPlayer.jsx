import React from 'react';
import AudioPlayer from '@mtfe/audio-player';
import classNames from 'classnames';
import { CloseOutlined } from '@ant-design/icons';

import './FloattingAudioPlayer.css';

export default React.memo(({ src, isVisible, onClose }) => (
    <div
        className={classNames(
            'floating-audio-player',
            isVisible && 'fap-side-up',
        )}
    >
        <div className="fap-container">
            <CloseOutlined onClick={onClose} className="fap-close-icon" />
            <AudioPlayer
                autoPlay
                style={{
                    width: 280,
                }}
                src={src}
            />
        </div>
    </div>
));
