.search-form {
    padding: 20px 0;
    .form-item {
        display: inline-block;
        width: 400px;
        padding: 20px 15px;
        & > div {
            display: inline-block;
            vertical-align: top;
            width: 65%;
            &.form-label {
                width: 100px;
                text-align: right;
            }
        }
        & > input {
            display: inline-block;
            vertical-align: middle;
            width: 65%;
        }
        &.sm-item {
            width: 200px;
        }
        .mrc-input-calendar {
            width: 115px;
        }
    }
}
.detail-table {
    padding: 20px;
}
.nav-cards {
    margin-top: 20px;
}

.info-title {
    font-size: 15px;
    font-weight: 500;
    margin-top: 20px;
}
.split-line {
    border-top: 1px solid #dedede;
}
.im-detail {
    display: flex;
    flex-wrap: wrap;
    .im-detail-item {
        display: flex;
        min-width: 500px;
        margin-right: 40px;
        margin-top: 10px;
        .im-item-left {
            width: 210px;
            margin-right: 20px;
        }
        .im-item-item {
            display: flex;
            margin-top: 10px;
            .im-item-label {
                width: 80px;
                color: #333;
            }
            .im-item-value {
                flex: 1;
            }
        }
        .im-img {
            cursor: pointer;
            width: 60px;
            height: 60px;
            border-radius: 50%;
        }
    }
}
.picPreview {
    .ant-modal-confirm-body {
        max-height: 80vh;
        overflow-y: scroll;
    }
}

.tab-item-wrap {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px dashed #ddd;
    .ant-tabs-bar {
        margin: 0;
    }
}