
.etfa-modal .ant-modal-close {
    display: block;
}


.etfa-container {
    height: 60vh;
    overflow: scroll;
    position: relative;
    padding: 0 16px;
    margin-left: -48px;
}

.etfa-container .play-audio-icon {
    margin: -2px 4px 0 0;
    vertical-align: middle;
    cursor: pointer;
}

.etfa-container .play-audio-icon:hover {
    fill: #1890ff;
}

.etfa-close-icon {
    position: absolute;
}

.etfa-container::-webkit-scrollbar {
    -webkit-appearance: none;
}

.etfa-container::-webkit-scrollbar:vertical {
    width: 11px;
}

.etfa-container::-webkit-scrollbar:horizontal {
    height: 11px;
}

.etfa-container::-webkit-scrollbar-thumb {
    border-radius: 8px;
    border: 2px solid white;
    /* should match background, can't be transparent */
    background-color: rgba(0, 0, 0, .5);
}

.etfa-container .basic {
    display: flex;
    padding: 10px 0;
}

.etfa-container .basic .empty {
    flex: 1
}

.etfa-container .basic .content {
    display: flex;
    flex: 1
}

.etfa-container .basic .content .avatar {
    padding: 0 10px;
}

.etfa-container .basic .content .avatar .avatar-circle {
    width: 30px;
    height: 30px;
    border-radius: 0px;
    background: #40a9ff;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 30px;
}

.etfa-container .basic.right .content {
    flex-direction: row-reverse;
}

.etfa-container .basic.right .content .info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}


.etfa-container .user-name {
    font-size: 12px;
    color: grey;
}


.etfa-container .basic.right .user-name {
    font-size: 12px;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
}

.etfa-container .user-name span+span {
    padding: 0 10px;
}

.etfa-container .message {
    padding: 4px 0 4px 0;
}
