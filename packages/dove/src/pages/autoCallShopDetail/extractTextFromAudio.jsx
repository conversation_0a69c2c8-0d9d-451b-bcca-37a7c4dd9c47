import React from 'react';
import {
    recognizeAsyncSpeechFragment,
    recognizeSpeechFragment,
} from '@mtfe/audio-player';
import { message, Modal as RooModal } from 'antd';
import classNames from 'classnames';

import './extractTextFromAudio.css';

const MESSAGE_POSITION = {
    LEFT: 1,
    RIGHT: 0,
};

const padTime = time => String(time).padStart(2, 0);

const parseTime = time => {
    const secNum = parseInt(time, 10);
    const hours = Math.floor(secNum / 3600);
    const minutes = Math.floor((secNum - hours * 3600) / 60);
    const seconds = secNum - hours * 3600 - minutes * 60;
    return `${padTime(hours)}:${padTime(minutes)}:${padTime(seconds)}`;
};

const ClientAvatar = () => (
    <svg viewBox="0 0 1024 1024" width="20" height="20">
        <path
            d="M487.334 487.118c-134.3 0-243.556-109.26-243.556-243.558C243.778 109.26 353.034 0 487.334 0s243.562 109.26 243.562 243.56c0 134.298-109.262 243.558-243.562 243.558z m0-471.024c-125.424 0-227.462 102.04-227.462 227.466s102.036 227.464 227.462 227.464S714.8 368.984 714.8 243.56 612.758 16.094 487.334 16.094z"
            fill="#4A555F"
        />
        <path
            d="M487.338 452.248c-115.072 0-208.69-93.618-208.69-208.688S372.266 34.872 487.338 34.872c115.066 0 208.684 93.618 208.684 208.688s-93.618 208.688-208.684 208.688z"
            fill="#FFD452"
        />
        <path
            d="M34.566 664.47l-13.978-7.978c2.54-4.456 5.172-8.86 7.874-13.214l13.674 8.492a399.382 399.382 0 0 0-7.57 12.7z m16.156-25.924l-13.328-9.03c3.076-4.532 6.23-9 9.472-13.402l12.962 9.546a397.572 397.572 0 0 0-9.106 12.886z m18.71-25.404l-12.574-10.052a437.176 437.176 0 0 1 10.484-12.612l12.176 10.52a414.86 414.86 0 0 0-10.086 12.144z m20.62-23.872l-11.756-10.996a431.782 431.782 0 0 1 11.442-11.762l11.316 11.442a402.22 402.22 0 0 0-11.002 11.316z m22.43-22.19l-10.856-11.88a422.328 422.328 0 0 1 12.318-10.834l10.394 12.288c-4.02 3.4-7.97 6.876-11.856 10.426z m24.104-20.378l-9.902-12.686a423.532 423.532 0 0 1 13.13-9.846l9.398 13.066a399.882 399.882 0 0 0-12.626 9.466z m25.61-18.434l-8.886-13.422a424.854 424.854 0 0 1 13.862-8.788l8.352 13.758a401.586 401.586 0 0 0-13.328 8.452z m26.964-16.368l-7.796-14.08a417.4 417.4 0 0 1 14.508-7.672l7.24 14.374a403.16 403.16 0 0 0-13.952 7.378z m28.172-14.202l-6.664-14.648a419.476 419.476 0 0 1 15.068-6.506l6.088 14.9a396.702 396.702 0 0 0-14.492 6.254z m29.224-11.932l-5.502-15.126c2.4-0.874 4.82-1.724 7.246-2.554a19.644 19.644 0 0 0 5.36-2.806l9.62 12.902a35.458 35.458 0 0 1-9.77 5.132 395.6 395.6 0 0 0-6.954 2.452z m28.872-24.18l-15.194-5.296a19.158 19.158 0 0 0 1.074-6.3 18.646 18.646 0 0 0-0.64-4.792l15.54-4.202c0.786 2.91 1.19 5.93 1.194 8.974a35.122 35.122 0 0 1-1.974 11.616z m-21.38-26.684a307.3 307.3 0 0 1-10.61-12.674l12.616-9.99a286.644 286.644 0 0 0 10.054 12.002l-12.06 10.662z m-20.512-25.922a299.684 299.684 0 0 1-9.158-13.78l13.654-8.526a279.476 279.476 0 0 0 8.656 13.026l-13.152 9.28z m-17.54-28.048a304.08 304.08 0 0 1-7.566-14.702l14.512-6.966a290.674 290.674 0 0 0 7.156 13.92l-14.102 7.748z m-14.298-29.814a295.034 295.034 0 0 1-5.878-15.468l15.194-5.296a279.132 279.132 0 0 0 5.564 14.622l-14.88 6.142z m-10.886-31.244c-1.52-5.32-2.902-10.712-4.108-16.032l15.696-3.554a276.302 276.302 0 0 0 3.888 15.156l-15.476 4.43zM183.498 287.6a306.522 306.522 0 0 1-2.284-16.38l16-1.782c0.572 5.13 1.294 10.348 2.158 15.51l-15.874 2.652z m-3.658-32.872a304.272 304.272 0 0 1-0.45-16.538h16.094c0 5.23 0.142 10.496 0.43 15.652l-16.074 0.886zM1003.412 1023.998h-16.094v-16.094h16.094v16.094z m0-32.188h-16.094v-16.094h16.094v16.094z m0-32.19h-16.094v-16.094h16.094v16.094z m0-32.188h-16.094v-16.094h16.094v16.094z m0-32.19h-16.094v-16.094h16.094v16.094z m-16.126-32.09a416.426 416.426 0 0 0-0.502-15.778l16.074-0.82c0.278 5.438 0.456 10.902 0.524 16.394l-16.096 0.204z m-1.618-31.512a392.26 392.26 0 0 0-1.74-15.674l15.958-2.098c0.712 5.406 1.316 10.846 1.812 16.316l-16.03 1.456z m-4.082-31.284c-0.89-5.198-1.88-10.362-2.97-15.49l15.748-3.342a408.674 408.674 0 0 1 3.086 16.122l-15.864 2.71z m-6.548-30.852a398.538 398.538 0 0 0-4.186-15.214l15.434-4.572a427.558 427.558 0 0 1 4.354 15.818l-15.602 3.968zM944.4 1024h-16.094v-155.848c0-191.542-155.832-347.372-347.374-347.372h-187.188c-191.542 0-347.374 155.83-347.374 347.372V1024h-16.094v-155.848c0-200.416 163.052-363.468 363.468-363.468h187.188c200.416 0 363.468 163.05 363.468 363.468V1024z"
            fill="#4A555F"
        />
        <path
            d="M580.948 539.564h-187.232c-181.17 0-328.598 147.426-328.598 328.598V1024h844.426v-155.838c0.002-181.172-147.424-328.598-328.596-328.598z m-215.904 266.74l56.384-23.016a35.054 35.054 0 0 1 13.198-2.576c14.27 0 26.986 8.532 32.35 21.782l9.496 23.23 5.74 13.788c17.596-6.118 43.508-26.34 57.994-40.558 17.222-17.488 35.194-42.274 40.826-58.206l-13.68-5.688-23.39-9.55c-17.866-7.244-26.45-27.68-19.206-45.546l23.016-56.386c5.364-13.198 18.078-21.726 32.35-21.726 4.506 0 8.958 0.856 13.198 2.574l23.336 9.548c8.37 3.434 45.708 20.922 56.278 63.522 11.804 47.854-15.718 105.74-81.922 172.05-56.544 56.652-107.082 85.356-150.216 85.356-60.568 0-82.994-53.488-85.462-59.928l-9.442-23.176c-7.242-17.812 1.34-38.252 19.152-45.494z"
            fill="#F58E6F"
        />
        <path
            d="M170.484 911.694h16.094V1024h-16.094zM792.622 911.694h16.094V1024h-16.094zM440.822 942.944c-66.018 0-90.428-58.396-92.994-65.054l-9.388-23.032c-8.938-21.95 1.646-47.082 23.586-56.028l56.388-22.974a42.78 42.78 0 0 1 16.22-3.182 42.834 42.834 0 0 1 39.818 26.776l12.07 29.48c14.974-7.434 35.024-22.918 48.042-35.722 14.57-14.778 28.83-34.102 35.84-48.152l-29.49-12.078c-21.946-8.944-32.53-34.078-23.592-56.03l22.978-56.384c6.632-16.264 22.256-26.774 39.812-26.774 5.584 0 11.038 1.07 16.22 3.182l23.392 9.53c0.278 0.114 0.544 0.242 0.806 0.382 9.786 4.15 48.98 23.362 60.192 68.662 12.568 50.79-15.696 111.238-84.014 179.668-58.11 58.21-110.558 87.726-155.886 87.73z m-6.188-154.178c-3.49 0-6.9 0.67-10.142 1.992l-56.388 22.974c-13.726 5.596-20.348 21.322-14.758 35.054l9.44 23.17c2.216 5.748 22.754 54.892 78.026 54.892v8.048l0.01-8.048c40.896-0.002 89.516-27.932 144.5-83.008 64.058-64.166 90.904-119.488 79.782-164.43-9.688-39.138-44.574-55.154-51.468-57.966a7.576 7.576 0 0 1-0.75-0.352l-22.628-9.218a26.734 26.734 0 0 0-10.142-1.992 26.806 26.806 0 0 0-24.912 16.754l-22.974 56.38c-5.59 13.734 1.026 29.46 14.758 35.054l37.13 15.27a8.044 8.044 0 0 1 4.484 10.108c-6.476 18.306-26.364 44.616-42.662 61.144-13.528 13.306-40.866 35.514-61.082 42.516a8.048 8.048 0 0 1-10.06-4.502l-15.256-37.086a26.794 26.794 0 0 0-24.908-16.754z"
            fill="#4A555F"
        />
        <path
            d="M440.814 908.072c35.234-0.002 80.61-26.8 131.224-77.496 58.258-58.356 84.134-109.068 74.84-146.656-7.502-30.322-34.918-42.878-40.336-45.088l-24.126-9.836a7.874 7.874 0 0 0-2.3-0.336c-3.368 0-6.25 1.938-7.518 5.058l-22.978 56.384c-1.094 2.692-0.434 5.044 0.052 6.208a8.086 8.086 0 0 0 4.4 4.372l23.398 9.532 37.282 15.586-8.45 23.908c-7.774 21.992-29.208 50.036-47 68.074-12.898 12.69-43.344 38.432-68.308 47.072l-23.812 8.242-15.482-37.046-9.546-23.452c-1.268-3.112-4.154-5.052-7.524-5.052-1.058 0-2.058 0.198-3.054 0.604l-56.388 22.974a8.124 8.124 0 0 0-4.454 10.58l9.442 23.172c1.87 4.828 17.922 43.196 60.638 43.196z"
            fill="#71CCE0"
        />
    </svg>
);

const RobotAvatar = () => (
    <svg viewBox="0 0 1024 1024" width="20" height="20">
        <path
            d="M412.8 331.2c-8 0-14.4-6.4-14.4-14.4v-13.6c0-8 6.4-14.4 14.4-14.4h200.8c8 0 14.4 6.4 14.4 14.4v13.6c0 8-6.4 14.4-14.4 14.4h-200.8zM308 693.2h402.4v170H308z"
            fill="#FFD552"
        />
        <path
            d="M801.2 1024H222.8V531.2h578.4V1024z m-566-12.4h553.2V544H235.2v467.6z"
            fill="#4A555F"
        />
        <path
            d="M250 558.4v438h524V558.4h-524z m481.2 326c0 8.8-3.6 16.4-9.2 22-5.6 5.6-13.6 9.2-22 9.2H318.4c-17.2 0-31.2-14-31.2-31.2v-212c0-8.8 3.6-16.4 9.2-22.4 5.6-5.6 13.6-9.2 22-9.2H700c17.2 0 31.2 14 31.2 31.2v212.4z"
            fill="#F68F6F"
        />
        <path
            d="M875.2 699.6h-86.8v-168.4h86.8c46.4 0 84 37.6 84 84s-37.2 84.4-84 84.4z m-74-12.4h74c39.6 0 71.6-32 71.6-71.6 0-39.6-32-71.6-71.6-71.6h-74v143.2z"
            fill="#4A555F"
        />
        <path
            d="M816 672.4v-114h59.6c31.2 0 56.8 25.6 56.8 56.8s-25.6 56.8-56.8 56.8H816z"
            fill="#71CDE1"
        />
        <path
            d="M621.6 483.6h-219.2c-42.4 0-76.8-34.4-76.8-76.8V271.6c0-42.4 34.4-76.8 76.8-76.8h219.2c42.4 0 76.8 34.4 76.8 76.8v135.2c-0.4 42.4-34.8 76.8-76.8 76.8z m-219.2-276c-35.2 0-64 28.8-64 64v135.2c0 35.2 28.8 64 64 64h219.2c35.2 0 64-28.8 64-64V271.6c0-35.2-28.8-64-64-64h-219.2z"
            fill="#4A555F"
        />
        <path
            d="M621.6 222.4h-219.2c-27.2 0-49.2 22-49.2 49.2v135.2c0 27.2 22 49.2 49.2 49.2h219.2c27.2 0 49.2-22 49.2-49.2V271.6c0-27.2-22-49.2-49.2-49.2z m27.2 94.4c0 19.6-15.6 35.2-35.2 35.2h-200.8c-19.6 0-35.2-15.6-35.2-35.2v-13.6c0-19.6 15.6-35.2 35.2-35.2h200.8c19.6 0 35.2 15.6 35.2 35.2v13.6z"
            fill="#71CDE1"
        />
        <path
            d="M543.6 544h-63.2v-72.8h63.2V544z m-50.4-12.8h38v-47.6h-38v47.6zM908 898.4l-52.4-211.2h20c12 0 24.4-3.2 34.8-9.2l7.2-4 52 208.8-61.6 15.6z m-36.4-198.8l45.6 183.2 36.8-9.2-45.2-181.2c-10.4 4.8-22 7.2-33.6 7.2h-3.6zM613.6 358.4h-200.8c-22.8 0-41.6-18.8-41.6-41.6v-13.6c0-22.8 18.8-41.6 41.6-41.6h200.8c22.8 0 41.6 18.8 41.6 41.6v13.6c0 23.2-18.8 41.6-41.6 41.6z m-200.8-83.6c-16 0-28.8 12.8-28.8 28.8v13.6c0 16 12.8 28.8 28.8 28.8h200.8c16 0 28.8-12.8 28.8-28.8v-13.6c0-16-12.8-28.8-28.8-28.8h-200.8z"
            fill="#4A555F"
        />
        <path
            d="M897.2 961.6l-10.4-42.4 22-34 26.8-6.8 26.8-6.8 35.6 20 10.4 42.4-12.4 3.2-9.2-37.2-26-14.8-22.4 5.6-21.6 5.2-16.4 25.6 9.2 36.8zM235.2 699.6H148.4c-46.4 0-84-37.6-84-84s37.6-84 84-84h86.8v168zM148.4 544c-39.6 0-71.6 32-71.6 71.6 0 39.6 32 71.6 71.6 71.6h74V544H148.4z"
            fill="#4A555F"
        />
        <path
            d="M148.4 672.4c-31.2 0-56.8-25.6-56.8-56.8s25.6-56.8 56.8-56.8H208v114H148.4z"
            fill="#71CDE1"
        />
        <path
            d="M116 898.4l-61.2-15.2 52-208.8 7.2 4c10.4 6 22.8 9.2 34.8 9.2h20L116 898.4z m-46-24.8l36.8 9.2 45.6-183.2h-3.6c-11.6 0-23.2-2.4-33.6-7.2l-45.2 181.2z"
            fill="#4A555F"
        />
        <path
            d="M126.8 961.6l-12.4-3.2 9.2-36.8-16.4-25.6-21.6-5.2-22.4-5.6-26 14.8-9.2 37.2-12.4-3.2 10.4-42.4 35.6-20 26.8 6.8 26.8 6.8 22 34zM548.4 155.6h-72.8v-36.4c0-20 16.4-36.4 36.4-36.4s36.4 16.4 36.4 36.4v36.4zM488 142.8h48v-24c0-13.2-10.8-24-24-24s-24 10.8-24 24v24z"
            fill="#4A555F"
        />
        <path
            d="M505.6 149.2h12.4v52.4h-12.4zM384.8 726.8h12.4V828h-12.4z"
            fill="#4A555F"
        />
        <path
            d="M339.6 720.8h103.6v12.4H339.6zM643.2 726.8h12.4V828h-12.4z"
            fill="#4A555F"
        />
        <path
            d="M597.6 720.8h103.6v12.4h-103.6zM563.728 722.56l8.768 8.768-101.228 101.284-8.768-8.764z"
            fill="#4A555F"
        />
        <path
            d="M471.464 722.528l101.2 101.312-8.772 8.764-101.2-101.312zM333.2 812.4h12.4v16h-12.4zM438 268.4h12.4v84h-12.4zM576.4 268.4h12.4v84h-12.4zM129.1 690.64l12.024 3.024-48.308 192.428-12.028-3.02zM895.02 692.024l48.156 191.224-12.024 3.028-48.16-191.224zM505.6 477.6h12.4v60h-12.4z"
            fill="#4A555F"
        />
        <path
            d="M737.6 890.8H280.8v-224.8h456.8v224.8z m-444.4-12.8h431.6v-199.6H293.2v199.6z"
            fill="#4A555F"
        />
        <path
            d="M737.6 678.8H280.8v-6.4c0-20.8 16.8-37.6 37.6-37.6H700c20.8 0 37.6 16.8 37.6 37.6v6.4z m-443.6-12.8H724c-2.8-10.8-12.8-18.8-24.4-18.8H318.4c-11.6 0-21.6 8-24.4 18.8zM699.6 922H318.4c-20.8 0-37.6-16.8-37.6-37.6v-6.4h456.8v6.4c0 20.8-17.2 37.6-38 37.6z m-405.6-31.2c2.8 10.8 12.8 18.8 24.4 18.8H700c11.6 0 21.6-8 24.4-18.8H294z"
            fill="#4A555F"
        />
        <path
            d="M333.2 641.2h12.4v31.2h-12.4zM390 641.2h12.4v31.2h-12.4zM446.4 641.2h12.4v31.2h-12.4zM503.2 641.2h12.4v31.2h-12.4zM560 641.2h12.4v31.2H560zM616.8 641.2h12.4v31.2h-12.4zM673.6 641.2h12.4v31.2h-12.4zM333.2 884.4h12.4v31.2h-12.4zM390 884.4h12.4v31.2h-12.4zM446.4 884.4h12.4v31.2h-12.4zM503.2 884.4h12.4v31.2h-12.4zM560 884.4h12.4v31.2H560zM616.8 884.4h12.4v31.2h-12.4zM673.6 884.4h12.4v31.2h-12.4zM505.6 0h12.4v40h-12.4z"
            fill="#4A555F"
        />
        <path
            d="M606.456 20.428l9.64 7.8-25.16 31.096-9.64-7.8z"
            fill="#4A555F"
        />
        <path
            d="M418.484 20.432l25.16 31.096-9.64 7.8-25.16-31.096z"
            fill="#4A555F"
        />
    </svg>
);

const PlayIcon = () => (
    <svg
        className="play-audio-icon"
        viewBox="0 0 1024 1024"
        width="16"
        height="16"
    >
        <path
            d="M512 79.8892047A426.8607953 426.8607953 0 1 1 85.1392047 506.75 427.34403441 427.34403441 0 0 1 512 79.8892047m0-16.1079547A442.96875 442.96875 0 1 0 954.96875 506.75 442.96875 442.96875 0 0 0 512 63.78125z"
            p-id="1758"
        />
        <path d="M654.07215941 510.61590941l-231.31022735-133.53494382v267.06988676l231.31022735-133.53494294z" />
    </svg>
);

const USER_NAMES = [];
USER_NAMES[MESSAGE_POSITION.LEFT] = '机器人';
USER_NAMES[MESSAGE_POSITION.RIGHT] = '用户';

const playSegment = (audio, start, end) => {
    audio.pause();
    setTimeout(() => {
        audio.currentTime = start;
        audio.play();
        audio.ontimeupdate = () => {
            if (audio.currentTime >= end) {
                audio.pause();
            }
        };
    }, 200);
};

export default url =>
    new Promise(resolve => {
        const audio = new Audio(url);
        try {
            url = new URL(url);
        } catch (error) {
            console.error(error);
        }
        const handleClose = () => {
            audio.pause();
        };

        const xhr = new XMLHttpRequest();
        xhr.open('GET', url.pathname || url);
        xhr.responseType = 'arraybuffer';
        xhr.onload = () => {
            const cx = new AudioContext();
            cx.decodeAudioData(
                xhr.response,
                async ({ numberOfChannels, duration }) => {
                    const channel = numberOfChannels || 2;
                    try {
                        const request =
                            duration > 60
                                ? recognizeAsyncSpeechFragment
                                : recognizeSpeechFragment;
                        const response = await request({
                            speechUrl: url,
                            channel,
                        });
                        const texts = response;
                        RooModal.success({
                            title: '通话录音文本翻译',
                            width: '50%',
                            className: 'etfa-modal',
                            content: (
                                <div className="etfa-container">
                                    {texts.map(line => (
                                        <div
                                            className={classNames(
                                                line.channel ===
                                                    MESSAGE_POSITION.RIGHT
                                                    ? 'right'
                                                    : 'left',
                                                'basic',
                                            )}
                                        >
                                            {line.channel ===
                                                MESSAGE_POSITION.RIGHT && (
                                                <div className="empty" />
                                            )}
                                            <div className="content">
                                                <div className="avatar">
                                                    <div className="avatar-circle">
                                                        {line.channel ===
                                                        MESSAGE_POSITION.LEFT ? (
                                                            <RobotAvatar />
                                                        ) : (
                                                            <ClientAvatar />
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="info">
                                                    <div className="user-name">
                                                        <span>
                                                            {
                                                                USER_NAMES[
                                                                    line.channel
                                                                ]
                                                            }
                                                        </span>
                                                        <span>{`${parseTime(
                                                            line.start,
                                                        )} - ${parseTime(
                                                            line.end,
                                                        )}`}</span>
                                                    </div>
                                                    <div className="message">
                                                        <span>
                                                            <span
                                                                onClick={() =>
                                                                    playSegment(
                                                                        audio,
                                                                        line.start,
                                                                        line.end,
                                                                    )
                                                                }
                                                            >
                                                                <PlayIcon />
                                                            </span>
                                                            {line.text}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            {line.channel ===
                                                MESSAGE_POSITION.LEFT && (
                                                <div className="empty" />
                                            )}
                                        </div>
                                    ))}
                                </div>
                            ),
                            okText: '关闭',
                            onCancel: () => {
                                handleClose();
                            },
                            onOk: () => {
                                handleClose();
                            },
                        });
                    } catch (error) {
                        console.error(error);
                        message.error('加载失败，请重试');
                    }
                    resolve();
                },
            );
        };
        xhr.send(null);
    });
