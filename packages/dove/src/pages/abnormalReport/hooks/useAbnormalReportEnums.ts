import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useRequest } from 'ahooks';
import axios from 'axios';

interface EnumItem {
    code: number;
    desc: string;
}

interface AbnormalReportEnums {
    phoneTypes: EnumItem[];
    phoneStatuses: EnumItem[];
    abnormalReportTypes: EnumItem[];
}

export const useAbnormalReportEnums = () => {
    const { data } = useRequest<AbnormalReportEnums, any[]>(
        async () => {
            // @ts-ignore
            const res = await apiCaller.get('/xianfu/api-v2/dove/display/common/enums/query', {});
            if (res.code === 0) {
                return res.data as any as AbnormalReportEnums;
            }
            return {
                phoneTypes: [],
                phoneStatuses: [],
                abnormalReportTypes: [],
            };
        },
        {
            cacheKey: 'abnormalReportEnums',
        },
    );

    return {
        phoneTypes: data?.phoneTypes || [],
        phoneStatuses: data?.phoneStatuses || [],
        abnormalReportTypes: data?.abnormalReportTypes || [],
        getPhoneTypeDesc: (code: number) => {
            return data?.phoneTypes.find(v => v.code === code)?.desc || '-';
        },
        getPhoneStatusDesc: (code: number) => {
            return data?.phoneStatuses.find(v => v.code === code)?.desc || '-';
        },
        getAbnormalReportTypeDesc: (code: number) => {
            return data?.abnormalReportTypes.find(v => v.code === code)?.desc || '-';
        },
    };
};
