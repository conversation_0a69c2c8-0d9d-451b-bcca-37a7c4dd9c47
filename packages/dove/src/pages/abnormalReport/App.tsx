import React, { useEffect, useState } from 'react';
import {
    Form,
    Input,
    Select,
    Button,
    Table,
    Row,
    Col,
    Modal,
    Image,
    Space,
    Tooltip,
    DatePicker,
    Flex,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import './index.scss';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useAbnormalReportEnums } from './hooks/useAbnormalReportEnums';
import CitySelector from '@src/components/CitySelector';
import ReportModal from './components/ReportModal';
import { message } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import CitySelectorAntd from '@src/components/CitySelectorAntd';

const AbnormalReport: React.FC = () => {
    const [form] = Form.useForm();
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [tableData, setTableData] = useState([]);
    const [total, setTotal] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [loading, setLoading] = useState(false);

    const { phoneStatuses, abnormalReportTypes } = useAbnormalReportEnums();

    const statusOptions = phoneStatuses.map(item => ({
        value: item.code,
        label: item.desc,
    }));

    const typeOptions = abnormalReportTypes.map(item => ({
        value: item.code,
        label: item.desc,
    }));

    const columns = [
        {
            title: '序号',
            dataIndex: 'id',
            key: 'id',
            width: 80,
        },
        {
            title: '上报号码',
            dataIndex: 'abnormalNo',
            key: 'abnormalNo',
        },
        {
            title: '上报时间',
            dataIndex: 'reportTime',
            key: 'reportTime',
            render: v => dayjs(v * 1000).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
            title: '异常类型',
            dataIndex: 'abnormalTypeId',
            key: 'abnormalTypeId',
            render: (id: number) => {
                const option = typeOptions.find(item => item.value === id);
                return option ? option.label : '-';
            },
        },
        {
            title: '号码状态',
            dataIndex: 'numberStatus',
            key: 'numberStatus',
            render: (id: number) => {
                const option = statusOptions.find(item => item.value === id);
                return option ? option.label : '-';
            },
        },
        {
            title: '归档地址',
            dataIndex: 'belongingCityName',
            key: 'belongingCityName',
        },
        {
            title: '补充说明文字',
            dataIndex: 'descWord',
            key: 'descWord',
            width: 200,
            ellipsis: {
                showTitle: false,
            },
            render: text => (
                <Tooltip placement="topLeft" title={text || '-'}>
                    {text || '-'}
                </Tooltip>
            ),
        },
        {
            title: '补充说明图片',
            dataIndex: 'descPicUrlList',
            key: 'descPicUrl',
            render: (urls: string[]) =>
                urls?.length
                    ? urls.map((url, index) => (
                          <Image
                              key={index}
                              src={url}
                              alt="补充图片"
                              style={{ width: 50, height: 50, marginRight: 8 }}
                          />
                      ))
                    : '-',
        },
        {
            title: '累计上报次数',
            dataIndex: 'reportCount',
            key: 'reportCount',
        },
        {
            title: '操作',
            key: 'action',
            render: (_, record) => (
                <Space>
                    <Button
                        type="link"
                        href={`./numberPool?displayNumber=${record.abnormalNo}`}
                    >
                        号码管理
                    </Button>
                </Space>
            ),
        },
    ];

    const handleSearch = async (values: any) => {
        setLoading(true);
        const {
            uploadTime,
            abnormalStatus,
            abnormalType,
            abnormalNumber,
            belongingCityIds,
        } = values;

        // 构造查询参数
        const params = {
            startTime: uploadTime
                ? (uploadTime[0] as Dayjs)?.startOf('day').unix()
                : undefined,
            endTime: uploadTime
                ? (uploadTime[1] as Dayjs)?.endOf('day').unix()
                : undefined,
            numberStatus: abnormalStatus,
            abnormalType,
            abnormalNumber: abnormalNumber ? abnormalNumber : undefined,
            isQueryBd: false,
            pageSize,
            page: currentPage,
            belongingCityIds: belongingCityIds?.map(v => v.cityId),
        };

        try {
            const response: any = await apiCaller.post(
                // @ts-ignore
                '/xianfu/api-v2/dove/dovecall/query/number/report',
                params,
            );
            if (response.code === 0) {
                setTableData(response.data.data || []);
                setTotal(response.data.total || 0);
            }
        } catch (error) {
            console.error('查询失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleTableChange = (pagination: any) => {
        setCurrentPage(pagination.current);
        setPageSize(pagination.pageSize);
        form.submit(); // 触发表单提交以刷新数据
    };

    useEffect(() => {
        // Get displayNumber from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const displayNumber = urlParams.get('displayNumber');
        if (displayNumber) {
            form.setFieldsValue({ abnormalNumber: displayNumber });
            form.submit();
        }
    }, []);

    const handleReportSubmit = async (values: any) => {
        try {
            const params = { ...values };
            params.descImages = params.descImages?.fileList.map(
                v => v.response.data,
            );
            const response: any = await apiCaller.post(
                // @ts-ignore
                '/xianfu/api-v2/dove/dovecall/number/report',
                params,
            );

            if (response.code === 0) {
                const { reportResult, reportResultMsg } = response.data;
                if (reportResult) {
                    message.success(reportResultMsg || '上报成功');
                    setIsModalVisible(false);
                    // 刷新列表
                    form.submit();
                } else {
                    message.error(reportResultMsg || '上报失败');
                }
            } else {
                message.error(response.msg || '上报失败');
            }
        } catch (error) {
            console.error('上报失败:', error);
            message.error('上报失败，请稍后重试');
        }
    };

    useEffect(() => {
        form.submit();
    }, []);

    return (
        <div className="abnormal-report">
            <Form
                form={form}
                layout="inline"
                onFinish={handleSearch}
                className="search-form"
            >
                <Row gutter={24} className="form-row">
                    <Col span={6}>
                        <Form.Item name="uploadTime" label="上报时间">
                            <DatePicker.RangePicker style={{ width: '100%' }} />
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item name="abnormalStatus" label="号码状态">
                            <Select
                                placeholder="请选择号码状态"
                                options={statusOptions}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item name="abnormalType" label="异常类型">
                            <Select
                                placeholder="请选择异常类型"
                                options={typeOptions}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item
                            name="belongingCityIds"
                            label="归属地" // @ts-ignore
                            getValueProps={value => {
                                return value
                                    ? value?.map(v => [v.provinceId, v.cityId])
                                    : undefined;
                            }}
                        >
                            <CitySelectorAntd maxLevel={2} multiple />
                        </Form.Item>
                    </Col>
                </Row>
                <Row gutter={24} className="form-row">
                    <Col span={6}>
                        <Form.Item name="abnormalNumber" label="号码">
                            <Input placeholder="请输入号码" allowClear />
                        </Form.Item>
                    </Col>
                    <Col span={6} offset={12}>
                        <Flex justify="end">
                            <Button
                                type="primary"
                                htmlType="submit"
                                className="search-btn"
                            >
                                查询
                            </Button>
                            <Button className="reset-btn" htmlType="reset">
                                重置
                            </Button>
                        </Flex>
                    </Col>
                </Row>
                <Row>
                    <Button
                        type="primary"
                        className="reset-btn"
                        onClick={() => setIsModalVisible(true)}
                    >
                        异常号码上报
                    </Button>
                </Row>
            </Form>

            <div className="table-container">
                <Table
                    loading={loading}
                    columns={columns}
                    dataSource={tableData}
                    rowKey="id"
                    pagination={{
                        current: currentPage,
                        pageSize: pageSize,
                        total: total,
                        showTotal: total => `共 ${total} 条`,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        onChange: (page, pageSize) => {
                            handleTableChange({ current: page, pageSize });
                        },
                    }}
                />
            </div>

            <ReportModal
                isVisible={isModalVisible}
                onCancel={() => setIsModalVisible(false)}
                onSubmit={handleReportSubmit}
                typeOptions={typeOptions}
            />
        </div>
    );
};

export default AbnormalReport;
