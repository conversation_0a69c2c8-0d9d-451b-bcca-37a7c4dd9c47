import React, { useState } from 'react';
import { Select } from 'antd';
import type { SelectProps } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import debounce from 'lodash/debounce';
import dayjs from 'dayjs';

interface NumberOption {
    value: string;
    label: string;
}

const NumberSelect: React.FC<SelectProps> = props => {
    const [options, setOptions] = useState<NumberOption[]>([]);
    const [loading, setLoading] = useState(false);

    const searchCallRecord = async (recordId: string) => {
        try {
            const res = await apiCaller.get(
                // @ts-ignore
                '/xianfu/api/dove/doveqc/callRecord/r/getCallRecordList',
                {
                    recordId: parseInt(recordId),
                    pageSize: 10,
                    page: 1,
                    endTime: dayjs().format('YYYY-MM-DD'),
                    startTime: dayjs().add(-1, 'month').format('YYYY-MM-DD'),
                    direction: -1,
                    isVisit: 0,
                    callTag: -1,
                    connectStatus: -1,
                    qualifiedResult: -1,
                },
            );

            // @ts-ignore
            if (res.code === 0 && res.data?.list?.length > 0) {
                // @ts-ignore
                const records = res.data.list;
                return records.map((record: any) => ({
                    value: record.displayNumber,
                    label: record.displayNumber,
                }));
            }
            return [];
        } catch (error) {
            console.error('Search call record failed:', error);
            return [];
        }
    };

    const handleSearch = debounce(async (value: string) => {
        if (!value) {
            setOptions([]);
            return;
        }

        // If input length is 8, search for call records
        if ([8, 9].includes(value.length) && /^\d+$/.test(value)) {
            setLoading(true);
            try {
                const recordOptions = await searchCallRecord(value);
                setOptions([...recordOptions, { label: value, value }]);
            } finally {
                setLoading(false);
            }
        } else {
            // For other cases, use the input value directly
            setOptions([
                {
                    value: value,
                    label: value,
                },
            ]);
        }
    }, 300);

    return (
        <Select
            showSearch
            placeholder="请输入号码或者信鸽通话ID"
            loading={loading}
            filterOption={false}
            onSearch={handleSearch}
            options={options}
            {...props}
        />
    );
};

export default NumberSelect;
