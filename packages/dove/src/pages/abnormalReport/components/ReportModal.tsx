import React, { useState } from 'react';
import { Modal, Form, Input, Select, Button, Upload, message } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import type { UploadFile } from 'antd/es/upload/interface';
import NumberSelect from './NumberSelect';
import { getPrefix } from '@src/module/request/getPrefix';

interface ReportModalProps {
    isVisible: boolean;
    onCancel: () => void;
    onSubmit: (values: any) => void;
    typeOptions: { value: number; label: string }[];
}

const ReportModal: React.FC<ReportModalProps> = ({
    isVisible,
    onCancel,
    onSubmit,
    typeOptions,
}) => {
    const [form] = Form.useForm();
    const [fileList, setFileList] = useState<UploadFile[]>([]);

    const handleUploadChange = ({
        fileList: newFileList,
    }: {
        fileList: UploadFile[];
    }) => {
        // 限制图片数量
        if (newFileList.length > 8) {
            message.warning('最多上传8张图片');
            return;
        }
        setFileList(newFileList);
    };

    const uploadButton = (
        <div>
            <PlusOutlined />
            <div style={{ marginTop: 8 }}>上传图片</div>
        </div>
    );

    const handleCancel = () => {
        form.resetFields();
        setFileList([]);
        onCancel();
    };

    const handleSubmit = async (values: any) => {
        const submitData = {
            abnormalNumber: values.reportNumber,
            abnormalType: values.abnormalType,
            descWord: values.textContent || '',
            descImages: values.images,
        };

        onSubmit(submitData);
    };

    const actionUrl = `${getPrefix('/impc/task/upload')}/impc/task/upload`;
    return (
        <Modal
            title="异常号码上报"
            open={isVisible}
            onCancel={handleCancel}
            footer={null}
            width={520}
        >
            <Form form={form} layout="vertical" onFinish={handleSubmit}>
                <Form.Item
                    name="abnormalType"
                    label="异常类型"
                    rules={[{ required: true, message: '请选择异常类型' }]}
                >
                    <Select
                        placeholder="请选择异常类型"
                        options={typeOptions}
                    />
                </Form.Item>
                <Form.Item
                    name="reportNumber"
                    label="上报号码"
                    rules={[
                        { required: true, message: '请输入上报号码' },
                        { pattern: /^\d+$/, message: '请输入正确的号码格式' },
                    ]}
                >
                    <NumberSelect />
                </Form.Item>
                <Form.Item
                    name="textContent"
                    label="补充说明"
                    rules={[{ max: 100, message: '补充说明最多500字' }]}
                >
                    <Input.TextArea
                        placeholder="可对上报号码进行补充说明，如：提供号码被标记的截图"
                        rows={4}
                        maxLength={100}
                        showCount
                    />
                </Form.Item>
                <Form.Item name="images" label="补充图片">
                    <Upload
                        action={actionUrl}
                        listType="picture-card"
                        fileList={fileList}
                        onChange={handleUploadChange}
                        beforeUpload={file => {
                            const isImage = file.type.startsWith('image/');
                            if (!isImage) {
                                message.error('只能上传图片文件!');
                            }
                            const isLt5M = file.size / 1024 / 1024 < 5;
                            if (!isLt5M) {
                                message.error('图片必须小于5MB!');
                            }
                            return isImage && isLt5M;
                        }}
                        accept="image/*"
                    >
                        {fileList.length >= 8 ? null : uploadButton}
                    </Upload>
                </Form.Item>
                <Form.Item
                    className="modal-footer"
                    style={{ marginBottom: 0, textAlign: 'right' }}
                >
                    <Button onClick={handleCancel} style={{ marginRight: 8 }}>
                        取消
                    </Button>
                    <Button type="primary" htmlType="submit">
                        提交
                    </Button>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default ReportModal;
