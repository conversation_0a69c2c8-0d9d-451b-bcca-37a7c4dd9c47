import React, { useEffect, useState } from 'react';
import {
    Form,
    Input,
    Select,
    Button,
    Table,
    Row,
    Col,
    Space,
    message,
    DatePicker,
    Modal,
    Flex,
    App,
} from 'antd';
import type { TableColumnsType } from 'antd';
import { apiCaller, MethodType } from '@mfe/cc-api-caller-pc';
import dayjs from 'dayjs';
import type { AgentItem, SearchFormValues } from './types';
import AgentModal from './components/AgentModal';
import './index.scss';
import TeamSelect from '@src/components/callRecord/TeamSelect';
import MisSelect from '@src/components/MisSelector';
import { reachMethodOptions, AgentTypeMap } from '@src/constants';
import { getEnvironment, Environment } from '../bdDeciliter/common/utils';
import SceneTemplateSelect from '@src/components/SceneTemplateSelect';

const { RangePicker } = DatePicker;

// 定义版本历史项接口

// 状态枚举
enum AgentStatus {
    ENABLED = 1,    // 启用
    DISABLED = 2,   // 停用
    DELETED = 3     // 删除
}

// 状态映射（用于显示）
const STATUS_MAP = {
    [AgentStatus.ENABLED]: '启用',
    [AgentStatus.DISABLED]: '停用',
    [AgentStatus.DELETED]: '删除'
} as const;

const AgentManagement: React.FC = () => {
    const [form] = Form.useForm();
    const [tableData, setTableData] = useState<AgentItem[]>([]);
    const [total, setTotal] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [loading, setLoading] = useState(false);
    const [modalVisible, setModalVisible] = useState(false);
    const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>(
        'create',
    );
    const [currentRecord, setCurrentRecord] = useState<AgentItem | undefined>();
    const [teamList, setTeamList] = useState<any[]>([]);

    // 版本历史弹窗相关状态
    const [versionHistoryVisible, setVersionHistoryVisible] = useState(false);
    const [versionHistoryData, setVersionHistoryData] = useState<any[]>([]);
    const [versionHistoryLoading, setVersionHistoryLoading] = useState(false);
    const [versionHistoryTotal, setVersionHistoryTotal] = useState(0);
    const [versionHistoryPage, setVersionHistoryPage] = useState(1);
    const [versionHistoryPageSize, setVersionHistoryPageSize] = useState(10);
    const [currentVersionRecord, setCurrentVersionRecord] = useState<AgentItem | undefined>();

    const [bizId, setBizId] = useState<number | undefined>(undefined);

    // 打开bdDeciliter页面函数
    const openBdDeciliterPage = (id?: string | number, bizId?: string | number) => {
        const env = getEnvironment();
        const isLocal = env === Environment.DEV;

        let url = isLocal ? '/bdDeciliter' : '/page/dove/bdDeciliter';

        // 构建参数
        const params = new URLSearchParams();
        if (id) params.append('id', String(id));
        if (bizId) params.append('bizId', String(bizId));

        // 如果有参数，添加到URL
        const queryString = params.toString();
        if (queryString) {
            url = `${url}?${queryString}`;
        }

        window.open(url, '_blank');
    };

    const fetchData = async (params: any, page: number, size: number) => {
        console.log('-->', bizId, params);
        // 确保有业务线参数才能请求数据
        if (!bizId && !params.bizId) {
            message.warning('请先选择业务线');
            return;
        }

        setLoading(true);
        try {
            const res = await apiCaller.post(
                '/xianfu/api-v2/dove/agent/query',
                {
                    ...params,
                    bizId: params.bizId || bizId,
                    page,
                    pageSize: size,
                    forUse: false,
                    newestVersion: 1
                },
            );
            if (res.code === 0) {
                setTableData(res.data.data as any);
                setTotal(res.data.total);
            } else {
                message.error(res.msg || '获取数据失败');
            }
        } catch (error) {
            console.error('Fetch data failed:', error);
            message.error('获取数据失败');
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = async (values: SearchFormValues) => {
        const { createTime, owner, ...rest } = values;
        const params = {
            ...rest,
            createTimeMin: createTime?.[0]
                ? dayjs(createTime[0]).valueOf()
                : undefined,
            createTimeMax: createTime?.[1]
                ? dayjs(createTime[1]).valueOf()
                : undefined,
            owner: owner?.[0],
        };
        setCurrentPage(1);
        await fetchData(params, 1, pageSize);
    };

    const getTeamList = async () => {
        try {
            const res = await apiCaller.send(
                '/xianfu/api-v2/dove/staff/biz/query' as any,
                {},
                {
                    method: MethodType.GET,
                },
            );
            if (res.code !== 0) {
                message.error('获取业务线失败');
                return;
            }
            const teamList = res.data?.bizList || [];
            console.log('teamList', teamList);
            setTeamList(teamList);

            // 如果有业务线数据且当前 bizId 为空，则默认选中第一条
            if (teamList.length > 0 && bizId === undefined) {
                const defaultBizId = teamList[0].bizId;
                console.log('defaultBizId', defaultBizId);
                setBizId(defaultBizId);
                form.setFieldValue('bizId', defaultBizId);

                // 选中默认业务线后立即请求数据
                const currentFormValues = form.getFieldsValue();
                handleSearch({
                    ...currentFormValues,
                    bizId: defaultBizId
                });
            } else if (bizId !== undefined) {
                // 如果 bizId 已经有值（例如从 URL 获取），则用当前 bizId 触发搜索
                const currentFormValues = form.getFieldsValue();
                handleSearch({
                    ...currentFormValues,
                    bizId: bizId
                });
            }
        } catch (error) {
            console.error('获取业务线失败:', error);
            message.error('获取业务线失败');
        }
    };

    useEffect(() => {
        // 从URL获取bizId参数
        const urlParams = new URLSearchParams(window.location.search);
        const bizIdParam = urlParams.get('bizId');
        if (bizIdParam) {
            setBizId(Number(bizIdParam));
            form.setFieldValue('bizId', Number(bizIdParam));
        }
        getTeamList();
    }, []);

    // 处理业务线选择变化
    const handleBizIdChange = (value: number) => {
        setBizId(value);
        const currentFormValues = form.getFieldsValue();
        // 使用新的bizId值进行查询
        fetchData({
            ...currentFormValues,
            bizId: value
        }, currentPage, pageSize);
    };

    const handleReset = () => {
        // 重置所有字段，bizId由其独立组件管理
        form.resetFields();
        // 重新触发一次基于当前 bizId 的搜索
        handleSearch({ bizId: bizId });
    };

    // 获取版本历史的函数
    const fetchVersionHistory = async (record: AgentItem, page: number, size: number) => {
        setVersionHistoryLoading(true);
        try {
            const res = await apiCaller.post(
                '/xianfu/api-v2/dove/agent/query',
                {
                    groupId: record?.groupId,
                    page,
                    pageSize: size
                } as any,
            );

            if (res.code === 0) {
                // 将API返回的数据转换为VersionHistoryItem结构
                setVersionHistoryData(res.data.data || []);
                setVersionHistoryTotal(res.data.total);
            } else {
                message.error(res.msg || '获取版本历史失败');
            }
        } catch (error) {
            console.error('Fetch version history failed:', error);
            message.error('获取版本历史失败');
        } finally {
            setVersionHistoryLoading(false);
        }
    };

    // 打开版本历史弹窗
    const handleOpenVersionHistory = (record: AgentItem) => {
        setCurrentVersionRecord(record);
        setVersionHistoryPage(1);
        setVersionHistoryVisible(true);
        fetchVersionHistory(record, 1, versionHistoryPageSize);
    };

    // 关闭版本历史弹窗
    const handleCloseVersionHistory = () => {
        setVersionHistoryVisible(false);
    };

    const columns: TableColumnsType<AgentItem> = [
        {
            title: '模板id',
            dataIndex: 'id',
            key: 'id',
            width: 80
        },
        {
            title: '模板名称',
            dataIndex: 'name',
            key: 'name',
            fixed: 'left',
        },
        {
            title: '模板类型',
            dataIndex: 'type',
            key: 'type',
            render: (type: number) => AgentTypeMap[type] || "",
            width: 100,
        },
        {
            title: '场景',
            dataIndex: 'sceneName',
            key: 'sceneName',

        },
        {
            title: '版本',
            dataIndex: 'version',
            key: 'version',
            render: (version: string, record) => (
                <a onClick={() => handleOpenVersionHistory(record)}>{version || '1.0'}</a>
            ),
        },
        {
            title: '所属业务线',
            dataIndex: 'bizName',
            key: 'bizName',
        },
        {
            title: '应用领域',
            dataIndex: 'objectTypeName',
            key: 'objectType',
        },
        {
            title: '应用渠道',
            dataIndex: 'agentChannel',
            key: 'agentChannel',
            render: (v: any[]) =>
                v?.map(
                    subItem =>
                        reachMethodOptions.find(op => op.value === subItem.type)
                            ?.label,
                ) || '-',
        },
        {
            title: '模板状态',
            dataIndex: 'status',
            key: 'status',
            render: (status: number) => (
                <span style={{ color: status === 1 ? '#52c41a' : '#ff4d4f' }}>
                    {status === 1 ? '启用' : '停用'}
                </span>
            ),
        },
        {
            title: '创建人',
            dataIndex: 'owner',
            key: 'owner',
            render: (owner: any) =>
                owner ? `${owner?.name}（${owner?.mis}）` : '-',
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            render: (time: number) =>
                time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-',
        },
        {
            title: '最后修改人',
            dataIndex: 'lastOperator',
            key: 'lastOperator',
            render: (operator: any) =>
                operator ? `${operator?.name}（${operator?.mis}）` : '-',
        },
        {
            title: '最后修改时间',
            dataIndex: 'updateTime',
            key: 'updateTime',
            render: (time: number) =>
                time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-',
        },
        {
            title: '操作',
            key: 'action',
            width: 120,
            fixed: 'right',
            render: (_, record) => (
                <Space size="middle">
                    <a onClick={() => handleEdit(record)}>编辑</a>
                    {record.status === 1 ? (
                        <a onClick={() => handleStatusChange(record, AgentStatus.DISABLED)}>停用</a>
                    ) : (
                        <a onClick={() => handleStatusChange(record, AgentStatus.ENABLED)}>启用</a>
                    )}
                    <a onClick={() => handleStatusChange(record, AgentStatus.DELETED)}>删除</a>
                </Space>
            ),
        },
    ];

    const handleAdd = () => {
        // setModalMode('create');
        // setCurrentRecord(undefined);
        // setModalVisible(true);
        // const bizId = form.getFieldValue('bizId');
        openBdDeciliterPage(undefined, bizId);
    };

    const handleView = (record: AgentItem) => {
        setModalMode('view');
        setCurrentRecord(record);
        setModalVisible(true);
    };

    const handleEdit = (record: AgentItem) => {
        // setModalMode('edit');
        // setCurrentRecord(record);
        // setModalVisible(true);
        // const bizId = form.getFieldValue('bizId');
        openBdDeciliterPage(record.id, bizId);
    };

    const { modal } = App.useApp();
    // const handleDelete = (record: AgentItem) => {
    //     modal.confirm({
    //         title: '是否删除模板?',
    //         icon: null,
    //         content: null,
    //         onOk: async () => {
    //             try {
    //                 const res = await apiCaller.post(
    //                     '/xianfu/api-v2/dove/agent/delete',
    //                     { id: record.id },
    //                 );
    //                 if (res.code === 0) {
    //                     message.success('删除成功');
    //                     form.submit();
    //                 } else {
    //                     message.error(res.msg || '删除失败');
    //                 }
    //             } catch (error) {
    //                 console.error('Delete failed:', error);
    //                 message.error('删除失败');
    //             }
    //         },
    //     });
    // };

    const handleVersionStatus = async (record) => {
        message.info(`版本状态: ${record?.status === 1 ? '停用' : '发布'}`);
        const res = await apiCaller.post(
            record?.status === 1
                ? '/xianfu/api-v2/dove/agent/delete' as any
                : '/xianfu/api-v2/dove/agent/active' as any,
            {
                id: record.id,
            }
        );
        if (res.code === 0) {
            message.success('更新成功');
            // 更新成功后重新获取版本历史数据
            if (currentVersionRecord) {
                fetchVersionHistory(currentVersionRecord, versionHistoryPage, versionHistoryPageSize);
            }
            // 同时刷新主列表数据
            const currentFormValues = form.getFieldsValue();
            fetchData({
                ...currentFormValues,
                bizId: currentFormValues.bizId
            }, currentPage, pageSize);
        }
    };

    const handleModalCancel = () => {
        setModalVisible(false);
    };

    const handleModalSuccess = () => {
        setModalVisible(false);
        form.submit();
    };

    // 处理模板状态变更
    const handleStatusChange = async (record: AgentItem, status: number) => {
        // 如果是删除操作，先弹出确认对话框
        if (status === AgentStatus.DELETED) {
            modal.confirm({
                title: '确认是否删除此模版',
                content: '删除后将无法恢复，是否确认删除？',
                okText: '确认',
                cancelText: '取消',
                onOk: async () => {
                    await changeAgentStatus(record, status);
                }
            });
        } else {
            // 其他状态变更直接执行
            await changeAgentStatus(record, status);
        }
    };

    // 抽取实际执行状态变更的逻辑为单独函数
    const changeAgentStatus = async (record: AgentItem, status: number) => {
        try {
            const res = await apiCaller.post(
                '/xianfu/api-v2/dove/agent/operate' as any,
                { id: record.id, status }
            );
            if (res.code === 0) {
                message.success(`${STATUS_MAP[status]}成功`);
                // 刷新列表数据
                const currentFormValues = form.getFieldsValue();
                fetchData({
                    ...currentFormValues,
                    bizId: currentFormValues.bizId
                }, currentPage, pageSize);
            } else {
                message.error(res.msg || `${STATUS_MAP[status]}失败`);
            }
        } catch (error) {
            console.error('状态变更失败:', error);
            message.error(`${STATUS_MAP[status]}失败`);
        }
    };

    // 版本历史表格列配置
    const versionHistoryColumns: TableColumnsType = [
        {
            title: '版本',
            dataIndex: 'version',
            key: 'version',
        },
        {
            title: '发布状态',
            dataIndex: 'status',
            key: 'status',
            render: (status: number) => (
                <span style={{
                    borderRadius: '2px',
                    background: status === 1 ? '#FFF6F0' : '#EEEEEE',
                    display: 'flex',
                    width: 'fit-content',
                    flexDirection: 'column',
                    flexWrap: 'nowrap',
                    padding: '2px 4px 2px 4px',
                    color: status === 1 ? '#FF6A00' : '#999999'
                }}>
                    {status === 1 ? '已发布' : '未发布'}
                </span>
            ),
        },
        {
            title: '更新时间',
            dataIndex: 'updateTime',
            key: 'updateTime',
            render: (time: number) => (
                time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-'
            ),
        },
        {
            title: '操作',
            key: 'operation',
            render: (_, record) => (
                console.log('record', record),
                <Space>
                    <a onClick={() => handleVersionStatus(record)}>{record?.status == 1 ? '停用' : '发布'}</a>
                    <a onClick={() => handleVersionEdit(record)}>编辑</a>
                    <a onClick={() => handleVersionCopy(record)}>复制</a>
                    {/* <a onClick={() => handleVersionDelete(record)}>删除</a> */}
                </Space>
            ),
        },
    ];

    // 版本历史操作处理函数
    const handleVersionEdit = (record) => {
        // 实际应用中需要根据版本记录获取对应的Agent详情
        // const bizId = form.getFieldValue('bizId');
        openBdDeciliterPage(record.id, bizId);
    };

    const handleVersionDelete = (record) => {
        // 实际应用中需要根据版本记录获取对应的Agent详情
        message.info(`删除版本: ${record.version}`);
    };

    const handleVersionCopy = async (record) => {
        const res = await apiCaller.post('/xianfu/api-v2/dove/agent/copy', {
            id: record.id,
        });
        if (res.code === 0) {
            message.success('复制成功');
            console.log(res);
            // 复制成功后重新获取版本历史数据
            if (currentVersionRecord) {
                fetchVersionHistory(currentVersionRecord, versionHistoryPage, versionHistoryPageSize);
            }
            // 同时刷新主列表数据
            const currentFormValues = form.getFieldsValue();
            fetchData({
                ...currentFormValues,
                bizId: currentFormValues.bizId
            }, currentPage, pageSize);
            setTimeout(() => {
                openBdDeciliterPage(res.data as any, bizId);
            }, 1000);
        }
    };

    return (
        <div className="agent-management" style={{ position: 'relative' }}>
            {/* 业务线筛选项 */}
            <Flex align='center' justify='flex-end' style={{ marginBottom: 30}}>
                <span style={{ marginRight: 8, color: 'rgba(0, 0, 0, 0.85)' }}>业务线:</span>
                    <Select
                        value={bizId}
                        onChange={handleBizIdChange}
                        style={{ width: 240 }}
                        options={teamList.map(item => ({ label: item.bizName, value: item.bizId }))}
                        placeholder="请选择业务线"
                        allowClear={false}
                />
            </Flex>
            <Form
                form={form}
                layout="horizontal"
                onFinish={handleSearch}
                className="search-form"
                labelCol={{ flex: '80px' }}
                wrapperCol={{ flex: 'auto' }}
            >
                <Row gutter={[24, 12]} className="form-row" wrap>
                    <Col span={8}>
                        <Form.Item name="name" label="模板名称">
                            <Input placeholder="请输入模板名称" allowClear />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item name="id" label="模板id">
                            <Input placeholder="请输入模板id" allowClear />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item name="type" label="模板类型">
                            <Select
                                placeholder="请选择模板类型"
                                options={[
                                    { label: '总部应用', value: 2 },
                                    { label: '场景模板', value: 1 },
                                    { label: '城市应用', value: 3 },
                                ]}
                                allowClear
                            />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item name="channel" label="应用渠道">
                            <Select
                                placeholder="请选择应用渠道"
                                options={[{ label: '智能外呼', value: 2 }]}
                                allowClear
                                mode="multiple"
                            />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item name="sceneName" label="场景模板">
                            <SceneTemplateSelect placeholder="请选择场景模板" />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item name="status" label="模板状态">
                            <Select
                                placeholder="请选择模板状态"
                                options={[
                                    { label: '启用', value: 1 },
                                    { label: '停用', value: 2 },
                                ]}
                                allowClear
                                mode="multiple"
                            />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item name="owner" label="创建人">
                            <MisSelect
                                placeholder="请输入创建人"
                                mis={false}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item name="createTime" label="创建时间">
                            <RangePicker
                                format="YYYY-MM-DD"
                                style={{ width: '100%' }}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={8}></Col>
                    <Col span={8}></Col>
                    <Col span={8}></Col>
                </Row>
                <Row justify="space-between" style={{ marginBottom: 16 }}>
                    <Button type="primary" onClick={handleAdd}>
                        创建模板
                    </Button>
                    <Flex justify="end" gap={16}>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        <Button onClick={handleReset}>重置</Button>
                    </Flex>
                </Row>
            </Form>

            <div className="table-container">
                <Table
                    columns={columns}
                    dataSource={tableData}
                    loading={loading}
                    rowKey="id"
                    scroll={{ x: 'max-content' }}
                    pagination={{
                        current: currentPage,
                        pageSize,
                        total,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: total => `共 ${total} 条`,
                        onChange: (page, size) => {
                            setCurrentPage(page);
                            setPageSize(size);
                            const values = form.getFieldsValue();
                            fetchData({
                                ...values,
                                bizId: values.bizId
                            }, page, size);
                        },
                    }}
                />
            </div>

            {/* 版本历史弹窗 */}
            <Modal
                title={`${currentVersionRecord?.name || '模板'}-版本历史`}
                open={versionHistoryVisible}
                onCancel={handleCloseVersionHistory}
                footer={null}
                width={800}
            >
                <Table
                    columns={versionHistoryColumns}
                    dataSource={versionHistoryData}
                    loading={versionHistoryLoading}
                    rowKey="version"
                    pagination={{
                        current: versionHistoryPage,
                        pageSize: versionHistoryPageSize,
                        total: versionHistoryTotal,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: total => `共 ${total} 条`,
                        onChange: (page, size) => {
                            setVersionHistoryPage(page);
                            setVersionHistoryPageSize(size);
                            if (currentVersionRecord) {
                                fetchVersionHistory(currentVersionRecord, page, size);
                            }
                        },
                    }}
                />
            </Modal>

            <AgentModal
                visible={modalVisible}
                mode={modalMode}
                data={currentRecord}
                onCancel={handleModalCancel}
                onSuccess={handleModalSuccess}
            />
        </div>
    );
};

export default AgentManagement;
