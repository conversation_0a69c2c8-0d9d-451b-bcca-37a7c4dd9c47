我将根据图片生成一个详细的 PRD 文档。

# Agent 管理系统 PRD

## 1. 产品概述

### 1.1 产品定位

Agent 管理系统是一个用于管理和配置智能体(Agent)的后台管理系统，支持对 Agent 的创建、查询、编辑和删除等基础操作。

### 1.2 目标用户

-   系统管理员
-   Agent 配置管理员
-   业务运营人员

## 2. 功能需求

### 2.1 Agent 列表管理

#### 2.1.1 查询功能

**搜索条件：**

-   所属业务线：下拉选择框
-   模型名称：文本输入框
-   应用领域：下拉选择框
-   模型类型：下拉选择框
-   模型状态：下拉选择框（启用/停用）
-   模型负责人：文本输入框
-   创建时间：时间范围选择器

**列表展示字段：**

-   序号（自动生成）
-   模型名称
-   所属业务线
-   应用领域
-   模型类型
-   模型状态（启用/停用）
-   模型负责人
-   创建时间
-   最后修改人
-   最后修改时间
-   操作按钮
    -   查看：点击后弹出表单弹窗，回填当前 Agent 数据，所有字段置为只读状态
    -   编辑：点击后弹出表单弹窗，回填当前 Agent 数据，所有字段可编辑
    -   删除：点击后弹出确认弹窗，提示"确认删除该 Agent 吗?"，确认后调用删除接口

#### 2.1.2 列表功能

-   分页展示
-   支持修改每页显示数量
-   支持页码快速跳转
-   显示总数据条数

### 2.2 Agent 创建/编辑

#### 2.2.1 基础信息配置

**必填项：**

-   名称
-   Agent ID
-   类型（下拉选择）
-   负责人
-   状态（启用/停用）

**选填项：**

-   描述（多行文本）

#### 2.2.2 Agent 配置信息

**必填项：**

-   BOT ID
-   APPKEY
-   基础权限配置（多选）

**选填项：**

-   活动参数配置（多选）
-   模型技能配置（多选）
-   使用方（多行文本）
-   模型使用限制（多行文本）

## 3. 交互规范

### 3.1 列表页交互

-   查询条件支持重置和提交
-   表格支持排序和筛选
-   操作按钮悬停显示提示文本
-   状态使用不同颜色区分（启用-绿色、停用-红色）

### 3.2 新建/编辑弹窗

-   使用 Modal 弹窗形式，标题为"新建 Agent"或"编辑 Agent"
-   弹窗宽度为 800px，高度自适应
-   表单分为两个部分：Agent 信息和 Agent 配置，采用一页式展示
-   必填项使用红色星号标记
-   保存时进行表单验证

#### 3.2.1 Agent 信息区域

-   标题显示"Agent 信息"
-   表单布局采用两列布局，标签右对齐
-   字段说明：
    -   名称：单行文本输入，最大长度 50 字符
    -   Agent ID：单行文本输入，最大长度 20 字符，仅支持字母、数字和下划线
    -   类型：下拉选择框
    -   负责人：单行文本输入，最大长度 20 字符
    -   状态：下拉选择框，选项：启用/停用
    -   描述：多行文本输入框，高度至少 3 行，支持换行

#### 3.2.2 Agent 配置区域

-   标题显示"Agent 配置"
-   表单布局采用单列布局，标签左对齐
-   字段说明：
    -   木星租户ID/企微账号/信鸽IM账号：单行文本输入
    -   BOT ID：单行文本输入
    -   APPKEY：单行文本输入
    -   基础权限配置：多选下拉框，已选项显示为标签形式，支持删除
        -   选项示例：A 店配送、商家端通知权限等
        -   每个选项右侧显示删除图标(x)
    -   活动参数配置：多选下拉框，已选项显示为标签形式
        -   选项示例：参数名称等
    -   模型技能配置：多选下拉框
    -   Agent 投放：多行文本输入框
    -   使用方：多行文本输入框
    -   模型使用限制：多行文本输入框

#### 3.2.3 表单交互

-   底部按钮：
    -   取消：左侧，点击关闭弹窗
    -   保存：右侧，主色调按钮，点击提交表单
-   底部提示信息：
    -   显示"如需审核需参数配置完成，请联系 xxx@xxx"
-   表单验证规则：
    -   必填项为空时，显示红色错误提示
    -   提交时统一校验所有字段
-   加载状态：
    -   提交时显示 loading 状态
    -   禁用所有输入框和按钮

#### 3.2.4 辅助功能

-   团队确认弹窗：
    -   标题："团队确认"
    -   内容："Agent 创建后，将被允许无主题聊天，请谨慎操作，并不允许用户提供敏感内容或违法内容"
    -   输入框：提供 BOT Name 输入
    -   按钮：取消/确认提交
-   添加客服弹窗：
    -   标题："添加客服"
    -   内容：显示"C 类客户（明细，自动同步）"
    -   字段：
        -   自动关联
        -   标签描述
    -   按钮：取消/确认

## 4. 权限控制

-   查看权限：所有用户
-   新建权限：管理员
-   编辑权限：管理员和 Agent 负责人
-   删除权限：仅管理员

## 5. 错误处理

-   表单提交失败显示错误提示
-   网络请求失败显示错误提示
-   必填项为空时进行提示
-   删除操作需二次确认

## 6. 性能要求

-   列表页首次加载时间 < 2s
-   查询响应时间 < 1s
-   新建/编辑保存响应时间 < 1s
-   支持同时处理至少 100 个并发请求

## 7. 技术规范

### 7.1 接口规范

```typescript
// 查询接口
POST /xianfu/api/dove/agent/query
请求参数：
{
  page: number;
  pageSize: number;
  bizId?: number;
  channel?: string;
  owner?: number;
  status?: number[];
  createTimeMin?: number;
  createTimeMax?: number;
}

返回结果：
{
    page: number;
    pageSize: number;
    total: number;
    data: {
        id: number;
        name: string;
        description: string;
        agentChannel: {
            type: number;
            jupiterTenantId: string;
            botId: string;
        }[];
        bizId: number;
        objectType: number;
        status: number;
        auth: {
            type: number;
            org: number[];
        };
        placeholder: string[];
        owner: {
            mis: string;
            name: string;
            id: number;
        };
        lastOperator: {
            mis: string;
            name: string;
            id: number;
        };
        createTime: number;
        updateTime: number;
    }[];
}


// 创建接口
POST /xianfu/api/dove/agent/create
请求参数：
{
  name: string;
  agentId: string;
  type: string;
  owner: string;
  status: string;
  description?: string;
  botId: string;
  appKey: string;
  permissions: string[];
  activities?: string[];
  modelConfig?: string[];
  usage?: string;
  restrictions?: string;
}
```

### 7.2 状态码规范

-   0: 成功
-   非 0: 失败，需显示对应的错误信息

## 8. 后续优化建议

1. 添加批量操作功能
2. 支持配置模板管理
3. 添加操作日志记录
4. 增加数据统计和分析功能
5. 支持配置导入导出

## 9. 其他

-   示意图：packages/dove/src/pages/agentManagement/image.png
