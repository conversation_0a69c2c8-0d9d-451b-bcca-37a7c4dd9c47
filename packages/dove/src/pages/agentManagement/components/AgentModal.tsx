import React, { useEffect, useState } from 'react';
import {
    Modal,
    Form,
    Input,
    Select,
    Space,
    message,
    Button,
    Row,
    Col,
} from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import type { AgentModalProps } from '../types';
import './AgentModal.scss';
import OrgnazitionPickerRoo from '@src/components/callRecord/OrgnazitionPickerRoo';
import { PlusOutlined } from '@ant-design/icons';
import useObjectType from '@src/hooks/useObjectType';
import { FormInstance, useWatch } from 'antd/es/form/Form';
import TeamSelect from '@src/components/callRecord/TeamSelect';
import MisSelect from '@src/components/MisSelector';
import PlaceholderSelector from './PlaceHolderSelector';
import { ReachMethodEnum } from '@src/constants';

const { TextArea } = Input;

const AgentChannelEnum = {
    AI_CALL: 2,
    IM_GROUP: 5,
};

const AuthTypeEnum = {
    FRONT_LINE: 1, // 一线使用
    BACK_LINE: 2, // 后线运营使用
} as const;

const ModalContent = ({
    form,
    mode,
    loading,
}: {
    form: FormInstance;
    mode: 'create' | 'edit' | 'view';
    loading: boolean;
}) => {
    const { data: objectTypeEnum } = useObjectType();
    const objectTypeWatched = useWatch('objectType', form);
    const agentChannelWatched = useWatch(['agentChannel', 'type'], form);
    const authTypeWatched = useWatch(['auth', 'type'], form);

    const [forceUpdate, setForceUpdate] = useState(0);
    useEffect(() => {
        setForceUpdate(forceUpdate + 1);
    }, [objectTypeWatched, objectTypeEnum]);
    return (
        <>
            <Form
                form={form}
                layout="horizontal"
                disabled={mode === 'view' || loading}
                labelCol={{ flex: '120px' }}
                wrapperCol={{ flex: 'auto' }}
            >
                <div className="section">
                    <div className="section-title">Agent信息</div>
                    <div className="section-content">
                        <Row gutter={24}>
                            <Col span={12}>
                                <Form.Item
                                    name="name"
                                    label="名称"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入名称',
                                        },
                                        {
                                            max: 50,
                                            message: '最大长度为50字符',
                                        },
                                    ]}
                                >
                                    <Input placeholder="请输入名称" />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    name="owner"
                                    label="负责人"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入负责人',
                                        },
                                    ]}
                                >
                                    <MisSelect
                                        placeholder="请输入负责人"
                                        mis={false}
                                        labelInValue
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    name="bizId"
                                    label="业务线"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请选择业务线',
                                        },
                                    ]}
                                >
                                    <TeamSelect
                                        optionPath="/xianfu/api-v2/dove/staff/biz/query"
                                        extraOption={{
                                            bizId: form.getFieldValue('bizId'),
                                            bizName:
                                                form.getFieldValue('bizName'),
                                        }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    name="status"
                                    label="状态"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请选择状态',
                                        },
                                    ]}
                                >
                                    <Select placeholder="请选择状态">
                                        <Select.Option value={1}>
                                            启用
                                        </Select.Option>
                                        <Select.Option value={2}>
                                            停用
                                        </Select.Option>
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    name="objectType"
                                    label="触达对象"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请选择触达对象',
                                        },
                                    ]}
                                >
                                    <Select
                                        style={{ width: '100%' }}
                                        placeholder="请选择触达对象"
                                        options={(objectTypeEnum as any)?.map?.(
                                            v => ({
                                                value: v.objectType,
                                                label: v.name,
                                            }),
                                        )}
                                        onChange={() => {
                                            form.setFieldValue(
                                                ['agentChannel', 'type'],
                                                undefined,
                                            );
                                        }}
                                    />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    name={['agentChannel', 'type']}
                                    label="应用渠道"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请选择应用渠道',
                                        },
                                    ]}
                                >
                                    <Select
                                        placeholder="请先选择触达对象"
                                        fieldNames={{
                                            label: 'name',
                                            value: 'code',
                                        }}
                                        options={(objectTypeEnum as any[])
                                            ?.find?.(
                                                v =>
                                                    v.objectType ===
                                                    objectTypeWatched,
                                            )
                                            ?.contactType?.filter(
                                                v =>
                                                    v.code !==
                                                    ReachMethodEnum.IM_MASS_SEND,
                                            )}
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row>
                            <Col span={24}>
                                <Form.Item name="description" label="描述">
                                    <TextArea
                                        rows={3}
                                        placeholder="请输入描述"
                                    />
                                </Form.Item>
                            </Col>
                        </Row>
                    </div>
                </div>

                <div className="section">
                    <div className="section-title">Agent配置</div>
                    <div className="section-content">
                        {agentChannelWatched === AgentChannelEnum.AI_CALL && (
                            <Form.Item
                                name={['agentChannel', 'jupiterTenantId']}
                                label="木星租户ID"
                                rules={[
                                    {
                                        required: true,
                                        message: '请输入木星租户ID',
                                    },
                                ]}
                            >
                                <Input placeholder="请输入木星租户ID/企微账号/信鸽IM账号" />
                            </Form.Item>
                        )}

                        {agentChannelWatched === AgentChannelEnum.AI_CALL && (
                            <Form.Item
                                name={['agentChannel', 'routePoint']}
                                label="路由点"
                                rules={[
                                    { required: true, message: '请输入路由点' },
                                ]}
                            >
                                <Input placeholder="请输入路由点" />
                            </Form.Item>
                        )}

                        <Form.Item
                            name="intention"
                            label="意向配置"
                            initialValue={{
                                elementList: [
                                    {
                                        tag: '其他',
                                        description:
                                            '除自定义分类之外的所有客户',
                                        jupiterRank: 'other',
                                    },
                                    {
                                        tag: '未接通',
                                        description: '用户没有接通电话',
                                        jupiterRank: 'E',
                                    },
                                ],
                            }}
                        >
                            <Form.List name={['intention', 'elementList']}>
                                {(fields, { add, remove }) => (
                                    <>
                                        {fields.map(
                                            ({ key, name, ...restField }) => (
                                                <Space
                                                    key={key}
                                                    style={{
                                                        display: 'flex',
                                                        marginBottom: 8,
                                                    }}
                                                    align="start"
                                                >
                                                    <Form.Item
                                                        {...restField}
                                                        name={[
                                                            name,
                                                            'jupiterRank',
                                                        ]}
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message:
                                                                    '请选择等级',
                                                            },
                                                        ]}
                                                    >
                                                        <Select
                                                            placeholder="等级"
                                                            style={{
                                                                width: 120,
                                                            }}
                                                            disabled
                                                        />
                                                    </Form.Item>
                                                    <Form.Item
                                                        {...restField}
                                                        name={[name, 'tag']}
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message:
                                                                    '请输入标签',
                                                            },
                                                        ]}
                                                    >
                                                        <Input
                                                            placeholder="意向名称"
                                                            disabled={
                                                                form.getFieldValue(
                                                                    [
                                                                        'intention',
                                                                        'elementList',
                                                                        name,
                                                                        'jupiterRank',
                                                                    ],
                                                                ) === 'other' ||
                                                                form.getFieldValue(
                                                                    [
                                                                        'intention',
                                                                        'elementList',
                                                                        name,
                                                                        'jupiterRank',
                                                                    ],
                                                                ) === 'E'
                                                            }
                                                        />
                                                    </Form.Item>
                                                    <Form.Item
                                                        {...restField}
                                                        name={[
                                                            name,
                                                            'description',
                                                        ]}
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message:
                                                                    '请输入描述',
                                                            },
                                                        ]}
                                                    >
                                                        <Input.TextArea
                                                            placeholder="意向描述"
                                                            disabled={
                                                                form.getFieldValue(
                                                                    [
                                                                        'intention',
                                                                        'elementList',
                                                                        name,
                                                                        'jupiterRank',
                                                                    ],
                                                                ) === 'other' ||
                                                                form.getFieldValue(
                                                                    [
                                                                        'intention',
                                                                        'elementList',
                                                                        name,
                                                                        'jupiterRank',
                                                                    ],
                                                                ) === 'E'
                                                            }
                                                        />
                                                    </Form.Item>

                                                    {fields.length - 1 ===
                                                        name &&
                                                        form.getFieldValue([
                                                            'intention',
                                                            'elementList',
                                                            name,
                                                            'jupiterRank',
                                                        ]) !== 'other' &&
                                                        form.getFieldValue([
                                                            'intention',
                                                            'elementList',
                                                            name,
                                                            'jupiterRank',
                                                        ]) !== 'E' && (
                                                            <Button
                                                                type="link"
                                                                onClick={() =>
                                                                    remove(name)
                                                                }
                                                            >
                                                                删除
                                                            </Button>
                                                        )}
                                                </Space>
                                            ),
                                        )}
                                        <Form.Item>
                                            <Button
                                                type="dashed"
                                                onClick={() => {
                                                    add();

                                                    const alphabet =
                                                        'ABCDFGHIJKLMNOPQRSTUVWXYZ'.split(
                                                            '',
                                                        );
                                                    const index = fields.length;
                                                    form.setFieldValue(
                                                        [
                                                            'intention',
                                                            'elementList',
                                                            index,
                                                            'jupiterRank',
                                                        ],
                                                        alphabet[index - 2],
                                                    );
                                                }}
                                                block
                                                icon={<PlusOutlined />}
                                            >
                                                添加意向
                                            </Button>
                                        </Form.Item>
                                    </>
                                )}
                            </Form.List>
                        </Form.Item>

                        <Form.Item name="placeholder" label="动态参数配置">
                            <PlaceholderSelector />
                        </Form.Item>

                        {/* <Form.Item name="modelConfig" label="模型技能组配置">
                            <Select
                                mode="multiple"
                                placeholder="请选择模型技能组"
                            />
                        </Form.Item> */}

                        <div className="section-title">Agent权限</div>
                        <Form.Item name={['auth', 'type']} label="权限控制类型">
                            <Select
                                placeholder="请选择权限控制类型"
                                options={[
                                    {
                                        label: '一线使用',
                                        value: AuthTypeEnum.FRONT_LINE,
                                    },
                                    {
                                        label: '后线运营使用',
                                        value: AuthTypeEnum.BACK_LINE,
                                    },
                                ]}
                            />
                        </Form.Item>

                        {authTypeWatched === AuthTypeEnum.FRONT_LINE && (
                            <Form.Item
                                name={['auth', 'org']}
                                label="组织架构权限"
                            >
                                <OrgnazitionPickerRoo
                                    disabled={mode === 'view'}
                                />
                            </Form.Item>
                        )}
                    </div>
                </div>
            </Form>
            <div className="form-tip">
                如需新增参数或技能点，请联系xuezhongyuan02
            </div>
        </>
    );
};

const AgentModal: React.FC<AgentModalProps> = ({
    visible,
    onCancel,
    onSuccess,
    data,
    mode = 'create',
}) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = React.useState(false);

    useEffect(() => {
        if (visible && data && mode !== 'create') {
            const params: any = { ...data };
            params.owner = { value: data.owner.id, label: data.owner.name };
            params.agentChannel = data.agentChannel?.[0];
            params.placeholder = data.placeholder.map((item: any) => item.name);
            form.setFieldsValue(params);
        } else {
            form.resetFields();
        }
    }, [visible, data]);

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            setLoading(true);

            const params = { ...values };
            params.agentChannel = [values.agentChannel];
            if (values.owner?.value) {
                params.ownerId = values.owner?.value;
            }

            if (mode === 'edit') {
                params.id = data?.id;
            }
            const api =
                mode === 'create'
                    ? '/xianfu/api-v2/dove/agent/create'
                    : '/xianfu/api-v2/dove/agent/edit';
            // @ts-ignore
            const res = await apiCaller.post(api, params);

            if (res.code === 0) {
                message.success(`${mode === 'create' ? '创建' : '更新'}成功`);
                onSuccess?.();
            } else {
                message.error(res.msg || '操作失败');
            }
        } catch (error) {
            console.error('Form validation failed:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = () => {
        form.resetFields();
        onCancel?.();
    };

    return (
        <Modal
            title={
                mode === 'create'
                    ? '新建Agent'
                    : mode === 'edit'
                        ? '编辑Agent'
                        : '查看Agent'
            }
            open={visible}
            width={800}
            onCancel={handleCancel}
            footer={
                mode === 'view' ? (
                    <Space>
                        <Button onClick={handleCancel}>关闭</Button>
                    </Space>
                ) : (
                    <Space>
                        <Button onClick={handleCancel}>取消</Button>
                        <Button
                            type="primary"
                            loading={loading}
                            onClick={handleSubmit}
                        >
                            保存
                        </Button>
                    </Space>
                )
            }
        >
            <ModalContent form={form} mode={mode} loading={loading} />
        </Modal>
    );
};

export default AgentModal;
