import React, { useEffect, useState } from 'react';
import { Select, Input, Space, message } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import type { SelectProps } from 'antd';

interface PlaceholderItem {
    id: number;
    name: string;
    showName: string;
    status: number;
    objectType: number;
}

interface PlaceholderSelectorProps {
    value?: string[];
    onChange?: (value: string[]) => void;
    objectType?: number;
}

const PlaceholderSelector: React.FC<PlaceholderSelectorProps> = ({
    value = [],
    onChange,
    objectType,
}) => {
    const [loading, setLoading] = useState(false);
    const [options, setOptions] = useState<SelectProps['options']>([]);
    const [searchText, setSearchText] = useState('');

    const fetchPlaceholders = async () => {
        try {
            setLoading(true);
            const res = await apiCaller.post(
                '/xianfu/api-v2/dove/placeholder/query',
                {
                    name: searchText,
                    showName: '',
                    status: 1,
                    objectType: objectType as any,
                    page: 1,
                    pageSize: 100,
                },
            );

            if (res.code !== 0) {
                message.error('获取占位符列表失败');
                return;
            }

            const placeholderOptions = res.data.data.map(
                (item: PlaceholderItem) => ({
                    label: item.showName,
                    value: item.name,
                }),
            );

            setOptions(placeholderOptions);
        } catch (error) {
            console.error('获取占位符列表失败:', error);
            message.error('获取占位符列表失败');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchPlaceholders();
    }, [searchText, objectType]);

    const handleSearch = (value: string) => {
        setSearchText(value);
    };

    return (
        <Space direction="vertical" style={{ width: '100%' }}>
            <Select
                mode="multiple"
                value={value}
                onChange={onChange}
                options={options}
                loading={loading}
                onSearch={handleSearch}
                filterOption={false}
                placeholder="请选择占位符"
                style={{ width: '100%' }}
            />
        </Space>
    );
};

export default PlaceholderSelector;
