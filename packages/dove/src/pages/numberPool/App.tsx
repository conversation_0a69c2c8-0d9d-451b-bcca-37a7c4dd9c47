import React, { useEffect, useState } from 'react';
import {
    Form,
    Input,
    Select,
    Button,
    Table,
    Row,
    Col,
    Space,
    Flex,
    message,
    Tooltip,
    DatePicker,
} from 'antd';
import type { TableColumnsType } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import dayjs from 'dayjs';
import { useAbnormalReportEnums } from '../abnormalReport/hooks/useAbnormalReportEnums';
import TenantSelector from '../../components/TenantSelector';
import ProviderSelector from '../../components/ProviderSelector';
import './index.scss';
import CitySelector from '@src/components/CitySelector';
import WarningSettingsModal from './components/WarningSettingsModal';
import { useWatch } from 'antd/es/form/Form';
import CitySelectorAntd from '@src/components/CitySelectorAntd';
import { useDebounceFn } from 'ahooks';

const { RangePicker } = DatePicker;

interface NumberPoolItem {
    id: number;
    name: string;
    description: string;
    bizId: string;
    objectType: string;
    status: number;
    channelConfig: string;
    permissionConfig: string;
    creator: string;
    createTime: string;
    lastModifier: string;
    lastModifyTime: string;
}

interface PreWarningItem {
    province: string;
    city: string;
    currentAmount: number;
    warningAmount?: number;
}

enum PhoneStatus {
    停用,
    启用,
}

const NumberPool: React.FC = () => {
    const [form] = Form.useForm();
    const [tableData, setTableData] = useState<NumberPoolItem[]>([]);
    const [total, setTotal] = useState(0);
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [loading, setLoading] = useState(false);
    const [warningModalVisible, setWarningModalVisible] = useState(false);
    const [selectedTenantId, setSelectedTenantId] = useState<string>('');

    const { phoneTypes, phoneStatuses, getPhoneTypeDesc, getPhoneStatusDesc } =
        useAbnormalReportEnums();

    const typeOptions = phoneTypes?.map(item => ({
        value: item.code,
        label: item.desc,
    }));

    const statusOptions = phoneStatuses?.map(item => ({
        value: item.code,
        label: item.desc,
    }));

    const columns: TableColumnsType<NumberPoolItem> = [
        {
            title: '序号',
            key: 'index',
            width: 80,
            render: (_, __, index) => (currentPage - 1) * pageSize + index + 1,
        },
        {
            title: 'Agent名称',
            dataIndex: 'name',
            key: 'name',
        },
        {
            title: '描述',
            dataIndex: 'description',
            key: 'description',
            ellipsis: {
                showTitle: false,
            },
            render: (text: string) => (
                <Tooltip placement="topLeft" title={text}>
                    {text || '-'}
                </Tooltip>
            ),
        },
        {
            title: '业务ID',
            dataIndex: 'bizId',
            key: 'bizId',
        },
        {
            title: '对象类型',
            dataIndex: 'objectType',
            key: 'objectType',
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            render: (status: number) => (
                <span style={{ color: status === 1 ? '#52c41a' : '#ff4d4f' }}>
                    {status === 1 ? '启用' : '停用'}
                </span>
            ),
        },
        {
            title: '渠道配置',
            dataIndex: 'channelConfig',
            key: 'channelConfig',
        },
        {
            title: '权限配置',
            dataIndex: 'permissionConfig',
            key: 'permissionConfig',
        },
        {
            title: '创建人',
            dataIndex: 'creator',
            key: 'creator',
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
            title: '最后修改人',
            dataIndex: 'lastModifier',
            key: 'lastModifier',
        },
        {
            title: '最后修改时间',
            dataIndex: 'lastModifyTime',
            key: 'lastModifyTime',
            render: (time: string) => dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
            title: '操作',
            key: 'action',
            width: 200,
            render: (_, record) => (
                <Space size="middle">
                    <a onClick={() => handleView(record)}>查看</a>
                    <a onClick={() => handleEdit(record)}>编辑</a>
                    <a onClick={() => handleDelete(record)}>删除</a>
                </Space>
            ),
        },
    ];

    const fetchData = async (params: any, page, pageSize) => {
        if (!params.jupiterTenantIds) return;
        params.jupiterTenantIds = [params.jupiterTenantIds];
        params.belongingCityIds = params.belongingCityIds?.map(v => v.cityId);
        setLoading(true);
        try {
            const res: any = await apiCaller.post(
                // @ts-ignore
                '/xianfu/api-v2/dove/display/number/pool/query',
                {
                    ...params,
                    page,
                    pageSize,
                },
            );
            if (res.code === 0) {
                setTableData(res.data.data);
                setTotal(res.data.total);
            } else {
                message.error(res.msg || '获取数据失败');
            }
        } catch (error) {
            console.error('Fetch data failed:', error);
            message.error('获取数据失败');
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = async (values: any) => {
        const { createTime, ...rest } = values;
        const params = {
            ...rest,
            createTimeMin: createTime?.[0]?.unix(),
            createTimeMax: createTime?.[1]?.unix(),
        };
        setCurrentPage(1);
        await fetchData(params, 1, pageSize);
    };

    const handleTenantChange = (value: string) => {
        setSelectedTenantId(value);
        form.resetFields([
            'belongingCityIds',
            'phoneTypes',
            'phoneStatus',
            'phoneProviders',
        ]);
        form.submit();
    };

    const handleReset = () => {
        form.resetFields();
        handleSearch({});
    };

    const handleToggleOpen = async (record: NumberPoolItem) => {
        record.belongingCityId = record.belongingCity?.cityId;
        const res = await apiCaller.post(
            // @ts-ignore
            '/xianfu/api-v2/dove/display/number/pool/update',
            {
                ...record,
                phoneStatus:
                    record.phoneStatus === PhoneStatus.启用
                        ? PhoneStatus.停用
                        : PhoneStatus.启用,
            },
        );
        if (res.code === 0) {
            message.success(res.msg || '操作成功');
            form.submit();
        }
    };

    const handleToggleBackup = async (record: NumberPoolItem) => {
        record.belongingCityId = record.belongingCity?.cityId;
        const res = await apiCaller.post(
            // @ts-ignore
            '/xianfu/api-v2/dove/display/number/pool/update',
            { ...record, phoneBackup: record.phoneBackup ? 0 : 1 },
        );
        if (res.code === 0) {
            message.success(res.msg || '操作成功');
            form.submit();
        }
    };

    const handleOpenWarningModal = () => {
        if (!selectedTenantId) {
            message.warning('请先选择租户');
            return;
        }
        setWarningModalVisible(true);
    };

    const handleWarningModalCancel = () => {
        setWarningModalVisible(false);
    };

    const handleWarningModalSave = async (values: any) => {
        try {
            const alertConfigs = [values];

            const res = await apiCaller.post(
                // @ts-ignore
                '/xianfu/api-v2/dove/display/number/pool/alert/save',
                { alertConfigs },
            );

            if (res.code === 0) {
                message.success('预警设置保存成功');
                setWarningModalVisible(false);
            }
        } catch (error) {
            console.error('Failed to save warning settings:', error);
            message.error('保存预警设置失败');
        }
    };

    useEffect(() => {
        // Get displayNumber from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const displayNumber = urlParams.get('displayNumber');
        if (displayNumber) {
            form.setFieldsValue({ displayNumbers: [displayNumber] });
            form.submit();
        }
    }, []);

    const [syncing, setSyncing] = useState(false);
    const onSync = useDebounceFn(
        async () => {
            setSyncing(true);
            const res = await apiCaller.post(
                // @ts-ignore
                '/xianfu/api-v2/dove/display/number/pool/sync',
                { jupiterTenantIdList: [selectedTenantId] },
            );
            setSyncing(false);
            if (res.code === 0) {
                message.success('同步成功');
                form.submit();
            }
        },
        { wait: 200 },
    ).run;

    return (
        <div className="agent-management">
            <Form
                form={form}
                layout="horizontal"
                onFinish={handleSearch}
                className="search-form"
                labelCol={{ flex: '80px' }}
                wrapperCol={{ flex: 'auto' }}
            >
                <Row gutter={[24, 12]} className="form-row" wrap>
                    <Col span={8}>
                        <Form.Item name="bizId" label="业务ID">
                            <Input placeholder="请输入业务ID" type="number" allowClear />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item name="channel" label="渠道">
                            <Input placeholder="请输入渠道" allowClear />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item name="owner" label="所有者">
                            <Input placeholder="请输入所有者ID" type="number" allowClear />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item name="status" label="状态">
                            <Select
                                mode="multiple"
                                placeholder="请选择状态"
                                options={[
                                    { label: '启用', value: 1 },
                                    { label: '停用', value: 0 },
                                ]}
                                allowClear
                            />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item name="createTime" label="创建时间">
                            <RangePicker
                                showTime
                                format="YYYY-MM-DD HH:mm:ss"
                            />
                        </Form.Item>
                    </Col>
                </Row>
                <Row justify="end" style={{ marginBottom: 16 }}>
                    <Space>
                        <Button type="primary" htmlType="submit">
                            查询
                        </Button>
                        <Button onClick={handleReset}>重置</Button>
                        <Button type="primary" onClick={handleAdd}>
                            新建Agent
                        </Button>
                    </Space>
                </Row>
            </Form>

            <div className="table-container">
                <Table
                    columns={columns}
                    dataSource={tableData}
                    loading={loading}
                    rowKey="id"
                    pagination={{
                        current: currentPage,
                        pageSize,
                        total,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: total => `共 ${total} 条`,
                        onChange: (page, size) => {
                            setCurrentPage(page);
                            setPageSize(size);
                            fetchData(form.getFieldsValue(), page, size);
                        },
                    }}
                />
            </div>

            <WarningSettingsModal
                visible={warningModalVisible}
                onCancel={handleWarningModalCancel}
                onSave={handleWarningModalSave}
                jupiterTenantId={selectedTenantId}
            />
        </div>
    );
};

export default NumberPool;
