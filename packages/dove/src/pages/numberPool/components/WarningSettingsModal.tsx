import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, Table, message, Space } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import CitySelector from '@src/components/CitySelector';
import MisSelect from '@src/components/MisSelector';
import CitySelectorAntd from '@src/components/CitySelectorAntd';

interface RuleInfo {
    provinceId: number;
    cityId: number;
    provinceName: string;
    cityName: string;
    availableCount: number;
    alertThreshold?: number;
}

interface WarningData {
    jupiterTenantId: string;
    receivers: string[];
    ruleInfo: RuleInfo[];
}

interface WarningSettingsModalProps {
    visible: boolean;
    onCancel: () => void;
    onSave: (values: any) => void;
    jupiterTenantId: string;
}

const WarningSettingsModal: React.FC<WarningSettingsModalProps> = ({
    visible,
    onCancel,
    onSave,
    jupiterTenantId,
}) => {
    const [form] = Form.useForm();
    const [addCityForm] = Form.useForm();
    const [warningData, setWarningData] = useState<RuleInfo[]>([]);
    const [loading, setLoading] = useState(false);
    const [id, setId] = useState<number>();
    const [showAddCityModal, setShowAddCityModal] = useState(false);

    const fetchWarningData = async () => {
        setLoading(true);
        try {
            const response: any = await apiCaller.post(
                // @ts-ignore
                '/xianfu/api-v2/dove/display/number/pool/alert/query',
                {
                    jupiterTenantIds: [jupiterTenantId],
                },
            );
            if (response.code !== 0) {
                return;
            }
            if (response.data?.length > 0) {
                const data = response.data[0];
                setId(data.id);
                form.setFieldValue('receivers', data.receivers);
                setWarningData(
                    data.ruleInfo?.sort(
                        (a, b) => a.provinceId - b.provinceId,
                    ) || [],
                );
            }
        } catch (error) {
            console.error('Failed to fetch warning data:', error);
            message.error('获取预警设置失败');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (visible && jupiterTenantId) {
            fetchWarningData();
        }
    }, [visible, jupiterTenantId]);

    const handleCancel = () => {
        form.resetFields();
        onCancel();
    };

    const handleSave = async () => {
        try {
            const values = await form.validateFields();
            const submitData = {
                id,
                jupiterTenantId,
                receivers: values.receivers,
                ruleInfo: warningData,
            };
            onSave(submitData);
        } catch (error) {
            console.error('Validation failed:', error);
        }
    };

    const handleAddCity = async () => {
        try {
            const values = await addCityForm.validateFields();
            const { city, alertThreshold } = values;

            // 确保选择了城市
            if (!city?.cityId) {
                message.warning('请选择具体城市');
                return;
            }

            // 检查是否已存在相同的城市
            const exists = warningData.some(
                item => item.cityId === city.cityId,
            );
            if (exists) {
                message.warning('该城市已存在');
                return;
            }

            const newCity: RuleInfo = {
                provinceId: city.provinceId,
                cityId: city.cityId,
                provinceName: city.provinceName,
                cityName: city.cityName,
                availableCount: 0,
                alertThreshold,
            };

            setWarningData(
                [...warningData, newCity].sort(
                    (a, b) => a.provinceId - b.provinceId,
                ),
            );
            setShowAddCityModal(false);
            addCityForm.resetFields();
        } catch (error) {
            console.error('Validation failed:', error);
        }
    };

    const handleUpdateThreshold = (record: RuleInfo, value: number) => {
        const newData = warningData.map(item => {
            if (item.cityId === record.cityId) {
                return { ...item, alertThreshold: value };
            }
            return item;
        });
        setWarningData(newData);
    };

    const handleRemoveCity = (record: RuleInfo) => {
        const newData = warningData.filter(
            item => item.cityId !== record.cityId,
        );
        setWarningData(newData);
    };

    const warningColumns = [
        {
            title: '省份',
            dataIndex: 'provinceName',
            key: 'provinceName',
        },
        {
            title: '城市',
            dataIndex: 'cityName',
            key: 'cityName',
        },
        {
            title: '当前可用数量',
            dataIndex: 'availableCount',
            key: 'availableCount',
        },
        {
            title: '预警阈值',
            dataIndex: 'alertThreshold',
            key: 'alertThreshold',
            render: (text: number, record: RuleInfo) => (
                <Input
                    type="number"
                    min={0}
                    value={text}
                    onChange={e =>
                        handleUpdateThreshold(record, Number(e.target.value))
                    }
                    style={{ width: 100 }}
                />
            ),
        },
        {
            title: '操作',
            key: 'action',
            render: (_: any, record: RuleInfo) =>
                // 兜底城市
                record.cityId === -1 ? (
                    <span style={{ color: '#999' }}>不可删除</span>
                ) : (
                    <Button
                        type="link"
                        onClick={() => handleRemoveCity(record)}
                    >
                        删除
                    </Button>
                ),
        },
    ];

    return (
        <>
            <Modal
                title="号码池预警设置"
                open={visible}
                onCancel={handleCancel}
                footer={[
                    <Button key="cancel" onClick={handleCancel}>
                        取消
                    </Button>,
                    <Button key="submit" type="primary" onClick={handleSave}>
                        保存
                    </Button>,
                ]}
                width={800}
            >
                <Form form={form} layout="vertical">
                    <Form.Item
                        label="预警接收人"
                        name="receivers"
                        rules={[
                            { required: true, message: '请输入预警接收人' },
                        ]}
                    >
                        <MisSelect
                            mode={'multiple'}
                            placeholder="请输入预警接收人"
                            mis={true}
                        />
                    </Form.Item>
                </Form>

                <div
                    style={{
                        marginBottom: 16,
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                    }}
                >
                    <h4 style={{ margin: 0 }}>预警城市列表</h4>
                    <Button
                        type="primary"
                        onClick={() => setShowAddCityModal(true)}
                    >
                        添加城市
                    </Button>
                </div>

                <Table
                    columns={warningColumns}
                    dataSource={warningData}
                    rowKey="cityId"
                    loading={loading}
                    pagination={false}
                />
            </Modal>

            <Modal
                title="添加预警城市"
                open={showAddCityModal}
                onCancel={() => {
                    setShowAddCityModal(false);
                    addCityForm.resetFields();
                }}
                onOk={handleAddCity}
            >
                <Form form={addCityForm} layout="vertical">
                    <Form.Item
                        label="选择城市"
                        name="city"
                        rules={[{ required: true, message: '请选择城市' }]}
                        // @ts-ignore
                        getValueProps={value => {
                            return value
                                ? [value.provinceId, value.cityId]
                                : undefined;
                        }}
                    >
                        <CitySelectorAntd
                            maxLevel={2}
                            placeholder="请选择城市"
                        />
                    </Form.Item>
                    <Form.Item
                        label="预警阈值"
                        name="alertThreshold"
                        rules={[{ required: true, message: '请输入预警阈值' }]}
                    >
                        <Input type="number" placeholder="请输入预警阈值" />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    );
};

export default WarningSettingsModal;
