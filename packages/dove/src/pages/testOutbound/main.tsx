import { render } from '@src/module/root';
import useOutbound from '@mfe/cc-outbound/src/index';
import { Button, Input } from 'antd';
import { useState } from 'react';

const outbound = useOutbound({
    needBizSelector: false,
    bizId: 2323128,
});
const App = () => {
    const [v, setV] = useState('');
    return (
        <div>
            <Input value={v} onChange={e => setV(e.target.value.trim())} />
            <Button
                onClick={() => {
                    // @ts-ignore
                    outbound.callPhoneNumber(v, '1871821639088394308');
                }}
            >
                呼叫
            </Button>
        </div>
    );
};

render(<App />);
