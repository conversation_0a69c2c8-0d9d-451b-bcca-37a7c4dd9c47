import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useRequest } from 'ahooks';

const useObjectType = () => {
    return useRequest(async () => {
        const res = await apiCaller.post(
            '/xianfu/api-v2/dove/object/type/query',
            {},
        );
        if (res.code !== 0) {
            return [];
        }
        return res.data?.data;
    }, {});
};

export default useObjectType;
