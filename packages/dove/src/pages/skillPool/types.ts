export interface AgentItem {
    id: number;
    name: string;
    description: string;
    agentChannel: {
        type: number;
        jupiterTenantId: string;
        botId: string;
    }[];
    bizId: number;
    objectType: number;
    status: number;
    auth: {
        type: number;
        org: number[];
    };
    placeholder: string[];
    owner: {
        mis: string;
        name: string;
        id: number;
    };
    lastOperator: {
        mis: string;
        name: string;
        id: number;
    };
    createTime: number;
    updateTime: number;
}

export interface SearchFormValues {
    bizId?: string;
    name?: string;
    channel?: string;
    status?: number;
    owner?: string;
    createTime?: [string, string];
}

export interface AgentModalProps {
    visible: boolean;
    onCancel?: () => void;
    onSuccess?: () => void;
    data?: AgentItem;
    mode?: 'create' | 'edit' | 'view';
}
