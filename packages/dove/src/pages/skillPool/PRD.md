# 参数管理模块 PRD

## 1. 功能概述
参数管理模块用于管理和配置系统参数，提供参数的查询、展示等功能。

## 2. 页面结构

### 2.1 搜索区域
- **搜索条件**：
  - 参数状态：下拉选择框
  - 参数状态名：下拉选择框
  - 参数名称：输入框
  - 参数名：输入框
  - 搜索按钮：触发搜索操作

### 2.2 表格展示区域
- **表格字段**：
  - 序号
  - 参数名称
  - 参数状态
  - 参数类型
  - 参数状态
  - 协议类型
  - 操作
    - 应用/停用

### 2.3 空状态展示
- 当无数据时显示"无法停用"提示框
- 提示文案：当前配置已被使用，请先解除该配置项的所有绑定关系，然后再停用。

## 3. 功能详细说明

### 3.1 搜索功能
- 支持多条件组合搜索
- 点击搜索按钮触发查询
- 搜索条件支持重置

### 3.2 表格功能
- 支持分页展示
- 支持参数状态切换（应用/停用）
- 表格数据实时刷新

### 3.3 交互说明
- 停用参数时需进行合法性校验
- 若参数正在使用中，显示无法停用提示框

## 4. 技术实现

### 4.1 技术栈
- React + TypeScript
- Ant Design 组件库
- SCSS 样式处理

### 4.2 API 接口
使用 @mfe/cc-api-caller-pc 进行接口调用

### 4.3 数据流
1. 页面初始化加载数据
2. 搜索条件变更触发数据更新
3. 参数状态变更实时更新

## 5. 注意事项
- 需要处理各种异常情况
- 确保数据实时性
- 注意用户体验，添加适当的loading状态
- 做好错误提示 

## 6. 接口
- 列表查询path：/xianfu/api/dove/placeholder/query
- 应用参数path：/xianfu/api/dove/placeholder/active
- 停用参数path：/xianfu/api/dove/placeholder/inactive
- 接口类型定义在packages/dove/node_modules/@mfe/cc-api-caller/es/apiSpec/dynamic.d.ts

## 7. 其他
- 图示：packages/dove/src/pages/parameterManagement/image.png