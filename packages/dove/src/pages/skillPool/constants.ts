// 技能类型 COMPANY_CHANNEL(1, "企微渠道"),DOVE_IM_CHANNEL(2, "信鸽IM渠道"),INFO_IDENTIFICATION(3, "信息识别"), CRM_INFO_CHANGE(4, "供应链信息修改");
export enum SkillType {
    COMPANY_CHANNEL = 1,
    DOVE_IM_CHANNEL = 2,
    INFO_IDENTIFICATION = 3,
    CRM_INFO_CHANGE = 4,
}

export enum ParameterStatus {
    ACTIVE = 1,
    INACTIVE = 2,
}

export const StatusOptions = [
    {
        label: '生效',
        value: ParameterStatus.ACTIVE,
    },
    {
        label: '失效',
        value: ParameterStatus.INACTIVE,
    },
];

export const SkillOptions = [
    {
        label: '企微渠道',
        value: SkillType.COMPANY_CHANNEL,
    },
    {
        label: '信鸽IM渠道',
        value: SkillType.DOVE_IM_CHANNEL,
    },
    {
        label: '信息识别',
        value: SkillType.INFO_IDENTIFICATION,
    },
    {
        label: '供应链信息修改',
        value: SkillType.CRM_INFO_CHANGE,
    },
]

export const SkillTypeMap = {
    1: '企微渠道',
    2: '信鸽IM渠道',
    3: '信息识别',
    4: '供应链信息修改',
}

// API类型 THRIFT(1, "thrift"),HTTP(2, "http"),MCP(3, "mcp"),
export enum ApiType {
    THRIFT = 1,
    HTTP = 2,
    MCP = 3,
}

export const ApiTypeOptions = [
    {
        label: 'thrift',
        value: ApiType.THRIFT,
    },
    {
        label: 'http',
        value: ApiType.HTTP,
    },
    {
        label: 'mcp',
        value: ApiType.MCP,
    },
];

// 参数类型 STRING(1, "String"), INTEGER(2, "Integer"),LONG(3, "Long"), DOUBLE(4, "Double"),OBJECT(5, "Object"), BOOLEAN(6, "Boolean"),  ARRAY_STRING(7, "Array[String]"),ARRAY_INTEGER(8, "Array[Integer]"),ARRAY_LONG(9, "Array[Long]"),ARRAY_DOUBLE(10, "Array[Double]"),ARRAY_OBJECT(11, "Array[Object]"),ARRAY_BOOLEAN(12, "Array[Boolean]")
export enum ParameterType {
    STRING = 1,
    INTEGER = 2,
    LONG = 3,
    DOUBLE = 4,
    OBJECT = 5,
    BOOLEAN = 6,
    ARRAY_STRING = 7,
    ARRAY_INTEGER = 8,
    ARRAY_LONG = 9,
    ARRAY_DOUBLE = 10,
    ARRAY_OBJECT = 11,
    ARRAY_BOOLEAN = 12,
}

export const ParameterTypeOptions = [
    {
        label: 'String',
        value: ParameterType.STRING,
    },
    {
        label: 'Integer',
        value: ParameterType.INTEGER,
    },
    {
        label: 'Long',
        value: ParameterType.LONG,
    },
    {
        label: 'Double',
        value: ParameterType.DOUBLE,
    },
    {
        label: 'Object',
        value: ParameterType.OBJECT,
    },
    {
        label: 'Boolean',
        value: ParameterType.BOOLEAN,
    },
    {
        label: 'Array[String]',
        value: ParameterType.ARRAY_STRING,
    },
    {
        label: 'Array[Integer]',
        value: ParameterType.ARRAY_INTEGER,
    },
    {
        label: 'Array[Long]',
        value: ParameterType.ARRAY_LONG,
    },
    {
        label: 'Array[Double]',
        value: ParameterType.ARRAY_DOUBLE,
    },
    {
        label: 'Array[Object]',
        value: ParameterType.ARRAY_OBJECT,
    },
    {
        label: 'Array[Boolean]',
        value: ParameterType.ARRAY_BOOLEAN,
    }
];

export const WhetherOptions = [
    {
        label: '是',
        value: 1
    },
    {
        label: '否',
        value: 0
    },
];

export const RedIconStyle = {
     color: 'red', 
     marginRight: '6px'
};

export const DefaultParameterItem = {
    name: '',
    required: false,
    type: 1,
    description: ''
}

export const DefaultResultItem = {
    name: '',
    type: 1,
    description: ''
}