import {
    Form,
    Input,
    Select,
    Row,
    Col,
    Popover,
    message,
    Space,
    Button,
    Spin,
    InputNumber,
    Tabs,
    Typography,
} from 'antd';
import './AgentModal.scss';
import { FormInstance } from 'antd/es/form/Form';
import { ApiType, StatusOptions } from '../constants';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';
import { useEffect, useState } from 'react';
import ApiDocumentation from '@src/components/Skill/ApiDocumentation';
import { useRequest } from 'ahooks';

const { TextArea } = Input;

type ApiList =
    APISpec['/xianfu/api-v2/ai-infra/tool/getByMcpServer']['response'];

type ApiItem = ApiList[0];

const CreateSkillModalContent = ({
    form,
    mode,
    loading,
}: {
    form: FormInstance;
    mode: 'create' | 'edit' | 'view';
    loading: boolean;
}) => {
    // 添加搜索结果和选中项的状态
    const url = Form.useWatch(['skillConfig', 'url'], form);

    const fetchApiDetail = async (server = url) => {
        if (!server) {
            message.error('请输入mcp地址');
            return [];
        }

        const res = await apiCaller.post(
            '/xianfu/api-v2/ai-infra/tool/getByMcpServer',
            { mcpServer: server },
        );

        if (res.code !== 0) {
            return [];
        }
        const descList = res.data
            .map(it => `${it.name}: ${it.description}`)
            .join('\n');

        const description = form.getFieldValue(['skillConfig', 'description']);
        if (!description) {
            form.setFieldValue(['skillConfig', 'description'], descList);
        }

        return res.data;
    };

    const {
        data = [],
        run,
        loading: mcpLoading,
    } = useRequest(fetchApiDetail, {
        debounceWait: 300,
        manual: true,
    });

    useEffect(() => {
        const url = form.getFieldValue(['skillConfig', 'url']);
        if (!url) {
            return;
        }

        run(url);
    }, []);

    const formDisabled = mode === 'view' || loading;

    const tabItems = data.map(it => {
        return {
            key: it.name,
            label: it.name,
            children: (
                <>
                    <Typography.Paragraph>
                        技能名： {it.showName}
                    </Typography.Paragraph>
                    <Typography.Paragraph>
                        技能描述： {it.description}
                    </Typography.Paragraph>
                    <ApiDocumentation request={it.argumentList} />
                </>
            ),
        };
    });

    return (
        <div className="skill-create-modal-content">
            <Spin spinning={mcpLoading}>
                <Form
                    form={form}
                    layout="horizontal"
                    disabled={formDisabled}
                    labelCol={{ flex: '120px' }}
                    wrapperCol={{ flex: 'auto' }}
                    initialValues={{
                        skillConfig: {
                            apiType: ApiType.MCP,
                            timeout: 15,
                        },
                    }}
                >
                    <Form.Item
                        name={'skillConfig'}
                        label=""
                        initialValue={{
                            params: [],
                            result: [],
                        }}
                    >
                        <div className="section">
                            <div className="section-title">技能信息</div>
                            <div className="section-content">
                                <Row gutter={24}>
                                    <Col span={12}>
                                        <Form.Item
                                            name={['skillConfig', 'name']}
                                            label={
                                                <Space>
                                                    技能组英文名称
                                                    <Popover content="请输入技能英文名称，用于模型调用使用">
                                                        <QuestionCircleOutlined />
                                                    </Popover>
                                                </Space>
                                            }
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        '请输入技能英文名称',
                                                },
                                            ]}
                                        >
                                            <Input placeholder="请输入英文名称" />
                                        </Form.Item>
                                    </Col>
                                    <Col span={12}>
                                        <Form.Item
                                            name={['skillConfig', 'showName']}
                                            label={
                                                <Space>
                                                    技能组中文名称
                                                    <Popover content="请输入技能中文名称，用于展示使用">
                                                        <QuestionCircleOutlined />
                                                    </Popover>
                                                </Space>
                                            }
                                            rules={[
                                                {
                                                    required: true,
                                                    message:
                                                        '请输入技能中文名称',
                                                },
                                                {
                                                    max: 50,
                                                    message: '最大长度为50字符',
                                                },
                                            ]}
                                        >
                                            <Input placeholder="请输入中文名称" />
                                        </Form.Item>
                                    </Col>
                                </Row>
                                <Row gutter={16}>
                                    <Col span={19}>
                                        <Form.Item
                                            name={['skillConfig', 'url']}
                                            label="请求地址"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请选择请求地址',
                                                },
                                            ]}
                                        >
                                            <Input placeholder="请输入在公司mcp平台注册的请求信息，点击获取按钮自动加载技能描述及入参"></Input>
                                        </Form.Item>
                                    </Col>
                                    <Col span={4}>
                                        <Button
                                            type="primary"
                                            onClick={() => run()}
                                        >
                                            获取Mcp请求信息
                                        </Button>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span={24}>
                                        <Form.Item
                                            name={['skillConfig', 'timeout']}
                                            label="超时时间"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请输入超时时间',
                                                },
                                            ]}
                                        >
                                            <InputNumber
                                                addonAfter="s"
                                                style={{ width: 100 }}
                                                precision={0}
                                                min={0}
                                            />
                                        </Form.Item>
                                    </Col>
                                </Row>
                                <Row>
                                    <Col span={24}>
                                        <Form.Item
                                            name={[
                                                'skillConfig',
                                                'description',
                                            ]}
                                            label="技能组描述"
                                            rules={[
                                                {
                                                    required: true,
                                                    message: '请输入描述',
                                                },
                                            ]}
                                        >
                                            <TextArea
                                                disabled={
                                                    !data.length || formDisabled
                                                }
                                                rows={3}
                                                placeholder="点击获取按钮自动加载技能描述及入参，加载后可自由修改"
                                            />
                                        </Form.Item>
                                    </Col>
                                    <Col span={24}>
                                        {mode === 'create' ? null : (
                                            <Form.Item
                                                name={'status'}
                                                label="技能组状态"
                                            >
                                                <Select
                                                    placeholder="请选择技能状态"
                                                    style={{ width: '100%' }}
                                                    allowClear
                                                    options={StatusOptions}
                                                />
                                            </Form.Item>
                                        )}
                                    </Col>
                                </Row>
                            </div>
                        </div>

                        <div className="section">
                            <div className="section-title">MCP技能组信息</div>
                            <div className="section-content">
                                <Tabs items={tabItems} />
                            </div>
                        </div>
                    </Form.Item>
                </Form>
            </Spin>
        </div>
    );
};

export default CreateSkillModalContent;
