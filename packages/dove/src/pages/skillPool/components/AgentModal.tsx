import React, { useEffect } from 'react';
import { cloneDeep } from 'lodash';

import { apiCaller } from '@mfe/cc-api-caller-pc';
import { Modal, Form, Space, message, Button } from 'antd';
import type { AgentModalProps } from '../types';
import './AgentModal.scss';

import CreateSkillModalContent from './CreateSkillModalContent';

const AgentModal: React.FC<AgentModalProps> = ({
    visible,
    onCancel,
    onSuccess,
    data,
    mode = 'create',
}) => {
    const [form] = Form.useForm();
    const [loading, setLoading] = React.useState(false);

    useEffect(() => {
        if (visible && data && mode !== 'create') {
            const params: any = cloneDeep(data);
            if (params?.skillConfig?.params) {
                params.skillConfig.params = JSON.parse(
                    params?.skillConfig?.params,
                );
            }
            if (params?.skillConfig?.result) {
                params.skillConfig.result = JSON.parse(
                    params?.skillConfig?.result,
                );
            }
            form.setFieldsValue(params);
        } else {
            form.resetFields();
        }
    }, [visible, data]);

    const handleSubmit = async () => {
        try {
            const values = await form.validateFields();
            setLoading(true);

            const params = { ...values };
            if (values.owner?.value) {
                params.ownerId = values.owner?.value;
            }

            if (mode === 'edit') {
                params.id = data?.id;
            }

            if (params?.skillConfig?.params) {
                params.skillConfig.params = JSON.stringify(
                    params?.skillConfig?.params,
                );
            }

            if (params?.skillConfig?.result) {
                params.skillConfig.result = JSON.stringify(
                    params?.skillConfig?.result,
                );
            }

            // 编辑状态，如果params的status不存在，才使用data的status，否则就用params的
            if (mode === 'edit' && !params.status) {
                params.status = data?.status;
            }

            const api =
                mode === 'create'
                    ? '/xianfu/api-v2/dove/skill/create'
                    : '/xianfu/api-v2/dove/skill/edit';
            // @ts-ignore
            const res = await apiCaller.post(api, params);

            if (res.code === 0) {
                message.success(`${mode === 'create' ? '创建' : '更新'}成功`);
                onSuccess?.();
            } else {
                message.error(res.msg || '操作失败');
            }
        } catch (error) {
            console.error('Form validation failed:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = () => {
        form.resetFields();
        onCancel?.();
    };

    return (
        <Modal
            title={
                mode === 'create'
                    ? '新建技能组'
                    : mode === 'edit'
                    ? '编辑技能组'
                    : '查看技能组'
            }
            open={visible}
            width={850}
            onCancel={handleCancel}
            destroyOnClose
            footer={
                mode === 'view' ? (
                    <Space>
                        <Button onClick={handleCancel}>关闭</Button>
                    </Space>
                ) : (
                    <Space>
                        <Button onClick={handleCancel}>取消</Button>
                        <Button
                            type="primary"
                            loading={loading}
                            onClick={handleSubmit}
                        >
                            保存
                        </Button>
                    </Space>
                )
            }
        >
            <CreateSkillModalContent
                form={form}
                mode={mode}
                loading={loading}
            />
            {/* <MyForm /> */}
        </Modal>
    );
};

export default AgentModal;
