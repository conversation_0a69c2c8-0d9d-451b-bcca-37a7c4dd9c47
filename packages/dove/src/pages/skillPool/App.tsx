import React, { useState, useEffect } from 'react';
import {
    Form,
    Input,
    Select,
    Button,
    Table,
    message,
    Row,
    Col,
    Flex,
    Tag,
    App,
} from 'antd';
import AgentModal from './components/AgentModal';
import type { AgentItem } from './types';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import styles from './index.module.scss';
import { SkillType, ParameterStatus } from './constants';
interface ParameterItem {
    id: string;
    name: string;
    status: ParameterStatus;
    type: SkillType;
    protocolType: string;
}

const ParameterManagement: React.FC = () => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState<ParameterItem[]>([]);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });
    const [modalVisible, setModalVisible] = useState(false);
    const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>(
        'create',
    );
    const [currentRecord, setCurrentRecord] = useState<AgentItem | undefined>();
    // 获取参数列表
    const fetchParameterList = async (params: any) => {
        try {
            setLoading(true);
            const res = await apiCaller.post(
                '/xianfu/api-v2/dove/skill/query',
                {
                    ...params,
                    page: pagination.current,
                    pageSize: pagination.pageSize,
                },
            );

            if (res.code !== 0) {
                message.error(res.message || '获取参数列表失败');
                return;
            }

            // @ts-ignore
            setData(res.data.data || []);
            setPagination(prev => ({
                ...prev,
                total: res.data.total || 0,
            }));
        } catch (error) {
            console.error('获取参数列表失败:', error);
            message.error('获取参数列表失败');
        } finally {
            setLoading(false);
        }
    };

    // 处理搜索
    const handleSearch = async (values: any) => {
        setPagination(prev => ({ ...prev, current: 1 }));
        await fetchParameterList(values);
    };

    const { modal } = App.useApp();

    const handleView = record => {
        setModalMode('view');
        setCurrentRecord(record);
        setModalVisible(true);
    };

    const handleEdit = record => {
        setModalMode('edit');
        setCurrentRecord(record);
        setModalVisible(true);
    };

    const handleDelete = record => {
        modal.confirm({
            title: '确认删除该技能吗?',
            icon: null,
            onOk: async () => {
                try {
                    const res = await apiCaller.post(
                        '/xianfu/api-v2/dove/skill/delete',
                        { id: record.id },
                    );
                    if (res.code === 0) {
                        message.success('删除成功');
                        form.submit();
                    }
                } catch (error) {
                    console.error('Delete failed:', error);
                    message.error('删除失败');
                }
            },
        });
    };

    // 表格列配置
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            width: 60,
            render: (_: any, __: any, index: number) =>
                (pagination.current - 1) * pagination.pageSize + index + 1,
        },
        {
            title: '技能组英文名称',
            dataIndex: 'skillConfig',
            width: 140,
            render: (data: string, record: any, index: number) => {
                return record.skillConfig.name;
            },
        },
        {
            title: '技能组中文名称',
            dataIndex: 'skillConfig',
            width: 140,
            render: (data: string, record: any, index: number) => {
                return record.skillConfig.showName;
            },
        },
        {
            title: '技能组描述',
            dataIndex: 'skillConfig',
            width: 140,
            render: (data: string, record: any, index: number) => {
                return record.skillConfig.description;
            },
        },
        {
            title: '技能组状态',
            dataIndex: 'status',
            width: 100,
            render: (status: number) => {
                return (
                    <Tag
                        color={
                            status === ParameterStatus.ACTIVE
                                ? 'success'
                                : 'default'
                        }
                    >
                        {status === ParameterStatus.ACTIVE ? '生效' : '失效'}
                    </Tag>
                );
            },
        },
        {
            title: '被调用数量',
            width: 140,
            dataIndex: 'refCount',
        },
        {
            title: '操作',
            key: 'action',
            width: 140,
            render: (record: ParameterItem) => {
                return (
                    <div>
                        <a
                            className={styles.skillItemBtn}
                            onClick={() => handleView(record)}
                        >
                            查看
                        </a>
                        <a
                            className={styles.skillItemBtn}
                            onClick={() => handleEdit(record)}
                        >
                            编辑
                        </a>
                        <a
                            className={styles.skillItemBtn}
                            onClick={() => handleDelete(record)}
                        >
                            删除
                        </a>
                    </div>
                );
            },
        },
    ];

    // 初始加载
    useEffect(() => {
        fetchParameterList({});
    }, [pagination.current, pagination.pageSize]);

    const handleModalCancel = () => {
        setModalVisible(false);
    };

    const handleModalSuccess = () => {
        setModalVisible(false);
        form.submit();
    };

    return (
        <div className={styles.container}>
            <Form
                form={form}
                onFinish={handleSearch}
                className={styles.searchForm}
                labelCol={{ span: 5 }}
            >
                <Row gutter={24}>
                    <Col span={6}>
                        <Form.Item name="status" label="技能状态">
                            <Select
                                placeholder="请选择技能状态"
                                style={{ width: '100%' }}
                                allowClear
                                options={[
                                    {
                                        label: '生效',
                                        value: ParameterStatus.ACTIVE,
                                    },
                                    {
                                        label: '失效',
                                        value: ParameterStatus.INACTIVE,
                                    },
                                ]}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item name="name" label="英文名称">
                            <Input
                                placeholder="请输入技能组英文名称"
                                style={{ width: '100%' }}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item name="showName" label="中文名称">
                            <Input
                                placeholder="请输入技能组中文名称"
                                style={{ width: '100%' }}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Flex justify="start">
                            <Button type="primary" htmlType="submit">
                                搜索
                            </Button>
                        </Flex>
                    </Col>
                </Row>
                <Flex justify="start">
                    <Button
                        type="primary"
                        onClick={() => {
                            setModalMode('create');
                            setCurrentRecord(undefined);
                            setModalVisible(true);
                        }}
                    >
                        新建技能组
                    </Button>
                </Flex>
            </Form>
            <Table
                columns={columns}
                dataSource={data}
                rowKey="id"
                pagination={pagination}
                loading={loading}
                onChange={pagination =>
                    setPagination(prev => ({
                        ...prev,
                        current: pagination.current || 1,
                        pageSize: pagination.pageSize || 10,
                    }))
                }
            />
            <AgentModal
                visible={modalVisible}
                mode={modalMode}
                data={currentRecord}
                onCancel={handleModalCancel}
                onSuccess={handleModalSuccess}
            />
        </div>
    );
};

export default ParameterManagement;
