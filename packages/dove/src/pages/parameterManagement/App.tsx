import React, { useState, useEffect } from 'react';
import {
    Card,
    Form,
    Input,
    Select,
    Button,
    Table,
    message,
    Modal,
    Row,
    Col,
    Flex,
    Tag,
    App,
} from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import styles from './index.module.scss';

enum ParameterStatus {
    ACTIVE = 1,
    INACTIVE = 2,
}

interface ParameterItem {
    id: string;
    name: string;
    status: ParameterStatus;
    type: string;
    protocolType: string;
}

const ParameterManagement: React.FC = () => {
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState<ParameterItem[]>([]);
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });

    // 获取参数列表
    const fetchParameterList = async (params: any) => {
        try {
            setLoading(true);
            const res = await apiCaller.post(
                '/xianfu/api-v2/dove/placeholder/query',
                {
                    ...params,
                    page: pagination.current,
                    pageSize: pagination.pageSize,
                },
            );

            if (res.code !== 0) {
                message.error(res.message || '获取参数列表失败');
                return;
            }

            // @ts-ignore
            setData(res.data.data || []);
            setPagination(prev => ({
                ...prev,
                total: res.data.total || 0,
            }));
        } catch (error) {
            console.error('获取参数列表失败:', error);
            message.error('获取参数列表失败');
        } finally {
            setLoading(false);
        }
    };

    // 处理搜索
    const handleSearch = async (values: any) => {
        setPagination(prev => ({ ...prev, current: 1 }));
        await fetchParameterList(values);
    };

    // 处理重置
    const handleReset = () => {
        form.resetFields();
        handleSearch({});
    };

    const { modal } = App.useApp();
    // 处理状态切换
    const handleStatusChange = async (
        record: ParameterItem,
        newStatus: ParameterStatus,
    ) => {
        try {
            const path =
                newStatus === ParameterStatus.ACTIVE
                    ? '/xianfu/api-v2/dove/placeholder/active'
                    : '/xianfu/api-v2/dove/placeholder/inactive';

            const res = await apiCaller.post(
                path,
                // @ts-ignore
                { id: record.id },
                { silent: true },
            );

            if (res.code !== 0) {
                modal.warning({
                    icon: null,
                    title: '无法停用',
                    content: res.msg,
                    okText: '确定',
                });
                return;
            }

            message.success('操作成功');
            fetchParameterList(form.getFieldsValue());
        } catch (error) {
            console.error('状态切换失败:', error);
            message.error('操作失败');
        }
    };

    // 表格列配置
    const columns = [
        {
            title: '序号',
            dataIndex: 'index',
            width: 80,
            render: (_: any, __: any, index: number) =>
                (pagination.current - 1) * pagination.pageSize + index + 1,
        },
        {
            title: '参数名',
            dataIndex: 'name',
        },
        {
            title: '参数名称',
            dataIndex: 'showName',
        },
        {
            title: '参数状态',
            dataIndex: 'status',
            render: (status: number) => {
                return (
                    <Tag
                        color={
                            status === ParameterStatus.ACTIVE
                                ? 'success'
                                : 'default'
                        }
                    >
                        {status === ParameterStatus.ACTIVE ? '启用' : '停用'}
                    </Tag>
                );
            },
        },
        {
            title: '触达对象类型',
            dataIndex: 'objectTypeName',
        },
        {
            title: '引用次数',
            dataIndex: 'use',
        },
        {
            title: '操作',
            key: 'action',
            render: (record: ParameterItem) => (
                <Button
                    type="link"
                    onClick={() =>
                        handleStatusChange(
                            record,
                            record.status === ParameterStatus.ACTIVE
                                ? ParameterStatus.INACTIVE
                                : ParameterStatus.ACTIVE,
                        )
                    }
                >
                    {record.status === ParameterStatus.ACTIVE ? '停用' : '应用'}
                </Button>
            ),
        },
    ];

    // 初始加载
    useEffect(() => {
        fetchParameterList({});
    }, [pagination.current, pagination.pageSize]);

    return (
        <div className={styles.container}>
            <Form
                form={form}
                onFinish={handleSearch}
                className={styles.searchForm}
                labelCol={{ span: 5 }}
            >
                <Row gutter={24}>
                    <Col span={8}>
                        <Form.Item name="status" label="参数状态">
                            <Select
                                placeholder="请选择参数状态"
                                style={{ width: '100%' }}
                                allowClear
                                options={[
                                    {
                                        label: '启用',
                                        value: ParameterStatus.ACTIVE,
                                    },
                                    {
                                        label: '停用',
                                        value: ParameterStatus.INACTIVE,
                                    },
                                ]}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item name="showName" label="参数名称">
                            <Input
                                placeholder="请输入参数名称"
                                style={{ width: '100%' }}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={8}>
                        <Form.Item name="name" label="参数名">
                            <Input
                                placeholder="请输入参数名"
                                style={{ width: '100%' }}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={8}></Col>
                    <Col span={8}></Col>
                    <Col span={8}>
                        <Flex justify="end">
                            <Button type="primary" htmlType="submit">
                                搜索
                            </Button>
                            <Button
                                onClick={handleReset}
                                style={{ marginLeft: 8 }}
                            >
                                重置
                            </Button>
                        </Flex>
                    </Col>
                </Row>
            </Form>

            <Table
                columns={columns}
                dataSource={data}
                rowKey="id"
                pagination={pagination}
                loading={loading}
                onChange={pagination =>
                    setPagination(prev => ({
                        ...prev,
                        current: pagination.current || 1,
                        pageSize: pagination.pageSize || 10,
                    }))
                }
            />
        </div>
    );
};

export default ParameterManagement;
