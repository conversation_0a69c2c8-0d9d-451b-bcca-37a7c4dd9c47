import { useEffect, useState } from 'react';
import { Form, message, Space, Button } from 'antd';
import { useAntdTable } from 'ahooks';
import SearchForm from './searchForm';
import TableAntd from './TableAntd';
import { get } from './util';
import dayjs, { Dayjs } from 'dayjs';
import './index.scss';
import { QualifiedResult } from '~/pages/callRecord/types';

const Root = props => {
    const {
        noScroll,
        columnsToShow,
        // columnsToShow = [
        //     'recordId',
        //     'qcScore',
        //     {
        //         dataIndex: 'operation',
        //         render: (text, row, { markAuth, operation }) => (
        //             <Space
        //                 style={{
        //                     display: 'flex',
        //                     flexDirection: 'column',
        //                     justifyContent: 'space-around',
        //                 }}
        //             >
        //                 {Object.keys(row.operation).map(op => {
        //                     const operationName = row.operation[op];
        //                     return (
        //                         <Button
        //                             style={{ width: 80 }}
        //                             key={op}
        //                             onClick={() => {
        //                                 operation[op] && operation[op](row);
        //                             }}
        //                         >
        //                             {operationName}
        //                         </Button>
        //                     );
        //                 })}
        //                 {markAuth && (
        //                     <>
        //                         {row.markState === 2 && (
        //                             <Button
        //                                 style={{ width: 80 }}
        //                                 onClick={() =>
        //                                     operation.markTel(row.contactId)
        //                                 }
        //                             >
        //                                 标记封号
        //                             </Button>
        //                         )}
        //                         {row.markState === 1 && (
        //                             <Button
        //                                 style={{ width: 80 }}
        //                                 onClick={() =>
        //                                     operation.revokeMarkTel(
        //                                         row.contactId,
        //                                     )
        //                                 }
        //                             >
        //                                 撤销封号
        //                             </Button>
        //                         )}
        //                     </>
        //                 )}
        //             </Space>
        //         ),
        //     },
        // ],
        ...urlParams
    } = props || {};
    const [form] = Form.useForm();
    const [head, setHead] = useState({});
    const [loadingTable, setLoadingTable] = useState(false);
    const [options, setOptions] = useState([]);

    useEffect(() => {
        // Get displayNumber from URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const displayNumber = urlParams.get('displayNumber');
        if (displayNumber) {
            form.setFieldsValue({ displayNumber });
            form.submit();
        }

        get('/doveqc/callRecord/queryAIQcItem')
            .then(res => {
                if (res.code !== 0) {
                    return;
                }
                const data = res.data;
                data && setOptions(data);
            })
            .catch(err => {
                message.error({
                    content: err.msg || '初始化失败，请重试',
                    type: 'error',
                });
            });
    }, []);

    //从表单格式化得到query
    const getQuery = (current, pageSize) => {
        const query = { ...form.getFieldsValue(true) };
        const time = query.time || [];
        query.startTime = time[0]?.format('YYYY-MM-DD');
        query.endTime = time[1]?.format('YYYY-MM-DD');
        const callTime = query.callTime as Dayjs[];
        if (callTime?.length > 0) {
            const today = dayjs().startOf('day');
            query.startTalkingSecond = callTime[0].diff(today, 'second');
            query.endTalkingSecond = callTime[1].diff(today, 'second');
        } else {
            query.startTalkingSecond = -1;
            query.endTalkingSecond = -1;
        }
        query.bdUid = query.bdMis ? query.bdMis.id : undefined;
        if (query.qualifiedResult !== QualifiedResult.NOT_QUALIFIED) {
            delete query.unqualifiedItem;
        }
        delete query.time;
        delete query.bdMis;
        delete query.callTime;
        query.pageSize = pageSize;
        query.page = current;
        if (query.orgIds?.length) {
            query.orgIds = query.orgIds.join('_');
        } else {
            delete query.orgIds;
        }
        return query;
    };
    const getTableData = async ({ current, pageSize }) => {
        const query = getQuery(current, pageSize);
        setLoadingTable(true);
        return get('/doveqc/callRecord/r/getCallRecordList', query)
            .then(res => {
                if (res.code !== 0) {
                    return {
                        list: [],
                        total: 0,
                    };
                }
                const data = (res as { data }).data;
                setHead((data || {}).head || {});
                return {
                    list: (data || {}).list || [],
                    total: (data || {}).total || 0,
                };
            })
            .catch(err => {
                message.error({
                    content: err.msg || '请求出错，请重试',
                    type: 'error',
                });
                return { list: [], total: 0 };
            })
            .finally(() => {
                setLoadingTable(false);
            });
    };
    const { tableProps, search, cancel } = useAntdTable(getTableData, {
        defaultPageSize: 20,
        form,
        manual: true,
    });
    const { submit } = search;

    //导出操作
    const onExport = () => {
        const query = getQuery(
            tableProps.pagination.current,
            tableProps.pagination.pageSize,
        );
        get('/doveqc/callRecord/r/exportCallRecordList', query).then(res => {
            if (res.code !== 0) {
                message.error({
                    content: res.msg || '请求出错，请重试',
                    type: 'error',
                });
                return;
            }

            message.success({
                content: '导出任务提交成功！请等待大象通知',
                type: 'success',
            });
        });
    };

    return (
        <div style={{ overflowX: 'hidden' }}>
            <SearchForm
                {...urlParams}
                options={options}
                form={form}
                submit={() => {
                    cancel();
                    submit();
                }}
                onExport={onExport}
            />
            <TableAntd
                loadingTable={loadingTable}
                head={head}
                onSearch={submit}
                options={options}
                tableProps={tableProps}
                columnsToShow={columnsToShow}
                noScroll={noScroll}
            />
        </div>
    );
};

export default Root;
