import { bellwetherLinkParse } from '@mfe/bellwether-route';
import { Button, Table, Tag, Image, Spin, Space } from 'antd';
import { QuestionCircleOutlined, LoadingOutlined } from '@ant-design/icons';
import { useState } from 'react';
import { get, post, Toast } from './util';
import './tableAntd.scss';
import helpInfo from '~/assets/img/helpInfo.png';
import { OPTIONS, QualifiedResult } from '~/pages/callRecord/types';
import AudioDrawer from '@src/components/TaskReachDetail/AudioDrawer';
import { ColumnProps } from 'antd/es/table';

interface Props {
    head: any;
    onSearch: () => void;
    tableProps: any;
    loadingTable: boolean;
    options: any;
    columnsToShow: (string | ColumnProps)[];
    noScroll?: boolean;
}

const TableAntd = ({
    head,
    onSearch,
    tableProps,
    loadingTable,
    options,
    columnsToShow,
    noScroll,
}: Props) => {
    const [markAuth, setMarkAuth] = useState(false);

    const [loadingRecord, setLoadingRecord] = useState(false);

    const [visible, setVisible] = useState(false); //控制帮助信息（图片）预览

    const [drawerVisible, setDrawerVisible] = useState(false);
    const [drawerParams, setDrawerParams] = useState<{
        audioUrl?: string;
        contactId?: string;
        talkingSeconds?: number;
    }>({});

    const operation = {
        getMarkAuth: () => {
            get('/dovecall-web/agentCall/r/mark')
                .then(res => {
                    if (res && res.code === 0 && res.data) {
                        const { auth } = res.data;
                        setMarkAuth(!!auth);
                    }
                })
                .catch(e => {
                    console.log(e);
                });
        },
        markTel: contactId => {
            post(
                `/dovecall-web/agentCall/w/markTel?contactId=${contactId}`,
            ).then(
                () => {
                    Toast.success('操作成功');
                    setTimeout(() => {
                        onSearch();
                    }, 1000);
                },
                err => {
                    Toast.success((err && err.msg) || err || '操作失败');
                },
            );
        },
        revokeMarkTel: contactId => {
            post(
                `/dovecall-web/agentCall/w/cancel?contactId=${contactId}`,
            ).then(
                () => {
                    Toast.success('操作成功');
                    setTimeout(() => {
                        onSearch();
                    }, 1000);
                },
                err => {
                    Toast.success((err && err.msg) || err || '操作失败');
                },
            );
        },
        getCallSoundRaw: recordId =>
            get('/doveqc/callRecord/r/getSoundRecordById', {
                recordId,
            }),
        getCallSound: row => {
            setLoadingRecord(true);
            get('/doveqc/callRecord/r/getSoundRecordById', {
                recordId: row.recordId,
            })
                .then(res => {
                    setLoadingRecord(false);
                    if (res?.code === 0) {
                        setDrawerParams({
                            audioUrl: res.data.url,
                            contactId: row.recordId,
                            talkingSeconds: res.data.talkingSeconds,
                        });
                        setDrawerVisible(true);
                    }
                })
                .catch(() => {
                    setLoadingRecord(false);
                    Toast.error('无法获取录音');
                });
        },
        updateQcInfo: row => {
            window.open(
                bellwetherLinkParse(`callQC.html?recordId=${row.recordId}`),
            );
        },
    };

    let columns = [
        {
            title: '呼入/呼出时间',
            dataIndex: 'callTime',
            width: '140px',
        },
        {
            title: 'BD姓名',
            dataIndex: 'bdName',
            width: '100px',
        },
        {
            title: '商家类型',
            dataIndex: 'merchantType',
            width: '120px',
        },
        {
            title: '商家/品牌名称',
            dataIndex: 'poiName',
        },
        {
            title: '外显号码',
            dataIndex: 'displayNumber',
        },
        {
            title: 'KP信息',
            dataIndex: 'keyPerson',
            width: '100px',
        },
        {
            title: '通话类型',
            dataIndex: 'directionName',
            width: '100px',
        },
        {
            title: '通话标签',
            dataIndex: 'hangupReason',
            width: '140px',
            render: (_, row) => (
                <div className="dove__call-tag">
                    {row.hangupReason === 15 && (
                        <Tag color="#2E98AD">封号数据</Tag>
                    )}
                    {row.approve === 1 && <Tag color="#2E98AD">报备数据</Tag>}
                    {row.bizFinish === 1 && (
                        <Tag color="#2E98AD">关联工单24h内解决</Tag>
                    )}
                    {row.talkingTimeLen > 10 && (
                        <Tag color="#2E98AD">10s有效接通</Tag>
                    )}
                </div>
            ),
        },
        {
            title: '通话时长',
            dataIndex: 'talkingSeconds',
            width: '100px',
        },
        {
            title: '电话拜访',
            dataIndex: 'visitStatusName',
            width: '100px',
        },
        {
            title: (
                <>
                    AI质检结果
                    <QuestionCircleOutlined onClick={() => setVisible(true)} />
                </>
            ),
            width: '140px',
            dataIndex: 'qualifiedResult',
            render: (text, row) => {
                const descriptions = new Map([
                    [QualifiedResult.NOT_START, '未质检'],
                    [QualifiedResult.NOT_QUALIFIED, '不合格'],
                    [QualifiedResult.QUALIFIED, '合格'],
                    [QualifiedResult.DURATION_TOO_SHORT, '未质检-通话<10s'],
                    [QualifiedResult.NO_NEED, '-'],
                ]);
                return row.qualifiedUrl ? (
                    <a
                        href={row.qualifiedUrl}
                        target={'_blank'}
                        className={'link-table-antd'}
                    >
                        {descriptions.get(text)}
                    </a>
                ) : (
                    descriptions.get(text)
                );
            },
        },
        {
            title: 'AI质检不合格原因',
            dataIndex: 'unqualifiedItem',
            width: 200,
            render: text => {
                // options对应情况
                //     [1, '无开场白-公司名称&业务'],
                //     [2, '无开场白-个人介绍'],
                //     [3, '无开场白-礼貌问好'],
                //     [4, '沟通表达-抢话'],
                //     [5, '结束语-无礼貌结束语'],
                //     [6, '礼貌用语-不文明用词'],
                if (!text || !text.length) {
                    return '-';
                }
                return (
                    <div style={{ flexDirection: 'column', display: 'flex' }}>
                        {text?.map(item => (
                            <div style={{ padding: '3px' }}>
                                <Tag color={'gray'}>
                                    {
                                        (options as OPTIONS).filter(
                                            op => op.itemId === item,
                                        )[0].itemName
                                    }
                                </Tag>
                            </div>
                        ))}
                    </div>
                );
            },
        },
        {
            title: '人工质检评分',
            dataIndex: 'qcScore',
            width: '140px',
            render: (_, row) =>
                row.qcScore !== '-' ? (
                    <a
                        target="__blank"
                        href={bellwetherLinkParse(
                            `callQC.html?recordId=${row.recordId}`,
                        )}
                    >
                        {row.qcScore}
                    </a>
                ) : (
                    row.qcScore
                ),
        },
        {
            dataIndex: 'bizId',
            title: '工单ID',
            width: '100px',
            render: (_, text) => {
                const bizId = text.bizId;
                return (
                    <a
                        target="__blank"
                        className={'link-table-antd'}
                        href={bellwetherLinkParse(
                            `/page/dove/html/dove/biz/poiBizList.html?bizId=${bizId}`,
                        )}
                    >
                        {bizId}
                    </a>
                );
            },
        },
        {
            dataIndex: 'contactId',
            title: '木星通话ID',
            render: (_, row) => row.contactId || '-',
        },
        {
            dataIndex: 'recordId',
            width: 140,
            title: '信鸽电话ID',
            render: (_, row) => row.recordId || '-',
        },
        {
            dataIndex: 'operation',
            title: '操作',
            fixed: 'right',
            width: '120px',
            render: (_, row) => (
                <Space
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'space-around',
                    }}
                >
                    {Object.keys(row.operation).map(op => {
                        const operationName = row.operation[op];
                        return (
                            <Button
                                style={{ width: 80 }}
                                key={op}
                                onClick={() => {
                                    operation[op] && operation[op](row);
                                }}
                            >
                                {operationName}
                            </Button>
                        );
                    })}
                    {markAuth && (
                        <>
                            {row.markState === 2 && (
                                <Button
                                    style={{ width: 80 }}
                                    onClick={() =>
                                        operation.markTel(row.contactId)
                                    }
                                >
                                    标记封号
                                </Button>
                            )}
                            {row.markState === 1 && (
                                <Button
                                    style={{ width: 80 }}
                                    onClick={() =>
                                        operation.revokeMarkTel(row.contactId)
                                    }
                                >
                                    撤销封号
                                </Button>
                            )}
                        </>
                    )}
                </Space>
            ),
        },
    ];

    if (head.callCost) {
        columns = columns.filter(item => item.dataIndex !== 'callCost');
    }

    let finalColumns = [...columns];
    if (columnsToShow?.length) {
        finalColumns = columnsToShow
            .map(v => {
                if (typeof v === 'object') {
                    const theCol = { ...v };
                    if (v.dataIndex === 'operation') {
                        const finalRender = (text, record) =>
                            (v.render as any)?.(text, record, {
                                markAuth,
                                operation,
                                getCallSound: operation.getCallSoundRaw,
                            });
                        theCol.render = finalRender;
                        !v.title && (theCol.title = '操作');
                    }
                    return theCol;
                }
                const theCol = columns.find(
                    item => item.dataIndex === v,
                ) as any;
                return theCol;
            })
            .filter(Boolean);
    }

    return (
        <Spin
            indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />}
            spinning={loadingRecord}
        >
            <Table
                columns={finalColumns}
                rowKey="recordId"
                scroll={noScroll ? undefined : { x: 2200 }}
                loading={loadingTable}
                {...tableProps}
            />
            <Image
                width={200}
                style={{ display: 'none' }}
                src={helpInfo}
                preview={{
                    visible,
                    scaleStep: 0.5,
                    src: helpInfo,
                    onVisibleChange: value => {
                        setVisible(value);
                    },
                }}
            />
            <AudioDrawer
                key={drawerParams.contactId}
                visible={drawerVisible}
                talkingSeconds={drawerParams.talkingSeconds}
                textPath={`${import.meta.env.VITE_API_PREFIX}/data/old/text`}
                onClose={() => {
                    setDrawerVisible(false);
                    setDrawerParams({});
                }}
                {...drawerParams}
            />
        </Spin>
    );
};

export default TableAntd;
