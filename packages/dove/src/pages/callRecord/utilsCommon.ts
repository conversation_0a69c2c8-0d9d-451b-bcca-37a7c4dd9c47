import { APICallerConfig, MethodType, apiCaller } from '@mfe/cc-api-caller-pc';
import { message } from 'antd';

export function get(url, params?, config?: APICallerConfig) {
    return apiCaller.send(url, params, { method: MethodType.GET, ...config });
}

export function post(url, data?, config?: APICallerConfig) {
    return apiCaller.send(url, data, config);
}

export default {
    get,
    post,
    Toast: {
        success: message.success,
        warn: message.warning,
        error: message.error,
    },
};
