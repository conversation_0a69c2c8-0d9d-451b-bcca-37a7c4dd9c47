import {
    Button,
    Col,
    Form,
    Row,
    DatePicker,
    Select,
    Checkbox,
    TimePicker,
    Space,
    Input,
    FormInstance,
} from 'antd';
import { useEffect, useState } from 'react';
import TeamSelect from '../../components/callRecord/TeamSelect';
import dayjs, { Dayjs } from 'dayjs';
import PoiTypeSelect from '~/components/callRecord/Poi/PoiTypeSelect';
import PoiSelector from '~/components/callRecord/Poi/PoiSelector';
import MisSelectRoo from '../../components/rooPlus/MisSearch';
import OrganizationSelector from '@src/components/rooPlus/OrganizationSelector';
import AdaptiveGrid from '@src/components/AdaptiveGrid';
import useUrlState from '@src/hooks/useUrlState';
import { removeUndefinedProperties, str2number } from './util';

import weekday from 'dayjs/plugin/weekday';
import localeData from 'dayjs/plugin/localeData';

// 扩展 dayjs 插件
dayjs.extend(weekday);
dayjs.extend(localeData);

const dialTypeList = [
    { value: -1, label: '全部' },
    { value: 1, label: '呼入数据' },
    { value: 2, label: '呼出数据' },
];

const dialTagList = [
    { value: -1, label: '全部' },
    { value: 1, label: '封号数据' },
    { value: 2, label: '报备数据' },
    { value: 3, label: '关联工单24h内解决' },
    { value: 4, label: '10s有效接通' },
];

const connectionStatusList = [
    { value: -1, label: '全部' },
    { value: 1, label: '已接通' },
    { value: 2, label: '未接通' },
];

interface Props {
    form: FormInstance;
    submit: () => void;
    onExport: () => void;
    options: any[];
    // url参数
    showForm?: boolean;
    startTime?: string;
    endTime?: string;
    wmPoiId?: number;
    isFromFederation?: boolean;
}

const SearchForm = ({
    form,
    submit,
    onExport,
    options,
    showForm = true,
    isFromFederation = false,
    ...urlParams
}: Props) => {
    const [showReason, setShowReason] = useState(false);
    const bizId = Form.useWatch('bizId', form);
    const poiType = Form.useWatch('poiType', form);

    const [loaded, setLoaded] = useState(false);
    const { setUrlState, urlState } = useUrlState(urlState => {
        form.setFieldsValue({
            time: [dayjs().subtract(7, 'days'), dayjs().subtract(1, 'days')],
            direction: -1,
            isVisit: 0,
            callTag: -1,
            connectStatus: -1,
            callTime: [],
            qualifiedResult: -1,
            unqualifiedItem: -1,
        });
        const query = {
            ...removeUndefinedProperties(urlParams),
            ...removeUndefinedProperties(urlState),
        };
        if (query.startTime && query.endTime) {
            form.setFieldValue('time', [
                dayjs(query.startTime),
                dayjs(query.endTime),
            ]);
        }

        if (query.bdUid && query.bdName) {
            form.setFieldValue('bdMis', {
                id: str2number(query.bdUid),
                name: query.bdName,
            });
        }

        if (query.orgId || query.orgIds) {
            form.setFieldValue(
                'orgIds',
                (query.orgIds || query.orgId)?.split('_').map(Number),
            );
        }

        if (query.wmPoiId) {
            form.setFieldValue('wmPoiId', query.wmPoiId);
        }

        [
            'qualifiedResult',
            'bizId',
            'poiType',
            'connectStatus',
            'callTag',
            'direction',
            'unqualifiedItem',
            'recordId',
        ].forEach(key => {
            if (query[key]) {
                form.setFieldValue(key, str2number(query[key]));
                if (key === 'qualifiedResult' && Number(query[key]) === 0) {
                    setShowReason(true);
                }
            }
        });

        setLoaded(true);
    });

    useEffect(() => {
        loaded && submit();
    }, [loaded]);

    if (!loaded || !showForm || urlState.showForm === 'false') {
        return null;
    }
    const updateUrl = () => {
        // 模块联邦不修改url
        if (isFromFederation) {
            return;
        }
        // showForm为false时不需更新url query
        const values = form.getFieldsValue();
        const { time, bdMis, callTime, orgIds, ...draftOrigin } = values;
        const draft: Record<string, string | number> = Object.keys(draftOrigin)
            .filter(
                k =>
                    // -1是默认值，没必要更新到url
                    ![-1, undefined, ''].includes(draftOrigin[k]),
            )
            .map(k => ({ [k]: draftOrigin[k] }))
            .reduce(
                (pre, cur) => ({
                    ...pre,
                    ...cur,
                }),
                {},
            );

        if (bdMis) {
            draft.misId = bdMis.login;
        }
        if (orgIds?.length) {
            draft.orgIds = orgIds.join('_');
        }
        if (time) {
            draft.startTime = (time[0] as Dayjs).format('YYYY-MM-DD');
            draft.endTime = (time[1] as Dayjs).format('YYYY-MM-DD');
        }
        setUrlState(draft);
    };
    return (
        <Form onValuesChange={updateUrl} layout="vertical" form={form}>
            <AdaptiveGrid>
                <Form.Item label="业务线：" name="bizId">
                    <TeamSelect />
                </Form.Item>
                <Form.Item label="选择时间：" name="time">
                    <DatePicker.RangePicker
                        style={{
                            display: 'flex',
                        }}
                        format={'YYYY-MM-DD'}
                    />
                </Form.Item>
                <Form.Item label="组织结构：" name={'orgIds'}>
                    <OrganizationSelector
                        multiple
                        onInit={ids => {
                            const orgIds = form.getFieldValue('orgIds');
                            if (!orgIds?.length) {
                                form.setFieldValue('orgIds', ids);
                            }
                            submit();
                        }}
                    />
                </Form.Item>
                <Form.Item label="BD：" name="bdMis">
                    <MisSelectRoo
                        misId={urlState?.misId}
                        onInit={item => {
                            form.setFieldValue('bdMis', item);
                            // setFieldValue不会出发onValuesChange，所以手动触发
                            updateUrl();
                            submit();
                        }}
                    />
                </Form.Item>
                <Form.Item label="呼叫类型：" name="direction">
                    <Select options={dialTypeList} />
                </Form.Item>
                <Form.Item label="商家类型：" name="poiType">
                    <PoiTypeSelect bizId={form.getFieldValue('bizId')} />
                </Form.Item>
                <Form.Item label="商家：" name="wmPoiId">
                    <PoiSelector poiType={poiType} bizId={bizId} />
                </Form.Item>
                <Form.Item label="通话标签：" name="callTag">
                    <Select
                        placeholder="请选择通话标签"
                        options={dialTagList}
                    />
                </Form.Item>
                <Form.Item label="是否接通：" name="connectStatus">
                    <Select
                        placeholder="请选择是否接通"
                        options={connectionStatusList}
                    />
                </Form.Item>
                <Form.Item label="通话时长范围：" name="callTime">
                    <TimePicker.RangePicker style={{ display: 'flex' }} />
                </Form.Item>
                <Form.Item label="AI质检结果：" name="qualifiedResult">
                    <Select
                        onChange={v => {
                            setShowReason(v === 0);
                            form.setFieldValue('unqualifiedItem', -1);
                        }}
                        options={[
                            { value: -1, label: '全部' },
                            { value: 0, label: '不合格' },
                            { value: 1, label: '合格' },
                        ]}
                    ></Select>
                </Form.Item>
                {showReason && (
                    <Form.Item
                        label="AI质检不合格原因："
                        name="unqualifiedItem"
                    >
                        <Select
                            options={[
                                { value: -1, label: '全部' },
                                ...options.map(item => ({
                                    label: item.itemName,
                                    value: item.itemId,
                                })),
                            ]}
                        ></Select>
                    </Form.Item>
                )}
                <Form.Item label="信鸽电话ID" name="recordId">
                    <Input placeholder="请输入信鸽电话ID" />
                </Form.Item>
                <Form.Item label="外显号码：" name="displayNumber">
                    <Input placeholder="请输入号码" allowClear />
                </Form.Item>
            </AdaptiveGrid>

            <Row justify={'space-between'}>
                <Col span={4}>
                    <Form.Item name={'isVisit'}>
                        <Checkbox
                            onChange={() => {
                                form.setFieldValue(
                                    'isVisit',
                                    +!form.getFieldValue('isVisit'),
                                );
                            }}
                        >
                            已录入电话拜访
                        </Checkbox>
                    </Form.Item>
                </Col>
                <Col
                    span={3}
                    style={{ display: 'flex', justifyContent: 'end' }}
                >
                    <Space>
                        <Button onClick={submit} type={'primary'}>
                            查询
                        </Button>
                        <Button onClick={onExport} type={'primary'}>
                            导出
                        </Button>
                    </Space>
                </Col>
            </Row>
        </Form>
    );
};
export default SearchForm;
