audio::-internal-media-controls-download-button {
    display: none;
}

audio::-webkit-media-controls-enclosure {
    overflow: hidden;
}

audio::-webkit-media-controls-panel {
    width: calc(100% + 30px);
}
audio {
    z-index: 10;
}

.filter-wrapper {
    margin-left: 20px;
}

.filter-row {
    margin-bottom: 15px;
}

.filter-item {
    display: inline-block;
    width: 24%;
    vertical-align: bottom;
    margin: 0 1% 0 0;

    .mrc-input-calendar {
        width: auto;
    }
}

.filter-btns {
    float: right;

    .btn {
        margin-left: 20px;
    }
}

.mrc-calendar-container {
    width: 47%;
}

.table-operation {
    margin-right: 5px;
    border: none;
    outline: none;
    font-size: 12px;
    color: #00abe4;

    &:hover {
        text-decoration: underline;
    }
}

.dove__call-tag {
    .roo-tag {
        display: inline-block;
        margin: 2px 0;
    }

    &:empty {
        &::before {
            content: '-';
        }
    }
}

.brand-select {
    .ant-select-selection {
        height: 36px;
        line-height: 36px;
        .ant-select-selection__rendered {
            height: 36px;
        }
    }

}
