import React, { useCallback } from 'react';
import { Table, Modal } from 'antd';
import { useAntdTable } from 'ahooks';
import moment from 'moment';
import { MethodType, apiCaller } from '@mfe/cc-api-caller-pc';

interface ReminderModalProps {
    bizId: number;
    visible: boolean;
    onVisibleChange: (v: boolean) => void;
}

enum ReminderType {
    POI, // 商家来电
    BIZ_SERVICE, // 商服
}

const literal = {
    [ReminderType.BIZ_SERVICE]: '商服催单',
    [ReminderType.POI]: '商家电话催单',
};
const ReminderModal = (props: ReminderModalProps) => {
    const { bizId, visible, onVisibleChange } = props;
    const getData = useCallback(
        ({ current, pageSize }) =>
            apiCaller
                .xSend(
                    '/impc/task/reminder/r/list',
                    {
                        pageNum: current,
                        pageSize,
                        bizId,
                    },
                    { method: MethodType.GET },
                )
                .then(res => {
                    if (res.code !== 0) {
                        return {
                            list: [],
                            total: 0,
                        };
                    }

                    return {
                        list: res.data.dataList,
                        total: res.data.total,
                    };
                })
                .catch(() => ({
                    list: [],
                    total: 0,
                })),
        [props.bizId],
    );

    const { tableProps } = useAntdTable(getData, {
        defaultPageSize: 20,
    });
    const columns = [
        {
            title: '催单时间',
            dataIndex: 'createTime',
            render: createTime =>
                moment(createTime * 1000).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
            title: '催单来源',
            dataIndex: 'source',
            render: source => literal[source] || '-',
        },
    ];

    return (
        <Modal
            title="催单记录"
            visible={visible}
            onCancel={() => onVisibleChange(false)}
            footer={null}
        >
            <Table
                columns={columns}
                rowKey="id"
                {...tableProps}
                locale={{ emptyText: '暂无催单记录' }}
            />
        </Modal>
    );
};

export default ReminderModal;
