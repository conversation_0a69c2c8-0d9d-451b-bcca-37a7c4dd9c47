import React, { useEffect, useRef } from 'react';
import useOutBound, { CalleeType, CallerType } from '@mfe/cc-outbound';
import { Button, message } from 'antd';

type UnReadonly<T> = T extends Readonly<infer U> ? U : never;

const enum BizType {
    HUNTER = 1,
    FARMER,
}

interface BizDoveCall {
    phone: number;
    bizType: BizType;
    poiId: number;
    onFinined?: () => void;
}

const BizDoveCall = (props: BizDoveCall) => {
    const { phone, bizType, poiId } = props;
    const callPhoneNumber = useRef<any>(null);

    const calleeType =
        bizType === BizType.FARMER ? CalleeType.WM_POI : CalleeType.HUNTER_TASK;

    useEffect(() => {
        const outbound = useOutBound(
            {
                callerType: CallerType.TELEMARKETING,
                calleeType: CalleeType.HUNTER_TASK,
                preCallPrefix: import.meta.env.VITE_API_PREFIX,
                onEventError: e => message.error(e.ErrorMessage),
                onEventReleased: props.onFinined,
            },
            [],
        );
        callPhoneNumber.current = outbound.callPhoneNumber;
    }, []);

    const makeCall = () => {
        if (!callPhoneNumber.current) {
            return;
        }
        callPhoneNumber.current(phone, poiId, {
            calleeType,
        });
    };

    if (![BizType.FARMER, BizType.HUNTER].includes(bizType) || !phone) {
        return null;
    }

    return (
        // @ts-ignore
        <Button type="link" onClick={makeCall}>
            外呼商家
        </Button>
    );
};

export default BizDoveCall;
