import React, { useState } from 'react';
import {
    DatePicker,
    Select,
    Form,
    Table,
    Modal,
    Button,
    Input,
    message,
    Spin,
    Tooltip,
    Space,
} from 'antd';
import './index.scss';
import { DoveCallType } from './poiBizTypes';
import ReminderModal from './RemindModal';
import BizDoveCall from './BizDoveCall';
import CloseBizModal from './CloseBizModal';
import BizModal from './BizModal';
import '@roo/roo/theme/blue/index.css';
import dayjs from 'dayjs';
import MisSearch from '@src/components/rooPlus/MisSearch';
import OrganizationSelector from '@src/components/rooPlus/OrganizationSelector';
import PoiSelector from '@src/components/callRecord/Poi/PoiSelector';
import AdaptiveGrid from '@src/components/AdaptiveGrid';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import BizTags from '@src/components/biz/BizTags';
import { useRequest } from 'ahooks';
import useLoginUser from '@src/hooks/useLoginUser';

const { RangePicker } = DatePicker;
const { Option } = Select;

const selectOptions = [
    {
        name: '是',
        value: 1,
    },
    {
        name: '全部',
        value: -1,
    },
];

const withNoSelectOptions = selectOptions.concat({ name: '否', value: 0 });

const bizStatusOptions = [
    // {
    //     name: '全部',
    //     value: null,
    // },
    {
        name: '待响应',
        value: 10,
    },
    {
        name: '进行中',
        value: 20,
    },
    {
        name: '已处理',
        value: 30,
    },
];

const bizTypeOptions = [
    {
        name: 'IM 工单',
        value: DoveCallType.IM,
    },
    {
        name: '电话工单',
        value: DoveCallType.CALL,
    },
    {
        name: '在线电话工单',
        value: DoveCallType.ONLINE_CALL,
    },
    {
        name: '信鸽电话工单',
        value: DoveCallType.DOVE_CALL,
    },
    {
        name: '全部',
        value: -1,
    },
];

const bizRemindCountOptions = [
    {
        name: '0次',
        value: 0,
    },
    {
        name: '1次',
        value: 1,
    },
    {
        name: '2次',
        value: 2,
    },
    {
        name: '大于等于3次',
        value: 3,
    },
    {
        name: '全部',
        value: -1,
    },
];

const columns = ({ setState, handleSearchList, user }) => [
    {
        title: '工单id',
        dataIndex: 'bizId',
        width: 80,
    },
    {
        title: '工单名称',
        dataIndex: 'bizName',
        width: 100,
    },
    {
        title: '工单状态',
        width: 100,
        dataIndex: 'status',
        render: status => {
            const key = bizStatusOptions.find(item => item.value === status);
            return key ? key.name : '-';
        },
    },
    {
        title: '工单类型',
        width: 100,
        dataIndex: 'bizChannel',
        render: channel => {
            switch (channel) {
                case DoveCallType.IM:
                    return 'IM 工单';
                case DoveCallType.CALL:
                    return '电话工单';
                case DoveCallType.ONLINE_CALL:
                    return '在线电话工单';
                case DoveCallType.DOVE_CALL:
                    return '信鸽电话工单';
                default:
                    return '';
            }
        },
    },
    {
        title: '催单次数',
        width: 100,
        render: data => {
            const { reminderCount, bizId } = data;
            return (
                <span
                    className="reminder-count"
                    onClick={() => {
                        setState({
                            remindModalVisible: true,
                            bizId,
                        });
                    }}
                >
                    {reminderCount || 0}次
                </span>
            );
        },
    },
    {
        title: '响应时长（分钟）',
        width: 100,
        dataIndex: 'replyDuration',
    },
    {
        title: '解决时长（分钟）',
        width: 100,
        dataIndex: 'finishDuration',
    },
    {
        title: '工单关联标签',
        width: 150,
        dataIndex: 'bizTag',
        render: tags => <BizTags tags={tags} />,
    },
    {
        title: '首次分配给bd的时间',
        width: 150,
        dataIndex: 'fisrtDispatchTime',
    },
    {
        title: '工单响应时间',
        width: 150,
        dataIndex: 'replyTime',
    },
    {
        title: '工单关闭时间',
        width: 150,
        dataIndex: 'finishTime',
    },
    {
        title: '处理人',
        width: 150,
        dataIndex: 'unameAndMis',
    },
    {
        title: '所在目录',
        width: 100,
        dataIndex: 'catalogue',
        render: catalogue => (catalogue && catalogue.join('-')) || '-',
    },
    {
        title: '关联拜访',
        width: 120,
        dataIndex: 'visitObjectVos',
        render: poiVoList =>
            (poiVoList &&
                poiVoList.map(item => {
                    const { visitId } = item;
                    const VisitDetailUrl = `/page/jaguar/visit/record-detail?id=${visitId}`;
                    return (
                        <a
                            key={item.bizId}
                            href={VisitDetailUrl}
                            target="_blank"
                            rel="noreferrer"
                        >
                            {visitId}
                        </a>
                    );
                })) ||
            '-',
    },
    {
        title: '关联通话记录',
        dataIndex: 'dovecallIdList',
        width: 150,
        render: (dovecallIdList, record) => (
            <Space direction={'vertical'}>
                {(dovecallIdList &&
                    dovecallIdList.map(v => {
                        return (
                            <a
                                href={`/page/dove/callRecord?recordId=${v}&startTime=${dayjs(
                                    record.fisrtDispatchTime,
                                ).format('YYYY-MM-DD')}&endTime=${dayjs(
                                    record.fisrtDispatchTime,
                                )
                                    .add(1, 'month')
                                    .format('YYYY-MM-DD')}`}
                                target="_blank"
                            >
                                {v}
                            </a>
                        );
                    })) ||
                    '-'}
            </Space>
        ),
    },
    {
        title: '关联IM聊天记录',
        dataIndex: 'im',
        width: 150,
        render: (_, item) => {
            const isOwner = user.uid === item.uid;
            return (
                <Tooltip
                    title={
                        !isOwner
                            ? '为保护隐私，不可查看非本人会话消息'
                            : undefined
                    }
                >
                    <Button
                        disabled={!isOwner}
                        type={'link'}
                        onClick={() => {
                            window.open(
                                `${
                                    import.meta.env.DEV ? '' : '/page/dove'
                                }/doveIM?bizId=${item.bizId}`,
                            );
                        }}
                    >
                        点击跳转
                    </Button>
                </Tooltip>
            );
        },
    },
    {
        title: '工单创建时间',
        dataIndex: 'bizCreateTime',
        width: 150,
    },
    {
        title: '发起人',
        dataIndex: 'poiIdAndName',
        width: 150,
    },
    {
        title: '发起人归属组织架构',
        dataIndex: 'orgNamePath',
        width: 400,
    },
    {
        title: '操作',
        width: 200,
        fixed: 'right',
        render: data => {
            const { status, bizId, bizName, showTransToBusiness, showEndBiz } =
                data;

            return (
                <div className="operation">
                    <BizDoveCall {...data} onFinined={handleSearchList} />
                    {status <= 20 ? (
                        <Button
                            className="authView"
                            data-auth-url="/impc/dispatchBiz"
                            auth-url-prefix={import.meta.env.VITE_API_PREFIX}
                            type="link"
                            onClick={() => {
                                setState({
                                    keyData: { bizId, bizName },
                                    showTransModal: true,
                                });
                            }}
                        >
                            分配工单
                        </Button>
                    ) : null}
                    {status <= 20 ? (
                        <Button
                            className="authView"
                            data-auth-url="/impc/closeBiz"
                            auth-url-prefix={import.meta.env.VITE_API_PREFIX}
                            type="link"
                            onClick={() => {
                                setState({
                                    keyData: { bizId, bizName },
                                    showCloseBizModal: true,
                                });
                            }}
                        >
                            关闭工单
                        </Button>
                    ) : null}
                    {showTransToBusiness ? (
                        <Button
                            type="link"
                            onClick={() => {
                                setState({
                                    showTransToBusinessModal: true,
                                    keyData: { bizId },
                                });
                            }}
                        >
                            转给商服
                        </Button>
                    ) : null}
                    {showEndBiz ? (
                        <Button
                            type="link"
                            onClick={() => {
                                setState({
                                    showEndBizModal: true,
                                    keyData: { bizId },
                                });
                            }}
                        >
                            结束工单
                        </Button>
                    ) : null}
                </div>
            );
        },
    },
];

const parseTimeFromFilter = (query, key) => {
    const value = query[key];
    let newValue;
    if (value) {
        newValue = key.includes('EndTime')
            ? dayjs(value).endOf('day').unix()
            : dayjs(value).startOf('day').unix();
    }
    query[key] = newValue;
};

const transFilterToQuery = filter => {
    const query = JSON.parse(JSON.stringify(filter));
    [
        'bizCreateBeginTime',
        'bizCreateEndTime',
        'firstDispatchBeginTime',
        'firstDispatchEndTime',
        'firstReplyBeginTime',
        'firstReplyEndTime',
        'firstFinishBeginTime',
        'firstFinishEndTime',
    ].forEach(parseTimeFromFilter.bind(null, query));
    query.uid = query.conductor && query.conductor.id;
    query.hunterTaskId = +query.hunterTaskId || undefined;
    return query;
};
const formatStatusList = raw => (raw ? raw.split(',').map(Number) : []);

const query = new URLSearchParams(window.location.search);
const initData = {
    unFinishFor24h: -1,
    unRespFor15m: -1,
    bizCreateBeginTime: query.get('startTime')
        ? dayjs(query.get('startTime'))
        : dayjs().subtract(7, 'd'),
    bizCreateEndTime: query.get('endTime')
        ? dayjs(query.get('endTime'))
        : dayjs().add(1, 'd'),
    orgIdList:
        query.get('orgId') && Number.isInteger(+query.get('orgId'))
            ? [+query.get('orgId')]
            : undefined,
    bizId: +query.get('bizId') || undefined,
    bizIds: query.get('bizIds') || undefined,
    status: -1,
    statusList: formatStatusList(query.get('statusList')),
    bizChannel: -1,
    isVisit: -1,
    isDoveCall: -1,
    bizType: -1,
    pageNum: 1,
    pageSize: 20,
    poiId: undefined,
    reminderCount: -1,
    hunterTaskId: undefined,
};

class PoiBizList extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            isFilterUnFold: false,
            showTransModal: false,
            keyData: {
                bizId: query.get('bizId') || undefined,
                bizName: '',
            },
            filter: { ...initData },
            person: undefined,
            total: 0,
            bizId: null,
            remindModalVisible: false,
            showTransToBusinessModal: false,
            showEndBizModal: false,
            showCloseBizModal: false,
            toBusinessVal: undefined,
            endBizVal: undefined,
        };
    }

    componentDidMount = () => {
        this.handleSearchList();
        const defaultTracker = window.LXAnalytics('getTracker');
        defaultTracker('pageView', {}, {}, 'c_waimai_m_69d94yq8');
    };

    handleSearchList = () => {
        const { filter } = this.state;
        const bizListQuery = transFilterToQuery(filter);

        this.props.search(bizListQuery);
    };

    handleFilterChange = (key, value, needToFetch) => {
        const { filter } = this.state;
        filter[key] = value;
        this.setState({ filter }, () => {
            if (needToFetch) {
                this.handleSearchList();
            }
        });
    };

    renderUnFoldButton = () => {
        const { isFilterUnFold } = this.state;
        const clickCallback = () =>
            this.setState({ isFilterUnFold: !isFilterUnFold });
        return isFilterUnFold ? (
            <span onClick={clickCallback} className="group-button-fold">
                <DownOutlined />
                展开
            </span>
        ) : (
            <span onClick={clickCallback} className="group-button-fold">
                <UpOutlined />
                收起
            </span>
        );
    };

    handleTransBiz = () => {
        const {
            person,
            keyData: { bizId },
        } = this.state;
        apiCaller
            .post('/impc/message/w/dispatchBiz', {
                bizId,
                uid: person.id,
            })
            .then(res => {
                if (res.code === 0) {
                    message.success('分配成功');
                    setTimeout(() => {
                        this.setState({ showTransModal: false });
                        window.AnimationPlaybackEventlocation.reload();
                    }, 1000);
                }
            });
    };
    handlerEndTask = reason => {
        const {
            keyData: { bizId },
        } = this.state;
        apiCaller
            .post('/impc/message/w/endTask', { bizId, reason })
            .then(res => {
                if (res.code === 0) {
                    message.success('提交成功');
                    setTimeout(() => {
                        this.setState({ showEndBizModal: false });
                        this.handleSearchList();
                    }, 1000);
                }
            })
            .catch(() => {
                this.setState({
                    showEndBizModal: false,
                });
            });
    };

    handlerToBusiness = reason => {
        const {
            keyData: { bizId },
        } = this.state;
        apiCaller
            .post('/impc/message/w/trans', { bizId, reason })
            .then(res => {
                if (res.code === 0) {
                    message.success('提交成功');
                    setTimeout(() => {
                        this.setState({ showTransToBusinessModal: false });
                        this.handleSearchList();
                    }, 1000);
                }
            })
            .catch(() => {
                this.setState({
                    showEndBizModal: false,
                });
            });
    };

    handleExport = () => {
        const { filter } = this.state;
        const bizListQuery = transFilterToQuery(filter);
        apiCaller
            .post('/impc/message/r/exportBizList', bizListQuery)
            .then(res => {
                if (res.code === 0) {
                    res.data && window.open(res.data);
                }
            });
    };

    render = () => {
        const {
            isFilterUnFold,
            showTransModal,
            keyData,
            filter,
            person,
            bizId,
            remindModalVisible,
            showTransToBusinessModal,
            showEndBizModal,
            showCloseBizModal,
        } = this.state;
        const { data = {}, loading } = this.props;
        const { bizList = [], total = 0 } = data;

        return (
            <div>
                <div className="biz-filter bizForm">
                    <Form layout="vertical">
                        <AdaptiveGrid limit={isFilterUnFold ? 8 : undefined}>
                            <Form.Item label="组织架构">
                                <OrganizationSelector
                                    value={filter.orgIdList}
                                    multiple
                                    onInit={ids => {
                                        this.handleFilterChange(
                                            'orgIdList',
                                            ids,
                                            true,
                                        );
                                    }}
                                    onChange={ids => {
                                        this.handleFilterChange(
                                            'orgIdList',
                                            ids,
                                        );
                                    }}
                                    params={{
                                        backtrackOrgType: 3,
                                    }}
                                />
                            </Form.Item>
                            <Form.Item label="超24h未解决" required>
                                <Select
                                    value={filter.unFinishFor24h}
                                    onChange={value => {
                                        this.handleFilterChange(
                                            'unFinishFor24h',
                                            value,
                                        );
                                    }}
                                >
                                    {selectOptions.map(op => (
                                        <Option key={op.value} value={op.value}>
                                            {op.name}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>

                            <Form.Item label="超15min未响应" required>
                                <Select
                                    value={filter.unRespFor15m}
                                    onChange={value => {
                                        this.handleFilterChange(
                                            'unRespFor15m',
                                            value,
                                        );
                                    }}
                                >
                                    {selectOptions.map(op => (
                                        <Option key={op.value} value={op.value}>
                                            {op.name}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                            <Form.Item label="工单创建时间" required>
                                <RangePicker
                                    style={{ width: '100%' }}
                                    value={[
                                        filter.bizCreateBeginTime,
                                        filter.bizCreateEndTime,
                                    ]}
                                    onChange={([start, end] = []) => {
                                        this.handleFilterChange(
                                            'bizCreateBeginTime',
                                            start,
                                        );
                                        this.handleFilterChange(
                                            'bizCreateEndTime',
                                            end,
                                        );
                                    }}
                                />
                            </Form.Item>
                            <Form.Item label="工单id">
                                <Input
                                    placeholder="请输入工单id"
                                    value={filter.bizId || filter.bizIds}
                                    onChange={e => {
                                        const { value } = e.target;
                                        this.handleFilterChange('bizId', value);
                                    }}
                                />
                            </Form.Item>
                            <Form.Item label="工单名称">
                                <Input
                                    placeholder="请输入工单名称"
                                    value={filter.bizName}
                                    onChange={e => {
                                        const { value } = e.target;
                                        this.handleFilterChange(
                                            'bizName',
                                            value,
                                        );
                                    }}
                                />
                            </Form.Item>
                            <Form.Item label="工单状态" required>
                                <Select
                                    value={filter.statusList}
                                    placeholder="请选择"
                                    mode="multiple"
                                    onChange={value => {
                                        this.handleFilterChange(
                                            'statusList',
                                            value,
                                        );
                                    }}
                                >
                                    {bizStatusOptions.map(op => (
                                        <Option key={op.value} value={op.value}>
                                            {op.name}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                            <Form.Item label="工单类型" required>
                                <Select
                                    value={filter.bizChannel}
                                    onChange={value => {
                                        this.handleFilterChange(
                                            'bizChannel',
                                            value,
                                        );
                                    }}
                                >
                                    {bizTypeOptions.map(op => (
                                        <Option key={op.value} value={op.value}>
                                            {op.name}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                            <Form.Item label="首次分配给BD时间">
                                <RangePicker
                                    style={{ width: '100%' }}
                                    value={[
                                        filter.firstDispatchBeginTime,
                                        filter.firstDispatchEndTime,
                                    ]}
                                    onChange={([start, end] = []) => {
                                        this.handleFilterChange(
                                            'firstDispatchBeginTime',
                                            start,
                                        );
                                        this.handleFilterChange(
                                            'firstDispatchEndTime',
                                            end,
                                        );
                                    }}
                                />
                            </Form.Item>
                            <Form.Item label="工单响应时间">
                                <RangePicker
                                    style={{ width: '100%' }}
                                    value={[
                                        filter.firstReplyBeginTime,
                                        filter.firstReplyEndTime,
                                    ]}
                                    onChange={([start, end] = []) => {
                                        this.handleFilterChange(
                                            'firstReplyBeginTime',
                                            start,
                                        );
                                        this.handleFilterChange(
                                            'firstReplyEndTime',
                                            end,
                                        );
                                    }}
                                />
                            </Form.Item>
                            <Form.Item label="工单解决时间">
                                <RangePicker
                                    style={{ width: '100%' }}
                                    value={[
                                        filter.firstFinishBeginTime,
                                        filter.firstFinishEndTime,
                                    ]}
                                    onChange={([start, end] = []) => {
                                        this.handleFilterChange(
                                            'firstFinishBeginTime',
                                            start,
                                        );
                                        this.handleFilterChange(
                                            'firstFinishEndTime',
                                            end,
                                        );
                                    }}
                                />
                            </Form.Item>
                            <Form.Item label="处理人">
                                <MisSearch
                                    value={filter.conductor}
                                    misId={query.get('misId')}
                                    onInit={value => {
                                        this.handleFilterChange(
                                            'conductor',
                                            value,
                                            true,
                                        );
                                    }}
                                    onChange={value => {
                                        this.handleFilterChange(
                                            'conductor',
                                            value,
                                        );
                                    }}
                                />
                            </Form.Item>
                            <Form.Item label="是否关联拜访" required>
                                <Select
                                    value={filter.isVisit}
                                    onChange={value => {
                                        this.handleFilterChange(
                                            'isVisit',
                                            value,
                                        );
                                    }}
                                >
                                    {withNoSelectOptions.map(op => (
                                        <Option key={op.value} value={op.value}>
                                            {op.name}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                            <Form.Item label="是否关联通话记录" required>
                                <Select
                                    value={filter.isDoveCall}
                                    onChange={value => {
                                        this.handleFilterChange(
                                            'isDoveCall',
                                            value,
                                        );
                                    }}
                                >
                                    {withNoSelectOptions.map(op => (
                                        <Option key={op.value} value={op.value}>
                                            {op.name}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                            <Form.Item label="发起人">
                                <PoiSelector
                                    value={filter.poiId}
                                    onChange={poiInfo => {
                                        this.handleFilterChange(
                                            'poiId',
                                            poiInfo,
                                        );
                                    }}
                                />
                            </Form.Item>
                            <Form.Item label="催单次数" required>
                                <Select
                                    value={filter.reminderCount}
                                    onChange={value => {
                                        this.handleFilterChange(
                                            'reminderCount',
                                            value,
                                        );
                                    }}
                                >
                                    {bizRemindCountOptions.map(op => (
                                        <Option key={op.value} value={op.value}>
                                            {op.name}
                                        </Option>
                                    ))}
                                </Select>
                            </Form.Item>
                            <Form.Item label="自入驻任务ID">
                                <Input
                                    style={{ height: 34 }}
                                    placeholder="请输入自入驻任务ID"
                                    value={filter.hunterTaskId}
                                    onChange={e => {
                                        const { value } = e.target;
                                        this.handleFilterChange(
                                            'hunterTaskId',
                                            value,
                                        );
                                    }}
                                />
                            </Form.Item>
                        </AdaptiveGrid>
                    </Form>
                    <div className="button-group">
                        <Button
                            className="button-group-button"
                            type="primary"
                            onClick={() => {
                                this.handleFilterChange('pageNum', 1, true);
                            }}
                        >
                            搜索
                        </Button>
                        <Button
                            className="button-group-button"
                            type="primary"
                            onClick={() => {
                                this.setState({
                                    filter: {
                                        ...JSON.parse(JSON.stringify(initData)),
                                        bizIds: undefined,
                                    },
                                });
                            }}
                        >
                            重置
                        </Button>
                        <Button
                            className="button-group-button"
                            type="primary"
                            onClick={this.handleExport.bind(this)}
                        >
                            导出
                        </Button>
                        {this.renderUnFoldButton()}
                    </div>
                </div>
                <div className="biz-table">
                    <Table
                        loading={loading}
                        columns={columns({
                            setState: this.setState.bind(this),
                            handleSearchList: this.handleSearchList,
                            user: this.props.user,
                        })}
                        dataSource={bizList}
                        scroll={{ x: 1300 }}
                        rowKey="bizId"
                        pagination={{
                            total,
                            pageSize: 20,
                            onChange: page => {
                                this.handleFilterChange('pageNum', page, true);
                            },
                        }}
                    />
                </div>
                {bizId && remindModalVisible && (
                    <ReminderModal
                        bizId={bizId}
                        visible={remindModalVisible}
                        onVisibleChange={v =>
                            this.setState({ remindModalVisible: v })
                        }
                    />
                )}
                <Modal
                    title="分配工单"
                    visible={showTransModal}
                    onOk={() => {
                        this.handleTransBiz();
                    }}
                    onCancel={() => {
                        this.setState({
                            showTransModal: false,
                            keyData: {},
                            person: undefined,
                        });
                    }}
                    okText="确认"
                    cancelText="取消"
                >
                    <Form>
                        <Form.Item label="工单名字">
                            <Input value={keyData.bizName} disabled />
                        </Form.Item>
                        <Form.Item label="处理人" required>
                            <MisSearch
                                value={person}
                                onChange={p => {
                                    if (p) {
                                        this.setState({ person: p });
                                    } else {
                                        this.setState({ person: undefined });
                                    }
                                }}
                            />
                        </Form.Item>
                    </Form>
                </Modal>

                <BizModal
                    visible={showEndBizModal}
                    title="请解决商家问题后结束工单，确定继续此操作吗?"
                    cancel={() => {
                        this.setState({
                            showEndBizModal: false,
                        });
                    }}
                    onOk={reason => {
                        this.handlerEndTask(reason);
                    }}
                />

                <BizModal
                    visible={showTransToBusinessModal}
                    title="请确认工单问题不在您的职责范围内再转给商服，确定继续此操作吗?"
                    cancel={() => {
                        this.setState({
                            showTransToBusinessModal: false,
                        });
                    }}
                    onOk={reason => {
                        this.handlerToBusiness(reason);
                    }}
                />

                <CloseBizModal
                    bizId={keyData.bizId}
                    open={showCloseBizModal}
                    onCancel={() => this.setState({ showCloseBizModal: false })}
                    onOk={() => {
                        this.setState({ showCloseBizModal: false });
                        this.handleSearchList();
                    }}
                ></CloseBizModal>
            </div>
        );
    };
}

const WrapperPoiBizList = () => {
    const [state, setState] = useState({ total: 0, bizList: [] });
    const { user } = useLoginUser();

    const fethchList = async params => {
        cancel();
        const res = await apiCaller.post('/impc/message/r/getBizList', params);

        if (res.code !== 0) {
            return;
        }
        setState({
            bizList: res.data.bizListVos,
            total: res.data.total,
        });

        // return {
        //     bizList: res.data.bizListVos,
        //     total: res.data.total,
        // };
    };

    const { run, loading, cancel } = useRequest(fethchList, {
        manual: true,
    });
    if (!user) {
        return <Spin>加载中</Spin>;
    }
    return (
        <PoiBizList search={run} loading={loading} data={state} user={user} />
    );
};

export default WrapperPoiBizList;
