import { Form, Modal, Input } from 'antd';
import { useWatch } from 'antd/es/form/Form';

const MessageForm = props => {
    const message = useWatch('message', props.form) || '';

    return (
        <Form form={props.form}>
            <Form.Item
                rules={[
                    {
                        required: true,
                        message: '请输入解决方案',
                    },
                ]}
                name="message"
            >
                <Input.TextArea
                    placeholder="请输入解决方案"
                    maxLength={50}
                    rows={6}
                    showCount
                />
            </Form.Item>
        </Form>
    );
};

const EndBizModal = props => {
    const [form] = Form.useForm();

    const { visible, title, cancel } = props;

    const submit = () => {
        const formData = form.getFieldsValue();
        if (!formData.message) {
            return;
        }
        props.onOk(formData.message);
    };

    return (
        <Modal
            title={title}
            visible={visible}
            onCancel={cancel}
            onOk={submit}
            closable={false}
        >
            <MessageForm form={form} />
        </Modal>
    );
};

export default EndBizModal;
