import { Form, Modal, Typography, Upload, message } from 'antd';
import { UploadFile } from 'antd/lib/upload/interface';
import React, { useEffect, useState } from 'react';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { getPrefix } from '@src/module/request/getPrefix';
import { PlusCircleFilled, PlusOutlined } from '@ant-design/icons';

interface CloseBizModal {
    bizId: number;
    open: boolean;
    onCancel: () => void;
    onOk: () => void;
}

const enum FileType {
    IMAGE,
    AUDIO,
}

const formlayout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 14 },
};

const CloseBizModal = (props: CloseBizModal) => {
    const [fileList, setFileList] = useState({
        images: [] as UploadFile[],
        audios: [] as UploadFile[],
    });

    const isUploading = (arr: UploadFile[] = []) =>
        arr.some(f => f.status === 'uploading');

    const isError = (arr: UploadFile[] = []) =>
        arr.some(f => f.status === 'error');

    const prepare = () => {
        const { audios, images } = fileList;

        if (isUploading([...audios, ...images])) {
            message.error('请等待上传完成~');
            return;
        }

        if (isError([...audios, ...images])) {
            message.warning('上传失败的图片/音频将会被移除哦～');
        }

        return {
            imageUrlList: images.map(f => f.response?.data).filter(Boolean),
            audioUrlList: audios.map(f => f.response?.data).filter(Boolean),
        };
    };

    const onCloseBiz = async () => {
        const params = prepare();
        if (!params) {
            return;
        }

        const { imageUrlList, audioUrlList } = params;
        if (!imageUrlList.length && !audioUrlList.length) {
            message.error('请至少上传图片或音频中的一种');
            return;
        }
        const res = await apiCaller.xSend('/impc/message/w/endTask', {
            bizId: props.bizId,
            imageUrlList,
            audioUrlList,
        });

        if (res.code !== 0) {
            return;
        }
        message.success('已完成关单');
        props.onOk();
    };

    const handleChange = (
        { fileList }: { fileList: UploadFile[] },
        type: FileType,
    ) => {
        if (FileType.AUDIO === type) {
            setFileList(x => ({ ...x, audios: fileList }));
            return;
        }
        setFileList(x => ({ ...x, images: fileList }));
    };

    useEffect(() => {
        setFileList({ images: [], audios: [] });
    }, [props.bizId]);

    const actionUrl = `${getPrefix('/impc/task/upload')}/impc/task/upload`;
    return (
        <Modal
            title="请上传关单材料"
            visible={props.open}
            onCancel={props.onCancel}
            onOk={onCloseBiz}
        >
            <Typography.Text type="secondary">
                图片证明、音频证明二选一即可
            </Typography.Text>
            <Form layout="horizontal" {...formlayout} style={{ marginTop: 20 }}>
                <Form.Item
                    label="上传图片"
                    extra="包括但不仅限于你的微信、企微等各个通讯工具的聊天记录截图"
                >
                    <Upload
                        listType="picture-card"
                        accept="image/*"
                        action={actionUrl}
                        fileList={fileList.images}
                        multiple
                        onChange={list => handleChange(list, FileType.IMAGE)}
                    >
                        <div>
                            <PlusOutlined />
                            <div className="ant-upload-text">上传图片</div>
                        </div>
                    </Upload>
                </Form.Item>

                <Form.Item
                    label="上传音频"
                    extra="包括但不仅限于你帮商家处理问题的通话记录等等"
                >
                    <Upload
                        accept="audio/*"
                        listType="picture-card"
                        action={actionUrl}
                        fileList={fileList.audios}
                        multiple
                        onChange={list => handleChange(list, FileType.AUDIO)}
                    >
                        <div>
                            <PlusOutlined />
                            <div className="ant-upload-text">上传音频</div>
                        </div>
                    </Upload>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default CloseBizModal;
