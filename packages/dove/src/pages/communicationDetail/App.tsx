import React, { useMemo, useState } from 'react';
import {
    Form,
    Input,
    Select,
    DatePicker,
    Button,
    Row,
    Space,
    message,
    InputNumber,
} from 'antd';
import { UpOutlined, DownOutlined } from '@ant-design/icons';
import './App.scss';
import TableList from './components/TableList';
import {
    reachMethodOptions,
    executionStatusOptions,
    reachSystemoptions,
    ReachSystemEnum,
    ReachMethodEnum,
    reachSystemMap,
} from '@src/constants';
import MisSelect from '@src/components/MisSelector';
import TeamSelect from '@src/components/callRecord/TeamSelect';
import SelectAgent from "@src/components/SelectAgent";
import OrganizationSelector from '@src/components/rooPlus/OrganizationSelector';
import useObjectType from '@src/hooks/useObjectType';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import AdaptiveGrid from '@src/components/AdaptiveGrid';
import InputCommaSplit from '@src/components/InputCommaSplit';
import dayjs from 'dayjs';

interface SearchListProps {
    onSearch?: (values: any) => void;
}

const { RangePicker } = DatePicker;

export const getSupportedReachMethods = (
    reachSystem: ReachSystemEnum[],
): ReachMethodEnum[] => {
    const supportedMethods: ReachMethodEnum[] = [];

    reachSystemMap.forEach((system, method) => {
        if (!reachSystem?.length || reachSystem.includes(system)) {
            supportedMethods.push(method);
        }
    });

    return supportedMethods;
};

const SearchList: React.FC<SearchListProps> = ({ onSearch }) => {
    const [form] = Form.useForm();
    const [isExpanded, setIsExpanded] = useState(false);
    const [searchParams, setSearchParams] = useState<{
        bizId?: number[];
        orgId?: number[];
        taskId?: number[];
        staffId?: number;
        contactObjectType?: number[];
        submitTimeMin?: number;
        submitTimeMax?: number;
        contactType?: number[];
        reachStatus?: number[];
        startTimeMin?: number;
        startTimeMax?: number;
        executionStatus?: number[];
        rank?: string[];
        page?: number;
        pageSize?: number;
    }>({});

    // 统一的 Form.Item 布局配置
    const formItemLayout = {
        // labelCol: { span: 8 },
        // wrapperCol: { span: 16 },
    };

    const prepareParams = () => {
        const values = form.getFieldsValue(true);
        const {
            contactId,
            system,
            contactType,
            contactTimeRange,
            createTimeRange,
            contactObjectName,
            contactResult,
            ...rest
        } = values;

        const availableContacTypes = getSupportedReachMethods(system);

        const params = {
            ...rest,
            submitTimeMin: createTimeRange?.[0]?.valueOf(),
            submitTimeMax: createTimeRange?.[1]?.valueOf(),
            startTimeMin: contactTimeRange?.[0]
                ? contactTimeRange[0].valueOf()
                : undefined,
            startTimeMax: contactTimeRange?.[1]
                ? contactTimeRange[1].valueOf()
                : undefined,
            executionStatus: contactResult ? [contactResult] : undefined,
            taskId: values.taskId ? [Number(values.taskId)] : undefined,
            contactId: contactId ? [contactId] : undefined,
            contactType: contactType?.length
                ? contactType
                : availableContacTypes?.length
                    ? availableContacTypes
                    : undefined,
            staffId: values.staffId?.length
                ? Number(values.staffId[0])
                : undefined,
            contactObjectName: contactObjectName || undefined,
        };

        return params;
    };

    const handleSearch = () => {
        const params = prepareParams();
        setSearchParams(params);
        onSearch?.(params);
    };

    const handleReset = () => {
        form.resetFields();
        setSearchParams({});
    };

    const { data: objectTypeList } = useObjectType();
    const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

    const handleExport = async () => {
        const params = prepareParams();
        const taskId = new URLSearchParams(window.location.search).get('id');
        const res = await apiCaller.post('/xianfu/api-v2/dove/data/export', {
            ...params,
            taskId: params.taskId || Number(taskId),
            contactIdList: selectedRowKeys as any,
        });
        if (res.code !== 0) {
            return;
        }
        message.success('导出成功');
    };

    const talkingTimeLenMin = Form.useWatch('talkingTimeLenMin', form);
    const talkingTimeLenMax = Form.useWatch('talkingTimeLenMax', form);
    const system = Form.useWatch('system', form);

    const supportedReachMethodOptions = useMemo(() => {
        if (!system) {
            return reachMethodOptions;
        }
        const supportedReachMethods = getSupportedReachMethods(system);

        return reachMethodOptions.filter(it =>
            supportedReachMethods.includes(it.value),
        );
    }, [system]);

    const talkTimeError =
        talkingTimeLenMin &&
        talkingTimeLenMax &&
        talkingTimeLenMin > talkingTimeLenMax;

    return (
        <div className="communication-detail-list">
            <Form
                form={form}
                onFinish={handleSearch}
                initialValues={{
                    contactTimeRange: [
                        dayjs().subtract(6, 'day').startOf('day'),
                        dayjs().add(1, 'day').startOf('day'),
                    ],
                }}
                layout="vertical"
            >
                <AdaptiveGrid
                    visibleMode
                    limit={isExpanded ? undefined : 7}
                    justify={'start'}
                >
                    <Form.Item
                        {...formItemLayout}
                        label="沟通记录ID"
                        name="contactId"
                    >
                        <InputNumber
                            placeholder="请输入沟通记录ID"
                            maxLength={20}
                            controls={false}
                            style={{ width: '100%' }}
                            precision={0}
                            min={0}
                            stringMode
                        />
                    </Form.Item>
                    <Form.Item
                        {...formItemLayout}
                        label="商家ID"
                        name="contactObjectId"
                    >
                        <InputCommaSplit placeholder="请输入商家ID" />
                    </Form.Item>
                    <Form.Item
                        {...formItemLayout}
                        label="商家名称"
                        name="contactObjectName"
                    >
                        <Input placeholder="请输入商家名称" maxLength={20} />
                    </Form.Item>

                    <Form.Item
                        {...formItemLayout}
                        label="发起人"
                        name="staffId"
                    >
                        <MisSelect
                            placeholder="请输入"
                            mis={false}
                            mode={'multiple'}
                        />
                    </Form.Item>

                    <Form.Item label="触达时间" name="contactTimeRange">
                        <RangePicker style={{ width: '100%' }} showTime />
                    </Form.Item>

                    <Form.Item
                        {...formItemLayout}
                        label="发起系统"
                        name="system" // 这个是一个计算值，实际体现在触达类型上
                    >
                        <Select
                            placeholder="请选择"
                            style={{ width: '100%' }}
                            options={reachSystemoptions}
                            mode="multiple"
                            allowClear
                            onChange={() => form.resetFields(['contactType'])}
                        ></Select>
                    </Form.Item>
                    <Form.Item
                        {...formItemLayout}
                        label="执行状态"
                        name="contactResult"
                    >
                        <Select
                            placeholder="请选择"
                            style={{ width: '100%' }}
                            options={executionStatusOptions}
                            allowClear
                        ></Select>
                    </Form.Item>
                    <Form.Item
                        {...formItemLayout}
                        label="关联任务 ID"
                        name="taskId"
                    >
                        <Input placeholder="请输入" />
                    </Form.Item>

                    <Form.Item label="任务创建时间" name="createTimeRange">
                        <RangePicker style={{ width: '100%' }} showTime />
                    </Form.Item>

                    <Form.Item
                        {...formItemLayout}
                        label="触达类型"
                        name="contactType"
                    >
                        <Select
                            placeholder="请选择"
                            style={{ width: '100%' }}
                            options={supportedReachMethodOptions}
                            mode="multiple"
                        ></Select>
                    </Form.Item>
                    <Form.Item
                        {...formItemLayout}
                        label="商家类型"
                        name="contactObjectType"
                    >
                        <Select
                            placeholder="请选择"
                            style={{ width: '100%' }}
                            options={objectTypeList?.map(item => ({
                                label: item.name,
                                value: item.objectType,
                            }))}
                            mode="multiple"
                            allowClear
                        />
                    </Form.Item>
                    <Form.Item
                        {...formItemLayout}
                        label="业务租户"
                        name="bizId"
                    >
                        <TeamSelect
                            mode="multiple"
                            optionPath="/xianfu/api-v2/dove/staff/biz/query"
                            onChange={console.log}
                        />
                    </Form.Item>
                    <Form.Item
                        {...formItemLayout}
                        label="组织结构"
                        name="orgId"
                    >
                        <OrganizationSelector
                            multiple
                            onInit={ids => {
                                const orgIds = form.getFieldValue('orgId');
                                if (!orgIds?.length) {
                                    form.setFieldValue('orgId', ids);
                                }
                                form.submit();
                            }}
                        />
                    </Form.Item>
                    <Form.Item
                        {...formItemLayout}
                        label="沟通时长"
                        validateStatus={talkTimeError ? 'error' : undefined}
                        help={
                            talkTimeError
                                ? '沟通开始时间不能大于沟通结束时间'
                                : ''
                        }
                    >
                        <Space>
                            <Form.Item
                                noStyle
                                name="talkingTimeLenMin"
                                normalize={value => value && value * 1000}
                                getValueProps={value => value && value / 1000}
                            >
                                <InputNumber
                                    precision={0}
                                    min={0}
                                    placeholder="最小时长"
                                />
                            </Form.Item>
                            <Space>
                                <span>{'<='}</span>
                                <span>时</span>
                                <span>长</span>
                                <span>{'<='}</span>
                            </Space>
                            <Form.Item
                                noStyle
                                name="talkingTimeLenMax"
                                normalize={value => value && value * 1000}
                                getValueProps={value => value && value / 1000}
                            >
                                <InputNumber
                                    precision={0}
                                    min={0}
                                    placeholder="最大时长"
                                />
                            </Form.Item>
                        </Space>
                    </Form.Item>
                    <Form.Item
                        {...formItemLayout}
                        label="关联agent"
                        name="agentId"
                    >
                        <SelectAgent
                            mode="multiple"
                            maxTagCount="responsive"
                            allowClear
                            placeholder="请选择关联agent"
                        />
                    </Form.Item>
                </AdaptiveGrid>
                <div className="communication-detail-op">
                    <Form.Item label=" " colon={false}>
                        <Space direction="vertical" style={{ marginRight: 14 }}>
                            <Row justify="end">
                                <Button
                                    type="link"
                                    onClick={() => setIsExpanded(!isExpanded)}
                                    style={{ marginRight: 8 }}
                                >
                                    {isExpanded ? '收起' : '展开'}{' '}
                                    {isExpanded ? (
                                        <UpOutlined />
                                    ) : (
                                        <DownOutlined />
                                    )}
                                </Button>
                                <Button
                                    style={{ marginRight: 8 }}
                                    onClick={handleReset}
                                >
                                    重置
                                </Button>
                                <Button type="primary" htmlType="submit">
                                    查询
                                </Button>
                            </Row>
                            <Row justify="end">
                                <Button type="primary" onClick={handleExport}>
                                    导出
                                </Button>
                            </Row>
                        </Space>
                    </Form.Item>
                </div>
            </Form>

            <TableList
                searchParams={searchParams as any}
                onRowSelect={setSelectedRowKeys}
            />
        </div>
    );
};

export default SearchList;
