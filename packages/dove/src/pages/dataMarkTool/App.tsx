import React from 'react';
import { Button, Empty, Typography } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import TaskList from './components/TaskList';
import CreateTaskModal from './components/CreateTaskModal';
import styles from './index.module.scss';
import TaskDetailList from './components/TaskDetailList';
import { useTaskStore, useViewStore, ViewMode } from './store';

const { Title } = Typography;

const titleStyle = {
    display: 'flex',
    alignItems: 'center',
    height: '64px',
    position: 'fixed',
    zIndex: 99,
    width: '100%',
    background: '#fff',
};

const App: React.FC = () => {
    // 从store中获取状态和方法
    const viewMode = useViewStore(state => state.viewMode);
    const backToTaskList = useViewStore(state => state.backToTaskList);
    const currentTask = useTaskStore(state => state.currentTask);

    // 渲染页面标题
    const renderPageTitle = () => {
        switch (viewMode) {
            case ViewMode.TASK_LIST:
                return null;
            case ViewMode.TASK_DETAIL:
                return (
                    <div style={titleStyle}>
                        <Button
                            icon={<ArrowLeftOutlined />}
                            style={{ marginRight: 16 }}
                            onClick={backToTaskList}
                        >
                            返回任务列表
                        </Button>
                        <Title level={4} style={{ margin: '0 140px 0 0' }}>
                            {currentTask?.name || '任务详情'}
                        </Title>

                        <Title level={4} style={{ margin: 0 }}>
                            对话详情
                        </Title>
                    </div>
                );
            default:
                return null;
        }
    };

    // 渲染主要内容
    const renderContent = () => {
        switch (viewMode) {
            case ViewMode.TASK_LIST:
                return <TaskList />;
            case ViewMode.TASK_DETAIL:
                return currentTask ? (
                    <TaskDetailList taskId={currentTask.id} />
                ) : (
                    <Empty description="未选择任务" />
                );
            default:
                return null;
        }
    };

    return (
        <div className={styles.container}>
            {renderPageTitle()}
            {renderContent()}
            <CreateTaskModal />
        </div>
    );
};

export default App;
