import { create } from 'zustand';
import { useTaskDetailStore } from './taskDetailStore';
import { useDialogStore } from './dialogStore';
// 视图模式枚举
export enum ViewMode {
  TASK_LIST = 'taskList',
  TASK_DETAIL = 'taskDetail',
}

// 视图状态接口
interface ViewState {
  // 当前视图模式
  viewMode: ViewMode;
  // 设置视图模式
  setViewMode: (mode: ViewMode) => void;
  // 返回任务列表
  backToTaskList: () => void;
  // 返回任务详情
  backToTaskDetail: () => void;
}

// 创建视图状态store
export const useViewStore = create<ViewState>((set) => ({
  // 初始视图模式为任务列表
  viewMode: ViewMode.TASK_LIST,
  
  // 设置视图模式
  setViewMode: (mode: ViewMode) => set({ viewMode: mode }),
  
  // 返回任务列表
  backToTaskList: () => {
    const detailState = useTaskDetailStore.getState();
    const dialogState = useDialogStore.getState();
    detailState.setCurrentTaskDetail(null);
    dialogState.setEditingMessageId(null);
    dialogState.setHasUnsavedChanges(false);
    set({ viewMode: ViewMode.TASK_LIST });
  },
  // 返回任务详情
  backToTaskDetail: () => set({ viewMode: ViewMode.TASK_DETAIL }),
}));