import { create } from 'zustand';
import { message } from 'antd';
import { isEmpty } from 'lodash';
import { OperateType, Task, TASK_TYPE_TEXT } from '../types';
import { useViewStore, ViewMode } from './viewStore';

// 任务列表状态接口
interface TaskState {
  // 任务列表数据
  taskList: Task[];
  // 任务总数
  taskTotal: number;
  // 加载状态
  loading: boolean;
  // 搜索参数
  searchParams: any;
  // 当前选中的任务
  currentTask: Task | null;
  // 创建任务模态框状态
  createTaskModalVisible: boolean;
  // 任务模态框编辑状态
  createTaskModalEditStatus: boolean;
  // 创建任务加载状态
  createTaskLoading: boolean;
  // 权限映射
  authMap: any;

  // 获取任务列表
  fetchTaskList: (params?: any) => Promise<void>;
  // 删除任务
  deleteTask: (id: string) => Promise<void>;
  // 暂停任务
  pauseTask: (id: string) => Promise<void>;
  // 恢复任务
  resumeTask: (id: string) => Promise<void>;
  // 查看任务详情
  viewTask: (task: Task) => void;
  // 设置当前任务
  setCurrentTask: (task: Task | null) => void;
  // 设置创建任务模态框状态
  setCreateTaskModalVisible: (visible: boolean, isEdit?: boolean) => void;
  // 创建任务
  createTask: (values: any, isEdit: boolean, dataGrabReqs) => Promise<void>;
  // 获取任务数据数量
  fetchTaskDataCount: (values: any, esItemSearchReqs) => Promise<number>;
  // 获取筛选项
  fetchSearchItem: (type) => Promise<any>;
  // 获取config
  fetchTaskConfig: (taskId) => Promise<any>;
  // 获取权限
  fetchAuth: () => Promise<any>;
}

// 创建任务列表状态store
export const useTaskStore = create<TaskState>((set, get) => ({
  // 初始化状态
  taskList: [],
  taskTotal: 0,
  loading: false,
  searchParams: {},
  currentTask: null,
  createTaskModalVisible: false,
  createTaskModalEditStatus: false,
  createTaskLoading: false,
  authMap: {},

  fetchAuth: async () => {
    const res: any = await apiCaller.post(
      // @ts-ignore
      '/xianfu/api-v2/dove/mark_task/uac_auth',
      {
        "uacCode": ["MARK_TASK_ALL_DATA"]
      }
    );

    if (res.code === 0) {
      set({ authMap: res.data });
      return res.data;
    } else {
      return null;
    }
    
  },
  // 获取config
  fetchTaskConfig: async (taskId) => {
    const res: any = await apiCaller.post(
      // @ts-ignore
      '/xianfu/api-v2/dove/mark_task/query_config',
      {
        taskId
      }
    );

    if (res.code === 0) {
      return res.data;
    } else {
      return null;
    }
  },
  // 获取任务列表
  fetchTaskList: async (params = {}) => {
    try {
      set({ loading: true, searchParams: params });
      // 实际项目中应该调用API

      const reqParams = {
        taskId: params.taskId,
        taskType: params.taskType,
        status: params.taskStatus,
        poiId: params.shopId,
        creatorMis: params.creator,
        followerMis: params.follower,
        dataType: params.dataType,
        dataId: params.dataId,
        page: 1 || params.page, // TODO: 待补充
        pageSize: 10 || params.pageSize // TODO: 待补充
      }
      const res: any = await apiCaller.post(
        // @ts-ignore
        '/xianfu/api-v2/dove/mark_task/list',
        reqParams
      );

      if (res.code === 0) {
        const { total = 0, data = [] } = res?.data || {};
        const taskData = data.map(item => ({
          id: item.taskId,
          name: item.taskName,
          rule: item.markStrategy,
          type: TASK_TYPE_TEXT[item.taskType],
          status: item.status,
          creator: {
            uid: item.creatorMis,
            name: item.creatorMis,
          },
          taskCnt: item.taskCnt,
          createTime: item.createTime,
          endTime: item.finishTime,
          joinNum: item.participantCount,
          progress: item.taskProgress,
          timeSpent: item.taskDuration,
        }));

        set({ taskList: taskData, taskTotal: total, loading: false });
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败，请重试');
    } finally {
      set({ loading: false });
    }
  },

  // 删除任务
  deleteTask: async (id: string) => {
    try {
      // 实际项目中应该调用API
      const reqParams = {
        taskId: id,
        operateType: OperateType.DELETE
      }
      const res: any = await apiCaller.post(
        // @ts-ignore
        '/xianfu/api-v2/dove/mark_task/operate',
        reqParams
      );
      console.log('debug deleteTask res', res, reqParams, id);

      if (res.code === 0) {
        message.success('任务删除成功');
        // 刷新任务列表
        get().fetchTaskList(get().searchParams);
      }
    } catch (error) {
      console.error('删除任务失败:', error);
      message.error('删除任务失败，请重试');
    }
  },

  // 暂停任务
  pauseTask: async (id: string) => {
    try {
      // 实际项目中应该调用API
      const reqParams = {
        taskId: id,
        operateType: OperateType.PAUSE
      }
      const res: any = await apiCaller.post(
        // @ts-ignore
        '/xianfu/api-v2/dove/mark_task/operate',
        reqParams
      );
      console.log('debug pauseTask res', res, reqParams, id);

      if (res.code === 0) {
        message.success('任务已暂停');
        // 刷新任务列表
        get().fetchTaskList(get().searchParams);
      }
    } catch (error) {
      console.error('暂停任务失败:', error);
      message.error('暂停任务失败，请重试');
    }
  },

  // 恢复任务
  resumeTask: async (id: string) => {
    try {
      // 实际项目中应该调用API
      const reqParams = {
        taskId: id,
        operateType: OperateType.START
      }
      const res: any = await apiCaller.post(
        // @ts-ignore
        '/xianfu/api-v2/dove/mark_task/operate',
        reqParams
      );

      if (res.code === 0) {
        message.success('任务已启动');
        // 刷新任务列表
        get().fetchTaskList(get().searchParams);
      }
    } catch (error) {
      console.error('恢复任务失败:', error);
      message.error('恢复任务失败，请重试');
    }
  },

  // 查看任务详情
  viewTask: (task: Task) => {
    set({ currentTask: task });
    useViewStore.getState().setViewMode(ViewMode.TASK_DETAIL);
    // 获取任务详情列表
    useTaskDetailStore.getState().fetchTaskDetailList({ taskId: task.id });
  },

  // 设置当前任务
  setCurrentTask: (task: Task | null) => set({ currentTask: task }),

  // 设置创建任务模态框状态
  setCreateTaskModalVisible: (visible: boolean, isEdit?: boolean) => set({
    createTaskModalVisible: visible,
    createTaskModalEditStatus: !!isEdit
  }),

  // 创建任务
  createTask: async (values: any = {}, isEdit: boolean, dataGrabReqs) => {
    try {
      const {
        dataCards,
        followPersons: misIdList,
        taskName,
        taskRule: markStrategy,
        taskType: type,
        taskId,
      } = values;
      set({ createTaskLoading: true });

      let reqParams: any;

      let res: any;
      if (isEdit) {
        reqParams = {
          taskName,
          taskId: taskId || 1,
          markStrategy
        }
        res = await apiCaller.post(
          // @ts-ignore
          '/xianfu/api-v2/dove/mark_task/edit',
          reqParams
        );
      } else {
        reqParams = {
          taskName,
          type,
          // status: 1, // 确认从哪取
          markStrategy,
          misIdList,
          dataGrabReqs,
          config: JSON.stringify(dataCards)
        }
        res = await apiCaller.post(
          // @ts-ignore
          '/xianfu/api-v2/dove/mark_task/create',
          reqParams
        );
      }

      console.log('debug createTask res', res, reqParams);

      if (res.code === 0) {
        message.success(`任务${isEdit ? '保存' : '新建'}成功`);
        set({ createTaskModalVisible: false });
        // 刷新任务列表
        get().fetchTaskList(get().searchParams);
      }
    } catch (error) {
      console.error(`${isEdit ? '保存' : '新建'}任务失败:`, error);
      message.error(`${isEdit ? '保存' : '新建'}任务失败，请重试`);
    } finally {
      set({ createTaskLoading: false });
    }
  },
  // 获取任务数据数量
  fetchTaskDataCount: async (values: any = {}, esItemSearchReqs): Promise<number> => {
    try {
      const {
        dataType: type,
        dataRange,
        merchantType: poiType,
        filterConditions = []
      } = values;
      const hasIllegalData = filterConditions.filter(item => {
        return !item.key || !item.right;
      });

      if (hasIllegalData.length > 0) {
        message.warning('筛选条件配置不完整，请核查');
        return -1;
      }
      const reqParams = {
        type,
        dateFrom: new Date(dataRange[0]).getTime(),
        dateEnd: new Date(dataRange[1]).getTime(),
        poiType,
        esItemSearchReqs: esItemSearchReqs
      }
      const res = await apiCaller.post(
        // @ts-ignore
        '/xianfu/api-v2/dove/mark_detail/data_cnt',
        reqParams
      );

      if (res.code === 0) {
        return res.data;
      } else {
        return -1;
      }
    } catch (error) {
      console.error(`获取任务数据数量失败:`, error);
      return -1;
    }
  },
  // 获取筛选项
  fetchSearchItem: async (type) => {
    try {
      const res = await apiCaller.get('/xianfu/api-v2/dove/mark_detail/search_item', {
        type
      });
      if (res.code === 0) {
        return res.data;
      } else {
        return 0;
      }
    } catch (error) {
      console.error(`获取任务数据数量失败:`, error);
      return 0;
    }
  }
}));

// 导入任务详情store，解决循环依赖问题
import { useTaskDetailStore } from './taskDetailStore';
import { apiCaller } from "@mfe/cc-api-caller-pc";