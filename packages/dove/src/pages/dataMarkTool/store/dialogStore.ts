import { create } from 'zustand';
import { message } from 'antd';
import {DataStatus, DialogData, DialogMessage, Speaker} from '../types';
import { useTaskDetailStore } from './taskDetailStore';
import { useViewStore, ViewMode } from './viewStore';
import {apiCaller} from "@mfe/cc-api-caller-pc";

// 对话数据状态接口
interface DialogState {
  // 对话数据
  dialogData: DialogData | null;
  // 初始的对话数据状态，用于对比是否做了修改做标记；
  initDialogData: DialogData | null;
  // 加载状态
  loading: boolean;
  // 编辑中的消息ID
  editingMessageId: string | null;
  // 是否有未保存的更改
  hasUnsavedChanges: boolean;
  // 是否自动下一条
  autoNext: boolean;
  // 是否是喜欢触发
  isLike: boolean;
  // 自动播放的音频时间；
  autoPlayTime: number;

  // 获取对话数据
  fetchDialogData: (taskDetailId: string) => Promise<void>;
  // 编辑消息
  editMessage: (message: DialogMessage) => void;
  // 保存编辑
  saveEdit: (values: any) => Promise<void>;
  // 取消编辑
  cancelEdit: () => void;
  // 提交所有更改
  submitAll: (values: any) => Promise<void>;
  // 设置编辑中的消息ID
  setEditingMessageId: (id: string | null) => void;
  // 设置是否有未保存的更改
  setHasUnsavedChanges: (hasChanges: boolean) => void;
  // 设置对话数据
  setDialogData: (data: DialogData | null) => void;
  // 设置 是否自动下一条
  setAutoNext: (data: boolean) => void;
  setIsLike: (isLike) => void;
  setAutoPlayTime: (time) => void;
}

// 创建对话数据状态store
export const useDialogStore = create<DialogState>((set, get) => ({
  // 初始化状态
  dialogData: null,
  initDialogData: null,
  loading: false,
  editingMessageId: null,
  hasUnsavedChanges: false,
  autoNext: false,
  isLike: false,
  autoPlayTime: 0,

  setAutoPlayTime: (time) => {
    set({ autoPlayTime: time });
  },
  setIsLike: (data) => {
    set({ isLike: data });
  },
  setAutoNext: (data) => {
    set({ autoNext: data });
  },
  // 获取对话数据
  fetchDialogData: async (taskDetailId: string) => {
    try {
      set({ loading: true });
      
      // 获取当前任务详情
      const currentTaskDetail = useTaskDetailStore.getState().currentTaskDetail;

      const reqParams = {
        id: taskDetailId,
      };

      const res: any = await apiCaller.post(
        // @ts-ignore
        '/xianfu/api-v2/dove/mark_detail/detail',
        reqParams
      );

      if (res.code === 0) {
        const data: DialogData = {
          id: res.data?.id,
          contentId: res.data?.contentId,
          followPerson: currentTaskDetail?.followPerson || 'user-1',
          createTime: Date.now(),
          type: (currentTaskDetail as any)?.type,
          info: res.data?.chatDetailList.map((it, index) => ({
            id: index + 1,
            speaker: it.speaker,
            oldMessage: it.oldMessage,
            state: it.state,
            timestamp: Date.now() - 300000 || it.timestamp,

            // 新数据
            newMessage: it.newMessage,
            modificationTag: it.modificationTag ? Number(it.modificationTag) : it.modificationTag,
            modificationReason: it.modificationReason,
            isSelected: it.isSelected,
            startTime: it.startTime,
            endTime: it.endTime
          })),
          audioUrl: res.data?.contactUrl || 'https://s3plus.meituan.com/readata/sagacious_pigeon/audio.mp3', // 模拟音频URL
          overallEvaluate: res.data?.overallEvaluate,
          overallTag: res.data?.overallTag,
        };

        set({ dialogData: data, initDialogData: data, loading: false });

        // 更新任务详情中的对话数据
        if (currentTaskDetail) {
          const updatedTaskDetail = {
            ...currentTaskDetail,
            dialogData: data
          };
          useTaskDetailStore.getState().updateTaskDetailInList(updatedTaskDetail);
        }
      }
    } catch (error) {
      console.error('获取对话数据失败:', error);
      message.error('获取对话数据失败，请重试');
      set({ loading: false });
    }
  },
  
  // 编辑消息
  editMessage: (message: DialogMessage) => {
    set({ editingMessageId: message.id });
  },
  
  // 保存编辑
  saveEdit: async (values: any) => {
    try {
      const dialogData = get().dialogData;
      const initDialogData: any = get().initDialogData;
      if (!dialogData) return;
      
      const editingMessageId = get().editingMessageId;
      const isLike = get().isLike;
      // 非编辑表单状态和非点击点赞按钮状态
      if (!editingMessageId && !isLike) return;
      
      const initData = initDialogData?.info?.filter(item => item.id === editingMessageId)[0];
      const updatedInfo = dialogData.info.map(msg => {
        if (msg.id === editingMessageId) {
          const newObj: any = {
            ...msg,
            newMessage: values.newMessage,
            modificationTag: values.modificationTag ? Number(values.modificationTag) : values.modificationTag,
            modificationReason: values.modificationReason,
            isSelected: values.isSelected ? 1 : 0,
          };

          // 发生了改变；
          if (initData.oldMessage !== newObj.newMessage ||
            initData.modificationTag !== newObj.modificationTag || 
            initData.modificationReason !== newObj.modificationReason ||
            initData.isSelected !== newObj.isSelected
          ) {
            newObj.state = DataStatus.COMPLETED;
          } else {
            newObj.state = DataStatus.PENDING;
          }
          
          return newObj;
        }
        return msg;
      });
      
      const updatedData = {
        ...dialogData,
        info: updatedInfo,
      };
      
      set({ 
        dialogData: updatedData, 
        editingMessageId: null,
        hasUnsavedChanges: true, 
        isLike: false
      });
      
      message.success('已保存修改，请记得点击"确认完成"保存所有更改');
    } catch (error) {
      console.error('保存编辑失败:', error);
      message.error('保存编辑失败，请重试');
    }
  },
  
  // 取消编辑
  cancelEdit: () => {
    set({ editingMessageId: null, hasUnsavedChanges: false});
  },
  
  // 提交所有更改
  submitAll: async (values: any) => {
    try {
      set({ loading: true });
      
      const dialogData = get().dialogData;
      if (!dialogData) {
        set({ loading: false });
        return;
      }
      
      const finalData = {
        ...dialogData,
        overallEvaluate: values.overallEvaluate,
        overallTag: values.overallTag,
      };

      const reqParams = {
        id: finalData.id,
        chatMessageMarkResultDtos: (finalData.info || [])
            .map(it => ({
              speaker: it.speaker,
              oldMessage: it.oldMessage,
              newMessage: it.newMessage,
              modificationTag: it.modificationTag ? Number(it.modificationTag) : it.modificationTag,
              modificationReason: it.modificationReason,
              state: it.state,
              isSelected: it.isSelected ? 1 : 0,
              startTime: it.startTime,
              endTime: it.endTime,
              timestamp: it.timestamp
            })),
        overallTag: finalData.overallTag,
        overallEvaluate: finalData.overallEvaluate,
      };

      const res: any = await apiCaller.post(
          // @ts-ignore
          '/xianfu/api-v2/dove/mark_detail/mark',
          reqParams
      );
      if (res.code === 0) {
        message.success('标注保存成功');
      
        const list = useTaskDetailStore.getState().taskDetailList;
        // 更新任务详情列表中的状态
        const currentTaskDetail = useTaskDetailStore.getState().currentTaskDetail;
        const autoNext = get().autoNext;

        if (currentTaskDetail) {
          const updatedTaskDetail = {
            ...currentTaskDetail,
            status: DataStatus.COMPLETED,
            updateTime: Date.now(),
            dialogData: finalData,
          };
          useTaskDetailStore.getState().updateTaskDetailInList(updatedTaskDetail);
        }
        
        set({ hasUnsavedChanges: false, loading: false });
        // 设置为下一条；
        if (autoNext && currentTaskDetail) {
          const index = list.findIndex(item => item.id === currentTaskDetail.id);
          const nextData = list[index + 1];
          if (nextData) {
            useTaskDetailStore.getState().setCurrentTaskDetail(nextData);
          }
        }
        // 返回任务详情列表
        useViewStore.getState().setViewMode(ViewMode.TASK_DETAIL);
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败，请重试');
      set({ loading: false });
    }
  },
  
  // 设置编辑中的消息ID
  setEditingMessageId: (id: string | null) => set({ editingMessageId: id }),
  
  // 设置是否有未保存的更改
  setHasUnsavedChanges: (hasChanges: boolean) => set({ hasUnsavedChanges: hasChanges }),
  
  // 设置对话数据
  setDialogData: (data: DialogData | null) => set({ dialogData: data }),
}));