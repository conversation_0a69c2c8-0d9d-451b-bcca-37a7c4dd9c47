import { create } from 'zustand';
import { message } from 'antd';
import {DataStatus, TaskDetail, TaskStatus} from '../types';
import { useDialogStore } from './dialogStore';
import {apiCaller} from "@mfe/cc-api-caller-pc";

// 任务详情状态接口
interface TaskDetailState {
  // 任务详情列表数据
  taskDetailList: TaskDetail[];
  // 任务详情总数
  taskDetailTotal: number;
  // 加载状态
  loading: boolean;
  // 搜索参数
  searchParams: any;
  // 当前选中的任务详情
  currentTaskDetail: TaskDetail | null;
  
  // 获取任务详情列表
  fetchTaskDetailList: (params?: any) => Promise<void>;

  // 设置当前任务详情
  setCurrentTaskDetail: (detail: TaskDetail | null) => void;
  // 更新任务详情列表中的项
  updateTaskDetailInList: (updatedDetail: TaskDetail) => void;
}

// 创建任务详情状态store
export const useTaskDetailStore = create<TaskDetailState>((set, get) => ({
  // 初始化状态
  taskDetailList: [],
  taskDetailTotal: 0,
  loading: false,
  searchParams: {},
  currentTaskDetail: null,
  
  // 获取任务详情列表
  fetchTaskDetailList: async (params = {}) => {
    try {
      set({ loading: true, searchParams: params });
      const reqParams = {
        contentId: params.dataId,
        objectId: params.shopId,
        taskId: params.taskId,
        dataType: params.dataType,
        status: params.dataStatus,
        followerUid: params.followPerson, // TODO: 确认是否数字
        pageNo: params.pageNo || 1, // TODO: 待补充
        pageSize: params.pageSize || 10 // TODO: 待补充
      }
      const res: any = await apiCaller.post(
          // @ts-ignore
          '/xianfu/api-v2/dove/mark_detail/list',
          reqParams
      );
      console.log('debug fetchTaskDetailList res', res, reqParams, params);

      if (res?.code === 0) {
        // 模拟数据
        const data = (res.data || []).map(it => ({
          id: it.id,
          dataId: it.contentId,
          taskId: it.taskId,
          shopId: it.shopId,
          followPerson: it.follower,
          status: it.status,
          type: it.dataType,
          createTime: it.callTime,
        }));
        set({taskDetailList: data, taskDetailTotal: res.total, loading: false});
      }
    } catch (error) {
      console.error('获取任务详情列表失败:', error);
      message.error('获取任务详情列表失败，请重试');
      set({ loading: false });
    }
  },
  
  // 设置当前任务详情
  setCurrentTaskDetail: (detail: TaskDetail | null) => {
    set({ currentTaskDetail: detail });
    const dialogState = useDialogStore.getState();
    dialogState.setAutoPlayTime(0);
  },
  // 更新任务详情列表中的项
  updateTaskDetailInList: (updatedDetail: TaskDetail) => {
    const updatedList = get().taskDetailList.map(detail => {
      if (detail.id === updatedDetail.id) {
        return updatedDetail;
      }
      return detail;
    });
    set({ taskDetailList: updatedList });
    
    // 如果当前选中的是被更新的项，也更新currentTaskDetail
    if (get().currentTaskDetail?.id === updatedDetail.id) {
      set({ currentTaskDetail: updatedDetail });
    }
  },
}));