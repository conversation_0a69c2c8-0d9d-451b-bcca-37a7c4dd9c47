import React, { useEffect, useState } from 'react';
import {
    Button,
    DatePicker,
    Form,
    Input,
    Select,
    Space,
    Table,
    Tag,
    Tooltip,
    Modal,
    Row,
    Col,
} from 'antd';
import {
    PlusOutlined,
    SearchOutlined,
    QuestionCircleOutlined,
} from '@ant-design/icons';
import MisSelectRoo from '../../../components/rooPlus/MisSearch';
import dayjs from 'dayjs';
import {
    DATA_TYPES_TEXT,
    DataTypes,
    Task,
    TASK_STATUS_REV_TEXT,
    TASK_STATUS_TEXT,
    TASK_TYPE_TEXT,
    TaskStatus,
    TaskType,
} from '../types';
import styles from '../index.module.scss';
import { useTaskStore } from '../store';
import './taskList.scss';

const TipIcon = () => {
    return (
        <Tooltip placement="top" title={'已标注任务个数/正常任务数据个数'}>
            <QuestionCircleOutlined />
        </Tooltip>
    );
};

const TaskList: React.FC = () => {
    // 从store中获取状态和方法
    const loading = useTaskStore(state => state.loading);
    const data = useTaskStore(state => state.taskList);
    const total = useTaskStore(state => state.taskTotal);
    const fetchTaskList = useTaskStore(state => state.fetchTaskList);
    const fetchAuth = useTaskStore(state => state.fetchAuth);
    const deleteTask = useTaskStore(state => state.deleteTask);
    const pauseTask = useTaskStore(state => state.pauseTask);
    const resumeTask = useTaskStore(state => state.resumeTask);
    const viewTask = useTaskStore(state => state.viewTask);
    const authMap = useTaskStore(state => state.authMap);
    const setCreateTaskModalVisible = useTaskStore(
        state => state.setCreateTaskModalVisible,
    );
    const setCurrentTask = useTaskStore(state => state.setCurrentTask);

    const [form] = Form.useForm();

    // 初始化加载任务列表
    useEffect(() => {
        fetchTaskList();
        fetchAuth();
    }, []);

    const handleSearch = (values: any) => {
        const {
            taskId,
            shopId,
            taskType,
            taskStatus,
            creator,
            follower,
            dataId,
        } = values;
        const params = {
            taskId,
            shopId,
            taskType,
            taskStatus,
            creator: creator?.login || '',
            follower: follower?.login || '',
            dataType: dataId?.type,
            dataId: dataId.id,
        };

        fetchTaskList(params);
    };

    const handleDelete = (id: string) => {
        Modal.confirm({
            title: '提示',
            content: '删除后数据无法找回，是否删除？',
            onOk() {
                deleteTask(id);
            },
            onCancel() {
                return;
            },
            okText: '确认',
            cancelText: '取消',
        });
    };

    const getTaskStatusTag = (status: string) => {
        let color;
        switch (TASK_STATUS_REV_TEXT[status]) {
            case TaskStatus.CREATING:
                color = 'blue';
                break;
            case TaskStatus.PENDING:
                color = 'orange';
                break;
            case TaskStatus.IN_PROGRESS:
                color = 'green';
                break;
            case TaskStatus.COMPLETED:
                color = 'purple';
                break;
            case TaskStatus.PAUSED:
                color = 'red';
                break;
            default:
                color = 'grey';
        }

        return <Tag color={color}>{status || '未知状态'}</Tag>;
    };

    const columns = [
        {
            title: '任务ID',
            dataIndex: 'id',
            key: 'id',
            render: (text: string, record: Task) => (
                <a onClick={() => viewTask(record)}>{text}</a>
            ),
        },
        {
            title: '任务名称',
            dataIndex: 'name',
            key: 'name',
            render: (text: string, record: Task) => (
                <a onClick={() => viewTask(record)}>{text}</a>
            ),
        },
        {
            title: '任务类型',
            dataIndex: 'type',
            key: 'type',
        },
        {
            title: '标注规则',
            dataIndex: 'rule',
            key: 'rule',
        },
        {
            title: '任务状态',
            dataIndex: 'status',
            key: 'status',
            render: (status: string) => getTaskStatusTag(status),
        },
        {
            title: '标注任务个数',
            dataIndex: 'taskCnt',
            key: 'taskCnt',
        },
        {
            title: (
                <div>
                    标注进度 <TipIcon />
                </div>
            ),
            dataIndex: 'progress',
            key: 'progress',
        },
        {
            title: '参与人数',
            dataIndex: 'joinNum',
            key: 'joinNum',
        },
        {
            title: '创建人',
            dataIndex: ['creator', 'name'],
            key: 'creator',
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            render: (time: number) =>
                time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-',
        },
        {
            title: '结束时间',
            dataIndex: 'endTime',
            key: 'endTime',
            render: (time: number) =>
                time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '-',
        },
        {
            title: '任务耗时',
            dataIndex: 'timeSpent',
            key: 'timeSpent',
        },
        {
            title: '操作',
            key: 'action',
            width: 200,
            render: (_, record: Task) => {
                return (
                    <Space size="middle">
                        {/* 所有状态都有删除、查看、编辑 */}
                        <a onClick={() => viewTask(record)}>查看</a>
                        {authMap['MARK_TASK_ALL_DATA'] && (
                            <a
                                onClick={() => {
                                    setCurrentTask(record);
                                    setCreateTaskModalVisible(true, true);
                                }}
                            >
                                编辑
                            </a>
                        )}
                        {/* 只有有权限的用户才可以删除 */}
                        {authMap['MARK_TASK_ALL_DATA'] && (
                            <a onClick={() => handleDelete(record.id)}>删除</a>
                        )}
                        {/* 进行中才有暂停 */}
                        {TASK_STATUS_REV_TEXT[record.status] ===
                            TaskStatus.IN_PROGRESS && (
                            <a onClick={() => pauseTask(record.id)}>暂停</a>
                        )}
                        {/* 已暂停 */}
                        {TASK_STATUS_REV_TEXT[record.status] ===
                            TaskStatus.PAUSED && (
                            <a onClick={() => resumeTask(record.id)}>启动</a>
                        )}
                    </Space>
                );
            },
        },
    ];

    return (
        <div>
            <Form
                form={form}
                layout="inline"
                onFinish={handleSearch}
                className={styles.searchForm}
            >
                <Row style={{ width: '100%' }}>
                    <Col span={6}>
                        <Form.Item
                            name="taskId"
                            label="任务ID"
                            labelCol={{ span: 6 }}
                            wrapperCol={{ span: 18 }}
                        >
                            <Input placeholder="请输入任务ID" />
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item
                            name="shopId"
                            label="商家ID"
                            labelCol={{ span: 6 }}
                            wrapperCol={{ span: 18 }}
                        >
                            <Input placeholder="请输入商家ID" />
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item
                            name="taskType"
                            label="任务类型"
                            labelCol={{ span: 6 }}
                            wrapperCol={{ span: 18 }}
                        >
                            <Select
                                placeholder="请选择任务类型"
                                style={{ width: '100%' }}
                                allowClear
                                options={[
                                    {
                                        value: TaskType.PHONE,
                                        label: TASK_TYPE_TEXT[TaskType.PHONE],
                                    },
                                    {
                                        value: TaskType.IM,
                                        label: TASK_TYPE_TEXT[TaskType.IM],
                                    },
                                    {
                                        value: TaskType.KNOWLEDGE,
                                        label: TASK_TYPE_TEXT[
                                            TaskType.KNOWLEDGE
                                        ],
                                    },
                                ]}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item
                            name="taskStatus"
                            label="任务状态"
                            labelCol={{ span: 6 }}
                            wrapperCol={{ span: 18 }}
                        >
                            <Select
                                placeholder="请选择任务状态"
                                style={{ width: '100%' }}
                                allowClear
                                options={[
                                    {
                                        value: TaskStatus.CREATING,
                                        label: TASK_STATUS_TEXT[
                                            TaskStatus.CREATING
                                        ],
                                    },
                                    {
                                        value: TaskStatus.PENDING,
                                        label: TASK_STATUS_TEXT[
                                            TaskStatus.PENDING
                                        ],
                                    },
                                    {
                                        value: TaskStatus.IN_PROGRESS,
                                        label: TASK_STATUS_TEXT[
                                            TaskStatus.IN_PROGRESS
                                        ],
                                    },
                                    {
                                        value: TaskStatus.COMPLETED,
                                        label: TASK_STATUS_TEXT[
                                            TaskStatus.COMPLETED
                                        ],
                                    },
                                    {
                                        value: TaskStatus.PAUSED,
                                        label: TASK_STATUS_TEXT[
                                            TaskStatus.PAUSED
                                        ],
                                    },
                                ]}
                            />
                        </Form.Item>
                    </Col>
                </Row>

                <Row style={{ width: '100%' }}>
                    <Col span={6}>
                        <Form.Item
                            name="creator"
                            label="创建人"
                            labelCol={{ span: 6 }}
                            wrapperCol={{ span: 18 }}
                        >
                            <MisSelectRoo
                                placeholder="请输入创建人"
                                style={{ width: '100%' }}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item
                            name="follower"
                            label="跟进人"
                            labelCol={{ span: 6 }}
                            wrapperCol={{ span: 18 }}
                        >
                            <MisSelectRoo
                                placeholder="请输入跟进人"
                                style={{ width: '100%' }}
                            />
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item
                            label="数据ID"
                            style={{ marginBottom: 0 }}
                            labelCol={{ span: 6 }}
                            wrapperCol={{ span: 18 }}
                        >
                            <Form.Item
                                name={['dataId', 'type']}
                                style={{
                                    display: 'inline-block',
                                    width: '130px',
                                    margin: 0,
                                }}
                            >
                                <Select
                                    placeholder="选择数据类型"
                                    className="data-id-left"
                                    options={[
                                        {
                                            value: DataTypes.HUMAN_DATA,
                                            label: DATA_TYPES_TEXT[
                                                DataTypes.HUMAN_DATA
                                            ],
                                        },
                                        {
                                            value: DataTypes.AI_DATA,
                                            label: DATA_TYPES_TEXT[
                                                DataTypes.AI_DATA
                                            ],
                                        },
                                        {
                                            value: DataTypes.MOCK_DATA,
                                            label: DATA_TYPES_TEXT[
                                                DataTypes.MOCK_DATA
                                            ],
                                        },
                                    ]}
                                />
                            </Form.Item>
                            <Form.Item
                                name={['dataId', 'id']}
                                style={{
                                    display: 'inline-block',
                                    width: 'calc(100% - 130px)',
                                    margin: 0,
                                }}
                            >
                                <Input
                                    placeholder="请输入数据ID"
                                    className="data-id-right"
                                />
                            </Form.Item>
                        </Form.Item>
                    </Col>
                    <Col span={6}>
                        <Form.Item style={{ textAlign: 'right' }}>
                            <Button
                                type="primary"
                                htmlType="submit"
                                icon={<SearchOutlined />}
                            >
                                查询
                            </Button>
                            {authMap['MARK_TASK_ALL_DATA'] && (
                                <Button
                                    type="primary"
                                    icon={<PlusOutlined />}
                                    style={{ marginLeft: '12px' }}
                                    onClick={() =>
                                        setCreateTaskModalVisible(true)
                                    }
                                >
                                    新建任务
                                </Button>
                            )}
                        </Form.Item>
                    </Col>
                </Row>
            </Form>

            <Table
                rowKey="id"
                loading={loading}
                columns={columns}
                dataSource={data}
                pagination={{
                    total,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: total => `共 ${total} 条`,
                }}
            />
        </div>
    );
};

export default TaskList;
