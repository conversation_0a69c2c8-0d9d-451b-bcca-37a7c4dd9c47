import React, { memo, useCallback } from 'react';
import { Avatar, Button, Form, FormInstance, Input, Radio, Select, Space, Tag, Tooltip, Typography } from 'antd';
import { EditOutlined, RobotOutlined, SaveOutlined, UserOutlined, LikeOutlined, LikeFilled, PlayCircleFilled } from '@ant-design/icons';
import { useDialogStore } from '../store';
import { DataStatus, DialogMessage, ModificationReason, Speaker } from '../types';
import styles from '../index.module.scss';
import { aiAvatar } from '@src/constants';

const { TextArea } = Input;
const { Text } = Typography;

interface MessageProps {
  message: DialogMessage;
  isEditing: boolean;
  editForm?: FormInstance;
  onEdit: (message: DialogMessage) => void;
  onSave: (data?) => Promise<void>;
  onCancel: () => void;
}

const modificationReasonMap = {
  [ModificationReason.TOO_LONG]: '字数太长',
  [ModificationReason.NOT_COLLOQUIAL]: '不够口语化',
  [ModificationReason.NOT_SUITABLE]: '不贴合外呼场景',
  [ModificationReason.INCORRECT]: '回答有误',
  [ModificationReason.OTHER]: '其他'
};

const MarkLike = (props) => {
  const { type, onChange } = props;
  return (
    <div>
      {
        type === 0 ? (
          <><LikeOutlined onClick={onChange} /> 标记为优质回复</>
        ) : (<><LikeFilled onClick={onChange} /> 取消标记为优质回复</>)
      }
    </div>
  )
};

// 消息头部组件
const MessageHeader: React.FC<{
  isAgent: boolean;
  timestamp?: number;
  state?: DataStatus;
  isSelected?: 1 | 0;
}> = memo(({ isAgent, timestamp, state, isSelected }) => (
  <div className={styles.messageHeader}>
    <div className={styles.messageInfo}>
      {/* 标签和名称放在同一行 */}
      <div className={styles.nameAndTags}>
        {isAgent && isSelected ? (
          <Tag color="blue" className={styles.messageTag}>优质回复</Tag>
        ) : null}
        {state === DataStatus.COMPLETED && (
          <Tag color="green" className={styles.messageTag}>已标注</Tag>
        )}
        <span className={styles.messageName}>{isAgent ? '客服' : '客户'}</span>
      </div>
      {timestamp && (
        <span className={styles.messageTime}>
          {new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </span>
      )}
    </div>
  </div>
));

// 修改信息组件
const ModificationInfo: React.FC<{
  tag?: ModificationReason;
  reason?: string;
}> = memo(({ tag, reason }) => (
  <>
    {tag && (
      <div style={{ marginTop: 8 }}>
        <Text type="secondary">修改原因：</Text>
        <Tag color="orange" style={{ marginLeft: 8 }}>
          {modificationReasonMap[tag] || '未知原因'}
        </Tag>
      </div>
    )}
    {reason && (
      <div style={{ marginTop: 8 }}>
        <Text type="secondary">修改意见：</Text>
        <Text>{reason}</Text>
      </div>
    )}
  </>
));

// 操作按钮组件
const ActionButtons: React.FC<{
  isAgent: boolean;
  onEdit: () => void;
}> = memo(({ isAgent, onEdit }) => {
  if (!isAgent) return null;
  return (
    <div className={styles.messageActions}>
      <Tooltip title="编辑">
        <div className={styles.actionButton} onClick={onEdit}>
          <EditOutlined className={styles.actionIcon} />
        </div>
      </Tooltip>
    </div>
  );
});

// 编辑表单组件
const EditForm: React.FC<{
  form: FormInstance;
  isAgent: boolean;
  onSave: (data) => Promise<void>;
  onCancel: () => void;
}> = memo(({ form, isAgent, onSave, onCancel }) => {
  const setHasUnsavedChanges = useDialogStore(state => state.setHasUnsavedChanges);
  return (
    <Form
      form={form}
      layout="vertical"
      className={styles.editForm}
      onValuesChange={() => {
        setHasUnsavedChanges(true);
      }}
    >
      <Form.Item
        name="newMessage"
        rules={[{ required: true, message: '请输入修改后的内容' }]}
      >
        <TextArea rows={4} placeholder="请输入修改后的内容" />
      </Form.Item>
      {isAgent && (
        <>
          <Form.Item name="modificationTag" label="修改原因" rules={[
            {
              required: true
            }
          ]}>
            <Select placeholder="请选择修改原因" allowClear>
              {Object.entries(modificationReasonMap).map(([value, label]) => (
                <Select.Option key={value} value={Number(value)}>{label}</Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="modificationReason" label="修改意见">
            <Input placeholder="请输入修改意见" />
          </Form.Item>
        </>
      )}
      <Form.Item>
        <Space>
          <Button 
            type="primary" 
            onClick={async () => {
              const data = await form.validateFields();
              onSave(data)}
            } 
            icon={<SaveOutlined />}
          >
            保存
          </Button>
          <Button onClick={onCancel}>取消</Button>
        </Space>
      </Form.Item>
    </Form>
  )
});

// 消息内容组件
const MessageContent: React.FC<{
  message: DialogMessage;
  hasBeenEdited: boolean;
  handleEdit: any;
}> = memo(({ message, hasBeenEdited, handleEdit }) => (
  <div className={styles.messageBubble} onClick={handleEdit}>
    <div style={{ whiteSpace: 'pre-wrap' }}>
      {message.newMessage || message.oldMessage}
    </div>
    {hasBeenEdited && (
      <div style={{ marginTop: 8 }}>
        <Text type="secondary">原始内容：</Text>
        <div style={{ marginTop: 4, whiteSpace: 'pre-wrap', color: '#999' }}>
          {message.oldMessage}
        </div>
      </div>
    )}
  </div>
));

const Message: React.FC<MessageProps> = ({
  message,
  isEditing,
  editForm,
  onEdit,
  onSave,
  onCancel,
}) => {
  const isAgent = message.speaker === Speaker.AGENT;
  const messageClass = isAgent ? styles.agentMessage : styles.customerMessage;
  const hasBeenEdited = Boolean(message.newMessage && message.newMessage !== message.oldMessage);
  const setIsLike = useDialogStore(state => state.setIsLike);
  const isLike = useDialogStore(state => state.isLike);
  const setEditingMessageId = useDialogStore(state => state.setEditingMessageId);
  const setAutoPlayTime = useDialogStore(state => state.setAutoPlayTime);

  const handleEdit = useCallback(() => {
    if (!isAgent) return;
    onEdit(message);
  }, [isAgent, message, onEdit]);

  if (isEditing && !isLike) {
    return (
      <div className={messageClass}>
        <div className={styles.messageAvatar}>
          <Avatar
            icon={isAgent ? <img src={aiAvatar}></img> : <UserOutlined />}
            style={{ backgroundColor: isAgent ? 'transparent' : '#87d068' }}
          />
        </div>
        <div className={styles.messageContent}>
          <MessageHeader isAgent={isAgent} timestamp={message.timestamp} />
          <EditForm
            form={editForm}
            isAgent={isAgent}
            onSave={onSave}
            onCancel={onCancel}
          />
        </div>
      </div>
    );
  }

  const changeLike = () => {
    setIsLike(true);
    setEditingMessageId(message.id);
    const newData: any = {
      newMessage: message.newMessage || message.oldMessage,
      modificationTag: message.modificationTag ? Number(message.modificationTag) : message.modificationTag,
      modificationReason: message.modificationReason,
      isSelected: message.isSelected ? 0 : 1,
    };
    onSave(newData);
    setIsLike(false);
  };

  const play = () => {
    setAutoPlayTime(message.startTime);
  };

  return (
    <div className={messageClass}>
      <div className={styles.messageAvatar}>
        <Avatar
          icon={isAgent ? <img src={aiAvatar}></img> : <UserOutlined />}
          style={{ backgroundColor: isAgent ? 'transparent' : '#87d068' }}
        />
      </div>
      <div className={styles.messageContent}>
        <MessageHeader
          isAgent={isAgent}
          timestamp={message.timestamp}
          state={message.state}
          isSelected={message.isSelected}
        />
        <div className={styles.messagePop}>
          {/* 客服的播放按钮 */}
          {
            isAgent && <PlayCircleFilled onClick={play} />
          }
          <MessageContent message={message} hasBeenEdited={hasBeenEdited} handleEdit={handleEdit} />
          {/* 用户的播放按钮 */}
          {
            !isAgent && <PlayCircleFilled onClick={play} />
          }
        </div>

        <ModificationInfo
          tag={message.modificationTag}
          reason={message.modificationReason}
        />
        {
          isAgent && (<div style={{ textAlign: 'right', marginTop: 6 }}>
            {/* isSelected有null，去除null的影响 */}
            <MarkLike type={message.isSelected ? 1 : 0} onChange={changeLike} />
          </div>)
        }
      </div>
    </div>
  );
};

export default memo(Message);