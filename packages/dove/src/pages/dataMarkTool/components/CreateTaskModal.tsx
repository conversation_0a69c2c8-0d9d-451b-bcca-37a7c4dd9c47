import React, { useMemo, useState, useEffect } from 'react';
import dayjs from 'dayjs';
import { isEmpty } from 'lodash';
import { Button, Card, DatePicker, Form, Input, message, Modal, Select, Space, Typography, Tooltip, Spin } from 'antd';
import { DeleteOutlined, MinusCircleOutlined, PlusCircleOutlined, PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import OrganizationSelector from '@src/components/rooPlus/OrganizationSelector';
import { DATA_TYPES_TEXT, DataTypes, TaskType } from '../types';
import { useTaskStore } from '../store/taskStore';
import { apiCaller } from "@mfe/cc-api-caller-pc";
import SelectAgent from '@src/components/SelectAgent';
import SceneSelect from '@src/components/SceneSelect';

const { TextArea } = Input;
const { Option } = Select;
const { Title } = Typography;

// 定义样式常量
const sectionTitleStyle = {
  margin: '24px 0 16px'
};

const formItemLabelStyle = {
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  fontWeight: 500
};

interface CreateTaskModalProps {

}

const getEsItemSearchReqs = (conditions, searchOptions, dataType) => {
  let result: any = [];
  const options = searchOptions[dataType] || [];
  result = conditions.map(condition => {
    const filterOp = options.filter(op => op.id === condition.key)[0];
    const newObj = {
      id: condition.key,
      type: '',
      esExpression: '',
      inputData: condition.right,
      esField: ''
    };
    if (filterOp) {
      newObj.type = filterOp.type;
      newObj.esExpression = filterOp.esExpression;
      newObj.esField = filterOp.esField;
    }
    return newObj;
  });
  return result;
};

const defaultInitValues = {
  taskType: TaskType.PHONE,
  dataCards: [{
    filterConditions: [{}]  // 为每个dataCard添加一个默认的空filterCondition
  }],
};

const CreateTaskModal: React.FC<CreateTaskModalProps> = () => {
  // 从store中获取状态和方法
  const createTask = useTaskStore(state => state.createTask);
  const fetchTaskDataCount = useTaskStore(state => state.fetchTaskDataCount);
  const fetchSearchItem = useTaskStore(state => state.fetchSearchItem);
  const createTaskLoading = useTaskStore(state => state.createTaskLoading);
  const createTaskModalVisible = useTaskStore(state => state.createTaskModalVisible);
  const createTaskModalEditStatus = useTaskStore(state => state.createTaskModalEditStatus);
  const setCreateTaskModalVisible = useTaskStore(state => state.setCreateTaskModalVisible);
  const currentTask = useTaskStore(state => state.currentTask);
  const fetchTaskConfig = useTaskStore(state => state.fetchTaskConfig);

  // 保留组件内部状态
  const [form] = Form.useForm();
  const [dataType, setDataType] = useState<DataTypes | null>(null);
  const [importedDataCounts, setImportedDataCounts] = useState<Record<number, number>>({});
  const [inputMisValue, setInputMisValue] = useState<string>('');
  const [misValidated, setMisValidated] = useState<boolean>(false);
  const [misValidationError, setMisValidationError] = useState<string>('');
  const [searchOptions, setSearchOptions] = useState<any>([]);
  const [editLoading, setEditLoading] = useState<boolean>(false);

  const editActionText = useMemo(() => createTaskModalEditStatus ? '编辑' : '创建', [createTaskModalEditStatus]);
  const saveActionText = useMemo(() => createTaskModalEditStatus ? '保存' : '创建', [createTaskModalEditStatus]);

  const getAll = async () => {
    const list = [DataTypes.AI_DATA, DataTypes.HUMAN_DATA, DataTypes.MOCK_DATA];
    const promiseList = await Promise.allSettled(list.map(data => fetchSearchItem(data)));
    const newOptionData: any = {};
    promiseList.forEach((data, index) => {
      const key = list[index];
      if (data.status === 'fulfilled') {
        newOptionData[key] = data.value;
      }
    });
    setSearchOptions(newOptionData);
  };

  useEffect(() => {
    if (createTaskModalEditStatus && currentTask) {
      setEditLoading(true);
      fetchTaskConfig(currentTask.id).then(data => {
        if (data) {
          const values: any = {
            taskName: data.taskName,
            taskType: data.taskType,
            taskRule: data.markStrategy
          };
          try {
            const config = JSON.parse(data.dataConfig);
            config.forEach((data) => {
              if (data.dataRange?.[0] && data.dataRange?.[1]) {
                data.dataRange = [dayjs(data.dataRange[0]), dayjs(data.dataRange[1])];
              }
            });
            values.dataCards = config;
            console.log(values);
          } catch (error) {
            console.log('error is', error);
          }
          if (data.misIdList) {
            if (typeof (data.misIdList) === 'string') {
              setInputMisValue(data.misIdList);
            } else {
              if (Array.isArray(data.misIdList)) {
                setInputMisValue(data.misIdList.join(','));
              }
            }
          }
          form.setFieldsValue(values);
        }
      }).finally(() => {
        setEditLoading(false);
      });
    }
  }, [currentTask, createTaskModalEditStatus]);

  // 获取元数据
  useEffect(() => {
    getAll();
  }, []);

  const misAccounts = useMemo(() => {
    if (!inputMisValue) {
      return [];
    }
    const splitStr = inputMisValue.includes(',') ? ',' : '\n';
    const str = splitStr === ',' ? inputMisValue.replace(/\n/g, '') : inputMisValue.replace(/,/g, '\n');
    return str.split(splitStr).filter(it => it && it !== '\n');
  }, [inputMisValue]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      // 只有创建才会做校验
      if (!createTaskModalEditStatus) {
        // 检查是否有导入的数据
        const totalImportedCount = Object.values(importedDataCounts).reduce((sum, count) => sum + count, 0);
        if (totalImportedCount === 0) {
          message.error(`请导入数据后再${saveActionText}任务`);
          return;
        }
        if (misAccounts.length === 0) {
          message.error('请添加至少一个跟进人');
          return;
        }
        if (!misValidated) {
          message.error('请先校验账号');
          return;
        }
      }

      const dataGrabReqs = values.dataCards.map(it => ({
        type: it.dataType,
        dateFrom: new Date(it.dataRange[0]).getTime(),
        dateEnd: new Date(it.dataRange[1]).getTime(),
        poiType: it.merchantType,
        esItemSearchReqs: getEsItemSearchReqs(it.filterConditions, searchOptions, it.dataType)
      }));

      const queryData = {
        ...values,
        followPersons: misAccounts,
      };

      // 编辑态要加上taskId
      if (createTaskModalEditStatus && currentTask) {
        queryData.taskId = currentTask.id;
      }
      await createTask(queryData, createTaskModalEditStatus, dataGrabReqs);
      // 重置表单
      form.resetFields();
      setDataType(null);
      setImportedDataCounts({});
      setMisValidated(false);
      setMisValidationError('');
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setDataType(null);
    setImportedDataCounts({});
    setMisValidated(false);
    setMisValidationError('');
    setCreateTaskModalVisible(false);
    setInputMisValue('');
  };

  const handleDataImport = async (index: number) => {
    const dataCards = form.getFieldValue('dataCards') || [];
    const currentCard = dataCards[index];
    if (!currentCard.dataType || !currentCard.dataRange || ![0, 1].includes(currentCard.merchantType)) {
      message.error('请先选择数据类型、数据范围和商家类型');
      return;
    }
    const esItemSearchReqs = getEsItemSearchReqs(currentCard.filterConditions, searchOptions, currentCard.dataType);
    const dataCount = await fetchTaskDataCount(currentCard, esItemSearchReqs);
    if (dataCount >= 0) {
      setImportedDataCounts(prev => ({
        ...prev,
        [index]: dataCount
      }));
      message.success(`成功导入 ${dataCount} 条数据`);
    }
  };

  const handleDeleteCardData = (index: number) => {
    setImportedDataCounts(prev => {
      const newCounts = { ...prev };
      delete newCounts[index];
      return newCounts;
    });
  };

  const validateMisAccounts = async () => {
    if (misAccounts.length === 0) {
      message.error('请添加至少一个跟进人');
      return;
    }

    setMisValidated(false);
    setMisValidationError('');

    try {
      // 调用校验接口
      const res = await apiCaller.post(
        '/xianfu/api-v2/dove/mark_task/validate_mis',
        {
          misIdList: misAccounts
        }
      );

      if (res.code === 0) {
        if (res.data?.length === 0) {
          setMisValidated(true);
          message.success('账号校验通过');
        } else {
          setMisValidationError(`账号校验失败，请检查账号${(res.data || []).toString()}是否正确`);
          message.error(`账号校验失败，请检查账号${(res.data || []).toString()}是否正确`);
        }
      } else {
        setMisValidationError('账号校验失败，请稍后重试');
        message.error('账号校验失败，请稍后重试');
      }
    } catch (error) {
      console.error('校验账号失败:', error);
      setMisValidationError('校验账号失败，请稍后重试');
      message.error('校验账号失败，请稍后重试');
    }
  };

  // 当输入值变化时，重置校验状态
  const handleMisInputChange = (e) => {
    setInputMisValue(e.target.value);
    setMisValidated(false);
    setMisValidationError('');
  };

  const canCreate = useMemo(() => {
    let result = true;
    if (!createTaskModalEditStatus) {
      // 检查是否有导入的数据
      const totalImportedCount = Object.values(importedDataCounts).reduce((sum, count) => sum + count, 0);
      if (totalImportedCount === 0) {
        result = false;
      }
      if (misAccounts.length === 0) {
        result = false;
      }
      if (!misValidated) {
        result = false;
      }
    }
    return result;
  }, [createTaskModalEditStatus, importedDataCounts, misAccounts, misValidated]);

  const getSearchItem = (data, dataType, name, outerName) => {
    const options = searchOptions[dataType] || [];
    const itemData = options.filter(item => item.id === data)[0];

    const changeOrg = (value) => {
      const dataCards = form.getFieldValue('dataCards') || [];
      const updatedDataCards = dataCards.map((card, cardIndex) => {
        if (cardIndex === outerName) {
          const updatedFilterConditions = card.filterConditions.map((condition, conditionIndex) => {
            if (conditionIndex === name) {
              return {
                ...condition,
                right: isEmpty(value) ? '' : value.join(','), // 清空 right 值
              };
            }
            return condition;
          });
          return {
            ...card,
            filterConditions: updatedFilterConditions,
          };
        }
        return card;
      });

      form.setFieldsValue({ dataCards: updatedDataCards });
      setImportedDataCounts(prev => ({
        ...prev,
        [name]: 0
      }));
    };

    // 默认；
    let result = (<Input
      style={{ flex: 1, marginRight: 8 }}
      placeholder="请输入筛选值，如有多个请用英文逗号分隔"
      onChange={() => {
        setImportedDataCounts(prev => ({
          ...prev,
          [name]: 0
        }));
      }}
    />);
    if (itemData) {
      if (itemData.type !== 'text') {
        if (itemData.type === 'enum') {
          return (
            <Select
              placeholder="请选择"
              style={{ flex: 1, marginRight: 8 }}
              onChange={() => {
                setImportedDataCounts(prev => ({
                  ...prev,
                  [name]: 0
                }));
              }}
            >
              {
                itemData.options ? itemData.options.map(op => {
                  return <Option value={op}>{op}</Option>
                }) : null
              }
            </Select>
          )
        } else {
          if (itemData.type === 'remote') {
            // 3为组织架构
            if (itemData.configType === 'org') {
              return (
                <div style={{ flex: 1, marginRight: 8 }}>
                  <OrganizationSelector
                    multiple
                    onChange={changeOrg}
                    disablePortal={true}
                    // 不设置的话，初始化有值直接请求会报错异常
                    onInit={changeOrg}
                  />
                </div>
              )
            } else {
              if (itemData.configType === 'selectAgent') {
                return (
                  <SelectAgent
                    style={{ width: '100%', flex: 1, marginRight: 8 }}
                    onChange={() => {
                      setImportedDataCounts(prev => ({
                        ...prev,
                        [name]: 0
                      }));
                    }}
                  />
                )
              } else {
                if (itemData.configType === 'sceneType') {
                  return (
                    <SceneSelect
                      style={{ width: '100%', flex: 1, marginRight: 8 }}
                      onChange={() => {
                        setImportedDataCounts(prev => ({
                          ...prev,
                          [name]: 0
                        }));
                      }}
                    />
                  )
                }
              }
            }
          }
        }
      }
    }

    return result;
  };

  return (
    <Modal
      title={`${editActionText}标注任务`}
      open={createTaskModalVisible}
      width={'80vw'}
      style={{ top: '5vh' }}
      bodyStyle={{
        height: '80vh',
        overflow: 'auto'
      }}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        // 新逻辑，创建的时候如果是不允许创建的状态，要有tip提示。
        saveActionText === '创建' && !canCreate ? (
          <Tooltip placement="top" title={'请确认导入数据的筛选条件及分配账号校验'}>
            <Button
              key="submit"
              type="primary"
              loading={createTaskLoading}
              onClick={handleSubmit}
              disabled={!canCreate}
            >
              {`${saveActionText}任务`}
            </Button>
          </Tooltip>
        ) : <Button
          key="submit"
          type="primary"
          loading={createTaskLoading}
          onClick={handleSubmit}
          disabled={!canCreate}
        >
          {`${saveActionText}任务`}
        </Button>
      ]}
    >
      <Form
        form={form}
        layout="horizontal"
        initialValues={defaultInitValues}
        disabled={createTaskModalEditStatus}
      >
        <div style={sectionTitleStyle}>
          <Title level={5}>基本信息</Title>
        </div>

        <Form.Item
          name="taskName"
          label={<span style={formItemLabelStyle}>任务名称</span>}
          rules={[{ required: true, message: '请输入任务名称' }]}
        >
          <Input placeholder="请输入4-20字的任务名称" maxLength={20} disabled={false} />
        </Form.Item>

        <Form.Item
          name="taskType"
          label={<span style={formItemLabelStyle}>任务类型</span>}
          rules={[{ required: true, message: '请选择任务类型' }]}
        >
          <Select placeholder="请选择任务类型">
            {/* 本期只保留这一个 */}
            <Option value={TaskType.PHONE}>通话数据标注</Option>
            {/* <Option value={TaskType.IM}>IM数据标注</Option>
            <Option value={TaskType.KNOWLEDGE}>知识库数据标注</Option> */}
          </Select>
        </Form.Item>
        <Form.Item
          name="taskRule"
          label={<span style={formItemLabelStyle}>标注规则</span>}
          rules={[{ required: true, message: '请输入标注规则' }]}
        >
          <TextArea rows={6} placeholder="请输入标注规则" disabled={false} />
        </Form.Item>

        <div style={sectionTitleStyle}>
          <Title level={5}>导入数据</Title>
        </div>

        <Form.List name="dataCards">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <Card
                  key={key}
                  style={{ marginBottom: 16 }}
                  extra={
                    fields.length > 1 ? (
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => {
                          remove(name);
                          handleDeleteCardData(name);
                        }}
                      />
                    ) : null
                  }
                >
                  <Form.Item
                    {...restField}
                    name={[name, 'dataType']}
                    label={<span style={formItemLabelStyle}>数据类型</span>}
                    rules={[{ required: true, message: '请选择数据类型' }]}
                  >
                    <Select
                      placeholder="请选择数据类型"
                      onChange={(value) => {
                        setDataType(value as DataTypes);
                        setImportedDataCounts(prev => ({
                          ...prev,
                          [name]: 0
                        }));
                        // 改变的时候修改筛选类型
                        form.setFieldsValue({
                          dataCards: form.getFieldValue('dataCards').map((card, index) => {
                            if (index === name) {
                              return {
                                ...card,
                                filterConditions: [{}], // 清空 filterConditions
                              };
                            }
                            return card;
                          }),
                        });
                      }}
                    >
                      <Option value={DataTypes.AI_DATA}>{DATA_TYPES_TEXT[DataTypes.AI_DATA]}</Option>
                      <Option value={DataTypes.HUMAN_DATA}>{DATA_TYPES_TEXT[DataTypes.HUMAN_DATA]}</Option>
                      <Option value={DataTypes.MOCK_DATA}>{DATA_TYPES_TEXT[DataTypes.MOCK_DATA]}</Option>
                    </Select>
                  </Form.Item>

                  <Form.Item
                    {...restField}
                    name={[name, 'dataRange']}
                    label={<div style={formItemLabelStyle}>数据范围<Tooltip placement="top" title={'数据默认加载通话时长大于5S的'}><QuestionCircleOutlined style={{ marginLeft: '4px' }} /></Tooltip></div>}
                    rules={[{ required: true, message: '请选择数据范围' }]}
                  >
                    <DatePicker.RangePicker
                      showTime={{ format: 'HH:mm:ss' }}
                      format="YYYY-MM-DD HH:mm:ss"
                      placeholder={['开始时间', '结束时间']}
                      style={{ width: '100%' }}
                      onChange={() => {
                        setImportedDataCounts(prev => ({
                          ...prev,
                          [name]: 0
                        }));
                      }}
                    />
                  </Form.Item>

                  <Form.Item
                    {...restField}
                    name={[name, 'merchantType']}
                    label={<span style={formItemLabelStyle}>商家类型</span>}
                    rules={[{ required: true, message: '请选择商家类型' }]}
                  >
                    <Select placeholder="请选择商家类型" onChange={() => {
                      setImportedDataCounts(prev => ({
                        ...prev,
                        [name]: 0
                      }));
                    }}>
                      <Option value={0}>外卖门店（已合作）</Option>
                      <Option value={1}>公海门店（未合作）</Option>
                    </Select>
                  </Form.Item>

                  <Form.List name={[name, 'filterConditions']}>
                    {(conditionFields, { add: addCondition, remove: removeCondition }) => {
                      const dataType = form.getFieldValue(['dataCards', name, 'dataType']);
                      return (
                        <>
                          {conditionFields.map((conditionField, index) => (
                            <div key={conditionField.key} style={{ display: 'flex', marginBottom: 8 }}>
                              <Form.Item
                                {...restField}
                                name={[conditionField.name, 'key']}
                                noStyle
                              >
                                <Select
                                  style={{ width: 120, marginRight: 8 }}
                                  placeholder="筛选条件"
                                  popupMatchSelectWidth={false}
                                  dropdownStyle={{minWidth: 180}}
                                  onChange={(value) => {
                                    const dataCards = form.getFieldValue('dataCards') || [];
                                    const updatedDataCards = dataCards.map((card, cardIndex) => {
                                      if (cardIndex === name) {
                                        const updatedFilterConditions = card.filterConditions.map((condition, conditionIndex) => {
                                          if (conditionIndex === conditionField.name) {
                                            return {
                                              ...condition,
                                              key: value, // 更新 key 值
                                              right: '', // 清空 right 值
                                            };
                                          }
                                          return condition;
                                        });
                                        return {
                                          ...card,
                                          filterConditions: updatedFilterConditions,
                                        };
                                      }
                                      return card;
                                    });

                                    form.setFieldsValue({ dataCards: updatedDataCards });
                                    setImportedDataCounts(prev => ({
                                      ...prev,
                                      [name]: 0
                                    }));
                                  }}
                                >
                                  {
                                    (searchOptions[dataType] || []).map(option => {
                                      return (
                                        <Option value={option.id} key={option.id}>{option.name}</Option>
                                      )
                                    })
                                  }
                                </Select>
                              </Form.Item>
                              <Form.Item
                                {...restField}
                                name={[conditionField.name, 'right']}
                                noStyle
                              >
                                {getSearchItem(form.getFieldValue(['dataCards', name, 'filterConditions', conditionField.name, 'key']), dataType, conditionField.name, name)}
                              </Form.Item>
                              {index === 0 ? (
                                <Button 
                                  type="primary" 
                                  icon={<PlusCircleOutlined />} 
                                  onClick={() => {
                                    setImportedDataCounts(prev => ({
                                      ...prev,
                                      [name]: 0
                                    }));
                                    addCondition();
                                  }} 
                                />
                              ) : (
                                <Button 
                                  danger 
                                  icon={<MinusCircleOutlined />} 
                                  onClick={() => {
                                    setImportedDataCounts(prev => ({
                                      ...prev,
                                      [name]: 0
                                    }));
                                    removeCondition(conditionField.name);
                                  }} 
                                />
                              )}
                            </div>
                          ))}
                          {conditionFields.length === 0 && (
                            <Button
                              type="dashed"
                              onClick={() => addCondition()}
                              icon={<PlusOutlined />}
                              style={{ width: '100%', marginBottom: 16 }}
                            >
                              添加筛选条件
                            </Button>
                          )}
                        </>
                      )
                    }}
                  </Form.List>

                  <div style={{ marginTop: 16, display: 'flex', justifyContent: 'space-between' }}>
                    <div>
                      {importedDataCounts[name] > 0 && (
                        <span>本条数据导入 {importedDataCounts[name]} 条数据</span>
                      )}
                    </div>
                    <Space>
                      <Button onClick={() => handleDeleteCardData(name)}>
                        删除
                      </Button>
                      <Button type="primary" onClick={() => handleDataImport(name)}>
                        确认
                      </Button>
                    </Space>
                  </div>
                </Card>
              ))}
              <div style={{ textAlign: 'center', width: '100%', marginBottom: 16 }}>
                <Button
                  style={{ width: '100%' }}
                  type="dashed"
                  icon={<PlusOutlined />}
                  onClick={() => add()}
                />
              </div>
            </>
          )}
        </Form.List>

        <div style={sectionTitleStyle}>
          <Title level={5}>人员分配</Title>
        </div>
        <Form.Item
          label={<span style={formItemLabelStyle}>分配mis</span>}
          required
          validateStatus={misValidationError ? 'error' : misValidated ? 'success' : ''}
          help={misValidationError}
        >
          <div style={{ display: 'flex', justifyContent: 'end' }}>
            <div style={{ position: 'absolute', left: -76, top: 30, zIndex: 9 }}>
              <Button onClick={validateMisAccounts}>校验账号</Button>
            </div>
            <TextArea
              size="small"
              style={{ width: 'calc(100% - 20px)', border: '1px solid #d9d9d9', borderRadius: 4, padding: 8, minHeight: 120 }}
              value={inputMisValue}
              onChange={handleMisInputChange}
              placeholder="批量录入可用英文逗号或换行符分隔"
              autoFocus
            />
          </div>
          <div style={{ marginTop: 8, marginLeft: 18 }}>
            系统将自动按标注数据总数及各类型进行均分
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateTaskModal;
