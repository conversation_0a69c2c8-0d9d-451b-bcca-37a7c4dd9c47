import React, { useEffect, useRef, useState, useMemo } from 'react';
import {
    Button,
    Card,
    Dropdown,
    Form,
    Input,
    Select,
    Tag,
    Pagination,
    Modal,
} from 'antd';
import { FilterOutlined, SearchOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import MisSelectRoo from '../../../components/rooPlus/MisSearch';
import {
    DATA_TYPES_REV_TEXT,
    DATA_TYPES_TEXT,
    DataStatus,
    DataTypes,
    TaskDetail,
} from '../types';
import styles from '../index.module.scss';
import DialogMarking from './DialogMarking';
import { useTaskDetailStore, useDialogStore } from '../store';
import './taskDetailList.scss';

interface TaskDetailListProps {
    taskId: string;
}

const TaskDetailList: React.FC<TaskDetailListProps> = ({ taskId }) => {
    // 从store中获取状态和方法
    const loading = useTaskDetailStore(state => state.loading);
    const data = useTaskDetailStore(state => state.taskDetailList);
    const total = useTaskDetailStore(state => state.taskDetailTotal);
    const fetchTaskDetailList = useTaskDetailStore(
        state => state.fetchTaskDetailList,
    );
    const setCurrentTaskDetail = useTaskDetailStore(
        state => state.setCurrentTaskDetail,
    );
    const currentTaskDetail = useTaskDetailStore(
        state => state.currentTaskDetail,
    );
    const hasUnsavedChanges = useDialogStore(state => state.hasUnsavedChanges);
    const setHasUnsavedChanges = useDialogStore(
        state => state.setHasUnsavedChanges,
    );
    const setEditingMessageId = useDialogStore(
        state => state.setEditingMessageId,
    );
    const [form] = Form.useForm();
    const leftPanelRef = useRef<HTMLDivElement>(null);
    const rightPanelRef = useRef<HTMLDivElement>(null);
    const [inputValue, setInput] = useState('');
    const [pageNo, setPageNo] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    // 初始化加载任务详情列表
    useEffect(() => {
        if (taskId) {
            fetchTaskDetailList({ taskId, pageNo, pageSize });
        }
    }, [taskId]);

    // 修改 ResizeObserver 监听，同时设置两个面板的高度
    useEffect(() => {
        const pageContent = document.querySelector('.page-content');
        if (!pageContent || !leftPanelRef.current || !rightPanelRef.current)
            return;

        const resizeObserver = new ResizeObserver(entries => {
            for (const entry of entries) {
                const height = entry.contentRect.height;
                const panelHeight = height - 20;
                // 同时设置左右两个面板的最大高度
                if (leftPanelRef.current) {
                    leftPanelRef.current.style.maxHeight = `${panelHeight}px`;
                }
                if (rightPanelRef.current) {
                    rightPanelRef.current.style.maxHeight = `${panelHeight}px`;
                }
            }
        });

        resizeObserver.observe(pageContent);

        return () => {
            resizeObserver.disconnect();
        };
    }, []);

    const initPagination = () => {
        setPageNo(1);
        setPageSize(10);
    };

    const handleSearch = (values: any) => {
        const { shopId, followPerson, dataStatus, dataType, dataId } = values;
        const params = {
            dataId,
            taskId,
            shopId,
            followPerson:
                typeof followPerson === 'object'
                    ? followPerson.id
                    : followPerson,
            dataStatus,
            dataType,
            pageNo: 1,
            pageSize,
        };
        initPagination();
        fetchTaskDetailList(params);
    };

    const handlePageChange = (page: number, pageSize: number) => {
        setPageNo(page);
        setPageSize(pageSize);
        const formData = form.getFieldsValue();
        fetchTaskDetailList({ ...formData, taskId, pageNo: page, pageSize }); // 根据分页参数重新请求数据
    };

    const handleReset = () => {
        form.resetFields();
        initPagination();
        fetchTaskDetailList({ taskId, pageNo: 1, pageSize: 10 });
    };

    const onChangeDataId = e => {
        const data = e.target.value;
        setInput(data);
    };

    const search = () => {
        const data = form.getFieldsValue();
        data.dataId = inputValue;
        handleSearch(data);
    };

    const getDataStatusTag = (status: DataStatus) => {
        switch (status) {
            case DataStatus.PENDING:
                return <Tag color="orange">待标注</Tag>;
            case DataStatus.COMPLETED:
                return <Tag color="green">已完成</Tag>;
            default:
                return <Tag>未知状态</Tag>;
        }
    };

    const getDataTypeTag = (type: DataTypes) => {
        switch (type) {
            case DataTypes.HUMAN_DATA:
                return <Tag color="blue">{DATA_TYPES_TEXT[type]}</Tag>;
            case DataTypes.AI_DATA:
                return <Tag color="purple">{DATA_TYPES_TEXT[type]}</Tag>;
            case DataTypes.MOCK_DATA:
                return <Tag color="cyan">{DATA_TYPES_TEXT[type]}</Tag>;
            default:
                return <Tag color="grey">未知类型</Tag>;
        }
    };

    const renderTaskCard = (task: TaskDetail) => {
        return (
            <Card
                key={task.dataId}
                className={styles.taskCard}
                onClick={() => {
                    if (hasUnsavedChanges) {
                        Modal.confirm({
                            title: '提示',
                            content: '如不保存会丢失数据，是否继续？',
                            onOk() {
                                setCurrentTaskDetail(task);
                                setEditingMessageId(null);
                                setHasUnsavedChanges(false);
                            },
                            onCancel() {
                                return;
                            },
                            okText: '确认',
                            cancelText: '取消',
                        });
                        return;
                    }
                    // 关闭可能打开的Message编辑状态
                    setEditingMessageId(null);
                    setCurrentTaskDetail(task);
                    setHasUnsavedChanges(false);
                }}
                style={
                    currentTaskDetail?.id === task.id
                        ? { border: '1px solid black' }
                        : {}
                }
            >
                <div className={styles.taskHeader}>
                    <span>数据ID: {task.dataId}</span>
                    {getDataStatusTag(task.status)}
                </div>
                <div className={styles.taskInfo}>
                    <div>数据类型: {getDataTypeTag(task.type)}</div>
                    <div>跟进人: {task.followPerson}</div>
                    <div>商家ID: {task.shopId}</div>
                    <div>
                        数据时间:{' '}
                        {dayjs(task.createTime).format('YYYY-MM-DD HH:mm:ss')}
                    </div>
                </div>
            </Card>
        );
    };

    const filterMenu = (
        <Form form={form} onFinish={handleSearch}>
            <Form.Item name="shopId" label="商家ID">
                <Input placeholder="请输入商家ID" />
            </Form.Item>
            <Form.Item name="followPerson" label="跟进人">
                <MisSelectRoo />
            </Form.Item>
            <Form.Item name="dataStatus" label="数据状态">
                <Select
                    placeholder="请选择数据状态"
                    allowClear
                    options={[
                        { value: DataStatus.PENDING, label: '待标注' },
                        { value: DataStatus.COMPLETED, label: '已完成' },
                    ]}
                />
            </Form.Item>
            <Form.Item name="dataType" label="数据类型">
                <Select
                    placeholder="请选择数据类型"
                    allowClear
                    options={[
                        {
                            value: DataTypes.HUMAN_DATA,
                            label: DATA_TYPES_TEXT[DataTypes.HUMAN_DATA],
                        },
                        {
                            value: DataTypes.AI_DATA,
                            label: DATA_TYPES_TEXT[DataTypes.AI_DATA],
                        },
                        {
                            value: DataTypes.MOCK_DATA,
                            label: DATA_TYPES_TEXT[DataTypes.MOCK_DATA],
                        },
                    ]}
                />
            </Form.Item>
            <div style={{ display: 'flex', justifyContent: 'end', gap: 8 }}>
                <Form.Item>
                    <Button onClick={handleReset}>清空选择</Button>
                </Form.Item>
                <Form.Item>
                    <Button type="primary" htmlType="submit">
                        确定
                    </Button>
                </Form.Item>
            </div>
        </Form>
    );

    return (
        <div className={styles.container}>
            <div
                ref={leftPanelRef}
                className={styles.leftPanel}
                style={{ overflow: 'auto' }}
            >
                <div className={styles.header}>
                    <Input
                        prefix={<SearchOutlined />}
                        placeholder="请输入需要检索的数据ID"
                        className={styles.searchInput}
                        onChange={onChangeDataId}
                    />
                    <Button type="primary" onClick={search}>
                        搜索
                    </Button>
                    <Dropdown
                        dropdownRender={() => filterMenu}
                        trigger={['click']}
                        placement="bottomRight"
                        overlayStyle={{
                            zIndex: 999,
                            background: '#fff',
                            padding: '16px',
                            borderRadius: '8px',
                            border: '1px solid #e8e8e8',
                        }}
                    >
                        <Button icon={<FilterOutlined />}>筛选</Button>
                    </Dropdown>
                </div>

                <div className={styles.taskList}>
                    {data.map(renderTaskCard)}
                </div>

                {!inputValue && (
                    <div
                        style={{
                            textAlign: 'right',
                            marginTop: 16,
                            display: 'flex',
                            justifyContent: 'end',
                            marginRight: 6,
                        }}
                    >
                        <Pagination
                            current={pageNo}
                            pageSize={pageSize}
                            responsive={true}
                            showLessItems={true}
                            total={total} // 总数据条数
                            onChange={handlePageChange}
                            showSizeChanger
                            className="detail-pagination"
                        />
                    </div>
                )}
            </div>
            <div
                ref={rightPanelRef}
                className={styles.rightPanel}
                style={{ overflow: 'auto' }}
            >
                {currentTaskDetail ? (
                    <div className={styles.chatPanel}>
                        <DialogMarking />
                    </div>
                ) : (
                    <div className={styles.emptyState}>
                        请选择一条数据查看详情
                    </div>
                )}
            </div>
        </div>
    );
};

export default TaskDetailList;
