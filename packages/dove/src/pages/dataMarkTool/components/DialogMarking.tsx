import React, { useCallback, useEffect, useState, useRef } from 'react';
import { Button, Card, Form, Input, Radio, Typography, Checkbox, Modal } from 'antd';
import { DialogMessage, OVERALL_TAG_TEXT, OverallTag } from '../types';
import styles from '../index.module.scss';
import Message from './Message';
import { useDialogStore, useTaskDetailStore } from '../store';

const { Title } = Typography;

const DialogMarking: React.FC = () => {
  // 从store中获取状态和方法
  const dialogData = useDialogStore(state => state.dialogData);
  const loading = useDialogStore(state => state.loading);
  const editingMessageId = useDialogStore(state => state.editingMessageId);
  const hasUnsavedChanges = useDialogStore(state => state.hasUnsavedChanges);
  const fetchDialogData = useDialogStore(state => state.fetchDialogData);
  const editMessage = useDialogStore(state => state.editMessage);
  const saveEdit = useDialogStore(state => state.saveEdit);
  const cancelEdit = useDialogStore(state => state.cancelEdit);
  const submitAll = useDialogStore(state => state.submitAll);
  const autoNext = useDialogStore(state => state.autoNext);
  const setAutoNext = useDialogStore(state => state.setAutoNext);
  const setHasUnsavedChanges = useDialogStore(state => state.setHasUnsavedChanges);
  const autoPlayTime = useDialogStore(state => state.autoPlayTime);
  const currentTaskDetail = useTaskDetailStore(state => state.currentTaskDetail);
  const [editForm] = Form.useForm();
  const [evaluationForm] = Form.useForm();
  const audioRef = useRef<HTMLAudioElement>(null);

  // 在组件挂载和currentTaskDetail变化时获取数据
  useEffect(() => {
    if (currentTaskDetail?.id) {
      fetchDialogData(currentTaskDetail.id);
    }
  }, [currentTaskDetail?.id]);

  // 当dialogData变化时，设置表单初始值
  useEffect(() => {
    if (dialogData) {
      evaluationForm.setFieldsValue({
        overallEvaluate: dialogData.overallEvaluate,
        overallTag: dialogData.overallTag || OverallTag.ACCEPTABLE,
      });
    }
  }, [dialogData, evaluationForm]);

  const changeMessage = (message: DialogMessage) => {
    editMessage(message);
    editForm.setFieldsValue({
      newMessage: message.newMessage || message.oldMessage,
      modificationTag: message.modificationTag ? Number(message.modificationTag) : message.modificationTag,
      modificationReason: message.modificationReason,
      isSelected: message.isSelected,
    });
  };

  const handleEditMessage = (message: DialogMessage) => {
    // 改变编辑的
    if (editingMessageId && message.id !== editingMessageId && hasUnsavedChanges) {
      Modal.confirm({
        title: '提示',
        content: '如不保存会丢失数据，是否继续？',
        onOk() {
          changeMessage(message);
          setHasUnsavedChanges(false);
        },
        onCancel() {
          return;
        },
        okText: '确认',
        cancelText: '取消'
      });
      return;
    }
    changeMessage(message);
  };

  const handleSaveEdit = async (data) => {
    try {
      let values = null;
      if (data) {
        values = data;
      } else {
        values = await editForm.validateFields();
      }
      saveEdit(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const handleCancelEdit = () => {
    cancelEdit();
    editForm.resetFields();
  };

  const handleSubmitAll = async () => {
    try {
      const values = await evaluationForm.validateFields();
      submitAll(values);
    } catch (error) {
      console.error('提交失败:', error);
    }
  };

  const renderAudioPlayer = () => {
    if (!dialogData || !dialogData.audioUrl) return null;

    return (
      <div className={styles.audioPlayer}>
        <audio ref={audioRef} controls style={{ width: '100%' }}>
          <source src={dialogData.audioUrl} type="audio/mpeg" />
          您的浏览器不支持音频播放
        </audio>
      </div>
    );
  };

  // 修改消息渲染方法，确保每个消息有唯一的key
  const renderMessage = useCallback((message: DialogMessage, index: number) => {
    return (
      <Message
        key={`message-${message.id}-${index}`} // 使用更加唯一的key
        message={message}
        isEditing={editingMessageId === message.id}
        editForm={editForm}
        onEdit={handleEditMessage}
        onSave={handleSaveEdit}
        onCancel={handleCancelEdit}
      />
    );
  }, [editingMessageId, editForm, handleEditMessage, handleSaveEdit, handleCancelEdit]);

  const handleSeek = (time: number) => {
    if (audioRef.current) {
      const invertTime = time / 1000;
      const playTime = typeof(invertTime) === 'number' && !isNaN(invertTime) ? invertTime : 0;
      audioRef.current.currentTime = playTime; 
      audioRef.current.play();
    }
  };

  useEffect(() => {
    handleSeek(autoPlayTime);
  }, [autoPlayTime]);

  return (
    <div className={styles.markingContainer}>
      {
        (currentTaskDetail as any).status === 2 ? (
          <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center', fontSize: '16px', fontWeight: 'bolder' }}>该数据无录音转文字结果</div>
        ) : (
          <>
            {loading ? (
              <div className={styles.loading}>加载中...</div>
            ) : dialogData ? (
              <>
                {renderAudioPlayer()}
                <Card className={styles.dialogBox}>
                  {dialogData.info.map(renderMessage)}
                </Card>
                <Card className={styles.evaluationSection}>
                  <Title level={5}>整体评价</Title>
                  <Form
                    form={evaluationForm}
                    layout="horizontal"
                    initialValues={{
                      overallEvaluate: dialogData?.overallEvaluate || '',
                      overallTag: dialogData?.overallTag || OverallTag.ACCEPTABLE,
                    }}
                  >
                    <Form.Item
                      name="overallTag"
                      style={{ marginBottom: 8 }}
                      label="整体效果"
                      rules={[
                        {
                            required: true,
                        },
                      ]}
                    >
                      <Radio.Group>
                        <Radio value={OverallTag.EXCELLENT}>{OVERALL_TAG_TEXT[OverallTag.EXCELLENT]}</Radio>
                        <Radio value={OverallTag.ACCEPTABLE}>{OVERALL_TAG_TEXT[OverallTag.ACCEPTABLE]}</Radio>
                        <Radio value={OverallTag.BARELY_ACCEPTABLE}>{OVERALL_TAG_TEXT[OverallTag.BARELY_ACCEPTABLE]}</Radio>
                        <Radio value={OverallTag.PROBLEMATIC}>{OVERALL_TAG_TEXT[OverallTag.PROBLEMATIC]}</Radio>
                      </Radio.Group>
                    </Form.Item>

                    <Form.Item
                      name="overallEvaluate"
                      label="整体评价"
                      style={{ marginBottom: 8 }}
                    >
                      <Input placeholder="请输入整体评价" />
                    </Form.Item>
                  </Form>

                  <div className={styles.markBtnSection}>
                    <div>
                      <Button
                        type="primary"
                        onClick={handleSubmitAll}
                        loading={loading}
                      >
                        确认完成
                      </Button>
                    </div>
                    <div>
                      <Checkbox checked={autoNext} onChange={() => setAutoNext(!autoNext)}>
                        自动下一条
                      </Checkbox>
                    </div>
                  </div>
                </Card>
              </>
            ) : (
              <div className={styles.empty}>暂无数据</div>
            )}
          </>
        )
      }
    </div>
  );
};

export default DialogMarking;