# 数据标注工具状态管理

本项目使用 Zustand 进行状态管理，解耦组件之间的通信。

## 状态管理结构

状态管理分为以下几个模块：

### 1. 视图状态 (viewStore)

管理应用的视图模式，包括：
- 任务列表视图
- 任务详情视图

### 2. 任务列表状态 (taskStore)

管理任务列表相关的状态，包括：
- 任务列表数据
- 加载状态
- 搜索参数
- 当前选中的任务
- 创建任务模态框状态
- 任务操作方法（获取、删除、暂停、恢复等）

### 3. 任务详情状态 (taskDetailStore)

管理任务详情相关的状态，包括：
- 任务详情列表数据
- 加载状态
- 搜索参数
- 当前选中的任务详情
- 任务详情操作方法（获取、查看等）

### 4. 对话数据状态 (dialogStore)

管理对话标注相关的状态，包括：
- 对话数据
- 加载状态
- 编辑状态
- 未保存更改状态
- 对话操作方法（获取、编辑、保存等）

## 组件改造

### App.tsx

App 组件是整个应用的入口，使用 zustand store 管理状态：
- 使用 `useViewStore` 管理视图状态
- 使用 `useTaskStore` 管理任务列表状态
- 移除了原有的状态管理代码，使组件更加简洁

### TaskList.tsx

TaskList 组件负责展示任务列表，使用 zustand store 管理状态：
- 使用 `useTaskStore` 获取任务列表数据和操作方法
- 移除了 props 传递，直接从 store 获取数据和方法

### TaskDetailList.tsx

TaskDetailList 组件负责展示任务详情列表，使用 zustand store 管理状态：
- 使用 `useTaskDetailStore` 获取任务详情列表数据和操作方法
- 只保留了 `taskId` 作为 props，其他数据和方法都从 store 获取

### DialogMarking.tsx

DialogMarking 组件负责对话标注，使用 zustand store 管理状态：
- 使用 `useDialogStore` 获取对话数据和操作方法
- 使用 `useTaskDetailStore` 获取当前任务详情
- 移除了所有 props 传递，完全依赖 store 进行状态管理

### CreateTaskModal.tsx

CreateTaskModal 组件负责创建任务，使用 zustand store 管理状态：
- 使用 `useTaskStore` 获取创建任务的方法
- 保留了部分内部状态，如表单状态、导入数据状态等
- 保留了 props 传递，因为这些 props 是由父组件控制的

### Message.tsx

Message 组件是一个通用组件，保留了 props 传递：
- 作为一个通用组件，不直接依赖 store
- 通过 props 接收数据和回调函数
- 这样设计使得组件更加灵活，可以在不同场景下复用

## 状态流转

1. 用户进入应用 -> 加载任务列表
2. 用户点击任务 -> 更新当前任务 -> 切换到任务详情视图 -> 加载任务详情列表
3. 用户点击任务详情 -> 更新当前任务详情 -> 加载对话数据
4. 用户编辑对话 -> 更新对话数据 -> 保存编辑
5. 用户提交标注 -> 更新任务详情状态 -> 返回任务详情视图

## 优势

1. **解耦组件通信**：组件之间不再通过props传递数据和回调函数，而是通过store进行通信
2. **状态集中管理**：所有状态都集中在store中管理，便于维护和调试
3. **代码可读性提高**：组件代码更加简洁，关注点分离
4. **状态共享**：多个组件可以共享同一个状态，避免状态同步问题
5. **性能优化**：通过选择性订阅状态，减少不必要的重渲染

## 使用方法

\`\`\`tsx
// 在组件中使用store
import { useTaskStore } from '../store';

const MyComponent = () => {
  // 只订阅需要的状态
  const loading = useTaskStore(state => state.loading);
  const data = useTaskStore(state => state.taskList);
  const fetchTaskList = useTaskStore(state => state.fetchTaskList);

  // 使用状态和方法
  useEffect(() => {
    fetchTaskList();
  }, [fetchTaskList]);

  return (
    <div>
      {loading ? <Spin /> : <Table dataSource={data} />}
    </div>
  );
};
\`\`\`