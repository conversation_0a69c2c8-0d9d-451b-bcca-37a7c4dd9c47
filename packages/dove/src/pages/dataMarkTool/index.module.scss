.container {
  display: flex;
  height: 100%;
  width: 100%;
  min-height: 700px;
}

.searchForm {
  :global {
    .ant-form-item {
      margin-bottom: 12px;
    }
  }
}

.leftPanel {
  width: 400px;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
}

.rightPanel {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.header {
  margin-top: 48px;
  padding: 16px;
  display: flex;
  gap: 8px;
  border-bottom: 1px solid #e8e8e8;
}

.searchInput {
  flex: 1;
}

.taskList {
  flex: 1;
  overflow-y: auto;
  padding: 12px 12px 0 0;

  :global {
    .ant-card-body {
      padding: 12px;
    }
  }
}

.taskCard {
  padding: 0;
  margin-bottom: 8px;
  cursor: pointer;
  &:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

.taskHeader {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.taskInfo {
  color: #666;
  font-size: 14px;
  > div {
    margin-bottom: 4px;
  }
}

.chatPanel {
  height: 100%;
  border-radius: 4px;
  margin-top: 44px;
}

.emptyState {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #999;
}

.markingContainer {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.audioPlayer {
  position: sticky;
  top: 0;
  z-index: 10;
  background: #fff;
  padding: 0 12px 4px 0;
}

.dialogBox {
  flex: 1;
  overflow-y: auto;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;

  :global {
    .ant-card-body {
      max-height: 0;
      padding: 12px 24px;
    }
  }
}

.evaluationSection {
  margin-top: auto;
  border-top: none;
  border-top-left-radius: 0;
  border-top-right-radius: 0;

  :global {
    .ant-card-body {
      padding: 12px 24px;
    }
  }
}

.buttonGroup {
  display: flex;
  justify-content: flex-end;
}

.markBtnSection {
  display: block;
  text-align: right;

  div {
    margin-top: 6px;
  }
}

.messagePop {
  display: flex;
  align-items: center;
  justify-content: center;

  & > span {
    margin: 0 6px;
    &:hover {
      cursor: pointer;
    }
  }
}

// 消息相关样式
.agentMessage, .customerMessage {
  display: flex;
  margin-bottom: 24px;
  position: relative;
}

.messageAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.messageContent {
  max-width: 70%;
  position: relative;
}

.messageHeader {
  margin-bottom: 4px;
  display: flex;
  align-items: center;

  & > div {
    display: flex;
    align-items: center;
  }
}

.messageName {
  font-weight: 500;
  margin-right: 8px;
}

.messageTime {
  font-size: 12px;
  color: #999;
}

.messageBubble {
  padding: 12px 16px;
  border-radius: 8px;
  position: relative;
  word-break: break-word;
  cursor: pointer;
}

.messageActions {
  position: absolute;
  bottom: -36px;
  left: 0;
  opacity: 0;
  transition: opacity 0.2s ease;
  display: flex;
  align-items: center;
  z-index: 10;
}

.actionButton {
  background-color: #222;
  color: #fff;
  border-radius: 4px;
  padding: 6px 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  position: relative;
  font-size: 14px;

  &:hover {
    background-color: #333;
  }

  .actionIcon {
    font-size: 14px;
  }
  &::after {
    content: '';
    position: absolute;
    top: -6px;
    left: 50%;
    transform: translateX(-50%);
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #222;
  }
}

.agentMessage {
  flex-direction: row-reverse;

  .messageAvatar {
    margin-right: 0;
    margin-left: 12px;
    margin-top:-4px;
  }

  .messageContent {
    align-items: flex-end;
  }

  .messageHeader {
    justify-content: flex-end;
  }

  .messageBubble {
    background-color: rgb(202, 227, 253); // 浅蓝色背景
    color: #000;
    border-top-right-radius: 0;
  }

  .messageActions {
    left: auto;
    right: 0;
  }

  &:hover .messageActions {
    opacity: 1;
  }
}

.customerMessage {
  .messageBubble {
    background-color: #f0f0f0; // 白色背景
    color: #000;
    border-top-left-radius: 0;
  }

  &:hover .messageActions {
    opacity: 1;
  }
}

.editForm {
  width: 400px;
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
