// 操作类型枚举
export enum OperateType {
  PAUSE = 2, // 暂停
  START = 1,  // 启动
  DELETE = 3,  // 删除
}

// 数据类型枚举
export enum DataTypes {
  AI_DATA = 1,  // AI外呼数据
  HUMAN_DATA = 2, // 人工外呼数据
  MOCK_DATA = 3, // 构造数据
}

// 数据类型枚举
export const DATA_TYPES_TEXT = {
  [DataTypes.HUMAN_DATA]: '人工外呼数据',
  [DataTypes.AI_DATA]: 'AI外呼数据',
  [DataTypes.MOCK_DATA]: '构造数据',
}

export const DATA_TYPES_REV_TEXT = {
  '人工外呼数据': DataTypes.HUMAN_DATA,
  'AI外呼数据': DataTypes.AI_DATA,
  '构造数据': DataTypes.MOCK_DATA,
}

// 任务状态枚举
export enum TaskStatus {
  CREATING = 1, // 任务创建中（数据拉取中）
  PENDING = 2,  // 待开始（一条都没标）
  IN_PROGRESS = 3, // 进行中（标记中，但有未完成的）
  COMPLETED = 4, // 已完成（全部都标记完）
  PAUSED = 5, // 已暂停（手动点击暂停）
}

// 任务状态枚举
export const TASK_STATUS_TEXT = {
  [TaskStatus.CREATING]: '任务创建中',
  [TaskStatus.PENDING]: '待开始',
  [TaskStatus.IN_PROGRESS]: '进行中',
  [TaskStatus.COMPLETED]: '已完成',
  [TaskStatus.PAUSED]: '已暂停',
}

// 任务状态枚举反向
export const TASK_STATUS_REV_TEXT = {
  '任务创建中': TaskStatus.CREATING,
  '待开始': TaskStatus.PENDING,
  '进行中': TaskStatus.IN_PROGRESS,
  '已完成': TaskStatus.COMPLETED,
  '已暂停': TaskStatus.PAUSED,
}

// 任务类型枚举
export enum TaskType {
  PHONE = 1,
  IM = 2,
  KNOWLEDGE = 3,
}

// 任务类型枚举
export const TASK_TYPE_TEXT = {
  [TaskType.PHONE]: '通话数据标注',
  [TaskType.IM]: 'IM数据标注',
  [TaskType.KNOWLEDGE]: '知识库数据标注',
}

// 数据状态枚举
export enum DataStatus {
  PENDING = 0, // 待标注
  COMPLETED = 1, // 已完成
}

// 修改原因枚举
export enum ModificationReason {
  TOO_LONG = 1, // 字数太长
  NOT_COLLOQUIAL = 2, // 不够口语化
  NOT_SUITABLE = 3, // 不贴合外呼场景
  INCORRECT = 4, // 回答有误
  OTHER = 5  // 其他
}

export enum Speaker {
  AGENT = 0,
  CUSTOMER = 1,
}

// 整体效果枚举
export enum OverallTag {
  EXCELLENT = 1, // 非常好
  ACCEPTABLE = 2, // 效果一般能接受
  BARELY_ACCEPTABLE = 3, // 勉强可接受
  PROBLEMATIC = 4, // 问题较多无法接受
}

export const OVERALL_TAG_TEXT = {
  [OverallTag.EXCELLENT]: '非常好', // 非常好
  [OverallTag.ACCEPTABLE]: '效果一般能接受', // 效果一般能接受
  [OverallTag.BARELY_ACCEPTABLE]: '勉强可接受', // 勉强可接受
  [OverallTag.PROBLEMATIC]: '问题较多无法接受', // 问题较多无法接受
}

// 对话消息接口
export interface DialogMessage {
  id: string;
  speaker: Speaker;
  oldMessage: string;
  newMessage?: string;
  modificationTag?: ModificationReason;
  modificationReason?: string;
  state: DataStatus;
  isSelected?: 1 | 0;
  timestamp?: number; // 消息发送时间
  startTime?: number;
  endTime?: number;
}

// 对话数据接口
export interface DialogData {
  id: string;
  contentId: string;
  followPerson: string;
  createTime: number;
  type: DataTypes;
  overallEvaluate?: OverallTag;
  overallTag?: string;
  info: DialogMessage[];
  audioUrl?: string;
}

// 任务接口
export interface Task {
  id: string;
  name: string;
  type: string;
  status: string;
  creator: {
    uid: string;
    name: string;
  };
  createTime: number;
  updateTime: number;
  followPersons: string[];
  dataCount: string;
  timeSpent?: string; // 任务耗时（毫秒）
}

// 任务详情接口
export interface TaskDetail {
  id: string;
  taskId: string;
  dataId: string;
  shopId?: string;
  shopName?: string;
  followPerson: string;
  status: DataStatus;
  type: DataTypes;
  createTime: number;
  updateTime: number;
  dialogData?: DialogData;
}