# 数据标注工具 PRD

## 一、需求概述

### 1.1 需求背景
在推进AI方向需求过程中，发现基建能力较为薄弱，尤其是生产优质数据这部分，成为了算法训练优化模型的主要卡点，所以需要建立一个数据标注工具供生产优质数据使用。

### 1.2 业务目标
- 支持多数据源、多数据类型标注
- 提供优质数据后可优化模型效果
- 为所有模型训练方向提供数据标注能力

## 二、功能模块

### 2.1 任务管理模块

#### 2.1.1 任务列表
- 展示所有标注任务，包括任务ID、任务名称、任务类型、任务状态、创建人、创建时间、跟进人、任务进度、任务耗时等信息
- 支持按任务ID、商家ID、任务类型、任务状态、创建人、数据ID等条件筛选
- 支持任务的删除、暂停、启动等操作
- 任务状态包括：任务创建中、待开始、进行中、已完成、已暂停

#### 2.1.2 创建任务
- 支持创建新的标注任务，包括任务名称、任务类型、数据导入、任务分配等信息
- 数据导入支持多种数据类型：人工外呼数据、AI外呼数据、构造数据
- 支持按dt+商家类型+标签+id导入数据
- 支持分配MIS账号，按标注数据总数及各类型进行均分

### 2.2 数据标注模块

#### 2.2.1 任务明细列表
- 展示任务下的所有数据明细，包括数据ID、商家ID、商家名称、跟进人、数据状态、数据类型、创建时间、更新时间等信息
- 支持按数据ID、商家ID、跟进人、数据状态、数据类型等条件筛选
- 数据状态包括：待标注、已完成
- 数据类型包括：人工外呼数据、AI外呼数据、构造数据

#### 2.2.2 对话数据标注
- 展示对话数据，包括客服和客户的对话内容
- 支持对客服回复内容进行修改，包括修改后回复、修改意见、是否为优质回复等字段
- 支持对整体对话进行评价，包括整体效果评价和整体评价标签
- 支持录音播放功能（若有录音）
- 修改原因枚举：字数太长、不够口语化、不贴合外呼场景、回答有误
- 整体效果枚举：非常好、效果一般能接受、勉强可接受、问题较多无法接受

## 三、页面流程

### 3.1 任务列表页面
1. 用户进入系统，默认展示任务列表页面
2. 用户可以通过筛选条件查询特定任务
3. 用户可以点击"新建任务"按钮创建新任务
4. 用户可以对任务进行操作，如查看、暂停、启动、删除等
5. 用户点击任务ID或任务名称，进入任务详情页面

### 3.2 创建任务流程
1. 用户点击"新建任务"按钮，弹出创建任务模态框
2. 用户填写任务名称、选择任务类型
3. 用户选择数据类型和数据来源，输入数据参数
4. 用户点击"确认导入"按钮，导入数据
5. 用户添加跟进人MIS账号
6. 用户点击"创建任务"按钮，创建任务

### 3.3 任务详情页面
1. 用户进入任务详情页面，展示任务下的数据明细列表
2. 用户可以通过筛选条件查询特定数据
3. 用户点击数据ID或"标注"按钮，进入数据标注页面

### 3.4 数据标注流程
1. 用户进入数据标注页面，展示对话数据
2. 用户可以点击客服回复旁的"编辑"按钮，修改回复内容
3. 用户填写修改后回复、选择修改原因、输入修改意见、选择是否为优质回复
4. 用户点击"保存"按钮，保存修改
5. 用户选择整体效果评价，输入整体评价标签
6. 用户点击"提交标注"按钮，提交所有修改

## 四、数据结构

### 4.1 任务数据结构
\`\`\`typescript
interface Task {
  id: string;
  name: string;
  type: string;
  status: TaskStatus;
  creator: {
    uid: string;
    name: string;
  };
  createTime: number;
  updateTime: number;
  followPersons: string[];
  dataCount: {
    total: number;
    completed: number;
    pending: number;
  };
  timeSpent?: number;
}
\`\`\`

### 4.2 任务详情数据结构
\`\`\`typescript
interface TaskDetail {
  id: string;
  taskId: string;
  dataId: string;
  shopId?: string;
  shopName?: string;
  followPerson: string;
  status: DataStatus;
  type: DataType;
  createTime: number;
  updateTime: number;
  dialogData?: DialogData;
}
\`\`\`

### 4.3 对话数据结构
\`\`\`typescript
interface DialogData {
  id: string;
  contentId: string;
  followPerson: string;
  createTime: number;
  type: DataType;
  overallEvaluate?: OverallEvaluation;
  overallTag?: string;
  info: DialogMessage[];
  audioUrl?: string;
}

interface DialogMessage {
  id: string;
  speaker: number; // 1 'customer' 或 0 'agent'
  oldMessage: string;
  newMessage?: string;
  modificationTag?: ModificationReason;
  modificationReason?: string;
  state: DataStatus;
  isSelected?: boolean;
  timestamp?: number;
}
\`\`\`

## 五、非功能需求

### 5.1 权限控制
- 管理员角色：可以看到全量任务，并创建任务时可以分配给别人
- 普通角色：只可以看到自己名下的任务，标注自己的任务
- 新建任务权限：当前只开发给管理员角色

### 5.2 性能要求
- 页面加载时间不超过3秒
- 数据标注保存响应时间不超过2秒
- 支持同时处理多个标注任务

### 5.3 用户体验
- 标注过程中如有未保存的修改，离开页面时提示用户
- 对话数据标注时，支持定位到录音相应时间点
- 已标注的对话显示明显的标记

## 六、接口设计

### 6.1 任务管理接口
- `/xianfu/api-v2/dove/dataMarkTool/task/query`：查询任务列表
- `/xianfu/api-v2/dove/dataMarkTool/task/create`：创建任务
- `/xianfu/api-v2/dove/dataMarkTool/task/delete`：删除任务
- `/xianfu/api-v2/dove/dataMarkTool/task/pause`：暂停任务
- `/xianfu/api-v2/dove/dataMarkTool/task/resume`：恢复任务

### 6.2 数据标注接口
- `/xianfu/api-v2/dove/dataMarkTool/taskDetail/query`：查询任务详情列表
- `/xianfu/api-v2/dove/dataMarkTool/dialog/{taskDetailId}`：获取对话数据
- `/xianfu/api-v2/dove/dataMarkTool/dialog/save`：保存标注数据

## 七、后续规划

### 7.1 功能扩展
- 支持知识库数据和IM数据标注
- 支持批量导入和导出数据
- 支持标注质量评估和审核
- 支持标注数据的统计分析

### 7.2 性能优化
- 优化大量数据的加载和渲染性能
- 实现数据的增量加载
- 优化音频播放体验