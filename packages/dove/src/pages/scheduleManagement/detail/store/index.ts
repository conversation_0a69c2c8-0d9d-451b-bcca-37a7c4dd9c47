// 顶层状态管理

import { useState, useEffect } from 'react';
import { Form, App } from 'antd';
import { createContainer } from 'unstated-next';
import { useAntdTable } from 'ahooks';
import qs from 'qs';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import {
    SceneDetailData,
    DEFAULT_FORM_VALUES,
    formatSceneDetailData,
    formatSceneFormData,
} from '../utils';
import { DETAIL_PAGE_TYPE, setSearchObjToUrl } from '@src/utils';

const useStore = () => {
    const searchObj = qs.parse(window.location.search, {
        ignoreQueryPrefix: true,
    }); // url 上 search 对象
    const { message, modal } = App.useApp(); // 消息实例
    const [form] = Form.useForm(); // 表单实例
    const [pageType, setPageType] = useState<DETAIL_PAGE_TYPE>(
        DETAIL_PAGE_TYPE.CREATE,
    ); // 页面类型
    const [loading, setLoading] = useState(false); // 是否在loading中
    const [detail, setDetail] = useState<SceneDetailData>({}); // 【场景】详情数据
    const [objectTypeOptions, setObjectTypeOptions] = useState<any[]>([]); // 【对象】可选项
    const [agentOptions, setAgentOptions] = useState<any[]>([]); // 【场景agent】可选项
    const [upstreamSystemsOptions, setUpstreamSystemsOptions] = useState<any[]>(
        [],
    ); // 【发起方】可选项
    const [unsavedChanges, setUnsavedChanges] = useState(false); // 是否存在未保存的修改

    // 初始化
    useEffect(() => {
        if (searchObj.sceneId) {
            if (searchObj.edit === '1') {
                // "编辑"场景
                setPageType(DETAIL_PAGE_TYPE.EDIT);
                // 查询场景详情数据
                getSceneDetailData(searchObj.sceneId);
                // 查询调度流程表格数据
                useScheduleProcessTable.search.submit();
            } else {
                // "查看"场景
                setPageType(DETAIL_PAGE_TYPE.VIEW);
                // 查询场景详情数据
                getSceneDetailData(searchObj.sceneId);
                // 查询调度流程表格数据
                useScheduleProcessTable.search.submit();
            }
        } else {
            if (searchObj.edit === '1') {
                // "新建"场景
                setPageType(DETAIL_PAGE_TYPE.CREATE);
                // 场景表单写入默认值
                form.setFieldsValue(DEFAULT_FORM_VALUES);
            }
        }
        getObjectTypeOptions();
        getAgentOptions();
        getUpstreamSystemsOptions();
    }, []);

    // 获取[场景]详情数据
    const getSceneDetailData = async (sceneId: number) => {
        setLoading(true);
        try {
            const res: any = await apiCaller.post(
                // @ts-ignore
                '/xianfu/api-v2/ai-infra/schedule/scene/searchSceneDetail',
                {
                    sceneId, // 场景id
                },
            );
            if (res.code === 0 && res.data) {
                // 保存详情数据
                setDetail(res.data);
                // 格式化详情数据
                const formData = formatSceneDetailData(res.data);
                // 写入表单
                form.setFieldsValue(formData);
                return Promise.resolve(res.data);
            } else {
                return Promise.reject(res.msg || '场景详情数据获取失败');
            }
        } catch (err) {
            return Promise.reject(err);
        } finally {
            setLoading(false);
        }
    };

    // 获取【对象】可选项
    const getObjectTypeOptions = async () => {
        try {
            const res: any = await apiCaller.post(
                // @ts-ignore
                '/xianfu/api-v2/ai-infra/schedule/scene/object-type-list',
                {},
            );
            if (res.code === 0) {
                const options = (res?.data || []).map(item => ({
                    value: item.code,
                    label: item.name,
                }));
                setObjectTypeOptions(options);
                return Promise.resolve(options);
            } else {
                return Promise.reject(res.msg || '获取【对象】可选项失败');
            }
        } catch (err) {
            return Promise.reject(err);
        }
    };

    // 获取【场景agent】可选项
    const getAgentOptions = async () => {
        try {
            const res: any = await apiCaller.post(
                // @ts-ignore
                '/xianfu/api-v2/dove/agent/query',
                {
                    page: 1,
                    pageSize: 100,
                    status: [1], // 只查询启用状态的 agent
                    forUse: true,
                } as any,
            );
            if (res.code === 0) {
                const options = (res?.data?.data || []).map(item => ({
                    value: item.id,
                    label: item.name,
                }));
                setAgentOptions(options);
                return Promise.resolve(options);
            } else {
                return Promise.reject(res.msg || '获取【场景agent】可选项失败');
            }
        } catch (err) {
            return Promise.reject(err);
        }
    };

    // 获取【发起方】可选项
    const getUpstreamSystemsOptions = async () => {
        try {
            const res: any = await apiCaller.post(
                // @ts-ignore
                '/xianfu/api-v2/ai-infra/schedule/system/list',
                {
                    type: 'upstreamSystem',
                },
            );
            if (res.code === 0) {
                const options = (res.data || []).map(item => ({
                    value: item.id,
                    label: item.name,
                }));
                setUpstreamSystemsOptions(options);
                return Promise.resolve(options);
            } else {
                return Promise.reject(res.msg || '获取【发起方】可选项失败');
            }
        } catch (err) {
            return Promise.reject(err);
        }
    };

    // [调度流程]表格查询hooks
    const useScheduleProcessTable = useAntdTable(
        async ({ current: pageNum, pageSize }) => {
            try {
                if (!searchObj.sceneId) {
                    return {
                        list: [],
                        total: 0,
                    };
                }
                const res: any = await apiCaller.post(
                    // @ts-ignore
                    '/xianfu/api-v2/ai-infra/schedule/process/query',
                    {
                        sceneId: searchObj.sceneId, // 场景id
                        pageSize,
                        pageNum,
                    },
                );
                if (res.code !== 0) {
                    return {
                        list: [],
                        total: 0,
                    };
                }
                return {
                    list: res?.data?.data || [],
                    total: res?.data?.total || 0,
                };
            } catch (error) {
                console.error('获取数据失败:', error);
                return {
                    list: [],
                    total: 0,
                };
            }
        },
        {
            defaultPageSize: 20,
            // form,
            manual: true, // 初始化时不查询
        },
    );

    /**
     * 新建[场景]动作Promise
     * @returns 场景id
     */
    const handleCreateScene = async () => {
        try {
            const formData = await form.validateFields();
            const params = formatSceneFormData(formData);
            setLoading(true);
            try {
                const res: any = await apiCaller.post(
                    // @ts-ignore
                    '/xianfu/api-v2/ai-infra/schedule/scene/createScene',
                    params,
                );
                if (res.code === 0) {
                    message.success('场景创建成功!');
                    // 查询场景详情数据
                    getSceneDetailData(res.data);
                    // 返回场景id
                    return Promise.resolve(res.data);
                } else {
                    return Promise.reject(res.msg || '场景创建失败');
                }
            } catch (err) {
                return Promise.reject(err);
            } finally {
                setLoading(false);
            }
        } catch (err) {
            message.error('表单验证失败，请检查红色报错信息');
            return Promise.reject(err);
        }
    };

    /**
     * 编辑[场景]动作Promise
     * @returns 场景id
     */
    const handleEditScene = async () => {
        try {
            const formData = await form.validateFields();
            const params = formatSceneFormData(formData);
            setLoading(true);
            try {
                const res: any = await apiCaller.post(
                    // @ts-ignore
                    '/xianfu/api-v2/ai-infra/schedule/scene/updateScene',
                    {
                        ...params,
                        id: detail.id,
                    },
                );
                if (res.code === 0) {
                    message.success('场景编辑成功!');
                    // 查询场景详情数据
                    getSceneDetailData(res.data);
                    // 返回场景id
                    return Promise.resolve(res.data);
                } else {
                    return Promise.reject(res.msg || '场景编辑失败');
                }
            } catch (err) {
                return Promise.reject(err);
            } finally {
                setLoading(false);
            }
        } catch (err) {
            message.error('表单验证失败，请检查红色报错信息');
            return Promise.reject(err);
        }
    };

    // 格式化表单数据（用于提交）
    const formatFormData = formData => {
        const params = {
            ...formData,
            managers: Array.isArray(formData.managers)
                ? formData.managers.map(item => {
                      const obj = JSON.parse(item.key);
                      return {
                          id: obj.id,
                          name: obj.name,
                          misId: obj.email,
                      };
                  })
                : [],
        };
        return params;
    };

    // 检查是否有未保存的[场景]数据，Promise
    const checkUnsavedSceneData = () => {
        return new Promise((resolve, reject) => {
            if (unsavedChanges) {
                // 需要先保存场景
                modal.confirm({
                    title: '提示',
                    content:
                        '[场景]数据有修改且未保存，是否先提交表单保存[场景]？',
                    maskClosable: true,
                    onOk: async () => {
                        try {
                            if (
                                pageType === DETAIL_PAGE_TYPE.CREATE &&
                                !detail.id
                            ) {
                                // "新建"场景，且第一次保存
                                const sceneId = await handleCreateScene();
                                // 把场景id写入url
                                setSearchObjToUrl({ sceneId });
                            } else {
                                // "编辑"场景
                                await handleEditScene();
                            }
                            setUnsavedChanges(false); // 设置[场景]数据已保存
                            // 返回校验成功，允许流程继续
                            resolve(null);
                        } catch (err) {
                            // 返回校验失败，不允许流程继续
                            reject(err);
                        }
                    },
                    onCancel: () => {
                        // 返回校验失败，不允许流程继续
                        reject(new Error('不保存[场景]'));
                    },
                });
            } else {
                // 返回校验成功，允许流程继续
                resolve(null);
            }
        });
    };

    // 检查是否有未保存的新建[场景]数据，Promise
    const checkUnaddSceneData = () => {
        return new Promise((resolve, reject) => {
            if (pageType === DETAIL_PAGE_TYPE.CREATE && !detail.id) {
                // 需要先保存场景
                modal.confirm({
                    title: '提示',
                    content:
                        '需要先创建[场景]数据才能新建[调度流程]，是否先提交表单保存[场景]？',
                    maskClosable: true,
                    onOk: async () => {
                        try {
                            // "新建"场景，且第一次保存
                            const sceneId = await handleCreateScene();
                            // 把场景id写入url
                            setSearchObjToUrl({ sceneId });
                            // 设置[场景]数据已保存
                            setUnsavedChanges(false);
                            // 返回校验成功，允许流程继续
                            resolve(null);
                        } catch (err) {
                            // 返回校验失败，不允许流程继续
                            reject(err);
                        }
                    },
                    onCancel: () => {
                        // 返回校验失败，不允许流程继续
                        reject(new Error('不保存[场景]'));
                    },
                });
            } else {
                // 返回校验成功，允许流程继续
                resolve(null);
            }
        });
    };

    return {
        form,
        pageType,
        searchObj,
        loading,
        detail,
        setDetail,
        objectTypeOptions,
        agentOptions,
        upstreamSystemsOptions,
        useScheduleProcessTable,
        getSceneDetailData,
        handleCreateScene,
        handleEditScene,
        unsavedChanges,
        setUnsavedChanges,
        checkUnsavedSceneData,
        checkUnaddSceneData,
    };
};

export default createContainer(useStore);
