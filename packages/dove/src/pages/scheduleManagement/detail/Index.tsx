import { Form, Steps, Button, Space, App, Spin } from 'antd';
import BasicInfoCard from './components/BasicInfoCard';
import SceneModelCard from './components/SceneModelCard';
import ScheduleProcessCard from './components/ScheduleProcessCard';
import useStore from './store';
import { DETAIL_PAGE_TYPE, setSearchObjToUrl } from '@src/utils';
import './index.scss';

const Index = () => {
    const {
        pageType, // 页面类型
        form, // 表单实例
        loading, // 是否在loading中
        detail, // 详情数据
        handleEditScene, // 编辑[场景]动作Promise
        handleCreateScene, // 新建[场景]动作Promise
        setUnsavedChanges, // 设置是否存在未保存的修改
        checkUnsavedSceneData, // 检查是否有未保存的[场景]数据
    } = useStore.useContainer();
    const { modal } = App.useApp();

    // 【提交】按钮点击事件
    const handleSubmit = () => {
        // console.log('detail', detail);
        modal.confirm({
            title: '保存数据',
            content: '确定要保存数据吗？',
            maskClosable: true,
            onOk: async () => {
                try {
                    // 存在详情id，说明是在“编辑”数据；没有详情id，说明是在“新建”数据
                    const sceneId = detail.id
                        ? await handleEditScene()
                        : await handleCreateScene();
                    // 把场景id写入url
                    setSearchObjToUrl({ sceneId });
                    // 页面刷新
                    window.location.reload();
                } catch (err) {
                    console.error('保存数据失败:', err);
                }
            },
        });
    };

    return (
        <div className="schedule-management-detail">
            <Steps
                style={{
                    padding: '0 100px 20px',
                }}
                current={-1}
                items={[
                    { title: '填写基础信息' },
                    { title: '关联场景模型' },
                    { title: '配置调度流程' },
                ]}
            />
            <Spin spinning={loading}>
                <Form
                    form={form}
                    labelCol={{ span: 4 }}
                    wrapperCol={{ span: 12 }}
                    onValuesChange={() => {
                        setUnsavedChanges(true);
                    }}
                >
                    <BasicInfoCard />
                    <SceneModelCard />
                    <ScheduleProcessCard />
                    <div
                        style={{
                            display: 'flex',
                            justifyContent: 'flex-end',
                            alignItems: 'center',
                            padding: '20px 0',
                        }}
                    >
                        <Space>
                            {[
                                DETAIL_PAGE_TYPE.CREATE,
                                DETAIL_PAGE_TYPE.EDIT,
                            ].includes(pageType) ? (
                                <>
                                    <Button
                                        onClick={async () => {
                                            const url = `${
                                                import.meta.env.DEV
                                                    ? ''
                                                    : '/page/dove'
                                            }/scheduleManagement/list`;
                                            try {
                                                await checkUnsavedSceneData();
                                                window.open(url, '_self');
                                            } catch (err) {
                                                // 点'取消'后，也允许回跳
                                                window.open(url, '_self');
                                            }
                                        }}
                                    >
                                        取消
                                    </Button>
                                    <Button
                                        type="primary"
                                        onClick={handleSubmit}
                                    >
                                        提交
                                    </Button>
                                </>
                            ) : (
                                <Button
                                    onClick={() => {
                                        window.open(
                                            `${
                                                import.meta.env.DEV
                                                    ? ''
                                                    : '/page/dove'
                                            }/scheduleManagement/list`,
                                            '_self',
                                        );
                                    }}
                                >
                                    返回调度场景列表
                                </Button>
                            )}
                        </Space>
                    </div>
                </Form>
            </Spin>
        </div>
    );
};
export default () => (
    <useStore.Provider>
        <Index />
    </useStore.Provider>
);
