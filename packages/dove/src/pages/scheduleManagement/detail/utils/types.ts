// 单条【场景】数据
export interface SceneDetailData {
    id?: number; // 场景id
    name?: string; // 【场景名称】
    objectType?: number; // 【对象】
    enable?: boolean; // 【状态】
    // 【管理员】
    managers?: {
        id: number; // uid
        misId: string; // misId
        name: string; // 姓名
    }[];
    description?: string; // 【场景描述】
    agentId?: number[]; // 【场景agent】
    // 【意向标签】
    intentionTagList?: {
        name: string; // 【标签符号】
        description: string; // 【标签名称】
    }[];
}

// 【场景】表单数据
export interface SceneFormData {
    name?: string; // 【场景名称】
    objectType?: number; // 【对象】
    enable?: boolean; // 【状态】
    // 【管理员】
    managers?: {
        value: number;
        label: string;
        key: string;
    }[];
    description?: string; // 【场景描述】
    agentId?: number[]; // 【场景agent】
    // 【意向标签】
    intentionTagList?: {
        name: string; // 【标签符号】
        description: string; // 【标签名称】
    }[];
}

// 【调度流程】基本信息，表单数据
export interface ScheduleProcessFormData {
    processName?: string; // 【流程名称】
    upstreamSystems?: number[]; // 【发起方】
    processDesc?: string; // 【流程描述】
    processStatus?: number; // 【状态】
}

// 【调度流程】表格行数据
export interface ScheduleProcessTableRow {
    id?: number; // 调度流程id
    bizId?: number; // 业务线id
    processName?: string; // 【流程名称】
    upstreamSystems?: number[]; // 【发起方】
    processDesc?: string; // 【流程描述】
    processStatus?: number; // 【状态】
    version?: number; // 版本
    sceneId?: number; // 场景id
    creatorUid?: number; // 创建人uid
    creatorMis?: string; // 创建人mis
    createTime?: number; // 创建时间
    updateTime?: number; // 更新时间
}
