import { SceneDetailData, SceneFormData } from './types';

// 格式化场景详情数据（用于表单反显）
export const formatSceneDetailData = (
    detail: SceneDetailData,
): SceneFormData => {
    const formData = {
        name: detail.name, // 【场景名称】
        description: detail.description, // 【场景描述】
        objectType: detail.objectType, // 【对象】
        enable: detail.enable, // 【状态】
        managers: Array.isArray(detail.managers)
            ? detail.managers.map(item => ({
                  value: item.id,
                  label: `${item.name}（${item.misId}）`,
                  key: JSON.stringify({
                      id: item.id,
                      name: item.name,
                      email: item.misId,
                  }),
              }))
            : [], // 【管理员】
        agentId: detail.agentId, // 【场景agent】
        intentionTagList: Array.isArray(detail.intentionTagList)
            ? detail.intentionTagList.map(item => ({
                  name: item.name, // 【标签符号】
                  description: item.description, // 【标签名称】
              }))
            : [], // 【意向标签】
    };
    return formData;
};

// 格式化场景表单数据（用于提交）
export const formatSceneFormData = (
    formData: SceneFormData,
): SceneDetailData => {
    const params = {
        name: formData.name, // 【场景名称】
        description: formData.description, // 【场景描述】
        objectType: formData.objectType, // 【对象】
        enable: formData.enable, // 【状态】
        managers: Array.isArray(formData.managers)
            ? formData.managers.map(item => {
                  const obj = JSON.parse(item.key);
                  return {
                      id: obj.id, // uid
                      name: obj.name, // 姓名
                      misId: obj.email, // misId
                  };
              })
            : [], // 【管理员】
        agentId: formData.agentId, // 【场景agent】
        intentionTagList: Array.isArray(formData.intentionTagList)
            ? formData.intentionTagList.map(item => ({
                  name: item.name, // 【标签符号】
                  description: item.description, // 【标签名称】
              }))
            : [], // 【意向标签】
    };
    return params;
};
