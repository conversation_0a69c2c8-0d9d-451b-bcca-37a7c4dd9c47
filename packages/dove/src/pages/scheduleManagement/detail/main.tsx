import { useEffect } from 'react';
import { ConfigProvider, App } from 'antd';
import { render } from '@src/module/root';
import Index from './Index';

const Wrapper = () => {
    useEffect(() => {
        const defaultTracker = window.LXAnalytics('getTracker');
        defaultTracker('pageView', {}, {}, 'c_waimai_m_m_crm_bi_mspuss76');
    }, []);

    return (
        <ConfigProvider
            theme={{
                token: {
                    colorPrimary: '#222',
                    colorLink: '#FF6A00',
                },
                components: {
                    Button: {
                        colorTextLightSolid: '#ffffff',
                    },
                    Table: {
                        rowSelectedBg: '#F5F6FA',
                        rowHoverBg: '#F5F6FA',
                        rowSelectedHoverBg: '#F5F6FA',
                    },
                },
            }}
        >
            <App>
                <div className={'noOutline'}>
                    <Index />
                </div>
            </App>
        </ConfigProvider>
    );
};

render(<Wrapper />, '调度详情');
