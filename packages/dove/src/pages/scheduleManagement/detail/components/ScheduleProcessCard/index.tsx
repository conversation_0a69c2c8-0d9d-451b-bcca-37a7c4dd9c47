/**
 * 【配置调度流程】模块
 */
import { Avatar, Button, App, Table, Space, Tag } from 'antd';
import type { TableColumnType } from 'antd';
import ScheduleProcessForm from './ScheduleProcessForm';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import AddScheduleBtn from './AddScheduleBtn';
import useStore from '../../store';
import {
    processStatusOptions,
    PROCESS_STATUS_ENABLE,
    PROCESS_STATUS_DISABLE,
    ScheduleProcessTableRow,
} from '../../utils';
import emptyImg from '@src/assets/img/empty.png';

const ScheduleProcessCard = () => {
    const { modal, message } = App.useApp();
    const {
        searchObj, // 查询参数
        useScheduleProcessTable, // 调度流程crud查询hooks
        upstreamSystemsOptions, // 【发起方】可选项
        checkUnsavedSceneData, // 检查是否有未保存的[场景]数据
    } = useStore.useContainer();

    // 表格列配置
    const columns = [
        {
            dataIndex: 'id',
            title: '流程ID',
            width: 100,
            render: text => text || '-',
        },
        {
            dataIndex: 'processName',
            title: '流程名称',
            width: 200,
            render: text => text || '-',
        },
        {
            dataIndex: 'processDesc',
            title: '流程描述',
            width: 200,
            render: text => text || '-',
        },
        {
            dataIndex: 'upstreamSystems',
            title: '发起方',
            width: 300,
            render: text =>
                Array.isArray(text)
                    ? text
                        .map(
                            item =>
                                upstreamSystemsOptions.find(
                                    cell => cell.value === item,
                                )?.label || '-',
                        )
                        .join('、')
                    : '-',
        },
        {
            dataIndex: 'processStatus',
            title: '启用状态',
            width: 100,
            render: text => {
                const option = processStatusOptions.find(
                    item => item.value === text,
                );
                return option ? (
                    <Tag color={option.color}>{option.label}</Tag>
                ) : (
                    '-'
                );
            },
        },
        {
            dataIndex: 'operate',
            title: '操作',
            width: 300,
            render: (text, record) => {
                return (
                    <Space wrap>
                        <Button
                            type="link"
                            size="small"
                            onClick={() => {
                                const modalInstance = modal.confirm({
                                    title: '编辑调度流程',
                                    width: 800,
                                    icon: null,
                                    maskClosable: true,
                                    content: (
                                        <ScheduleProcessForm
                                            initialValues={record}
                                            onCancel={() => {
                                                modalInstance.destroy();
                                            }}
                                            onOk={async formValues => {
                                                try {
                                                    const res: any =
                                                        await apiCaller.post(
                                                            // @ts-ignore
                                                            '/xianfu/api-v2/ai-infra/schedule/process/edit',
                                                            {
                                                                ...formValues,
                                                                id: record.id, // 调度流程id
                                                            },
                                                        );
                                                    if (res.code === 0) {
                                                        message.success(
                                                            '更新成功!',
                                                        );
                                                        modalInstance.destroy();
                                                        // 刷新[调度流程]表格
                                                        useScheduleProcessTable.refresh();
                                                        return Promise.resolve();
                                                    } else {
                                                        return Promise.reject(
                                                            res.msg ||
                                                            '更新失败',
                                                        );
                                                    }
                                                } catch (err) {
                                                    return Promise.reject(err);
                                                }
                                            }}
                                        />
                                    ),
                                    footer: null,
                                });
                            }}
                        >
                            编辑
                        </Button>
                        <Button
                            type="link"
                            size="small"
                            onClick={async () => {
                                try {
                                    await checkUnsavedSceneData();
                                    // 跳转页面地址
                                    let url = `${import.meta.env.DEV ? '' : '/page/dove'
                                        }/flowConfig?sceneId=${record.sceneId
                                        }&processId=${record.id}&fromXFIframe=1`;
                                    if (searchObj?.edit === '1') {
                                        url += '&edit=1';
                                    }
                                    window.open(url, '_self');
                                } catch (err) {
                                    console.log(err);
                                }
                            }}
                        >
                            配置流程
                        </Button>
                        {record.processStatus === PROCESS_STATUS_ENABLE ? (
                            <Button
                                type="link"
                                size="small"
                                onClick={() => {
                                    modal.confirm({
                                        title: '停用',
                                        content: (
                                            <div>
                                                <div>
                                                    确定要停用该调度流程吗？
                                                </div>
                                                <div>流程名称：
                                                    <span
                                                        style={{
                                                            fontWeight: 'bold',
                                                        }}
                                                    >
                                                        {record.processName}
                                                    </span>
                                                </div>
                                            </div>
                                        ),
                                        maskClosable: true,
                                        onOk: async () => {
                                            try {
                                                const res: any =
                                                    await apiCaller.post(
                                                        // @ts-ignore
                                                        '/xianfu/api-v2/ai-infra/schedule/process/edit',
                                                        {
                                                            // 复用编辑接口，要把所有详情值都带上
                                                            ...record,
                                                            processStatus:
                                                                PROCESS_STATUS_DISABLE, // 标记为"停用"状态
                                                        },
                                                    );
                                                if (res.code === 0) {
                                                    message.success(
                                                        '停用成功!',
                                                    );
                                                    // 刷新[调度流程]表格
                                                    useScheduleProcessTable.refresh();
                                                    return Promise.resolve();
                                                } else {
                                                    return Promise.reject(
                                                        res.msg || '停用失败',
                                                    );
                                                }
                                            } catch (err) {
                                                return Promise.reject(err);
                                            }
                                        },
                                    });
                                }}
                            >
                                停用
                            </Button>
                        ) : null}
                        {record.processStatus === PROCESS_STATUS_DISABLE ? (
                            <Button
                                type="link"
                                size="small"
                                onClick={() => {
                                    modal.confirm({
                                        title: '启用',
                                        content: (
                                            <div>
                                                <div>
                                                    确定要启用该调度流程吗？
                                                </div>
                                                <div>
                                                    流程名称：
                                                    <span
                                                        style={{
                                                            fontWeight: 'bold',
                                                        }}
                                                    >
                                                        {record.processName}
                                                    </span>
                                                </div>
                                            </div>
                                        ),
                                        maskClosable: true,
                                        onOk: async () => {
                                            try {
                                                const res: any =
                                                    await apiCaller.post(
                                                        // @ts-ignore
                                                        '/xianfu/api-v2/ai-infra/schedule/process/edit',
                                                        {
                                                            // 复用编辑接口，要把所有详情值都带上
                                                            ...record,
                                                            processStatus:
                                                                PROCESS_STATUS_ENABLE, // 标记为"启用"状态
                                                        },
                                                    );
                                                if (res.code === 0) {
                                                    message.success(
                                                        '启用成功!',
                                                    );
                                                    // 刷新[调度流程]表格
                                                    useScheduleProcessTable.refresh();
                                                    return Promise.resolve();
                                                } else {
                                                    return Promise.reject(
                                                        res.msg || '启用失败',
                                                    );
                                                }
                                            } catch (err) {
                                                return Promise.reject(err);
                                            }
                                        },
                                    });
                                }}
                            >
                                启用
                            </Button>
                        ) : null}
                        <Button
                            type="link"
                            size="small"
                            onClick={() => {
                                const modalInstance = modal.confirm({
                                    title: '复制调度流程',
                                    width: 800,
                                    icon: null,
                                    maskClosable: true,
                                    content: (
                                        <ScheduleProcessForm
                                            initialValues={record}
                                            onCancel={() => {
                                                modalInstance.destroy();
                                            }}
                                            onOk={async formValues => {
                                                try {
                                                    const res: any =
                                                        await apiCaller.post(
                                                            // @ts-ignore
                                                            '/xianfu/api-v2/ai-infra/schedule/process/copy',
                                                            {
                                                                ...formValues,
                                                                processId:
                                                                    record.id, // 调度流程id
                                                            },
                                                        );
                                                    if (res.code === 0) {
                                                        message.success(
                                                            '复制成功!',
                                                        );
                                                        modalInstance.destroy();
                                                        // 刷新[调度流程]表格
                                                        useScheduleProcessTable.refresh();
                                                        return Promise.resolve();
                                                    } else {
                                                        return Promise.reject(
                                                            res.msg ||
                                                            '复制失败',
                                                        );
                                                    }
                                                } catch (err) {
                                                    return Promise.reject(err);
                                                }
                                            }}
                                        />
                                    ),
                                    footer: null,
                                });
                            }}
                        >
                            复制
                        </Button>
                    </Space>
                );
            },
        },
    ].filter(Boolean) as TableColumnType<ScheduleProcessTableRow>[];

    return (
        <div className="form-card-wrap">
            <div className="form-card-title">
                <Avatar className="index-icon" size="small">
                    3
                </Avatar>
                <span>配置调度流程</span>
            </div>
            <div className="form-card-content">
                {Array.isArray(
                    useScheduleProcessTable?.tableProps?.dataSource,
                ) &&
                    useScheduleProcessTable?.tableProps?.dataSource?.length >
                    0 && (
                        <AddScheduleBtn
                            onDone={() => {
                                // 刷新[调度流程]表格
                                useScheduleProcessTable.search.submit();
                            }}
                        />
                    )}
                <Table
                    {...useScheduleProcessTable.tableProps}
                    rowKey="id"
                    bordered
                    columns={columns}
                    pagination={{
                        ...useScheduleProcessTable.tableProps.pagination,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: total => `共 ${total} 条`,
                    }}
                    locale={{
                        emptyText: (
                            <div>
                                <img src={emptyImg} alt="空" width={100} />
                                <div
                                    style={{
                                        fontSize: '14px',
                                        color: '#222',
                                        fontWeight: 'bold',
                                    }}
                                >
                                    暂无数据
                                </div>
                                <div
                                    style={{
                                        fontSize: '14px',
                                        color: '#666',
                                        marginBottom: '10px',
                                    }}
                                >
                                    您还未创建任何调度流程
                                </div>
                                <AddScheduleBtn
                                    onDone={() => {
                                        // 刷新[调度流程]表格
                                        useScheduleProcessTable.search.submit();
                                    }}
                                />
                            </div>
                        ),
                    }}
                />
            </div>
        </div>
    );
};

export default ScheduleProcessCard;
