/**
 * 【新建调度流程】按钮
 */
import { useState } from 'react';
import { Button, Modal, App } from 'antd';
import ScheduleProcessForm from '../ScheduleProcessForm';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import useStore from '../../../store';

const AddScheduleBtn = props => {
    const {
        onDone, // 操作成功后回调
    } = props;
    const {
        detail, // 详情数据
        checkUnaddSceneData, // 检查是否有未保存的新建[场景]数据
    } = useStore.useContainer();
    const { message } = App.useApp();
    const [showModal, setShowModal] = useState(false);

    // 关闭弹窗事件
    const onCancel = () => {
        setShowModal(false);
    };

    // 【保存】按钮点击事件
    const onOk = async (formValues: any) => {
        try {
            const res: any = await apiCaller.post(
                // @ts-ignore
                '/xianfu/api-v2/ai-infra/schedule/process/create',
                {
                    ...formValues,
                    sceneId: detail.id, // 场景id
                },
            );
            if (res.code === 0) {
                message.success('创建成功!');
                setShowModal(false);
                onDone?.();
                return Promise.resolve();
            } else {
                return Promise.reject(res.msg || '创建失败');
            }
        } catch (err) {
            return Promise.reject(err);
        }
    };

    return (
        <>
            <Button
                style={{ marginBottom: 16 }}
                type="primary"
                onClick={async () => {
                    try {
                        // 检查是否有未保存的新建[场景]数据
                        await checkUnaddSceneData();
                        // 允许流程继续
                        setShowModal(true);
                    } catch (err) {
                        return Promise.reject(err);
                    }
                }}
            >
                新建调度流程
            </Button>
            <Modal
                title="新建调度流程"
                width={800}
                open={showModal}
                maskClosable
                footer={null}
                onCancel={onCancel}
            >
                <ScheduleProcessForm onCancel={onCancel} onOk={onOk} />
            </Modal>
        </>
    );
};

export default AddScheduleBtn;
