/**
 * 【调度流程基本信息】表单弹窗
 */
import { Form, Input, Select, Flex, Space, Button, App } from 'antd';
import useStore from '../../../store';
import { ScheduleProcessFormData, processStatusOptions } from '../../../utils';

interface Props {
    initialValues?: ScheduleProcessFormData; // 初始化数据
    onCancel?: () => void; // 【取消】按钮点击事件
    onOk?: (values: ScheduleProcessFormData) => void; // 【保存】按钮点击事件
}

const ScheduleProcessForm = (props: Props) => {
    const { initialValues, onCancel, onOk } = props;
    const {
        upstreamSystemsOptions, // 【发起方】可选项
    } = useStore.useContainer();
    const { message } = App.useApp();
    const [form] = Form.useForm();

    // 【保存】按钮点击事件
    const handleOk = () => {
        form.validateFields()
            .then(values => {
                onOk && onOk(values);
            })
            .catch(() => {
                message.error('表单校验失败，请检查红色报错信息');
            });
    };

    return (
        <Form
            form={form}
            initialValues={initialValues}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
        >
            <Form.Item
                name="processName"
                label="流程名称"
                rules={[{ required: true, message: '此项必填' }]}
            >
                <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
                name="upstreamSystems"
                label="发起方"
                rules={[{ required: true, message: '此项必填' }]}
            >
                <Select
                    options={upstreamSystemsOptions}
                    mode="multiple"
                    allowClear
                    placeholder="请选择"
                />
            </Form.Item>
            <Form.Item
                name="processDesc"
                label="流程描述"
                rules={[{ required: true, message: '此项必填' }]}
            >
                <Input.TextArea placeholder="请输入" />
            </Form.Item>
            <Form.Item label="状态">
                {processStatusOptions.find(
                    item => item.value === initialValues?.processStatus,
                )?.label || '停用'}
            </Form.Item>
            <Flex justify="end" gap={16}>
                <Space>
                    <Button onClick={onCancel}>取消</Button>
                    <Button type="primary" onClick={handleOk}>
                        保存
                    </Button>
                </Space>
            </Flex>
        </Form>
    );
};

export default (props: Props) => (
    <useStore.Provider>
        <ScheduleProcessForm {...props} />
    </useStore.Provider>
);
