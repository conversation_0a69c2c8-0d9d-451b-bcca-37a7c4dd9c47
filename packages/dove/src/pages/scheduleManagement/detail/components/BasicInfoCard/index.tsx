/**
 * 【填写基础信息】模块
 */

import { Avatar, Input, Select } from 'antd';
import MisSelector from '@src/components/MisSelector';
import FormItem from '@src/components/FormItem';
import useStore from '../../store';
import { enableOptions } from '../../utils';
import { DETAIL_PAGE_TYPE } from '@src/utils';

const BasicInfoCard = () => {
    const {
        pageType, // 页面类型
        detail, // 详情数据
        objectTypeOptions, // 【对象】可选项
    } = useStore.useContainer();

    return (
        <div className="form-card-wrap">
            <div className="form-card-title">
                <Avatar className="index-icon" size="small">
                    1
                </Avatar>
                <span>填写基础信息</span>
            </div>
            <div className="form-card-content">
                <FormItem
                    name="name"
                    label="场景名称"
                    rules={[{ required: true, message: '此项必填' }]}
                >
                    {({ value, onChange }) => {
                        if (
                            [
                                DETAIL_PAGE_TYPE.CREATE,
                                DETAIL_PAGE_TYPE.EDIT,
                            ].includes(pageType)
                        ) {
                            // "新建"或"编辑"页面
                            return (
                                <Input
                                    value={value}
                                    onChange={onChange}
                                    placeholder="请输入"
                                />
                            );
                        }
                        // "查看"页面
                        return value || '-';
                    }}
                </FormItem>
                <FormItem
                    name="objectType"
                    label="对象"
                    rules={[{ required: true, message: '此项必填' }]}
                >
                    {({ value, onChange }) => {
                        if (
                            [
                                DETAIL_PAGE_TYPE.CREATE,
                                DETAIL_PAGE_TYPE.EDIT,
                            ].includes(pageType)
                        ) {
                            // "新建"或"编辑"页面
                            return (
                                <Select
                                    value={value}
                                    onChange={onChange}
                                    options={objectTypeOptions}
                                    allowClear
                                    disabled={!!detail?.id}
                                    placeholder="请选择"
                                />
                            );
                        }
                        // "查看"页面
                        return (
                            objectTypeOptions.find(item => item.value === value)
                                ?.label || '-'
                        );
                    }}
                </FormItem>
                <FormItem
                    name="enable"
                    label="状态"
                    rules={[{ required: true, message: '此项必填' }]}
                >
                    {({ value, onChange }) => {
                        if (
                            [
                                DETAIL_PAGE_TYPE.CREATE,
                                DETAIL_PAGE_TYPE.EDIT,
                            ].includes(pageType)
                        ) {
                            // "新建"或"编辑"页面
                            return (
                                <Select
                                    value={value}
                                    onChange={onChange}
                                    options={enableOptions}
                                    allowClear
                                    placeholder="请选择"
                                />
                            );
                        }
                        // "查看"页面
                        return (
                            enableOptions.find(item => item.value === value)
                                ?.label || '-'
                        );
                    }}
                </FormItem>
                <FormItem
                    name="managers"
                    label="管理员"
                    required
                    rules={[
                        {
                            validator: (rule, value) => {
                                if (!Array.isArray(value) || value.length < 1) {
                                    return Promise.reject(
                                        new Error('此项必填'),
                                    );
                                }
                                if (value.length > 3) {
                                    return Promise.reject(
                                        new Error('最多可选3人'),
                                    );
                                }
                                return Promise.resolve();
                            },
                        },
                    ]}
                >
                    {({ value, onChange }) => {
                        if (
                            [
                                DETAIL_PAGE_TYPE.CREATE,
                                DETAIL_PAGE_TYPE.EDIT,
                            ].includes(pageType)
                        ) {
                            // "新建"或"编辑"页面
                            return (
                                <MisSelector
                                    value={value}
                                    onChange={onChange}
                                    mode="multiple"
                                    mis
                                    allowClear
                                    labelInValue
                                    placeholder="请输入姓名或mis号，下拉选择，最多可选3位"
                                />
                            );
                        }
                        // "查看"页面
                        return Array.isArray(value)
                            ? value.map(item => item.label).join('、')
                            : '-';
                    }}
                </FormItem>
                <FormItem
                    name="description"
                    label="场景描述"
                    rules={[{ required: true, message: '此项必填' }]}
                >
                    {({ value, onChange }) => {
                        if (
                            [
                                DETAIL_PAGE_TYPE.CREATE,
                                DETAIL_PAGE_TYPE.EDIT,
                            ].includes(pageType)
                        ) {
                            // "新建"或"编辑"页面
                            return (
                                <Input.TextArea
                                    value={value}
                                    onChange={onChange}
                                    placeholder="请输入"
                                />
                            );
                        }
                        // "查看"页面
                        return value || '-';
                    }}
                </FormItem>
            </div>
        </div>
    );
};

export default BasicInfoCard;
