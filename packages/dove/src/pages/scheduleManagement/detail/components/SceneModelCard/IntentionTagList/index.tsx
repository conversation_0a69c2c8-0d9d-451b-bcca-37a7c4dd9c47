/**
 * 【意向标签】表单项
 */
import { Form, Input, Row, Col } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import useStore from '../../../store';
import FormItem from '@src/components/FormItem';
import removeImg from '@src/assets/img/remove.png';
import { DETAIL_PAGE_TYPE } from '@src/utils';
import './index.scss';

const MIN_LEN = 1; // 最小数量限制
const MAX_LEN = 100; // 最大数量限制

const IntentionTagList = () => {
    const {
        pageType, // 页面类型
    } = useStore.useContainer();

    if ([DETAIL_PAGE_TYPE.CREATE, DETAIL_PAGE_TYPE.EDIT].includes(pageType)) {
        // "新建"或"编辑"页面
        return (
            <Form.Item
                name="intentionTagList"
                label="意向标签"
                required
                rules={[
                    {
                        validator: (rule, value) => {
                            if (!Array.isArray(value) || value.length === 0) {
                                return Promise.reject(new Error('此项必填'));
                            }
                            if (value.length < MIN_LEN) {
                                return Promise.reject(
                                    new Error(`最少有${MIN_LEN}条数据`),
                                );
                            }
                            if (value.length > MAX_LEN) {
                                return Promise.reject(
                                    new Error(`最多有${MAX_LEN}条数据`),
                                );
                            }
                            return Promise.resolve();
                        },
                    },
                ]}
            >
                <Form.List name="intentionTagList">
                    {(fields, { add, remove }) => {
                        return (
                            <div className="intention-tag-list-wrap">
                                {fields.map(
                                    ({ name, key, ...restField }, index) => {
                                        return (
                                            <Row key={key} gutter={16}>
                                                <Col span={2}>
                                                    <Form.Item>
                                                        <span
                                                            style={{
                                                                fontWeight:
                                                                    'bold',
                                                            }}
                                                        >
                                                            标签{index + 1}
                                                        </span>
                                                    </Form.Item>
                                                </Col>
                                                <Col span={10}>
                                                    <Form.Item
                                                        {...restField}
                                                        name={[name, 'name']}
                                                        label="标签符号"
                                                        required
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message:
                                                                    '此项必填',
                                                            },
                                                        ]}
                                                    >
                                                        <Input placeholder="请输入" />
                                                    </Form.Item>
                                                </Col>
                                                <Col span={10}>
                                                    <Form.Item
                                                        {...restField}
                                                        name={[
                                                            name,
                                                            'description',
                                                        ]}
                                                        label="标签名称"
                                                        required
                                                        rules={[
                                                            {
                                                                required: true,
                                                                message:
                                                                    '此项必填',
                                                            },
                                                        ]}
                                                    >
                                                        <Input placeholder="请输入" />
                                                    </Form.Item>
                                                </Col>
                                                <Col span={2}>
                                                    <Form.Item>
                                                        {fields.length >
                                                        MIN_LEN ? (
                                                            <img
                                                                style={{
                                                                    cursor: 'pointer',
                                                                    width: '20px',
                                                                    height: '20px',
                                                                }}
                                                                src={removeImg}
                                                                onClick={() => {
                                                                    remove(
                                                                        name,
                                                                    );
                                                                }}
                                                            />
                                                        ) : null}
                                                    </Form.Item>
                                                </Col>
                                            </Row>
                                        );
                                    },
                                )}
                                <Row gutter={16}>
                                    <Col span={10} offset={2}>
                                        {fields.length < MAX_LEN ? (
                                            <span
                                                style={{
                                                    cursor: 'pointer',
                                                }}
                                                onClick={() => {
                                                    add();
                                                }}
                                            >
                                                <PlusOutlined
                                                    style={{
                                                        marginRight: '4px',
                                                    }}
                                                />
                                                <span>添加标签</span>
                                            </span>
                                        ) : null}
                                    </Col>
                                </Row>
                            </div>
                        );
                    }}
                </Form.List>
            </Form.Item>
        );
    }
    // "查看"页面
    return (
        <FormItem name="intentionTagList" label="意向标签">
            {({ value }) => {
                return (
                    <div className="intention-tag-list-wrap">
                        {Array.isArray(value)
                            ? value.map((item, index) => {
                                  return (
                                      <div>
                                          <span
                                              style={{
                                                  fontWeight: 'bold',
                                                  marginRight: '20px',
                                              }}
                                          >
                                              标签{index + 1}
                                          </span>
                                          <span>{item.name}</span>
                                          <span>：</span>
                                          <span>{item.description}</span>
                                      </div>
                                  );
                              })
                            : null}
                    </div>
                );
            }}
        </FormItem>
    );
};

export default IntentionTagList;
