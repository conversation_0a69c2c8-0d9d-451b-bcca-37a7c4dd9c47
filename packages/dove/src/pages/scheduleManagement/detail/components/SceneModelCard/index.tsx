/**
 * 【关联场景模型】模块
 */
import { Avatar } from 'antd';
import IntentionTagList from './IntentionTagList';
import FormItem from '@src/components/FormItem';
import SelectAgent from '@src/components/SelectAgent';
import useStore from '../../store';
import { DETAIL_PAGE_TYPE } from '@src/utils';

const SceneModelCard = () => {
    const {
        pageType, // 页面类型
        agentOptions, // 【场景agent】可选项
    } = useStore.useContainer();

    return (
        <div className="form-card-wrap">
            <div className="form-card-title">
                <Avatar className="index-icon" size="small">
                    2
                </Avatar>
                <span>关联场景模型</span>
            </div>
            <div className="form-card-content">
                <FormItem
                    name="agentId"
                    label="场景agent"
                    rules={[{ required: true, message: '此项必填' }]}
                >
                    {({ value, onChange }) => {
                        if (
                            [
                                DETAIL_PAGE_TYPE.CREATE,
                                DETAIL_PAGE_TYPE.EDIT,
                            ].includes(pageType)
                        ) {
                            // "新建"或"编辑"页面
                            return (
                                <SelectAgent
                                    value={value}
                                    onChange={onChange}
                                    allowClear
                                    mode="multiple"
                                    showSearch
                                    isNewAgentUrl={true} // 使用新接口
                                />
                            );
                        }
                        // "查看"页面
                        return Array.isArray(value)
                            ? value
                                .map(
                                    item =>
                                        agentOptions.find(
                                            option => option.value === item,
                                        )?.label,
                                )
                                .join('、')
                            : '-';
                    }}
                </FormItem>
                <IntentionTagList />
            </div>
        </div>
    );
};

export default SceneModelCard;
