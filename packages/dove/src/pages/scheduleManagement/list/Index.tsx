import { Table, Tag, Space, Button, App } from 'antd';
import type { TableColumnType } from 'antd';
import { useAntdTable } from 'ahooks';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { SceneData, enableOptions, ENABLE_TRUE, ENABLE_FALSE } from './utils';

const Index = () => {
    const { modal, message } = App.useApp();

    // crud查询hook
    const {
        // search, // crud 搜索方法（触发form.submit()）
        refresh, // 刷新 crud（不触发form.submit()，使用上一次搜索参数查询）
        tableProps, // Table 参数集
    } = useAntdTable(
        async ({ current: pageNum, pageSize }) => {
            try {
                const res: any = await apiCaller.post(
                    // @ts-ignore
                    '/xianfu/api-v2/ai-infra/schedule/scene/searchScenePage',
                    {
                        pageSize,
                        pageNum,
                    },
                );
                if (res.code !== 0) {
                    return {
                        list: [],
                        total: 0,
                    };
                }
                return {
                    list: res?.data?.data || [],
                    total: res?.data?.total || 0,
                };
            } catch (error) {
                console.error('获取数据失败:', error);
                return {
                    list: [],
                    total: 0,
                };
            }
        },
        {
            defaultPageSize: 20,
            // form,
            // manual: true, // 初始化时不查询
        },
    );

    // 表格列配置
    const columns: TableColumnType<SceneData>[] = [
        {
            dataIndex: 'id',
            title: '场景ID',
            render: text => text || '-',
        },
        {
            dataIndex: 'name',
            title: '场景名称',
            render: text => text || '-',
        },
        {
            dataIndex: 'creator',
            title: '创建人',
            render: text =>
                text?.misId ? `${text?.name}(${text?.misId})` : '-',
        },
        {
            dataIndex: 'enable',
            title: '状态',
            render: text => {
                const option = enableOptions.find(item => item.value === text);
                return option ? (
                    <Tag color={option.color}>{option.label}</Tag>
                ) : (
                    '-'
                );
            },
        },
        {
            dataIndex: 'operate',
            title: '操作',
            render: (text, record) => {
                return (
                    <Space wrap>
                        {record.enable === ENABLE_TRUE ? (
                            <Button
                                type="link"
                                onClick={() => {
                                    modal.confirm({
                                        title: '停用',
                                        content: (
                                            <div>
                                                <div>确定要停用该场景吗？</div>
                                                <div>场景名称：</div>
                                                <div
                                                    style={{
                                                        fontWeight: 'bold',
                                                    }}
                                                >
                                                    {record.name}
                                                </div>
                                            </div>
                                        ),
                                        maskClosable: true,
                                        onOk: async () => {
                                            try {
                                                const res: any =
                                                    await apiCaller.post(
                                                        // @ts-ignore
                                                        '/xianfu/api-v2/ai-infra/schedule/scene/updateScene',
                                                        {
                                                            // 这个接口实际上是更新接口，要传所有值
                                                            ...record,
                                                            enable: ENABLE_FALSE, // 标记为"停用"
                                                        },
                                                    );
                                                if (res.code === 0) {
                                                    message.success(
                                                        '停用成功!',
                                                    );
                                                    // 刷新表格
                                                    refresh();
                                                    return Promise.resolve();
                                                } else {
                                                    return Promise.reject(
                                                        res.msg || '停用失败',
                                                    );
                                                }
                                            } catch (err) {
                                                return Promise.reject(err);
                                            }
                                        },
                                    });
                                }}
                            >
                                停用
                            </Button>
                        ) : null}
                        {record.enable === ENABLE_FALSE ? (
                            <Button
                                type="link"
                                onClick={() => {
                                    modal.confirm({
                                        title: '停用',
                                        content: (
                                            <div>
                                                <div>确定要启用该场景吗？</div>
                                                <div>场景名称：</div>
                                                <div
                                                    style={{
                                                        fontWeight: 'bold',
                                                    }}
                                                >
                                                    {record.name}
                                                </div>
                                            </div>
                                        ),
                                        maskClosable: true,
                                        onOk: async () => {
                                            try {
                                                const res: any =
                                                    await apiCaller.post(
                                                        // @ts-ignore
                                                        '/xianfu/api-v2/ai-infra/schedule/scene/updateScene',
                                                        {
                                                            // 这个接口实际上是更新接口，要传所有值
                                                            ...record,
                                                            enable: ENABLE_TRUE, // 标记为"启用"
                                                        },
                                                    );
                                                if (res.code === 0) {
                                                    message.success(
                                                        '启用成功!',
                                                    );
                                                    // 刷新表格
                                                    refresh();
                                                    return Promise.resolve();
                                                } else {
                                                    return Promise.reject(
                                                        res.msg || '启用失败',
                                                    );
                                                }
                                            } catch (err) {
                                                return Promise.reject(err);
                                            }
                                        },
                                    });
                                }}
                            >
                                启用
                            </Button>
                        ) : null}
                        <Button
                            type="link"
                            onClick={() => {
                                window.open(
                                    `${
                                        import.meta.env.DEV ? '' : '/page/dove'
                                    }/scheduleManagement/detail?sceneId=${
                                        record.id
                                    }`,
                                    '_blank',
                                );
                            }}
                        >
                            查看详情
                        </Button>
                        <Button
                            type="link"
                            onClick={() => {
                                window.open(
                                    `${
                                        import.meta.env.DEV ? '' : '/page/dove'
                                    }/scheduleManagement/detail?edit=1&sceneId=${
                                        record.id
                                    }`,
                                    '_blank',
                                );
                            }}
                        >
                            编辑
                        </Button>
                    </Space>
                );
            },
        },
    ];

    return (
        <div>
            <Button
                style={{
                    marginBottom: '10px',
                }}
                type="primary"
                onClick={() => {
                    window.open(
                        `${
                            import.meta.env.DEV ? '' : '/page/dove'
                        }/scheduleManagement/detail?edit=1`,
                        '_blank',
                    );
                }}
            >
                新建调度场景
            </Button>
            <Table
                {...tableProps}
                rowKey="id"
                bordered
                columns={columns}
                pagination={{
                    ...tableProps.pagination,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: total => `共 ${total} 条`,
                }}
            />
        </div>
    );
};

export default Index;
