.loading-box {
    z-index: 1000 !important;
    position: fixed !important;
}

.new_bellwether-sidebar_parent-menu__polling {
    float: right;
    margin-right: 10px;
    border: 1px solid red;
    border-radius: 30%;
    background-color: #ab2b2b;
    font-size: 12px;
    padding: 2px 5px;
    color: #fff;
    line-height: 14px;
}

.container-fluid {
    width: auto !important;
}

.dove {
    position: initial;
}
.dove-web {
    display: flex;
    min-height: 600px;
    min-width: 800px;
    height: calc(100vh - 130px);
    overflow-y: hidden;
    overflow-x: auto;
    flex-direction: column;
    background-color: #f3f3f3;

    .dove-web-main {
        flex: auto;
        display: flex;
        overflow: hidden;
        .dove-web-session-list {
            width: 270px;
            height: 100%;
            border-right: 1px solid rgba(0, 0, 0, 0.05);
            overflow-y: auto;
        }
        .dove-web-right-content {
            flex: auto;
            position: relative;
        }
        .dove-web-tips {
            padding: 20px;
            color: rgba(0, 0, 0, 0.38);
        }

        .dove-web-button {
            margin: auto;
            width: 33%;
            box-sizing: content-box;
        }
    }
}

.ant-spin-nested-loading {
    height: 100%;
    .ant-spin-container {
        height: 100%;
    }
}

.page-content {
    height: 100%;
    padding-top: 70px;
    box-sizing: border-box;
    margin-top: auto !important;
}

.web-demo-bubble-panel {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    .web-demo-bubble-bubblelist {
        flex: auto;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        height: 100%;
        overflow: hidden;
    }

    .web-demo-sender {
        flex: 0 0 250px;
    }
}
