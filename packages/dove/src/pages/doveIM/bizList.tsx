import { apiCaller } from '@mfe/cc-api-caller-pc';
import { Avatar, List, Row, Col, Typography } from 'antd';
import { useEffect, useState } from 'react';
import VirtualList from 'rc-virtual-list';

const BizList = ({ getMessages }) => {
    const [data, setData] = useState<any[]>([]);
    const [hasMore, setHasMore] = useState(true);
    const appendData = async (lastId?) => {
        // @ts-ignore
        const res: any = await apiCaller.get('/dove/get/biz/list', {
            status: '10,20,30',
            lastId,
            pageSize: 20,
        });
        if (res?.code === 0) {
            !res?.data?.orderInfos?.length && setHasMore(false);
            setData([...data, ...((res?.data?.orderInfos as any[]) || [])]);
            return;
        }
    };

    const [activeId, setActiveId] = useState<number>();
    const bizId = Number(
        new URL(window.location.href).searchParams.get('bizId'),
    );
    useEffect(() => {
        appendData();
    }, []);
    useEffect(() => {
        if (!data?.length) {
            return;
        }
        const target = bizId
            ? data.find(item => item.wmBizId === bizId)
            : data[0];
        if (!target) {
            return;
        }
        setActiveId(target.wmBizId);
        getMessages(target);
    }, [data]);
    const onItemClick = item => {
        setActiveId(item.wmBizId);
        getMessages(item);
    };
    const onScroll = (e: any) => {
        if (
            hasMore &&
            Math.abs(
                e.currentTarget.scrollHeight -
                    e.currentTarget.scrollTop -
                    document.getElementsByClassName(
                        'ant-tabs-content-holder',
                    )?.[0]?.scrollHeight,
            ) <= 1
        ) {
            appendData(data[data.length - 1]?.wmBizId);
        }
    };
    return (
        <List style={{ width: 270 }}>
            <VirtualList
                data={data || []}
                height={
                    document.getElementsByClassName(
                        'ant-tabs-content-holder',
                    )?.[0]?.scrollHeight || 700
                }
                itemHeight={47}
                itemKey={item => item.wmBizId}
                onScroll={onScroll}
            >
                {(item: any) => {
                    return (
                        <Row
                            key={item.wmBizId}
                            gutter={2}
                            style={{
                                cursor: 'pointer',
                                paddingBottom: 15,
                                paddingTop: 15,
                                paddingRight: 16,
                                ...(activeId === item.wmBizId
                                    ? { background: 'rgba(0,0,0,0.1)' }
                                    : {}),
                            }}
                            onClick={() => onItemClick(item)}
                        >
                            <Col>
                                <Avatar src={item.avatar} size={40} />
                            </Col>
                            <Col>
                                <Row>
                                    <Typography.Text
                                        style={{ width: 186 }}
                                        ellipsis={{ tooltip: item.wmName }}
                                    >
                                        {item.wmName}
                                    </Typography.Text>
                                </Row>
                                <Row>
                                    <Typography.Text
                                        style={{
                                            color: '#999',
                                            fontSize: 12,
                                            width: 186,
                                        }}
                                        ellipsis={{
                                            tooltip: item.consultDescription,
                                        }}
                                    >
                                        {item.consultDescription}
                                    </Typography.Text>
                                </Row>
                            </Col>
                        </Row>
                    );
                }}
            </VirtualList>
        </List>
    );
};
export default BizList;
