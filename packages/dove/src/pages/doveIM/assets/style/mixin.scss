// mixins for clearfix
// ------------------------
@mixin clearfix() {
    zoom: 1;
&:before,
&:after {
        content: " ";
        display: table;
    }
&:after {
        clear: both;
        visibility: hidden;
        font-size: 0;
        height: 0;
    }
}

//单行文字截断
@mixin textoverflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

//多行
@mixin multextoverflow($line : 2) {
  display: -webkit-box;
  display: -moz-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  -webkit-line-clamp: $line;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

/* mixin for multiline */
@mixin multiLineEllipsis($lineHeight: 1em, $lineCount: 1, $bgColor: white){
  overflow: hidden;
  position: relative;
  line-height: $lineHeight;
  max-height: $lineHeight * $lineCount;
  text-align: justify;
  //margin-right: -1em;
  padding-right: 1em;
  &:before {
    content: '...';
    position: absolute;
    right: 0;
    bottom: 0;
  }
  &:after {
    content: '';
    position: absolute;
    right: 0;
    width: 1em;
    height: 1em;
    margin-top: 0.2em;
    background: $bgColor;
  }
}

@mixin user-select($select) {
  user-select: $select;
}

// Transitions

@mixin transition($transition) {
  -webkit-transition: $transition;
  -o-transition: $transition;
  transition: $transition;
}

// Sizing shortcuts

@mixin size($width, $height) {
  width: $width;
  height: $height;
}

@mixin square($size) {
  @include size($size, $size);
}

@mixin opacity($opacity) {
  opacity: $opacity;
  // IE8 filter
  $opacity-ie: ($opacity * 100);
  filter: "alpha(opacity=#{$opacity-ie})";
}

// Transformations
@mixin scale($ratio) {
  -webkit-transform: scale($ratio);
  -ms-transform: scale($ratio); // IE9 only
  -o-transform: scale($ratio);
  transform: scale($ratio);
}
@mixin scale($ratioX, $ratioY) {
  -webkit-transform: scale($ratioX, $ratioY);
  -ms-transform: scale($ratioX, $ratioY); // IE9 only
  -o-transform: scale($ratioX, $ratioY);
  transform: scale($ratioX, $ratioY);
}
@mixin scaleX($ratio) {
  -webkit-transform: scaleX($ratio);
  -ms-transform: scaleX($ratio); // IE9 only
  -o-transform: scaleX($ratio);
  transform: scaleX($ratio);
}
@mixin scaleY($ratio) {
  -webkit-transform: scaleY($ratio);
  -ms-transform: scaleY($ratio); // IE9 only
  -o-transform: scaleY($ratio);
  transform: scaleY($ratio);
}

@mixin dx-font {
  //大象的字体，为了表情对齐
  // font-family:Helvetica Neue,Helvetica,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Arial,PingFang SC,Heiti SC,Hiragino Sans GB,Microsoft YaHei,\\5FAE\8F6F\96C5\9ED1,sans-serif;
  font-family:Helvetica Neue,Helvetica,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Arial,PingFang SC,Heiti SC,Hiragino Sans GB,Microsoft YaHei,sans-serif;
}