//通用基础颜色
$color-darker               : rgba(0,0,0,.87);/*#333333*/
$color-dark                 : rgba(0,0,0,.6);/*#666666*/
$color-base                 : rgba(0,0,0,.38);/*#999999*/
$color-medium               : rgba(0,0,0,.24);/*#CCCCCC*/
$color-light                : rgba(0,0,0,.1);/*#E5E5E5*/
$color-light-1              : rgba(0,0,0,.05);
$color-lighter              : #F8F8F8;
$color-lightest             : #FFFFFF;

//通用辅助颜色
$supplement                 : #FF9801;
$danger                     : #FF5D4A;
$success                    : #5ABB3C;
$name-other                 : #596E8F;
$name-own                   : #CC841B;
$color-pink                 : #FF8A8A;//女性
$color-blue                 : #6B9AE0;//男性
$security-level-c4          : #FF5F57;// 密级C-4的icon颜色

//存放一些宽度
$main-title-height      : 60px;//顶部70px
$nav-height             : 50px; //顶部导航高度
$left-width             : 270px;//内容区域左边列表宽度

//z-index.scss
$min-zindex             : 1;
$medium-zindex          : 10;
$large-zindex           : 100;
$max-zindex             : 1000;

//普通元素的padding
$ctn-padding-xmin       : 1px;
$ctn-padding-min        : 2px;
$ctn-padding-normal     : 5px;
$ctn-padding-large      : 10px;
$ctn-padding-xlarge     : 15px;
$ctn-padding-xxlarge    : 16px;

//profile 宽高度

//thumb 头像大小(头像的尺寸：80，72，40，36，28，24，16.)
$thumb-base             : 28px;
$thumb-small            : 24px;
$thumb-xsmall           : 16px;
$thumb-setting          : 32px;
$thumb-large            : 36px;
$thumb-xlarge           : 40px;
$thumb-xxlarge          : 72px;
$thumb-xxxlarge         : 80px;


//原有的头像尺寸(需要废弃)
//$thumb
//$thumb-min              : 12px;
//$thumb-xxxxlarge        : $thumb-min * 6;
//$thumb-big              : 80px;



//这里存字体相关的
$font-family            : "Helvetica Neue",Helvetica,"Apple Color Emoji",'Segoe UI Emoji', 'Segoe UI Symbol',Arial,"PingFang SC","Heiti SC", "Hiragino Sans GB","Microsoft YaHei","微软雅黑",sans-serif;
$font-size-small        : 12px; //针对h6,说明文字
$font-size-lSmall       : 13px; //针对h6,说明文字
$font-size-base         : 14px; //正文,链接,h5
$font-size-large        : 16px; //h4
$font-size-xlarge       : 18px; //h3
$font-size-xxlarge      : 20px; //h2
$font-size-xxxlarge     : 24px; //h1
$line-height-base       : 1.5;
$line-height-computed   : floor(($font-size-base * $line-height-base));

$border-radius-base     : 2px;
$border-radius-normal   : 4px;
$border-radius-large    : 5px;
$border-radius-xlarge   : 10px;
$border-radius-xxlarge   : 15px;

$security-size-small    : 10px;
$security-size-middle   : 12px;
$security-size-big      : 14px;



// ICONFONT
$iconfont-css-prefix    : dxicon;
$iconfont-css-prefix-yp    : dxypicon;//云盘iconfont



$icon-url               : "/public/fonts/font_66138_8przrbl2ivs"; //经常会改变,以后会换成本地的地址
$icon-url-yp            : "/public/yunpan-fonts/font_567058_d5wd6ylot9c3ow29";//云盘icon引入地址


// Animation
$ease-out            : cubic-bezier(0.215, 0.61, 0.355, 1);
$ease-in             : cubic-bezier(0.55, 0.055, 0.675, 0.19);
$ease-in-out         : cubic-bezier(0.645, 0.045, 0.355, 1);
$ease-out-back       : cubic-bezier(0.12, 0.4, 0.29, 1.46);
$ease-in-back        : cubic-bezier(0.71, -0.46, 0.88, 0.6);
$ease-in-out-back    : cubic-bezier(0.71, -0.46, 0.29, 1.46);
$ease-out-circ       : cubic-bezier(0.08, 0.82, 0.17, 1);
$ease-in-circ        : cubic-bezier(0.6, 0.04, 0.98, 0.34);
$ease-in-out-circ    : cubic-bezier(0.78, 0.14, 0.15, 0.86);
$ease-out-quint      : cubic-bezier(0.23, 1, 0.32, 1);
$ease-in-quint       : cubic-bezier(0.755, 0.05, 0.855, 0.06);
$ease-in-out-quint   : cubic-bezier(0.86, 0, 0.07, 1);


// 按钮边框颜色,理论上应该是背景色加深一点就可以了

//$border-color-base      : #d9d9d9;        // base border outline a component
//$box-shadow-base        : 0 0 4px rgba(0, 0, 0, 0.17);
//$border-color-split     : #e9e9e9;        // split border inside a component
$cursor-disabled        : not-allowed;
$btn-font-weight        : normal;





$btn-default-color      : $color-darker;
$btn-default-bg         : $color-lighter;
$btn-default-border     : $color-light;






$btn-padding-base       : 8px 31px;
$btn-border-radius-base : 4px;

$btn-font-size-lg       : 14px;
$btn-padding-lg         : 4px 11px 5px 11px;
$btn-border-radius-lg   : $btn-border-radius-base;

$btn-padding-sm         : 1px 7px;
$btn-border-radius-sm   : $btn-border-radius-base;

$btn-circle-size        : 28px;
$btn-circle-size-lg     : 32px;
$btn-circle-size-sm     : 22px;

$msg-title-height: 60px;
$msg-border-color: $color-light;






//气泡页部分宽度定义
$msg-medium-min-width: 630px;
$msg-medium-max-width: 1000px;
$msg-medium-width : 860px;
$msg-medium-left : 0px;
$msg-medium-right : 0px;
$msg-medium-top: 0px;
$msg-medium-bottom: 0px;
$slidepanel-width: 470px;

//文件模块所需样式定义
$person-file-basic-background-left: #FBFBFB;
$person-file-basic-background: #FFFFFF;
$person-file-basic-background-str: F3F3F3;
$person-file-basic-border: #DDDDDD;
$person-file-basic-border-light: #EEEEEE;







$border-color               : rgba(0,0,0,0.1);
$chat-border-color          : #DDDDDD;
$chat-bg-color              : #F3F3F3;//右侧聊天窗口的背景色 rgba(255,255,255, 0.75)
$session-bg-color           : #FBFBFB;//右侧聊天窗口的背景色 rgba(255,255,255, 0.65)
$session-bg-color_str       : FBFBFB;//右侧聊天窗口的背景色 rgba(255,255,255, 0.65)
$right-bg-color             : #F3F3F3;
$right-bg-color_str         : F3F3F3;
$right-chat-bg-color        : transparent;
$search-ipt-bg-color        : rgba(255,255,255, 0.5);


$primary                    : #3974CC;

//主色
$navigation-bg              : rgba(43,87,153,1);
$navigation-hover-bg        : rgba($primary, 0.1);
$navigation-active-bg       : rgba(16,139,251,0.15);
$navigation-active-border   : $primary;
$navigation-color           : rgba($color-lightest, 0.5);//导航栏icon颜色

$divider-color              : rgba($color-lightest,0.1);


$hover-color: rgba($primary, 0.1);//hover时候的颜色
$active-color: rgba($primary, 0.15);//激活时候的颜色


//辅助颜色
$toast-common               : #F3F9FF;
$toast-abnormal             : #FFFAF2;
$toast-error                : #FFEFEF;
$toast-success              : #F2FAF0;
$toast-warn                 : #FFF8EC;
// Toast的字体颜色
$toast-word-error           : #F53530;
$toast-word-success         : #5ABB3C;
$toast-word-warn            : #FF7F00;



$color-grey                 : #F4F4F4;//搜索框
$color-you                  : #FFFFFF;// 原来的#CAE5F9;//
$color-me                   : #D0E7FF;//原来的#E5E5E5;

$color-you_str              : 'FFFFFF';
$color-me_str               : 'D0E7FF';

//输入框背景色
$input-background-color     : #FBFBFB;



$bg-image                   : $chat-bg-color;//背景图片

$main-bg-style              : url($chat-bg-color) left bottom/cover no-repeat at-2x;
$group-file-bg-image        : '';
$group-file-bg-group-path   : rgba(0,0,0,0.02);
$group-file-bg-group-path-border: 1px solid rgba(130, 130, 130, 0.1);
$group-file-search-border: 1px solid rgba(0, 0, 0, 0.1);
$group-file-header-tab-background-color: transparent;


$btn-bg-base            : $primary;
$border-color-base      : darken($primary, 5%);
$btn-primary-color      : $color-lightest;
$btn-primary-bg         : $primary;
$btn-primary-hover-bg   : #40A2FB;
$btn-primary-active-bg  : #1083EE;

$btn-ghost-color        : #FFFFFF;
$btn-ghost-bg           : $supplement;
$btn-ghost-border       : darken($supplement, 5%);
$btn-ghost-hover-bg     : #FFAC33;
$btn-ghost-active-bg    : #F29000;

$btn-disable-color      : #ccc;
$btn-disable-bg         : #F3F3F3;
$btn-disable-border     : $border-color-base;

$btn-danger-color        : #FFFFFF;
$btn-danger-bg           : $danger;
$btn-danger-border       : $danger;
$btn-danger-hover-bg     : #FF7D6E;
$btn-danger-active-bg    : #FF5D4A;



