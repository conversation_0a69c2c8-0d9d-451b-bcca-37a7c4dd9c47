import { MethodType, apiCaller } from '@mfe/cc-api-caller-pc';

const LOCAL_STORAGE_KEY = 'doveIM-getWmPoiIdByPubId';
export const getWmPoiIdByPubId = async pubId => {
    const ls = localStorage.getItem(LOCAL_STORAGE_KEY);
    let lsObj;
    try {
        lsObj = ls ? JSON.parse(ls) : {};
    } catch (e) {
        lsObj = {};
    }
    if (lsObj[pubId]) {
        return lsObj[pubId];
    }
    const res = await apiCaller.send(
        '/impc/poi/r/getWmPoiIdByPubId',
        { pubId },
        { method: MethodType.GET },
    );

    if (res.code !== 0) {
        return null;
    }

    const { poiId, extension } = res.data || {};
    lsObj[pubId] = { poiId, extension };
    localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify(lsObj));
    return { poiId, extension };
};
