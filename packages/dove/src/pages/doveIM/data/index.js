import { getEnvironment } from '@mfe/cc-ocrm-utils';
import MTDX from '@mtfe/xm-web-sdk/lib/dxSDK.src.extra.js';
import { CHANNEL_ID_LIST } from '../constants';

export const DX = MTDX.default;

const APPID = 35;
const DEVICE_TYPE = 2;

const env =
    getEnvironment() === 'test' ||
    location.host.includes('localhost') ||
    location.host.includes('127.0.0.1')
        ? 'test'
        : 'online';

export const {
    mtdx, // SDK core包对象，可以通过此对象来访问核心包方法，例如设置HTTP等
    EVENT_NAME, // 事件常量名
    eventEmitter, //事件分发器
    configManager, //用户信息相关
    sessionManager, //会话相关
    messageManager, //消息相关
    vcardManager, // 名片管理
} = new MTDX({
    appId: APPID,
    deviceType: DEVICE_TYPE,
    multiChannelIds: CHANNEL_ID_LIST,
    deviceId: MTDX.getUuid(),
    env,
    av: '1.0.0',
    shouldGetSessionFromDB: true,
    localSessionNumber: 1000,
    useChatRoomURL: false,
});

sessionManager.setInitialTimeRange({
    IMTimeRange: 30 * 24 * 60 * 60 * 1000,
    PubTimeRange: 30 * 24 * 60 * 60 * 1000,
});
