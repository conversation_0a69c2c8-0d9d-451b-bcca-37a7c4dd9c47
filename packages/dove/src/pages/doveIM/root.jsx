/**
 * @description [ doveIM ]
 * <AUTHOR> <EMAIL> ]
 * @date        [ 2020-08-03 ]
 */

import React, { Fragment, useEffect, useState } from 'react';
import { connect } from 'react-redux';
import { MethodType, apiCaller } from '@mfe/cc-api-caller-pc';

import { infoInit, login } from './redux/actions/info';
import { sessionInit, selectSession } from './redux/actions/session';
import { messageInit } from './redux/actions/message';
import { draftInit } from './redux/actions/draft';
import Session from './components/session';
import BubbleList from './components/bubble/bubbleList';
import BubbleListWithRedux from './components/bubble';
import ThemeModal from './components/theme/ThemeModal';
import './root.scss';
import { getNoticeAuth } from './components/utils/notice';
import LoginWrapper from './components/LoginWrapper';
import ThemeNotice from './components/theme/ThemeNotice';
import SenderWrapper from './components/SenderWrapper';
import BizList from './bizList';
import { Tabs, Spin, Button } from 'antd';
import Sender from './components/sender/wrapper';
import { eventEmitter, EVENT_NAME, configManager } from './data';

import { sessionManager, messageManager, mtdx, vcardManager } from './data';
import {
    CONSULT_CHANNEL_ID,
    GROUP_CHANNEL_ID,
    PUB_CHANNEL_ID,
    SessionType,
} from './constants';

const mapStateToProps = state => ({
    uid: state.info.get('uid'),
    currentUid: state.session.get('currentUid'),
    loginStatus: state.info.get('loginStatus'),
});

const mapDispatchToProps = {
    login,
    infoInit,
    sessionInit,
    messageInit,
    selectSession,
    draftInit,
};

const Tab = {
    POI: 'poi',
    BIZ: 'biz',
};
const DEFAULT_TAB = Tab.POI;

const LIMIT = 20;
const App = props => {
    useEffect(() => {
        const { infoInit, sessionInit, messageInit, login, draftInit } = props;
        sessionInit();
        messageInit();
        infoInit();
        draftInit();
        login();
        getNoticeAuth();
        if (props.currentUid) {
            props.selectSession(props.currentUid, 'pubchat');
        }
        const defaultTracker = window.LXAnalytics('getTracker');
        defaultTracker('pageView', {}, {}, 'c_waimai_m_u81xp7pc');
    }, []);
    useEffect(() => {
        if (
            props.loginStatus !== props.loginStatus &&
            props.loginStatus === 2 &&
            props.currentUid
        ) {
            props.selectSession(props.currentUid, 'pubchat');
        }
    }, [props.loginStatus]);

    const href = window.location.href;
    const noSession = href.includes('noSession') && href.includes('/chat/');

    const bizId = new URLSearchParams(window.location.search).get('bizId');
    const [currentTab, setCurrentTab] = useState(bizId ? Tab.BIZ : DEFAULT_TAB);

    const [messages, setMessages] = useState([]);
    const [sessionId, setCurrentSessionId] = useState();
    const [channelId, setCurrentChannelId] = useState(PUB_CHANNEL_ID);
    const [sessionType, setSessionType] = useState();
    const [hasMore, setHasMore] = useState(true);
    const [pubVcards, setPubVcards] = useState({});
    const [extension, setCurrentExtension] = useState();
    const [spinning, setSpinning] = useState(false);
    const [isFinish, setIsFinish] = useState(false);
    useEffect(() => {
        eventEmitter.on(EVENT_NAME.MESSAGE, (uid, message) => {
            getHistoryMessages(sessionId, sessionType, channelId);
        });
        eventEmitter.on(EVENT_NAME.UPDATE_MESSAGES, (uid, message) => {
            getHistoryMessages(sessionId, sessionType, channelId);
        });
    }, []);

    const getHistoryMessages = async (sessionId, sessionType, channelId) => {
        const historyMessages =
            await messageManager.fetchHistoryMessagesByOrder({
                uid: sessionId,
                type: sessionType,
                channelId,
                limit: LIMIT,
            });
        setHasMore(historyMessages.next > 0);
        const messages = messageManager.getMessages(sessionId, channelId);
        setMessages(messages);
    };
    const getMessages = async item => {
        setSpinning(true);
        const res = await apiCaller.send(
            '/impc/permission/r/sessionAuth',
            {
                bizId: item.wmBizId,
                poiId: item.wmPoiId,
            },
            { method: MethodType.GET },
        );
        // 0 未完成 1 进行中 2 已完成
        setIsFinish(item.taskStatus === 2);
        const extension = JSON.stringify({
            c_avatar_url: '',
            c_name: '美团外卖业务经理',
            poi_id: item.wmPoiId,
            poi_logo_url: item.avatar,
            poi_name: item.wmName,
            poi_type: 1,
        });
        setCurrentExtension(extension);
        if (res.code !== 0) {
            return;
        }
        const pubId = res.data.pubId;
        const channelId = item.isRoomChat
            ? GROUP_CHANNEL_ID
            : CONSULT_CHANNEL_ID;
        setCurrentChannelId(channelId);
        setCurrentSessionId(pubId);
        const sessionType = item.isRoomChat
            ? SessionType.GROUP
            : SessionType.SINGLE;
        setSessionType(sessionType);

        getHistoryMessages(pubId, sessionType, channelId);

        const pubVcards = await vcardManager.getPubVcard([res.data.pubId]);
        setPubVcards({ [res.data.pubId]: pubVcards });
        setSpinning(false);
    };
    const sendText = async text => {
        await messageManager.sendMessage({
            sessionType: sessionType,
            to: sessionId,
            fromName: '美团外卖业务经理',
            type: 1,
            channelId,
            extension,
            toAppId: 0,
            body: {
                text,
            },
        });
        getHistoryMessages(sessionId, sessionType, channelId);
    };
    return (
        <Fragment>
            <LoginWrapper>
                <div className="dove-web">
                    <div className="dove-web-main">
                        <Tabs
                            defaultActiveKey={currentTab}
                            style={{ height: '100%', paddingLeft: 16 }}
                            onChange={v => setCurrentTab(v)}
                            items={[
                                {
                                    key: Tab.POI,
                                    label: '商家',
                                    children: !noSession ? (
                                        <Session className="dove-web-session-list" />
                                    ) : null,
                                },
                                {
                                    key: Tab.BIZ,
                                    label: '工单',
                                    children: (
                                        <BizList getMessages={getMessages} />
                                    ),
                                },
                            ]}
                        ></Tabs>
                        <div className="dove-web-right-content">
                            {props.currentUid && currentTab === Tab.POI ? (
                                <div className="web-demo-bubble-panel">
                                    <ThemeNotice />
                                    <div className="web-demo-bubble-bubblelist">
                                        <BubbleListWithRedux
                                            key={props.currentUid}
                                        />
                                    </div>
                                    <SenderWrapper />
                                </div>
                            ) : null}
                            {currentTab === Tab.BIZ ? (
                                <Spin spinning={spinning}>
                                    <div className="web-demo-bubble-panel">
                                        <div className="web-demo-bubble-bubblelist">
                                            <BubbleList
                                                messageArray={messages}
                                                sessionId={sessionId}
                                                sessionType={sessionType}
                                                hasMore={hasMore}
                                                getHistoryMessages={() => {
                                                    getHistoryMessages(
                                                        sessionId,
                                                        sessionType,
                                                        channelId,
                                                    );
                                                }}
                                                vcardMap={pubVcards}
                                            />
                                        </div>
                                        {isFinish ? (
                                            <div className="dove-web-tips dove-web-button">
                                                <Button block disabled>
                                                    工单已结束
                                                </Button>
                                            </div>
                                        ) : (
                                            <div className="web-demo-sender">
                                                <Sender
                                                    onSubmit={sendText}
                                                    placeholder="如果不开心，深呼吸5秒再来和商家沟通"
                                                    onChange={() => {}}
                                                    currentUid={sessionId}
                                                    senderProps={{
                                                        to: sessionId,
                                                        sessionType,
                                                        extension,
                                                        channelId,
                                                    }}
                                                />
                                            </div>
                                        )}
                                    </div>
                                </Spin>
                            ) : null}
                        </div>
                    </div>
                </div>
            </LoginWrapper>

            <ThemeModal />
        </Fragment>
    );
};

export default connect(mapStateToProps, mapDispatchToProps)(App);
