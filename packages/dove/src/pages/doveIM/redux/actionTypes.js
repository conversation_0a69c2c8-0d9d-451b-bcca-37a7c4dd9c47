// login相关
export const LOGIN = 'login';
export const LOGIN_SUCCESS = 'login_success';
export const LOGIN_FAIL = 'login_fail';
export const KICK_OFF = 'kick_off';
export const DISCONNECT = 'desconnect';

// session相关
export const UPDATE_SESSIONS = 'update_session_by_unread';
export const SET_CURRENT_UID_AND_TYPE = 'set_current_uid_and_type';
export const SET_SESSION_STS = 'set_session_sts';
export const SET_TOTAL_UNREADS = 'total_unreads';
export const SET_CURRENT_EXTENSION = 'set_current_extension';
export const SET_CURRENT_AUTH = 'set_current_auth';
export const SET_CURRENT_SESSION_PAYLOAD = 'set_current_session_payload'; // 选择了pubId的中转信息

// themeChat相关
export const SET_THEME_CHAT_LOADING = 'set_theme_chat_loading'; // 加载聊天主题等信息时需要出loading
export const SET_CURRENT_DOVE_CHAT_THEME = 'set_current_dove_chat_theme'; // 信鸽IM聊天主题
export const SET_CURRENT_POI_ID = 'set_current_poi_id'; // 保存当前的wmPoiId
export const SET_THEME_MODAL_OPEN = 'set_theme_modal_open' // 主题选择弹窗显隐


// message相关
export const SET_MESSAGE_ARRAY = 'set_message_array';
export const SET_MESSAGE_ARRAY_SUCCESS = 'set_message_array_success';
export const SET_MESSAGE_AUDIO = 'set_message_audio';

// draft相关
export const SET_DRAFT = 'set_uid_and_draft';


export function createAction(type, ...argNames) {
    return function (...args) {
        let action = {type};
        argNames.forEach((arg, index) => {
            action[argNames[index]] = args[index];
        });
        return action;
    };
}
