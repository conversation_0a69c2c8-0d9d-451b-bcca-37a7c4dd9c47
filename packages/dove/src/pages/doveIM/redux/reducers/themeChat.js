import Immutable from 'immutable';
import {
    SET_THEME_CHAT_LOADING,
    SET_CURRENT_DOVE_CHAT_THEME,
    SET_CURRENT_POI_ID,
    SET_THEME_MODAL_OPEN,
} from '../actionTypes';

let initialState = Immutable.Map({
    doveThemeChat: {},
    loading: false,
    themeSelectModalOpen: false,
    wmPoiId: undefined,
    themeModalOpen: false,
});

export default function sessionReducer(state = initialState, action) {
    switch (action.type) {
        case SET_CURRENT_DOVE_CHAT_THEME: {
            return state.merge(
                Immutable.Map({
                    doveThemeChat: action.doveThemeChat || {},
                }),
            );
        }
        case SET_THEME_CHAT_LOADING: {
            return state.merge(
                Immutable.Map({
                    loading: action.loading,
                }),
            );
        }
        case SET_CURRENT_POI_ID: {
            return state.merge(
                Immutable.Map({
                    wmPoiId: action.wmPoiId,
                }),
            );
        }
        case SET_THEME_MODAL_OPEN: {
            return state.merge(
                Immutable.Map({
                    themeModalOpen: action.open,
                }),
            );
        }
        default:
            return state;
    }
}
