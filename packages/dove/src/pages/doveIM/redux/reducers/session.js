import Immutable from 'immutable';
import {
    UPDATE_SESSIONS,
    SET_CURRENT_UID_AND_TYPE,
    SET_SESSION_STS,
    SET_TOTAL_UNREADS,
    SET_CURRENT_EXTENSION,
    SET_CURRENT_AUTH,
    SET_CURRENT_SESSION_PAYLOAD,
} from '../actionTypes';
import { SESSION_STATUS } from '../../data/constants';

const matches = location.hash.match(/#\/chat\/(\d*)\?type=(\w*)/);
const currentUid = matches && matches[1];
const sessionType = matches && matches[2];
const initSession = currentUid ? [{
    appId: 35,
    channelId: 1016,
    message: {
        type: 1,
        body: {text: ""}
    },
    pubId: currentUid,
    readTime: 0,
    sessionId: `1016-${currentUid}`,
    svrTime: 0,
    time: 0,
    type: sessionType,
    uid: currentUid,
    unreads: 0,
}] : [];

let initialState = Immutable.Map({
    sessionList: Immutable.OrderedMap(),
    currentUid,
    sessionType,
    listStatus: SESSION_STATUS.EMPTY,
    sts: undefined,
    unreads: 0,
    extension: '',
    currentAuth: false,
    sessionPayload: undefined,
});

export default function sessionReducer(state = initialState, action) {
    switch (action.type) {
        case UPDATE_SESSIONS: {
            let sessionMap = arrayToObj(
                [initSession.pop()].concat(action.sessions).filter(Boolean)
            );
            let sessionList = state.get("sessionList");
            let result = sessionList;
            sessionMap.forEach(function (session, uid) {
                if (sessionList.has(uid)) {
                    result = result.set(
                        uid,
                        sessionList.get(uid).merge(session)
                    );
                } else {
                    result = result.set(uid, session);
                }
            });
            return state.merge(
                Immutable.Map({
                    sessionList: result,
                })
            );
        }
        case SET_CURRENT_UID_AND_TYPE: {
            return state.merge(
                Immutable.Map({
                    currentUid: action.uid,
                    sessionType: action.sessionType,
                })
            );
        }
        case SET_SESSION_STS: {
            return state.merge(
                Immutable.Map({
                    sts: action.sts,
                })
            );
        }
        case SET_TOTAL_UNREADS: {
            return state.merge(
                Immutable.Map({
                    unreads: action.unreads,
                })
            );
        }
        case SET_CURRENT_EXTENSION: {
            return state.merge(
                Immutable.Map({
                    extension: action.extension,
                })
            );
        }
        case SET_CURRENT_AUTH: {
            return state.merge(
                Immutable.Map({
                    currentAuth: action.currentAuth,
                })
            );
        }
        case SET_CURRENT_SESSION_PAYLOAD: {
            const {type: _, payload} = action
            return state.merge(
                Immutable.Map({
                    sessionPayload: action ? payload : undefined,
                }),
            );
        }
        default:
            return state;
    }
}

function arrayToObj(array) {
    var result = Immutable.Map();

    array.forEach((element) => {
        result = result.set(element.uid, Immutable.Map(element));
    });

    return result;
}
