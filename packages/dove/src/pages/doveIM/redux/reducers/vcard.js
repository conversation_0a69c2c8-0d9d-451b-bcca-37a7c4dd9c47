import Immutable from 'immutable';
import { vcardManager } from '../../data';

const actionTypes = {
    SET_VCARD: 'SET_VCARD'
};

const initialState = Immutable.Map({
    vcardMap: Immutable.Map()
});

export function getVcard(pubId) {
    return async function (dispatch, getState) {
        const vcardMap = getState().vcard.get('vcardMap');
        const pubIds = Array.isArray(pubId) ? pubId : [pubId];
        const needFetchPubIds = pubIds.filter(item => !vcardMap.get(item));
        if (needFetchPubIds.length) {
            const pubVcards = await vcardManager.getPubVcard(needFetchPubIds);
            dispatch({
                type: actionTypes.SET_VCARD,
                data: pubVcards
            });
        }
    };
}

export default function (state = initialState, action) {
    switch (action.type) {
        case actionTypes.SET_VCARD:
            const vcards = action.data;
            let vcardMap = state.get('vcardMap');
            vcards.forEach((item) => {
                vcardMap = vcardMap.set(item.uid || item.pubId, item);
            });
            state = state.set('vcardMap', vcardMap);
            return state;
        default:
            return state;
    }
}
