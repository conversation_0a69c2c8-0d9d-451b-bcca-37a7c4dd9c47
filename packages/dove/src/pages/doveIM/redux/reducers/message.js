import Immutable from 'immutable';
import { SET_MESSAGE_ARRAY, SET_MESSAGE_ARRAY_SUCCESS, SET_MESSAGE_AUDIO } from '../actionTypes';
import { MESSAGE_STATUS_REDUX } from '../../data/constants';


const initialState = Immutable.Map({
    messageArray: Immutable.List(),
    messageStatus: MESSAGE_STATUS_REDUX.EMPTY,
    messageAudio: ''
});

export default function messageReducer(state = initialState, action) {
    switch (action.type) {
        case SET_MESSAGE_ARRAY:
            return state.merge(Immutable.Map({
                messageStatus: MESSAGE_STATUS_REDUX.LOADING
            }));

        case SET_MESSAGE_ARRAY_SUCCESS:
            return state.merge(Immutable.Map({
                messageStatus: MESSAGE_STATUS_REDUX.SUCCESS,
                messageArray: Immutable.List(action.messageArray)
            }));

        case SET_MESSAGE_AUDIO:
            return state.merge(Immutable.Map({
                messageAudio: action.messageAudio
            }))

        default:
            return state;
    }
}