import Immutable from 'immutable';
import { SET_DRAFT } from '../actionTypes';

const initialState = Immutable.Map({
    draft: {}
});

export default function draftReducer(state = initialState, action) {
    switch (action.type) {
        case SET_DRAFT:
            const draft = state.get('draft');
            if (action.uid) {
                draft[action.uid] = action.draftText;
            }
            return state.merge(Immutable.Map({
                draft
            }));
        default:
            return state;
    }
}