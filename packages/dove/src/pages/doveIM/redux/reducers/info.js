import Immutable from 'immutable';
import { LOGIN, LOGIN_SUCCESS, LOGIN_FAIL, KICK_OFF, DISCONNECT } from '../actionTypes';
import { LOGIN_STATUS } from '../../data/constants';

let initialState = Immutable.Map({
    uid: null,
    loginStatus: LOGIN_STATUS.NO_LOGIN
});

export default function infoReducer(state = initialState, action) {
    switch (action.type) {
        case LOGIN:
            return state.merge(Immutable.Map({
                'loginStatus': LOGIN_STATUS.LOGIN
            }));

        case LOGIN_SUCCESS:
            return state.merge(Immutable.Map({
                'uid': action.uid,
                'loginStatus': LOGIN_STATUS.SUCCESS
            }));

        case LOGIN_FAIL:
            return state.merge(Immutable.Map({
                'loginStatus': LOGIN_STATUS.FAIL
            }));

        case KICK_OFF: {
            return state.merge(Immutable.Map({
                'loginStatus': LOGIN_STATUS.KICK_OFF
            }));
        }

        case DISCONNECT:
            return state.merge(Immutable.Map({
                'loginStatus': LOGIN_STATUS.DISCONNECT
            }));

        default:
            return state;
    }
}
