import {
    createAction,
    LOGIN,
    LOGIN_SUCCESS,
    LOGIN_FAIL,
    KICK_OFF,
    DISCONNECT,
} from '../actionTypes';
import { eventEmitter, EVENT_NAME, configManager } from '../../data';
import { LOGIN_STATUS } from '../../data/constants';
import { MethodType, apiCaller } from '@mfe/cc-api-caller-pc';

// const host = 'http://tangchun-wjtqj-sl-doveimapi.waimai.test.sankuai.com';

export let myGlobalUid;
export function infoInit() {
    return (dispatch, getState) => {
        eventEmitter.on(EVENT_NAME.AUTH_SUCCESS, ({ uid }) => {
            myGlobalUid = uid;
            dispatch(createAction(LOGIN_SUCCESS, 'uid')(uid));
        });

        eventEmitter.on(EVENT_NAME.KICK_OFF, () => {
            dispatch(createAction(KICK_OFF)());
        });

        eventEmitter.on(EVENT_NAME.AUTH_FAIL, ({ rescode }) => {
            dispatch(createAction(LOGIN_FAIL)());
        });
        eventEmitter.on(EVENT_NAME.DISCONNECT, () => {
            const loginStatus = getState().info.get('loginStatus');

            // 连接断开事件会多次触发，如果需要再此处进行相关业务逻辑处理，请注意避免重复处理情况
            // https://developers.sankuai.com/mt/xm/dx-sdk-web/custom/3/login2/#_6
            if (loginStatus === LOGIN_STATUS.KICK_OFF) {
                return;
            }
            dispatch(createAction(DISCONNECT)());
        });
    };
}

export function login() {
    return dispatch => {
        apiCaller
            .send('/impc/permission/r/viewAuth', {}, { method: MethodType.GET })
            .then(result => {
                if (result.code === 0) {
                    apiCaller
                        .xSend(
                            '/impc/permission/r/uauth',
                            {},
                            { method: MethodType.GET },
                        )
                        .then(res => {
                            if (res.code !== 0) {
                                return;
                            }

                            const { passport, token } = res.data;

                            if (passport && token) {
                                configManager.loginWithPassport({
                                    uid: passport,
                                    token,
                                    supportMultiDevices: false,
                                });
                                dispatch(createAction(LOGIN)());
                            } else {
                                dispatch(createAction(LOGIN_FAIL)());
                            }
                        });
                } else {
                    dispatch(createAction(LOGIN_FAIL)());
                }
            })
            .catch(error => {
                console.warn(error);
                dispatch(createAction(LOGIN_FAIL)());
            });
    };
}
