import { apiCaller, MethodType } from '@mfe/cc-api-caller-pc';
import {
    createAction,
    SET_CURRENT_DOVE_CHAT_THEME,
    SET_CURRENT_POI_ID,
    SET_CURRENT_SESSION_PAYLOAD,
    SET_THEME_CHAT_LOADING,
    SET_THEME_MODAL_OPEN,
} from '../actionTypes';

export function setCurrentPoiId(poiId) {
    return dispatch => {
        dispatch(createAction(SET_CURRENT_POI_ID, 'wmPoiId')(poiId));
    };
}

export function setThemeModalOpen(open) {
    return dispatch => {
        dispatch(createAction(SET_THEME_MODAL_OPEN, 'open')(open));
    };
}

export function getOnGoingChat(wmPoiId) {
    return async dispatch => {
        dispatch(setCurrentPoiId(wmPoiId));
        const res = await apiCaller.send(
            // "http://yapi.sankuai.com/mock/32942/impc/hasGoingChat",
            '/impc/message/hasGoingThemeChat',
            { wmPoiId },
            {
                method: MethodType.GET,
                // host: 'http://yapi.sankuai.com/mock/32942',
                // prefix: '',
            },
        );

        if (res.code !== 0 || !res.data) {
            return;
        }

        dispatch(
            createAction(
                SET_CURRENT_DOVE_CHAT_THEME,
                'doveThemeChat',
            )(res.data),
        );
    };
}

export function setThemeChatLoading(loading) {
    return dispatch => {
        dispatch(createAction(SET_THEME_CHAT_LOADING, 'loading')(loading));
    };
}

export function setDoveThemeChat(theme) {
    return dispatch => {
        dispatch(createAction(SET_CURRENT_SESSION_PAYLOAD)());

        dispatch(
            createAction(SET_CURRENT_DOVE_CHAT_THEME, 'doveThemeChat')(theme),
        );
    };
}
