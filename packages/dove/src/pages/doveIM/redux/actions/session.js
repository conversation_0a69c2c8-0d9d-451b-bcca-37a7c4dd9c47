import { createHashHistory } from 'history';
import {
    createAction,
    UPDATE_SESSIONS,
    SET_CURRENT_UID_AND_TYPE,
    SET_SESSION_STS,
    SET_TOTAL_UNREADS,
    SET_MESSAGE_AUDIO,
    SET_CURRENT_EXTENSION,
    SET_CURRENT_AUTH,
} from '../actionTypes';
import {
    EVENT_NAME,
    eventEmitter,
    sessionManager,
    messageManager,
} from '../../data';
import { getMessages } from './message';
import { getOnGoingChat, setThemeChatLoading } from './themeChat';
import { notice } from '../../components/utils/notice';
import { MethodType, apiCaller } from '@mfe/cc-api-caller-pc';
import { getWmPoiIdByPubId } from '~/pages/doveIM/data/getWmPoiIdByPubId';

const history = createHashHistory();

export function sessionInit() {
    return (dispatch, getState) => {
        eventEmitter.on(EVENT_NAME.UPDATE_SESSIONS, async ({ sessions }) => {
            // 固定 channelId
            const myBusinessSessions = sessions.filter(
                item => item.channelId === 1016,
            );
            // 处理信息，判断是否为商家消息
            myBusinessSessions.map(item => {
                const { message } = item;
                if (message.from !== getState().info.get('uid')) {
                    // console.log('notice-info', item);
                    notice(message);
                }
            });
            const totalUnreads = myBusinessSessions.reduce(
                (total, session) => total + session.unreads,
                0,
            );
            // 处理读数
            const currentTotalUnreads = getState().session.get('totalUnreads');
            if (totalUnreads !== currentTotalUnreads) {
                apiCaller
                    .send('/impc/message/w/unreadCount', {
                        count: totalUnreads,
                    })
                    .catch(console.warn);
                dispatch(
                    createAction(SET_TOTAL_UNREADS, 'sessions')(totalUnreads),
                );
            }
            dispatch(
                createAction(UPDATE_SESSIONS, 'sessions')(myBusinessSessions),
            );
        });
        eventEmitter.on(EVENT_NAME.PUB_OPPOSITE_READ, result => {
            const {
                uid,
                deviceType,
                items: [{ channelId, sts, chatId }],
            } = result;
            if (
                chatId === getState().session.get('currentUid') &&
                channelId === 1016
            ) {
                dispatch(createAction(SET_SESSION_STS, 'sts')(sts));
            }
        });
        eventEmitter.on(EVENT_NAME.EMPTY_SESSIONS, () => {
            console.log('session-update-empty');
            dispatch(createAction(UPDATE_SESSIONS, 'sessions')([]));
        });
    };
}

// uid其实是pubId
export function selectSession(pubId, type = 'pubchat', session, showSession) {
    return async (dispatch, getState) => {
        dispatch(setThemeChatLoading(true));

        const data = await getWmPoiIdByPubId(pubId);

        // 请求出错
        if (!data) {
            dispatch(setThemeChatLoading(false));
            return;
        }

        const { poiId, extension } = data || {};

        const result = await apiCaller.send(
            '/impc/permission/r/sessionAuth',
            {
                poiId,
                poiType: 0,
            },
            { method: MethodType.GET },
        );
        dispatch(
            createAction(
                SET_CURRENT_AUTH,
                'currentAuth',
            )(result.code === 0 ? true : false),
        );
        dispatch(getOnGoingChat(poiId));

        const innerAction = async () => {
            showSession =
                showSession ?? !window.location.href.includes('noSession');
            let url =
                '/chat/' +
                pubId +
                `?type=pubchat${showSession ? '' : '&noSession'}`;
            await dispatch(getMessages(pubId, 'pubchat'));
            // 因为获取对端已读时间接口不支持 cschat 类型会话，所以 cschat 类型没有已读未读功能
            const [messageReads = {}] =
                await messageManager.getPubOppositeReadTime({
                    chatIds: [
                        {
                            chatId: pubId,
                            peerUid: type === 'cschat' ? pubId : '0',
                        },
                    ],
                    channelId: 1016,
                });
            if (session) {
                messageManager.sendPubOppositeRead({
                    pubId,
                    peerUid: pubId,
                    type,
                    peerAppId: '0',
                    channelId: 1016,
                    sts: session.sessionSvrTime,
                });
            }
            const audio = getState().message.get('messageAudio');
            if (getState().message.get('messageAudio')) {
                audio.pause();
            }
            dispatch(
                createAction(
                    SET_CURRENT_UID_AND_TYPE,
                    'uid',
                    'sessionType',
                )(pubId, 'pubchat'),
            );
            dispatch(
                createAction(
                    SET_CURRENT_EXTENSION,
                    'extension',
                )(JSON.stringify(extension)),
            );
            dispatch(createAction(SET_SESSION_STS, 'sts')(messageReads.sts));
            dispatch(createAction(SET_MESSAGE_AUDIO, 'messageAudio')(''));
            history.push(url);
            dispatch(sendReadTime(pubId));
        };

        try {
            await innerAction();
        } catch (e) {
            console.log(e);
        }

        dispatch(setThemeChatLoading(false));
    };
}

export function sendReadTime(uid) {
    return dispatch => {
        let isExist = sessionManager.hasLocalSession(uid, 1016);
        sessionManager.readSession({ uid: uid, channelId: 1016 });
    };
}
