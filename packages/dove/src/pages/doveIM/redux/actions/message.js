import { createAction, SET_MESSAGE_ARRAY, SET_MESSAGE_ARRAY_SUCCESS, SET_MESSAGE_AUDIO } from '../actionTypes';
import { messageManager, EVENT_NAME, eventEmitter } from '../../data';
import { sendReadTime } from './session';

export function messageInit() {
    return (dispatch, getState) => {
        eventEmitter.on(EVENT_NAME.MESSAGE, ({message}) => {
            if (message.belongTo === getState().session.get('currentUid')) {
                dispatch(getMessagesFromMap(message.belongTo));
                messageManager.sendPubOppositeRead({
                    uid: message.belongTo,
                    peerUid: message.belongTo,
                    type: message.type,
                    peerAppId: '0',
                    channelId: 1016,
                    sts: message.svrTime
                });
                dispatch(sendReadTime(message.belongTo));
            }
        });

        eventEmitter.on(EVENT_NAME.UPDATE_MESSAGES, ({uid, message}) => {
            if (uid === getState().session.get('currentUid')) {
                dispatch(getMessagesFromMap(uid));
            }
        });
    };
}

export function getMessagesFromMap(uid) {
    return (dispatch) => {
        const messageArray = messageManager.getMessages(uid, 1016);
        dispatch(createAction(SET_MESSAGE_ARRAY_SUCCESS, 'messageArray')(messageArray));
    }
}

export function getHistoryMessages(uid, type) {
    return async (dispatch) => {
        const result = await messageManager.fetchHistoryMessagesByOrder({ uid, type, channelId: 1016 });
        const messageArray = messageManager.getMessages(uid, 1016);
        dispatch(createAction(SET_MESSAGE_ARRAY_SUCCESS, 'messageArray')(messageArray));
        return result;
    };
}

export function getMessages(uid, type) {
    return async (dispatch) => {
        const messageArray = messageManager.getMessages(uid, 1016);
        dispatch(createAction(SET_MESSAGE_ARRAY)());
        if (messageArray.length === 0) {
            await dispatch(getHistoryMessages(uid, type));
        } else {
            dispatch(getMessagesFromMap(uid));
        }
    };
}

export function playAudio(url, callback) {
    return (dispatch, getState) => {
        const currentAudio = getState().message.get('messageAudio');
        if (url === currentAudio.src) {
            currentAudio.play();
        } else {
            currentAudio && currentAudio.pause();
            var audio = new Audio(url);
            audio.setAttribute('crossOrigin', 'anonymous');  
            audio.play().catch(console.log);
            audio.onended = callback;
            dispatch(createAction(SET_MESSAGE_AUDIO, 'messageAudio')(audio));
        }
    }
}
