/**
 * @description [ 信鸽 IM PC ]
 * <AUTHOR> <EMAIL> ]
 * @date        [ 2020-08-03 ]
 */
import { Provider } from 'react-redux';
import { createStore, applyMiddleware } from 'redux';
import thunk from 'redux-thunk';
import reducers from './redux/index';
import Root from './root';
import { render } from '@src/module/root';

render(
    <Provider store={createStore(reducers, applyMiddleware(thunk))}>
        <Root />
    </Provider>,
    '信鸽IM',
);
