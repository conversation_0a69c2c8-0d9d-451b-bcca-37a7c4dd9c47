export const TIME_SECTION = {   // 分段时间，用于显示
    TODAY: '今天',
    YESTERDAY: '昨天',
    TWO_DAYS_AGO: '前天',
    ONE_WEEK: '一周内',
    ONE_MONTH: '一月内',
    THREE_MONTHS: '三个月内',
    ONE_YEAR: '一年内',
    EARLIER: '更早'
};

function _typeCalendar(date) {
    let month = date.getMonth() + 1;
    let day = date.getDate();
    let hour = date.getHours();
    let minute = date.getMinutes();
    let week = date.getDay();
    let weekName = '日一二三四五六';
    return month + '月' + day + '日周' + weekName[week] + ' ' +
        (hour < 10 ? '0' + hour : hour) + ':' + (minute < 10 ? '0' + minute : minute);
}

function _formatDoubleDigit(num) {
    return num < 10 ? ('0' + num) : num;
}

function _typeLiterary(date) {
    //参数是一个 Date 对象
    let timeStamps = _getTimeStamps();
    let time = date.getTime();

    if (time >= timeStamps.today) {
        //当天的消息
        return _formatDoubleDigit(date.getHours()) + ':' + _formatDoubleDigit(date.getMinutes());
    } else if (time >= timeStamps.yesterday) {
        return '昨天';
    } else if (time >= timeStamps.twoDaysAgo) {
        return '前天';
    } else {
        return `${date.getFullYear() % 100}/${date.getMonth() + 1}/${date.getDate()}`;
    }
};

function _typeTimeSection(date) {
    let timeStamps = _getTimeStamps();
    let time = date.getTime();

    if (time >= timeStamps.today) {
        return TIME_SECTION.TODAY;
    } else if (time >= timeStamps.yesterday) {
        return TIME_SECTION.YESTERDAY;
    } else if (time >= timeStamps.oneWeek) {
        return TIME_SECTION.ONE_WEEK;
    } else if (time >= timeStamps.oneMonth) {
        return TIME_SECTION.ONE_MONTH;
    } else if (time >= timeStamps.threeMonth) {
        return TIME_SECTION.THREE_MONTHS;
    } else if (time >= timeStamps.oneYear) {
        return TIME_SECTION.ONE_YEAR;
    } else {
        return TIME_SECTION.EARLIER;
    }
}

/**
 * 用于得到各种日期段的时间戳
 * @private
 */
function _getTimeStamps() {
    const ONE_DAY = 1000 * 60 * 60 * 24;
    let now = new Date(),
        yearNow = now.getFullYear(),  // 当前年
        monthNow = now.getMonth(),    // 当前月
        dayNow = now.getDate(),       // 当前日
        today = new Date(yearNow, monthNow, dayNow, 0, 0, 0).getTime(),   // 今天的开始时间
        yesterday = today - ONE_DAY,         // 昨天的开始时间
        twoDaysAgo = today - ONE_DAY * 2,    // 前天的开始时间
        oneWeek = today - ONE_DAY * 7,   // 一周前开始时间
        oneMonth = today - ONE_DAY * 30,                      // 一月前开始时间
        threeMonth = today - ONE_DAY * 3 * 30,                                              // 三个月前开始时间
        lastYearToday = new Date(yearNow - 1, monthNow, dayNow, 0, 0, 0).getTime();         //一年前的今天开始时间

    return {
        today: today,
        yesterday: yesterday,
        twoDaysAgo: twoDaysAgo,
        oneWeek: oneWeek,
        oneMonth: oneMonth,
        threeMonth: threeMonth,
        oneYear: lastYearToday
    }
}

/**
 * <AUTHOR>
 * @time 2014
 * @version 2016-2-3
 * @modify luochaungjie
 */
export default function parseTime(type, longTime) {
    //保证 longTime 一定是一个数字类型
    longTime = Number(longTime);
    if (String(longTime) === 'NaN') {
        return '';
    }
    let date;
    if (typeof longTime !== 'undefined') {
        date = new Date(longTime);
    } else {
        date = new Date();
    }


    if (type === 'L') {
        return date.getTime() + '';
    } else if (type === 'CALENDAR') {
        return _typeCalendar(date);
    } else if (type === 'LITERARY') {
        return _typeLiterary(date);
    } else if (type === 'TIME_SECTION') {
        return _typeTimeSection(date);
    }


    type = type || 'hh:mm';

    let times = [];

    if (type.indexOf('hh') != -1) {
        let h = date.getHours();
        if (h < 10) {
            h = '0' + h;
        }
        times.push(h);
    }

    if (type.indexOf('mm') != -1) {
        let m = date.getMinutes();
        if (m < 10) {
            m = '0' + m;
        }
        times.push(m);
    }

    if (type.indexOf('ss') != -1) {
        let s = date.getSeconds();
        if (s < 10) {
            s = '0' + s;
        }
        times.push(s);
    }

    let time = times.join(':');
    let today = new Date();

    if (longTime) {
        if (date.getFullYear() != today.getFullYear()) {
            time = date.getFullYear() + '-' + (date.getMonth() + 1) + '-' + date.getDate() + ' ' + time;
        } else if (date.getMonth() != today.getMonth() || date.getDate() != today.getDate()) {
            time = (date.getMonth() + 1) + '-' + date.getDate() + ' ' + time;
        }
    }
    return time;
};
