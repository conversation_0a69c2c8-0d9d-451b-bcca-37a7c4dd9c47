import classnames from 'classnames';

function flattenMiddleWare(middleWares) {
    let _array = [];
    for (let i = 0; i < middleWares.length; i++) {
        if (middleWares[i].isContents) {
            _array.push(...flattenMiddleWare(middleWares[i].isContents))
        } else {
            _array.push(middleWares[i]);
        }
    }
    return _array;
}

function applyMiddleWare(middleWares) {
    let next = function (str) {
        return str;
    };
    middleWares = flattenMiddleWare(middleWares).reverse();
    for (let i = 0; i < middleWares.length; i++) {
        next = middleWares[i](next);
    }
    next.isContents = middleWares;
    return next;
}

const HTML_CHARS = {
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&apos;',
    '&': '&amp;'
};

/**
 * HTML标签内容过滤
 * @param {String} html
 * @param {Boolean} isAll
 * @return {String}
 */
export function escapeHtml(html, isAll = false) {
    return ('' + html).replace(new RegExp('[<>"\'' + (isAll ? '&]' : ']'), 'g'), function ($0) {
        return HTML_CHARS[$0];
    });
}

// product后缀是为了满足酒旅境外度假需求,app后缀是Google新开放的
const TOP_LEVEL_DOMAIN = 'design|museum|travel|aero|arpa|asia|coop|info|jobs|mobi|name|biz|cat|com|edu|gov|int|mil|net|org|pro|tel|xxx|ac|ad|ae|af|ag|ai|al|am|an|ao|aq|ar|as|at|au|aw|ax|az|ba|bb|bd|be|bf|bg|bh|bi|bj|bm|bn|bo|br|bs|bt|bw|by|bz|ca|cc|cd|cf|cg|ch|ci|ck|cl|cm|cn|co|cr|cu|cv|cx|cy|cz|de|dj|dk|dm|do|dz|ec|ee|eg|er|es|et|eu|fi|fj|fk|fm|fo|fr|ga|gd|ge|gf|gg|gh|gi|gl|gm|gn|gp|gq|gr|gs|gt|gu|gw|gy|hk|hm|hn|hr|ht|hu|id|ie|il|im|in|io|iq|ir|is|it|je|jm|jo|jp|ke|kg|kh|ki|kn|kp|kr|kw|ky|kz|la|lb|lc|li|lk|lr|ls|lt|lu|lv|ly|ma|mc|md|me|mg|mh|mk|ml|mm|mn|mo|mp|mq|mr|ms|mt|mu|mv|mw|mx|my|mz|na|nc|ne|nf|ng|ni|nl|no|np|nr|nu|nz|om|pa|pe|pf|pg|ph|pk|pl|pm|pn|pr|ps|pt|pw|py|qa|re|ro|rs|ru|rw|sa|sb|sc|sd|se|sg|sh|si|sk|sl|sm|sn|so|sr|ss|st|sv|sy|sz|tc|td|tf|tg|th|tj|tk|tl|tm|tn|to|tr|tt|tv|tw|tz|ua|ug|uk|us|uy|uz|va|vc|ve|vg|vi|vn|vu|wf|ws|ye|yt|za|zm|zw|dp|product|app',
    REG_URL_STR = '(?:(https?|ftp|mtdaxiang):\\/\\/)?' +   //匹配协议，  $1
        '(?:(@)?' +   //作为向前查找的hack，   $2
        '(\\d{1,3}(?:\\.\\d{1,3}){3})|' +    //匹配ip，  $3
        '((?:[-a-z_0-9]{1,256}\\.){1,256}(?:' + TOP_LEVEL_DOMAIN + ')\\b)' + //匹配域，  $4
        ')' +
        '(:\\d{1,5})?' +     //匹配端口,  $5
        '(\\/[-a-z_0-9@:%+.~&/=()!\',*$]*)?' +   //匹配路径 $6
        '(\\?[-a-z_0-9@:%+.~&/=()!\',;{}*?$]*)?' +   //匹配查询参数   $7
        '(\\#[-a-z_0-9@:%+.~&/=()!\';{}*?#^$]*)?',//匹配锚点，   $8
    //匹配含[xxx|xxxxx]这种格式的 '|'后面内容随意
    REG_LINK_STR = '\\[\\s*' +
        '([^\\[\\|]+)' + //匹配连接文字, $1
        '\\s*\\|\\s*' +
        '([^\\] \\n]+)' +   //匹配后面内容， $2
        '\\s*\\]',
    // REG_ANCHOR_TAG = /(<a.*?\/a>)/gi,
    REG_URL = new RegExp(REG_URL_STR, 'gi'),
    REG_LINK = new RegExp(REG_LINK_STR, 'gmi'),
    REG_LINK_EXT = new RegExp('^' + REG_URL_STR + '$', 'i');

function addProtocol(url) {
    REG_LINK_EXT.lastIndex = 0;
    let protocol = (url.match(REG_LINK_EXT))[1];
    return (protocol ? '' : 'http://') + url;
}

/**
 * 过滤url并对特殊字符进行编码,先替换[]格式URL，再替换普通匹配URL保存起来，escape字符串后再进行替换还原
 * @param str
 * @returns {string}
 */
export function filterUrl(str, isLongText = false) {
    //替换[ | ] 中的文字和链接
    function replaceBracketLink(next) {
        const BRACKET_LINK_SIGN_STRING = '{{еёθιеёθιйкнфйкнф}}';//占位符
        const BRACKET_LINK_SIGN = new RegExp(BRACKET_LINK_SIGN_STRING, 'g');
        return function (str) {
            const urlList = [], hrefList = [];
            str = str.replace(REG_LINK, (match, text, link) => {
                if (REG_LINK_EXT.test(link)) {
                    hrefList.push(addProtocol(link));
                    urlList.push(escapeHtml(text, true));
                    return BRACKET_LINK_SIGN_STRING;
                }
                return match;
            });

            str = next(str);

            str = str.replace(BRACKET_LINK_SIGN, () => {
                const reg = /mtdaxiang:\/\/www\.meituan\.com\/profile\?uid=[\d-]+/ig;
                const href = hrefList.shift() || '';
                const url = (urlList.shift() || '').trim();
                const isAtAll = href.indexOf('profile?uid=-1') > -1;
                const className = classnames({
                    user: reg.test(href),
                    all: isAtAll
                });

                if (isAtAll) {
                    return `<span class="${className}">${url}</span>`;
                } else {
                    return `<a href="${href}" class="${className}" target="_blank" rel="noreferrer">${url}</a>`;
                }
            });
            return str;
        };
    }

    function replaceREGURL(next) {
        const URL_SIGN_STRING = '{{еёθιйкнф}}';//占位符
        const URL_SIGN = new RegExp(URL_SIGN_STRING, 'g');

        return function (str) {
            const urlList = [], hrefList = [];
            str = str.replace(REG_URL, (url
                //  ,protocol, mailPrefix, IP, domain, port, path, param, anchor
            ) => {
                let lastCharFlag = false;
                const lastChar = url && url[url.length - 1];             //url最后一位为）或者 ','剔除
                if (lastChar === ')' || lastChar === ',') {
                    url = url.slice(0, url.length - 1);
                    lastCharFlag = true;
                }
                urlList.push(escapeHtml(url, true));
                hrefList.push(addProtocol(url));
                return lastCharFlag ? URL_SIGN_STRING + lastChar : URL_SIGN_STRING;
            });

            str = next(str);

            // 对url 中的链接进行解义
            str = str.replace(URL_SIGN, (match, index, origin) => {
                if (isLongText) {  // 只在长文本被隐藏时对url是否被截断进行判断
                    if ((index === 0) && (match === origin)) { // 整个气泡页都是链接，按链接显示，点击进入长文本页面
                        const href = hrefList.shift();
                        const url = (urlList.shift() || '').trim();
                        return href && url ? `<a data-click = 'click-to-long-text' href = 'javascript:void(0);'>${url}</a>` : URL_SIGN_STRING;
                    }
                    if ((index === (origin.length - match.length)) && (index !== 0)) { // 若长文本最后有链接，考虑最后链接会被截断，将其收入长文本（隐藏部分）中，不在气泡页中显示
                        return '';
                    }
                }
                const href = hrefList.shift();
                const url = (urlList.shift() || '').trim();
                // 处理发送命中替换符的万一情况,能够显示消息,不过可能出现url和特殊替换符错位问题
                return href && url ? `<a href="${href}" target="_blank" rel="noreferrer">${url}</a>` : URL_SIGN_STRING;
            });
            return str;
        };
    }

    //对消息中< >进行转义
    function replaceRegExp(next) {
        return function (str) {
            str = str.replace(new RegExp('[<>"\'&]', 'g'), ($0) => {
                return HTML_CHARS[$0];
            });
            str = next(str);
            return str;
        };
    }

    //compose 中间件
    const filterURLMiddleWare = applyMiddleWare([
        replaceBracketLink,
        replaceREGURL,
        replaceRegExp
    ]);

    return filterURLMiddleWare(str);
}


/**
 * 把空格和换行转成html encode的形式
 * @param {string} text
 * @returns {string}
 */
export function spaceToHtml(text = '') {
    text = ('' + text).replace(/[ ]+/g, function ($0) {
        return ' ' + ($0.length > 1 ? $0.substr(1).replace(/ /g, '&nbsp;') : '');
    });

    return text.replace(/(\u000a|\u000D)/g, '<br/>') + '';
}

export function parseUrl(url = '', {pathFlag = false, paramFlag = true}) {
    REG_URL.lastIndex = 0;
    let result = REG_URL.exec(url) || [];
    // if (result[0] !== result['input']) {
    //     //未命中全部
    // }

    let protocol = result[1];
    let IP = result[3];
    let domain = result[4];
    let port = result[5];
    let path = result[6];
    let param = result[7] ? decodeURIComponent(result[7].slice(1)) : '';
    let anchor = result[8];

    return {
        protocol: protocol || 'http',
        IP: IP || '',
        domain: domain || '',
        port: port || '80',
        path: (pathFlag ? _parsePath(path) : path) || '',
        param: (paramFlag ? _parseParam(param) : param) || '',
        anchor: anchor || ''
    };
}

function _parsePath(path) {
    if (path) {
        let result = path.split('/');
        result.shift();

        return result;
    } else {
        return [];
    }
}

function _parseParam(param) {
    let result = {}, kv = param ? param.split('&') : [], tmp;
    for (let i in kv) {
        tmp = kv[i].split('=');
        result[tmp[0]] = tmp[1];
    }

    return result;
}

/**
 * 返回需要调用innerHTML的对象
 * @param string str
 * @returns {__html: *}
 */
export function createMarkup(str) {
    return {__html: str};
}
