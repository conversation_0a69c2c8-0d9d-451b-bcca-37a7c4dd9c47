import { EMOTIONS } from '../../data/constants';
import classNames from 'classnames';

function getEmotionString(emotionIndex) {
    const name = EMOTIONS[emotionIndex];
    const className = classNames({
        'msg-emotion': true,
        [`msg-emotion-${emotionIndex}`]: true
    });

    return `<span title='${name}' class='${className}'></span>`;
}

export function replaceFace(content) {
    return content.replace(new RegExp('\\[(' + EMOTIONS.join('|') + ')\\]', 'gi'), function ($0, $1) {
        const emotionIndex = EMOTIONS.findIndex((element) => {
            return element.toLowerCase() === $1.toLowerCase();
        });
        return emotionIndex !== -1 ? getEmotionString(emotionIndex) : $0;
    });
}