const HTML_CHARS = {
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&apos;',
    '&': '&amp;'
};

/**
 * HTML标签内容过滤
 * @param {String} html
 * @param {Boolean} isAll
 * @return {String}
 */
export function escapeHtml(html, isAll = false) {
    return ('' + html).replace(new RegExp('[<>"\'' + (isAll ? '&]' : ']'), 'g'), function ($0) {
        return HTML_CHARS[$0];
    });
}

/**
 * 把空格和换行转成html encode的形式
 * @param {string} text
 * @param {boolean} isCutOff,用于判断群公告的内容是否被折叠
 * @returns {string}
 */
export function spaceToHtml(text, isCutOff) {
    text = ('' + text).replace(/[ ]+/g, function ($0) {
        return ' ' + ($0.length > 1 ? $0.substr(1).replace(/ /g, '&nbsp;') : '');
    });
    // 群公告中，若公告内容被折叠，所有的'换行'换为'空格'，以便在一行中显示更多内容
    if (isCutOff) {
        return text.replace(/(\u000a|\u000D)/g, '&nbsp;');
    } else {
        return text.replace(/(\u000a|\u000D)/g, '<br/>');
    }
}

/**
 * 将被转义后的 html 实体字符转换成可显示的字符
 * @param input
 * @returns {string}
 */
export function decodeHtml(input) {
    let converter = document.createElement('div');
    converter.innerHTML = input;
    const output = converter.innerText || converter.textContent;
    converter = null;//解除对dom的引用
    return output;
}


/**
 * 解析custom内容
 */
export function parseCustomContent({ content, isShowStyle }) {  // isShowStyle为是否把content中携带的style信息显示出来
    const reg = /\[(size|color)=(#?[0-9A-Fa-f]+?)\]([\s\S]*?)(\[\/\1\])/;
    while (reg.test(content)) {
        content = content.replace(reg, function ($0, $1, $2, $3, $4) {
            if($0 === $4){
                
            }
            if (isShowStyle) {
                let style;
                switch ($1) {
                    case 'size':
                        style = 'font-size:' + $2 + 'px';
                        break;
                    case 'color':
                        style = 'color:' + $2;
                        break;
                }
                return '<span style="' + style + '">' + $3 + '</span>';
            } else {
                return $3;
            }
        });
    }
    return content;
}
