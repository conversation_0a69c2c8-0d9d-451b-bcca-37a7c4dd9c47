export function getNoticeAuth() {
    if (window.Notification && Notification.permission !== "granted") {
        // 如果没有权限，先尝试申请权限
        Notification.requestPermission(function (status) {
            if (Notification.permission !== status) {
                Notification.permission = status;
            }
        });
    }
}

export function notice(message) {
    if (document.visibilityState !== 'hidden') return;
    if (Notification.permission === "granted") {
        const { body: { text = '[收到一条消息，请查阅]' } = {}, extension } = message;
        const { poi_name: poiName, poi_logo_url: poiLogo } = JSON.parse(extension);
        const n = new Notification(poiName || '商家', {body: text, icon: poiLogo || undefined});
    }
    return;
}