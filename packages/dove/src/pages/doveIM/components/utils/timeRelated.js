const ONEDAY = 24 * 60 * 60 * 1000;

// export function formatMessageDate(time) {
//     let messageTime = new Date(time);
//     let now = new Date();
//     let result;
//     if (messageTime.getFullYear() === now.getFullYear()) {
//         result = formatDate('MM-dd HH:mm', messageTime);
//     } else {
//         result = formatDate('yyyy-MM-dd HH:mm', messageTime);
//     }
//     return result;
// }

/**
 * 返回固定格式的时间
 * @param fmt 时间格式 yyyy-MM-dd hh:mm:ss
 * @param time 时间戳
 * @returns {*}
 */
export function formatDate(fmt, time) {

    let now;
    if (time) {
        now = new Date(time);
    } else {
        now = new Date();
    }
    let FMT = new Map([['M+', now.getMonth() + 1],//月份
    ['d+', now.getDate()],
    ['h+', now.getHours() % 12 === 0 ? 12 : now.getHours() % 12],//日
    ['H+', now.getHours()],//小时
    ['m+', now.getMinutes()], //小时
    ['s+', now.getSeconds()],//分//秒
    ['q+', Math.floor((now.getMonth() + 3) / 3)],//季度
    ['S', now.getMilliseconds()]]); //毫秒

    const week = {
        '0': '/u65e5',
        '1': '/u4e00',
        '2': '/u4e8c',
        '3': '/u4e09',
        '4': '/u56db',
        '5': '/u4e94',
        '6': '/u516d'
    };
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (now.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    if (/(E+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, ((RegExp.$1.length > 1) ? (RegExp.$1.length > 2 ? '/u661f/u671f' : '/u5468') : '') + week[now.getDay() + '']);
    }
    for (let [key, value] of FMT) {
        if (new RegExp(`(${key})`).test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (value+'') : (('00' + value).substr(('' + value).length)));
        }
    }
    return fmt;
}

// export function getWeekDay(timestamp, needNumber = false) {
//     let weekArray = [I18n.get('_sunday'), I18n.get('_monday'), I18n.get('_tuesday'), I18n.get('_wednesday'), I18n.get('_thursday'), I18n.get('_friday'), I18n.get('_saturday')];
//     let date = new Date(timestamp);
//     if (needNumber) {
//         return date.getDay();
//     } else {
//         return weekArray[date.getDay()];
//     }
// }

export function getZeroTime(timeStamp = Date.now()) {
    return Math.floor(timeStamp / ONEDAY) * ONEDAY;
}

export function compareDateTime(time1, time2 = Date.now()) {
    let firstTime = new Date(time1);
    let secondTime = new Date(time2);

    return {
        year: firstTime.getFullYear() === secondTime.getFullYear(),
        month: firstTime.getMonth() === secondTime.getMonth(),
        date: firstTime.getDate() === secondTime.getDate(),
        day: firstTime.getDay() === secondTime.getDay(), //星期几
        hour: firstTime.getHours() === secondTime.getHours(),
        minute: firstTime.getMinutes() === secondTime.getMinutes(),
        second: firstTime.getSeconds() === secondTime.getSeconds(),
        milliSecond: firstTime.getMilliseconds() === secondTime.getMilliseconds()
    };
}

// //转化YYYYMMDD格式的日期字符串，返回时间戳
// export function getTimeByString(timeStr) {
//     if (typeof timeStr !== 'string' || timeStr.length !== 10) {
//         return new Date().getTime();
//     }
//     let year = timeStr.substring(0, 4),
//         month = timeStr.substring(5, 7),
//         day = timeStr.substring(8, 10);
//     return new Date(year, month - 1, day).getTime();
// }

// export function getWeekDay2(timestamp) {
//     let date = new Date(timestamp);
//     let arr = [I18n.get('_sunday2'), I18n.get('_monday2'), I18n.get('_tuesday2'), I18n.get('_wednesday2'), I18n.get('_thursday2'), I18n.get('_friday2'), I18n.get('_saturday2')];
//     return arr[date.getDay()];
// }

// export function isToday(time) {
//     let result = compareDateTime(time);
//     return result.year && result.month && result.date;
// }

// export function getIntervalTime(startTime, endTime) {
//     if (startTime >= endTime) {
//         return '00:00';
//     }

//     let daltaT = Math.floor((endTime - startTime) / 1000);

//     let minute = 0;
//     let second = 0;

//     if (daltaT > 60) {
//         minute = Math.floor(daltaT / 60);
//     }

//     second = daltaT % 60;

//     return `${fillZeroOnLeft(minute)}:${fillZeroOnLeft(second)}`;
// }

// function fillZeroOnLeft(num) {
//     if (num > 9) {
//         return `${num}`;
//     } else {
//         return `0${num}`;
//     }
// }


// /**
//  *
//  * @param time
//  * @returns {*}
//  */
// export function getTimeBelong(time) {
//     let r = compareDateTime(time);
//     let t = new Date(time);
//     let ct = new Date();
//     if (r.year && r.month && r.date) {
//         return I18n.get('_today')
//     }
//     if (Date.now() - time < ONEDAY * 7 && ct.getDay() > t.getDay()) {
//         return I18n.get('_this_week')
//     }
//     if (r.year && r.month) {
//         return I18n.get('_this_month')
//     }
//     return `${t.getFullYear()}/${t.getMonth() + 1}`;
// }

// /**
//  * 把时长转化为时间显示：01:23:11(时:分:秒)
//  * 用途：收藏里语音消息的显示
//  * @param duration [string] 时间间隔
//  */
// export function transferDurationToTime(duration) {
//     let hour = Math.floor(duration / 3600);
//     let minute = Math.floor((duration - hour * 3600) / 60);
//     let second = duration - minute * 60;
//     let time = `${fillZeroOnLeft(minute)}:${fillZeroOnLeft(second)}`;
//     if (hour) {
//         time = `${fillZeroOnLeft(hour)}:${time}`;
//     }
//     return time;
// }

/**
 * 将时长格式化为：4"
 * 用途：气泡页里语音消息的显示
 * @param duration
 */
export function formatDuration(duration) {
    if (duration < 60) {
        return `${duration}"`;
    }
    if (duration < 3600) {
        return `${Math.floor(duration / 60)}'${duration % 60}"`;
    }
}
