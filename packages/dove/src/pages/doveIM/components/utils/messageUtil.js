import {getZeroTime, compareDateTime, formatDate} from './timeRelated';

export function isMergeForwardMsg(message) {
    if (message && message.extension) {
        const { extension } = message;
        let objExt = {};
        if (typeof extension === 'string') {
            try {
                objExt = JSON.parse(extension)
            } catch (e) {
                return false;
            }
        }
        if (objExt.isMergeMessage) {
            return true;
        }
    } else {
        return false;
    }
}

export function isCancelMessage(message){
    if(message && message.isCancel){
        return true;
    }else{
        return false;
    }
}

export function formatMessageTime(time){
    let zeroTime = getZeroTime();
    let result = compareDateTime(zeroTime, time);

    if (result['year'] && result['month'] && result['date']) {
        return formatDate('HH:mm', time);
    }
    if (result['year']) {
        return formatDate('MM-dd HH:mm', time);
    }
    return formatDate('yyyy-MM-dd HH:mm', time);
}
