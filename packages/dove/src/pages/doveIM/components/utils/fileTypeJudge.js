

export let isPS = function (name) {
    return /^.*\.(psd)$/gi.test(name);
};
export let isAI = function (name) {
    return /^.*\.(ai|eps)$/gi.test(name);
};
export let isHEIC = function (name) {
    return /^.*\.(heic)$/gi.test(name);
};

export let isGif = function (name) {
    return /^.*\.(gif)$/gi.test(name);
};

export let isTiff = function (name) {
    return /^.*\.(tiff)$/gi.test(name);
};

export let isMSDoc = function (name) {
    return /^.*\.(doc|docx|dotx|dot|dotm|rtf)$/gi.test(name);
};

export let isMSExcel = function (name) {
    return /^.*\.(xls|xlsx|xlsm|xlm|xlsb|csv)$/gi.test(name);
};
export let isMSPPT = function (name) {
    return /^.*\.(ppt|pptx|pps|ppsx|potx|pot|pptm|potm|ppsm)$/gi.test(name);
};
export let isPDF = function (name) {
    return /^.*\.(pdf)$/gi.test(name);
};

export let isAudio = function (name) {
    return /^.*\.(mp2|wav|mp3|au|wma|aif)$/gi.test(name);
};
export let isVideo = function (name) {
    return /^.*\.(3gpp|asf|wmv|avi|flv|f4v|mkv|mov|mp4|m4a|mpeg|mpg|ts|ogg|mts|wma|rm|rmvb|webm)$/gi.test(name);
};

export let isText = function (name) {
    return /^.*\.(text|txt|html|htm|htmlx|js|java|c|cpp|go|py|ry|swift|ruby|oc|m|h|css|cs|jsp|asp|php|md|markdown|sass|scss|less|lisp|sql|sh|bash|json|log)$/gi.test(name);
};

export let isZip = function (name) {
    return /^.*\.(zip|rar|7z|gz|jar|tar)$/gi.test(name);
};

export let isDoc = function (name) {
    return isMSDoc(name) || isMSExcel(name) || isMSPPT(name) || isPDF(name);
};

export let isMedia = function(name) {
    return isAudio(name) || isVideo(name);
};

export let isPicture = function (name) {
    return /^.*\.(bmp|gif|jpg|png|tiff|jpeg)$/gi.test(name);
};

export let isImg = function (name) {
    return isPicture(name) || isPS(name) || isAI(name) || isHEIC(name);
};

export let isKey = function (name) {
    return /^.*\.(key)$/gi.test(name);
};

export let isNumbers = function (name) {
    return /^.*\.(numbers)$/gi.test(name);
};

export let isPages = function (name) {
    return /^.*\.(pages)$/gi.test(name);
};

export let isIWork = function (name) {
    return /^.*\.(key|numbers|pages)$/gi.test(name);
};

export let isSvg = function (name) {
    return /^.*\.(svg)$/gi.test(name);
};

export let isSketch = function (name) {
    return /^.*\.(sketch)$/gi.test(name);
};

export let isGraph = function (name) {
    return /^.*\.(xmgraph)$/gi.test(name);
};

export let isMindMap = function (name) {
    return /^.*\.(km|mm|mmap|xmind)$/gi.test(name);
};

export let isXmdoc = function (name) {
    return /^.*\.(xmdoc)$/gi.test(name);
};

export let isCoauthorMd = function (name) {
    return false && /^.*\.(md)$/gi.test(name);
};

export let isCoauthorMind = function (name) {
    return /^.*\.(km)$/gi.test(name);
};

export let isCoauthor = function (name) {
    return isXmdoc(name) || isCoauthorMd(name) || isCoauthorMind(name);
};

export let isMp4 = function (name) {
    return /^.*\.(mp4)$/gi.test(name);
};

export let isCommonPreview = function (name) {//不支持isIWork，只支持MP4
    return isImg(name) || isDoc(name) || isText(name) || isZip(name) || isAudio(name) || isMindMap(name) || isGraph(name) || isCoauthor(name);
}

export let isPreview = function (name) {
    return isImg(name) || isDoc(name) || isText(name) || isZip(name) || isMedia(name) || isIWork(name) || isMindMap(name) || isGraph(name) || isCoauthor(name);
};
