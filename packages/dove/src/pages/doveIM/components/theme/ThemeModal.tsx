import { message, Modal, Radio } from 'antd';
import { useEffect, useState } from 'react';
import { selectSession } from '../../redux/actions/session';
import {
    setDoveThemeChat,
    setThemeChatLoading,
    setThemeModalOpen,
} from '../../redux/actions/themeChat';
import { connect } from 'react-redux';
import './index.scss';
import { apiCaller, APISpec } from '@mfe/cc-api-caller-pc';

type Themes = APISpec['/impc/message/themeList']['response'];

const mapStateToProps = state => ({
    doveThemeChat: state.themeChat.get('doveThemeChat'),
    themeModalOpen: state.themeChat.get('themeModalOpen'),
    wmPoiId: state.themeChat.get('wmPoiId'),
    pubId: state.session.get('currentUid'),
});

const mapDispatchToProps = {
    setDoveThemeChat,
    selectSession,
    setThemeChatLoading,
    setThemeModalOpen,
};

const ThemeModal = props => {
    const { setDoveThemeChat, pubId } = props;
    const [themeList, setThemeList] = useState<Themes>([]);
    const [themeId, setThemeId] = useState();

    const openSessionAndSetThemeChat = async theme => {
        if (!pubId) {
            return;
        }

        setDoveThemeChat(theme);
    };

    const getThemeList = async () => {
        const res = await apiCaller.get(
            '/impc/message/themeList',
            { themeType: '1' },
            {
                // host: 'http://yapi.sankuai.com/mock/32942',
                // prefix: '',
            },
        );

        if (res.code !== 0 || !res.data.length) {
            // 出错则fallback到legacy
            return;
        }

        setThemeList(res.data);
    };

    const handleConfirmTheme = async () => {
        if (!props.wmPoiId) {
            return;
        }

        if (!themeId) {
            return message.error('请选择会话主题！');
        }

        const res = await apiCaller.send(
            '/impc/message/createThemeChat',
            {
                wmPoiId: props.wmPoiId,
                themeId,
            },
            // {
            //     host: 'http://yapi.sankuai.com/mock/32942',
            //     prefix: '',
            // },
        );

        if (res.code !== 0) {
            return;
        }

        openSessionAndSetThemeChat(res.data);
        props.setThemeModalOpen(false);
    };

    const onCancel = () => {
        props.setThemeModalOpen(false);
    };

    useEffect(() => {
        getThemeList();
    }, []);

    useEffect(() => {
        if (props.open) {
            setThemeId(undefined);
        }
    }, [props.open]);

    return (
        <Modal
            open={props.themeModalOpen}
            title="请选择本次会话主题"
            okText="确定"
            cancelText="取消"
            onOk={handleConfirmTheme}
            onCancel={onCancel}
        >
            <Radio.Group
                value={themeId}
                className="theme-selector"
                buttonStyle="solid"
                onChange={e => setThemeId(e.target.value)}
            >
                {themeList.map(t => (
                    <Radio.Button key={t.themeId} value={t.themeId}>
                        {t.themeName}
                    </Radio.Button>
                ))}
            </Radio.Group>
        </Modal>
    );
};

export default connect(mapStateToProps, mapDispatchToProps)(ThemeModal);
