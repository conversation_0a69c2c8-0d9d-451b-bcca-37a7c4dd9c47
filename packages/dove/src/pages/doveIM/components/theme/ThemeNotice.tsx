import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useCountDown } from 'ahooks';
import { Al<PERSON>, Button, message } from 'antd';
import { connect } from 'react-redux';
import { selectSession } from '../../redux/actions/session';
import { ThemeChatStatus } from '@src/pages/types';
import { setThemeModalOpen } from '../../redux/actions/themeChat';

const mapStateToProps = state => ({
    doveThemeChat: state.themeChat.get('doveThemeChat'),
    currentUid: state.session.get('currentUid'),
    pubId: state.session.get('currentUid'),
});

const mapDispatchToProps = {
    selectSession,
    setThemeModalOpen,
};

interface ThemeNotice {
    themeModalOpen: boolean;
    pubId: number;
    doveThemeChat: {
        themeChatId: number;
        themeChatCreateTime: string;
        status: ThemeChatStatus;
    };
    selectSession: (p: number, q?: any, r?: any, s?: boolean) => void;
    setThemeModalOpen: (v: boolean) => void;
    currentUid: number;
}

const pad = (s: number | string) => (String(s).length === 1 ? `0${s}` : s);

const ThemeNotice = (props: ThemeNotice) => {
    const { themeChatId, themeChatCreateTime, status } =
        props.doveThemeChat || {};

    const leftTime =
        48 * 60 * 60 * 1000 -
        (new Date().getTime() - new Date(themeChatCreateTime).getTime());

    const [countdown, { days, hours, minutes, seconds }] = useCountDown({
        leftTime,
    });
    const time = `${days}天${pad(hours)}时${pad(minutes)}分${pad(seconds)}秒`;

    const onCloseThemeChat = async () => {
        const res = await apiCaller.get('/impc/message/endThemeChat', {
            themeChatId,
        });
        if (res.code !== 0) {
            return;
        }

        message.success('操作成功！');
        // TODO: 这里有问题
        props.selectSession(props.currentUid, undefined, undefined, true);
    };

    if (!props.pubId || status === ThemeChatStatus.USE_LEGACY) {
        return null;
    }

    if (countdown <= 0 || status === ThemeChatStatus.FRESH) {
        return (
            <Alert
                type="warning"
                message="会话已结束"
                action={
                    <Button
                        size="small"
                        type="primary"
                        onClick={() => props.setThemeModalOpen(true)}
                    >
                        重新发起
                    </Button>
                }
            />
        );
    }

    return (
        <Alert
            type="warning"
            message={`会话将在${time}后结束`}
            action={
                <Button size="small" type="primary" onClick={onCloseThemeChat}>
                    结束会话
                </Button>
            }
        />
    );
};

export default connect(mapStateToProps, mapDispatchToProps)(ThemeNotice);
