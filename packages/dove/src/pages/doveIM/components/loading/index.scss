.comp-loading {
    display: inline-block;
    text-align: center;
    font-size: 0;
    
    .loading {
        animation: 0.85s loadingCircle steps(8) infinite;
        display: inline-block;
        background-size: cover;
        background-repeat: no-repeat;
    }

    &.rest {
        .loading {
            animation: none;
        }
    }

    &.loading-xsmall .loading {
        width: 12px;
        height: 12px;
    }

    &.loading-small .loading {
        width: 14px;
        height: 14px;
    }

    &.loading-base .loading {
        width: 16px;
        height: 16px;
    }

    &.loading-large .loading {
        width: 20px;
        height: 20px;
    }

    &.loading-xlarge .loading {
        width: 24px;
        height: 24px;
    }

    &.loading-xxlarge .loading {
        width: 28px;
        height: 28px;
    }

    &.loading-xxxlarge .loading {
        width: 32px;
        height: 32px;
    }
}

@keyframes loadingCircle {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}