import './index.scss'
import React from 'react';
import classname from 'classnames';
import loadingImg from './loading_gray.png';

export default class Loading extends Component {
    constructor() {
        super(props);
    }
    render () {
        let { className = '', style, size = 'base', ...other } = props;
        className = classname({
            'dx-comp-loading': true,
            [`loading-${size}`]: size,
            [className]: className
        });
        style = {
            WebkitBoxPack: 'center',
            ...style
        };
    
        return (
            <div className={className} style={style} {...other}>
                <div className="loading" style={{
                    backgroundImage: 'url(' + loadingImg + ')'
                }}>
                </div>
            </div>
        );
    }
}
