import './index.scss';
import * as React from 'react';
// import classNames from 'classnames';
// import Button from '../button';
import EmotionSelect from '../emotionSelect';
import FileUploader from '../fileUploader';
import { KEY_CODE } from '../../../data/constants';
import { Button } from 'antd';

const { ENTER } = KEY_CODE;

export default class Sender extends React.Component {
    static defaultProps = {
        placeholder: '说点什么...',
    };

    constructor(props) {
        super(props);
        this.state = {
            value: props.initValue || '',
        };
        this.textInput = null;
        this.cursorIndex = 0;
    }

    componentDidMount() {
        const { initValue = '' } = this.props;
        if (initValue && this.textInput) {
            this.textInput.value = initValue;
        }
    }

    componentWillReceiveProps(nextProps) {
        if (nextProps.currentUid !== this.props.currentUid) {
            if (!nextProps.initValue) {
                this.clearInput();
            } else {
                this.textInput.value = nextProps.initValue;
                this.onBlur();
            }
        }
    }

    saveTextInput(element) {
        if (element) {
            this.textInput = element;
        }
    }

    onSubmit() {
        const { onSubmit } = this.props;
        if (onSubmit && this.textInput) {
            onSubmit(this.textInput.value);
            this.clearInput();
        }
    }

    onEmotionSelect(emotionText) {
        this.insertMessageText(emotionText);
    }

    keyDownHandle(e) {
        if (ENTER === e.keyCode && !(e.shiftKey || e.ctrlKey)) {
            e.preventDefault();
            this.onSubmit();
        }
    }

    clearInput() {
        if (this.textInput) {
            this.textInput.value = '';
            this.cursorIndex = 0;
        }
    }

    onBlur() {
        if (this.textInput) {
            this.cursorIndex = this.textInput.selectionStart;
        }
    }

    insertMessageText(insertText) {
        if (!this.textInput || !insertText) {
            return;
        }
        const textInput = this.textInput;
        let text = textInput.value;
        let last;
        last = text.slice(this.cursorIndex, text.length);
        text = text.slice(0, this.cursorIndex);
        text += insertText + last;
        textInput.value = text;
        let newCursor = this.cursorIndex + insertText.length;
        textInput.setSelectionRange(newCursor, newCursor);
        textInput.focus();
    }

    onChange(e) {
        const { onChange } = this.props;
        onChange && onChange(e);
    }

    render() {
        const { value } = this.state;
        const { placeholder } = this.props;
        return (
            <div className="text-sender-box">
                <div className="text-sender-opt">
                    <EmotionSelect
                        className={'sender-emotion-select'}
                        onSelect={this.onEmotionSelect.bind(this)}
                    ></EmotionSelect>
                    <FileUploader senderProps={this.props.senderProps} />
                </div>
                <div className="text-sender-body">
                    <textarea
                        ref={this.saveTextInput.bind(this)}
                        className="sender-textarea"
                        placeholder={placeholder}
                        onKeyDown={this.keyDownHandle.bind(this)}
                        onBlur={this.onBlur.bind(this)}
                        onChange={this.onChange.bind(this)}
                    />
                    <div className="sender-submit">
                        <Button
                            type="primary"
                            disabled={!!value}
                            onClick={this.onSubmit.bind(this)}
                        >
                            发送
                        </Button>
                    </div>
                </div>
            </div>
        );
    }
}
