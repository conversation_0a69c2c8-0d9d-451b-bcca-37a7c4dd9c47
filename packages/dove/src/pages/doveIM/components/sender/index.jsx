import React, { Component } from 'react';
import Sender from './wrapper';
import { connect } from 'react-redux';
import { setDraft } from '../../redux/actions/draft';
import { messageManager } from '../../data';

const mapStateToProps = (state) => {
    return {
        currentUid: state.session.get('currentUid'),
        draft: state.draft.get('draft'),
        extension: state.session.get('extension')
    }
}

const mapDispatchToProps = {
    setDraft
}

class SenderWrap extends Component {
    constructor(props) {
        super(props);
        this.state = {
            textValue: undefined
        }
    }

    componentWillReceiveProps(nextProps) {
        if(nextProps.currentUid !== this.props.currentUid) {
            this.setState({ textValue: this.props.draft[nextProps.currentUid]});
        }
    }

    handleChange(e) {
        this.props.setDraft(this.props.currentUid, e.target.value);
    }

    sendText (value) {
        const { currentUid, extension } = this.props;
        // 消息发送后清除草稿
        this.props.setDraft(this.props.currentUid, undefined);
        if (typeof value === 'string' && value.trim()) {
            messageManager.sendMessage({
                sessionType: 'pubchat',
                to: currentUid,
                fromName: '美团外卖业务经理',
                type: 1,
                channelId: 1016,
                extension,
                toAppId: 0,
                body: {
                    text: value
                }
            });
        }
    }

    render() {
        return (
            <Sender
                onSubmit={this.sendText.bind(this)}
                placeholder='如果不开心，深呼吸5秒再来和商家沟通'
                initValue={this.props.draft[this.props.currentUid] || ''}
                onChange={this.handleChange.bind(this)}
                currentUid={this.props.currentUid}
            ></Sender>
        );
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(SenderWrap);
