// im 文件需求，id, url, name, format, size, token
import React, { Component, Fragment } from 'react';
import { Upload } from 'antd';

import axios from 'axios';
import { connect } from 'react-redux';
import { FileAddOutlined } from '@ant-design/icons';
import './index.scss';
import { messageManager } from '../../../data';
import { apiCaller } from '@mfe/cc-api-caller-pc';

const mapStateToProps = state => {
    return {
        currentUid: state.session.get('currentUid'),
        extension: state.session.get('extension'),
    };
};

class FileUploader extends Component {
    constructor(props) {
        super(props);
        this.state = {
            url: '',
            headers: {},
        };
    }

    beforeUpload(res) {
        return false;
    }

    onChange(payload) {
        const { file: res } = payload;
        const { name: _name, type, size } = res;
        const name = '' + Date.now() + '_' + parseInt(Math.random() * 1000);
        if (!(type === 'image/jpeg' || type === 'image/jpg')) {
            alert('请选择 jpeg 或 jpg 格式的图片');
            return;
        }
        if (size >= 20000000) {
            alert('图片大小超出限制，请传 20MB 以内的图片');
            return;
        }
        const { currentUid, extension } = this.props;
        const uploadRequestUrl = '/impc/mss/token';
        const self = this;
        apiCaller
            .send(uploadRequestUrl, {
                contentType: type,
                'Access-Control-Allow-Origin': '*',
            })
            .then(result => {
                if (result.code !== 0) {
                    return;
                }
                const {
                    accessKey,
                    endPoint,
                    policy,
                    prefix,
                    signature,
                    bucketName = 'doveim-public',
                } = result.data;
                var data = new FormData();
                const url = `https://s3plus.vip.sankuai.com/doveim-public`;
                data.append('AWSAccessKeyId', accessKey);
                data.append('policy', policy);
                data.append('Signature', signature);
                data.append('key', `${prefix}_${name}`);
                data.append('file', res, name);
                var xhr = new XMLHttpRequest();
                xhr.withCredentials = true;
                xhr.addEventListener('readystatechange', function () {
                    if (this.readyState === 4) {
                        const imageUrl = `${endPoint}/v1/mss_b2337692cf044b7aac9164ce94e9f90d/${bucketName}/${prefix}_${name}`;
                        const sendData = {
                            sessionType: 'pubchat',
                            to: currentUid,
                            fromName: '美团外卖业务经理',
                            type: 4,
                            channelId: 1016,
                            extension,
                            toAppId: 0,
                            body: {
                                thumbnail: imageUrl,
                                normal: imageUrl,
                                original: imageUrl,
                                type,
                                originSize: size,
                                token: '',
                            },
                            ...(self.props?.senderProps || {}),
                        };
                        messageManager.sendMessage(sendData);
                    }
                });
                // xhr.setRequestHeader('cache', false);
                xhr.open('POST', url);
                xhr.send(data);
            });
    }

    render() {
        const { url, headers } = this.state;
        return (
            <Upload
                className="text-sender-uploader"
                beforeUpload={this.beforeUpload.bind(this)}
                onChange={this.onChange.bind(this)}
                headers={headers}
                action={url}
                accept="image/jpeg,image/jpg"
                multiple={true}
            >
                <FileAddOutlined className="sender-uploader-icon" />
            </Upload>
        );
    }
}

export default connect(mapStateToProps)(FileUploader);
