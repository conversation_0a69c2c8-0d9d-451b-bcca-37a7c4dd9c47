import { EMOTIONS } from '../../../../data/constants'
import classNames from 'classnames';
import './index.scss';
import './emotion.scss';

export default function ({ className, onSelect, ...restProps }) {
    const wrapperClassName = classNames('dx-comp-emotion-panel', className);

    function onEmotionClick(emotionName) {
        onSelect && onSelect(`[${emotionName}]`);
    }

    const emotions = EMOTIONS.map((emotionName, emotionIndex) => {
        const emotionItemClassName = classNames({
            'msg-emotion': true,
            [`msg-emotion-${emotionIndex}`]: true
        });
        return (
            <span
                key={emotionIndex}
                onClick={() => onEmotionClick(emotionName)}
                title={emotionName}
                className={emotionItemClassName} />
        )
    })

    return (
        <div
            {...restProps}
            className={wrapperClassName}>
            {emotions}
        </div>
    )
}
