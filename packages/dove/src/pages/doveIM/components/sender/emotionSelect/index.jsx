import './index.scss';
import * as React from 'react';
import classNames from 'classnames';
import EmotionPanel from './emotionPanel';
import { Popover } from 'antd';
import { SmileOutlined } from '@ant-design/icons';

export default class EmotionSelect extends React.Component {
    constructor(props) {
        super(props);

        this.wrapper = null;
    }

    onSelect(selectText) {
        const { onSelect } = this.props;
        if (onSelect) {
            onSelect(selectText);
        }
    }

    render() {
        const { className = '', ...restProps } = this.props;
        const wrapperClassName = classNames({
            [className]: className,
            'dx-comp-emotiom-select': true,
        });
        return (
            <Popover
                title={null}
                trigger="click"
                placement="topLeft"
                content={
                    <EmotionPanel
                        {...restProps}
                        onSelect={this.onSelect.bind(this)}
                    />
                }
            >
                <div className={wrapperClassName}>
                    <SmileOutlined className={'emotion-select-btn'} />
                </div>
            </Popover>
        );
    }
}
