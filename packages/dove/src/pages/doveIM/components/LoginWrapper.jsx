import React, { useEffect } from "react";
import { connect } from "react-redux";
import { LOGIN_STATUS, LOGIN_STATUS_TEXT } from "../data/constants";
import { login } from "../redux/actions/info";
import { Modal, Spin } from "antd";

const mapStateToProps = state => ({
    themeChatLoading: state.themeChat.get('loading'),
    loginStatus: state.info.get('loginStatus'),
});

const mapDispatchToProps = {
    login,
};

const LoginWrapper = (props) => {
    useEffect(() => {
        const abnormal = [
            LOGIN_STATUS.KICK_OFF,
            LOGIN_STATUS.DISCONNECT,
        ].includes(props.loginStatus);
        // console.error(props.loginStatus)

        if (!abnormal) {
            return;
        }
        Modal.destroyAll();

        const title =
            props.loginStatus === LOGIN_STATUS.KICK_OFF
                ? "你已被踢下线"
                : "网络连接断开";

        const content =
            props.loginStatus === LOGIN_STATUS.KICK_OFF
                ? "你的账号在其他设备上进行登录"
                : "网络连接断开";
        Modal.error({
            okText: "重新登录",
            title,
            content,
            onOk: props.login,
        });
    }, [props.loginStatus]);

    const loading =
        props.themeChatLoading ||
        ![LOGIN_STATUS.SUCCESS, LOGIN_STATUS.KICK_OFF].includes(
            props.loginStatus
        );

    return (
        <Spin
            spinning={loading}
            tip={props.themeChatLoading ? "加载中..." : LOGIN_STATUS_TEXT[props.loginStatus]}
        >
            {props.children}
        </Spin>
    );
};

export default connect(mapStateToProps, mapDispatchToProps)(LoginWrapper);
