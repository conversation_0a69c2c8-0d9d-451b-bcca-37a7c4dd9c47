.preview-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    background-color: rgba(43,43,43,0.8);
    z-index: 999;
    .preview-panel-close {
        position: absolute;
        top: 8rem;
        right: 20px;
        z-index: 2;
        height: 80px;
        width: auto;
        cursor: pointer;
        width: 50px;
        font-size: 4rem;
        color: white;
    }
    .preview-panel-main {
      position: relative;
      top: 2.5%;;
      width: 85%;
      height: 95%;
      display: block;
      margin: auto auto;
      overflow: hidden;
      background: none;
      border: none;
      outline: none;
      .preview-panel-vedio {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        video {
          width: 300px;
        }
      }
      .preview-panel-image {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
}