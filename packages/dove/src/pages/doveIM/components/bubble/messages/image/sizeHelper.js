import {parseUrl} from '../../../utils/handleUrl';

export const BUBBLE_IMG_MIN_WIDTH = 40;
export const BUBBLE_IMG_MIN_HEIGHT = 40;
export const BUBBLE_IMG_MAX_WIDTH = (window).innerWidth - 120;//todo 图片宽度适配
export const BUBBLE_IMG_MAX_HEIGHT = 300;

export function getImgBubbleSize({
    width,
    height,
    orientation = 1,
    minHeight = BUBBLE_IMG_MIN_HEIGHT,
    maxHeight = BUBBLE_IMG_MAX_HEIGHT,
    minWidth = BUBBLE_IMG_MIN_WIDTH,
    maxWidth = BUBBLE_IMG_MAX_WIDTH}) {
    let MAX_WIDTH, MAX_HEIGHT, MIN_WIDTH, MIN_HEIGHT, MAXscale, MINscale;
    MAX_WIDTH = maxWidth || BUBBLE_IMG_MAX_WIDTH;
    MAX_HEIGHT = maxHeight || BUBBLE_IMG_MAX_HEIGHT;
    MAXscale = MAX_WIDTH / MAX_HEIGHT;
    MIN_WIDTH = minWidth || BUBBLE_IMG_MIN_WIDTH;
    MIN_HEIGHT = minHeight || BUBBLE_IMG_MIN_HEIGHT;
    MINscale = MIN_WIDTH / MIN_HEIGHT;

    const picScale = width / height;
    if (orientation >= 5) {//对于旋转打横的图片的长宽限制
        [MAX_WIDTH, MAX_HEIGHT] = [MAX_HEIGHT, MAX_WIDTH];
        [MIN_WIDTH, MIN_HEIGHT] = [MIN_HEIGHT, MIN_WIDTH];
    }
    if (width && height && (height > MAX_HEIGHT || width > MAX_WIDTH)) {
        if (picScale > MAXscale) {
            height = MAX_WIDTH * height / width;
            width = MAX_WIDTH;
        } else if (picScale === MAXscale) {
            width = MAX_WIDTH;
            height = MAX_HEIGHT;
        } else {
            width = MAX_HEIGHT * width / height;
            height = MAX_HEIGHT;
        }
    }
    if (width && height && (width < MIN_WIDTH || height < MIN_HEIGHT)) {
        if (picScale > MINscale) {
            width = width * MIN_HEIGHT / height;
            height = MIN_HEIGHT;
        } else if (picScale === MINscale) {
            width = MIN_WIDTH;
            height = MIN_HEIGHT;
        } else {
            height = height * MIN_WIDTH / width;
            width = MIN_WIDTH;
        }
    }
    return {
        width: parseInt(width + '') || '',
        height: parseInt(height + '') || ''
    };
}

export function getImgSizeAndOtFromUrl(url = '') {
    const URL = parseUrl(url, {});
    let width, height, orientation;
    if (Object.keys(URL.param).length >= 1) {
        height = URL.param.h;
        width = URL.param.w;
        orientation = URL.param.ot;
    }
    return {
        width,
        height,
        orientation
    };
}

//根据url获取气泡页图片尺寸
export function getBubbleImgSizeAndOtFromUrl({
    url,
    minHeight = BUBBLE_IMG_MIN_HEIGHT,
    maxHeight = BUBBLE_IMG_MAX_HEIGHT,
    minWidth = BUBBLE_IMG_MIN_WIDTH,
    maxWidth = BUBBLE_IMG_MAX_WIDTH,
}) {
    const {width, height, orientation} = getImgSizeAndOtFromUrl(url);
    if (width || height) {
        const {width: new_w, height: new_h} = getImgBubbleSize({width, height, orientation, minHeight, maxHeight, minWidth, maxWidth});
        return {
            width: new_w || 0,
            height: new_h || 0,
            orientation: orientation || 1
        };
    } else {
        return {
            width: 0,
            height: 0,
            orientation: 1
        }
    }
}



/**
 * 根据宽高/旋转，获取图片容器的size
 * @param width
 * @param height
 * @param orientation
 */
export function getImgWrapSize({
    width = 0,
    height = 0,
    orientation = 1,
    minHeight = BUBBLE_IMG_MIN_HEIGHT,
    maxHeight = BUBBLE_IMG_MAX_HEIGHT,
    minWidth = BUBBLE_IMG_MIN_WIDTH,
    maxWidth = BUBBLE_IMG_MAX_WIDTH,
} = {
        width: 0,
        height: 0,
        orientation: 1
    }) {
    let wrapHeight, wrapWidth;
    if (orientation >= 5) {//长宽交替
        [wrapHeight, wrapWidth] = [width, height];
    } else {
        [wrapHeight, wrapWidth] = [height, width];
    }

    //限制容器尺寸用于容器中心点和图片中心重合计算
    wrapWidth = Math.min(wrapWidth, maxWidth);//引用消息最大限制不同
    wrapHeight = Math.min(wrapHeight, maxHeight);
    wrapWidth = Math.max(wrapWidth, minWidth);
    wrapHeight = Math.max(wrapHeight, minHeight);

    return {wrapWidth, wrapHeight};
}
