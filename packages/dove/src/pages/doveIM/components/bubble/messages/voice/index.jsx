'use strict';
import React, {Component} from 'react';
import { SoundOutlined, NotificationOutlined } from '@ant-design/icons';
import { connect } from 'react-redux';
import { formatDuration } from '../../../utils/timeRelated';
import MessageBox from '../messageBox';
import classnames from 'classnames';
import ErrorMessage from '../errorMessage';
import { playAudio } from '../../../../redux/actions/message';
import './index.scss';

const mapStateToProps = (state) => ({
    audio: state.message.get('messageAudio')
  })

const mapDispatchToProps = {
    playAudio
}

class VoiceMessage extends Component {
    constructor(props) {
        super(props);
        this.timer = 0;
        this.fromSource = 'bubblePanel';
        const {message} = this.props;
        this.state = {
            isPlay: false,
            duration: message.body.duration
        };
    }

    componentWillReceiveProps(nextProps) {
        let {message} = this.props;
        this.controlLoop(nextProps.audio, message.body.url);
    }

    controlLoop(audio, messageUrl) {
        let {isPlay} = this.state;
        if (audio.src === `${messageUrl}?type=audio&format=mp3`) {
            if (!isPlay) {
                audio.play();
            }
        } else {
            this.setState({ isPlay: false });
        }
    }

    switchVoice() {
        let { isPlay } = this.state;
        if (isPlay) {
            this.stopVoice.call(this);
        } else {
            this.playVoice.call(this);
        }
    }

    stopVoice = () => {
        this.setState({isPlay: false});
        this.props.audio.pause();
    }

    playVoice() {
        let {message, playAudio} = this.props;
        let url = `${message.body.url}?type=audio&format=mp3`;
        playAudio(url, this.playEnd);
        this.setState({isPlay: true});   
    }

    playEnd = () => {
        this.setState({isPlay: false});
    }

    /**
     * 渲染气泡页的语音消息
     */
    renderAudioInBubble = () => {
        let {duration, isPlay} = this.state;
        return (
            <div className="autid-content">
                { isPlay ? <SoundOutlined className='left-audio-icon'/> : <NotificationOutlined className='left-audio-icon'/>}
                <span className="audio-time">{formatDuration(duration)}</span>
            </div>
        );
    };

    _render() {
        let {isMe, fromSource} = this.props;
        let {isPlay} = this.state;
        let className = classnames({
            'message-inner': true,
            'ico-voice': true,
            [fromSource]: fromSource,
            'me': isMe === true,
            'you': isMe === false,
            'isplay': isPlay === true
        });
        return (
            <MessageBox className={className} onClick={this.switchVoice.bind(this)}>
                {this.renderAudioInBubble()}
            </MessageBox>
        );
    }

    render() {
        try {
            return this._render();
        }catch(e) {
            return (<ErrorMessage message={this.props.message} error={e}/>);
        }
    }
}

export default connect(mapStateToProps,mapDispatchToProps)(VoiceMessage);
