import * as React from 'react';
import classnames from 'classnames';
import { countFileSize, getMsgFileJudge } from './profileHelper';
import MessageBox from '../messageBox';
import './index.scss';

export default class FileMessage extends React.Component {

    constructor(props) {
        super(props);
    }

    handleClick(e) {
        const { onClick, message } = this.props;
        onClick && onClick(e, message);
    }

    stopPropagation(e) {
        e.stopPropagation();
    }

    render() {
        const { message, className = '', renderOptBtn, onContextMenu } = this.props;

        const cls = classnames({
            'message-body-file': true,
            [className]: className
        });

        const fileIcon = getMsgFileJudge(message.body);

        const clsName = classnames({
            'file-icon': true,
            [fileIcon]: fileIcon
        });
        const size = countFileSize(message.body.size);
        let fileNameLf = '', fileNameRi = '';
        if (message.body.name.length <= 10) {//大概10个汉字会占满
            fileNameLf = message.body.name;
            fileNameRi = '';
        } else {
            fileNameLf = message.body.name.substr(0, message.body.name.length - 8);
            fileNameRi = message.body.name.substr(message.body.name.length - 8, 8);
        }

        return (
            <MessageBox
                onContextMenu={onContextMenu}
                className={cls}
                onClick={this.handleClick.bind(this)}
            >
                <div className="file-content">
                    <div className={clsName} />
                    <div className="file-info">
                        <div className="file-name" title={message.body.name}>
                            <span className="file-name-left">
                                {fileNameLf}
                            </span>
                            <span className="file-name-right">
                                {fileNameRi}
                            </span>
                        </div>
                        <div className="file-profile">
                            <span className="file-size">{size}</span>
                            <div className="file-opt-btn">
                                <div className="file-opt-download"
                                    onClick={this.stopPropagation}
                                >
                                    {renderOptBtn && renderOptBtn(message)}
                                    <a href={message.body.url}>下载</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </MessageBox>);
    }
}
