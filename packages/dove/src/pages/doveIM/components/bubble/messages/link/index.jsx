'use strict';
import * as React from 'react';
import classnames from 'classnames';
import { escapeHtml } from '../../../utils/handleUrl';
import DEFAULT_LINK_IMG from './defaultLinkImg.png';
import DEFAULT_IMG from './defaultImg.png';
import { isMergeForwardMsg } from '../../../utils/messageUtil';
import MessageBox from '../messageBox';
import Img from '../Img';
import './index.scss';
import { message } from 'antd';

export default class LinkMessage extends React.Component {
    constructor(props) {
        super(props);
    }

    handleClick(e) {
        const { onClick, message } = this.props;
        if (typeof onClick === 'function') {
            onClick(e, message);
        }
    }

    openLink = link => {
        if (!link) {
            return;
        }

        // 不是https?链接的，不开启跳转
        if (!/^http/.test(link)) {
            message.warning('该链接需要在app内打开');
            return;
        }

        window.open(link);
    };

    renderContent() {
        const { message } = this.props;
        const { body = {} } = message;
        let { content = '', image = '', link } = body;
        const isMergeMessage = isMergeForwardMsg(message);
        let defaultImage = DEFAULT_IMG;
        /**
         * 合并转发的时候格式化处理链接消息
         */
        if (isMergeMessage) {
            if (content) {
                const items = content.split('\n');
                const newItems = [];
                if (items && items.length) {
                    items.forEach((item, index) => {
                        if (item) {
                            newItems.push(
                                <div key={index} className="merge-message-item">
                                    {escapeHtml(item)}
                                </div>,
                            );
                        }
                    });
                    content = newItems;
                    defaultImage = DEFAULT_LINK_IMG;
                }
            }
        }
        const linkImg = (
            <div className="message-link-image">
                <div className="message-link-image-placeholder"></div>
                <Img
                    src={image}
                    placeholderSrc={defaultImage}
                    errorSrc={defaultImage}
                />
            </div>
        );

        return (
            <div
                className="message-link-content"
                onClick={() => this.openLink(link)}
            >
                <h4 className="message-link-title">
                    <span className="message-link-title-inner">
                        {message.body.title}
                    </span>
                </h4>
                <div className="message-link-info">
                    <div className="message-link-info-wrap">
                        {linkImg}
                        <div className="message-link-content">{content}</div>
                    </div>
                </div>
            </div>
        );
    }

    render() {
        const { message, onContextMenu } = this.props;
        const isMergeMessage = isMergeForwardMsg(message);
        const className = classnames({
            'message-body-link': true,
            'merge-message': isMergeMessage,
        });
        return (
            <MessageBox
                className={className}
                onClick={this.handleClick.bind(this)}
                onContextMenu={onContextMenu}
            >
                {this.renderContent()}
            </MessageBox>
        );
    }
}
