.message-body-file {
    cursor: pointer;
    margin: 0;
    padding: 10px;
    box-sizing: border-box;
    background:#FFF;
    flex:auto;
    max-width: 100%;
    .file-name {
        a {
            color: rgba(0,0,0,.87);
            text-decoration: none;
        }
    }

    .file-content {
        position: relative;
        display: flex;
        flex-wrap: nowrap;
        .file-icon {
            flex:0 0 40px;
            width: 40px;
            height: 40px;
            margin-right: 10px;
            vertical-align: top;
            background-size: contain;
            background-image: url('./fileTypeIcon/txt.png');
            &.zip {
                background-image: url('./fileTypeIcon/zip.png');
            }
            &.txt {
                background-image: url('./fileTypeIcon/txt.png');
            }
            &.doc {
                background-image: url('./fileTypeIcon/word.png');
            }
            &.excel {
                background-image: url('./fileTypeIcon/excel.png');
            }
            &.ppt {
                background-image: url('./fileTypeIcon/ppt.png');
            }
            &.pdf {
                background-image: url('./fileTypeIcon/pdf.png');
            }
            &.ps {
                background-image: url('./fileTypeIcon/ps.png');
            }
            &.mp3 {
                background-image: url('./fileTypeIcon/audio.png');
            }
            &.mp4 {
                background-image: url('./fileTypeIcon/video.png');
            }
            &.ai {
                background-image: url('./fileTypeIcon/ai.png');
            }
            &.pic {
                background-image: url('./fileTypeIcon/pic.png');
            }
            &.key {
                background-image: url('./fileTypeIcon/key.png');
            }
            &.numbers {
                background-image: url('./fileTypeIcon/numbers.png');
            }
            &.pages {
                background-image: url('./fileTypeIcon/pages.png');
            }
            &.sketch {
                background-image: url('./fileTypeIcon/sketch.png');
            }
            &.common {
                background-image: url('./fileTypeIcon/txt.png');
            }
            &.coauthor {
                background-image: url('./fileTypeIcon/coauthor/online-file-big.png');
            }
            &.mind {
                background-image: url('./fileTypeIcon/coauthor/mind.png');
            }
            &.mind-online {
                background-image: url('./fileTypeIcon/coauthor/mind-online.png');
            }
            &.overdue {
                background-image: url('./fileTypeIcon/overdue_file.png');
            }
        }
        .file-info {
            height: 40px;
            box-sizing: border-box;
            flex:1 1 auto;
            display:flex;
            flex-direction:column;
            justify-content: space-between;
            overflow: hidden;
            .file-name {
                flex:0 0 22px;
                height:22px;
                line-height: 22px;
                font-size: 14px;
                vertical-align: top;
                display: flex;
                flex-wrap: nowrap;
                align-items:center;
                .file-name-left{
                    display: inline-block;
                    text-overflow:ellipsis;
                    white-space:nowrap;
                    overflow:hidden;
                    vertical-align: top;
                    flex:0 1 auto;
                }
                .file-name-right{
                    vertical-align: top;
                    flex:0 0 auto;
                }
                .file-security-classification{
                    flex:0 0 25px;
                    margin-left:6px;
                    position: relative;
                    margin-bottom:-1px;
                }
            }

            .file-profile{
                flex:0 0 18px;
                height: 18px;
                line-height: 18px;
                position: relative;
                vertical-align: middle;
                font-size: 0;
                span{
                    vertical-align: middle;
                }
                .file-size {
                    line-height: 18px;
                    display: inline-block;
                    font-size: 12px;
                    color: rgba(0,0,0,.38);
                }

                .file-opt-btn {
                    display: inline;
                    position: absolute;
                    line-height:18px;
                    vertical-align: middle;
                    right: 10px;
                    font-size: 14px;
                    .file-msg-btn{
                        font-size: 14px;
                        display: inline-block;
                        height: 18px;
                        line-height:18px;
                        color: #3974CC;
                        cursor: pointer;
                        &.preview-file-btn, &.download-file-btn {
                            margin-right: 10px;
                        }
                    }

                    a{
                      text-decoration: none;
                    }
                }
            }
        }
    }
}
