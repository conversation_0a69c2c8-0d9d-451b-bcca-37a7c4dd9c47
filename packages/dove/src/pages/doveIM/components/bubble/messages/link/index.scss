@import "../../../../assets/style/index";

.message-body-link {
  width: 100%;
  cursor: pointer;
  background-color: $color-lightest;
  .message-link-title {
    font-size: $font-size-base;
    margin: 0 0 10px 0;
    padding: 10px 10px 0 10px;
    font-weight: normal;
    &-inner {
      display: block;
      text-decoration: none;
      color: $color-darker;
      word-break: break-all; //为了链接消息title为特长url的情况
    }
  }
  .merge-message-info {
    padding-bottom: 10px;
  }
  .message-link-info {
    padding: 0 10px;
    .message-link-info-wrap {
      display: block;
      text-decoration: none;
      overflow: hidden; //content margin溢出
      .message-link-image {
        position: relative;
        display: inline-block;
        width: 100%;
        overflow: hidden;
        font-size:0;
        border: 1px solid $color-light-1;
        box-sizing: border-box;
        text-align: center;
        .message-link-image-placeholder{
            padding-top:55.55555%;//长宽比9：5
        }
        img {
          position: absolute;
          margin: auto;
          top:0;
          bottom:0;
          left:0;
          right:0;
          width: 100%;
        }
      }
      .message-link-content {
        @include multextoverflow(2);
        color: $color-base;
        font-size: $font-size-small;
        margin: $ctn-padding-large 0;
        line-height: 1.5;
      }
    }
  }

  &.merge-message {
    background-color: #fff;
    width: 100%;
    box-sizing: border-box;
    padding:0px 5px 10px 5px;
    .message-link-title {
      margin-bottom: 10px;
      font-weight: normal;
      &-inner {
        display: block;
        text-decoration: none;
        color: $color-darker;
        @include textoverflow();
      }
    }
    .message-link-info {
      &-wrap {
        display: flex;
        text-decoration: none;
        .message-link-image {
          display: inline-block;
          vertical-align: top;
          border:none;
          height: 40px;
          width: 40px;
          img {
            width: 40px;
            height: 40px;
          }
        }
        .message-link-content {
          display: block;
          flex: 1;
          color: rgba(0, 0, 0, .38);
          margin: 0 0 0 10px;
          font-size: 12px;
          .merge-message-item {
            @include textoverflow();
          }
        }
      }
    }
  }
}
