import {
    isImg,
    isPS,
    isAI,
    isMSDoc,
    isMSExcel,
    isMSPPT,
    isPDF,
    isAudio,
    isVideo,
    isText,
    isZip,
    isKey,
    isNumbers,
    isPages,
    isSketch,
    isMindMap,
    isGraph,
    isXmdoc,
    isCoauthorMd,
    isCoauthorMind
} from '../../../utils/fileTypeJudge';

export function countFileSize(size) {
    let units = ['B', 'KB', 'MB', 'GB'];
    let result = size;
    for (let i = 0; i < 4; i++) {
        if (result < 1024) {
            result = result.toFixed(1) + units[i];
            break;
        }
        result /= 1024;
    }
    return result;
}


//给气泡页用于展示文件类型ICON的方法
export function getMsgFileJudge(body) {
    //协作文档的url示例: http://file-test.neixin.cn/pan/im/1/file/AQgA1PtcYzpb_PAKXwAAElX88Apw?mak=1
    let {name = ''} = body;
    let _type = getFileJudge(false, name);
    
    //不再依赖mak参数判定类型(mak是mAppKey的替代品, 与mAppKey共存亡)
    //let _query = Query.parse(URL.parse(url).query || '');
    // if( _query.mak === '1' ){
    //     _type = 'coauthor';
    // }else if(_query.mak === '2'){
    //     _type = 'mind-online';
    // }else if(_query.mak === '3' && isDXMember()){
    //     _type = 'coauthor';
    // }
    
    return _type;
};


export function getFileJudge(isDir, name) {
    let category;
    if (!!isDir) {
        category = 'folder';
    } else {
        name = !!name ? name.toLocaleLowerCase() : '';
        if (isXmdoc(name)) {
            category = 'coauthor';
        } else if (isCoauthorMind(name)) {
            category = 'mind-online';
        } else if (isCoauthorMd(name)) {
            category = 'coauthor';
        } else if (isZip(name)) {
            category = 'zip';
        } else if (isText(name)) {
            category = 'txt';
        } else if (isMSDoc(name)) {
            category = 'doc';
        } else if (isMSExcel(name)) {
            category = 'excel';
        } else if (isMSPPT(name)) {
            category = 'ppt';
        } else if (isPDF(name)) {
            category = 'pdf';
        } else if (isPS(name)) {
            category = 'ps';
        } else if (isAudio(name)) {
            category = 'mp3';
        } else if (isVideo(name)) {
            category = 'mp4';
        } else if (isAI(name)) {
            category = 'ai';
        } else if (isImg(name)) {
            category = 'pic';
        } else if (isKey(name)) {
            category = 'key';
        } else if (isNumbers(name)) {
            category = 'numbers';
        } else if (isPages(name)) {
            category = 'pages';
        } else if (isSketch(name)) {
            category = 'sketch';
        } else if (isMindMap(name)) {
            category = 'mind';
        } else if (isGraph(name)) {
            category = 'graph';
        } else {
            category = 'common';
        }
    }
    return category;
};
