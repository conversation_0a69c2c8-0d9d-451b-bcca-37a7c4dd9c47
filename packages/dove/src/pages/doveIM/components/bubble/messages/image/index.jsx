import * as React from 'react';
import './index.scss';
import classnames from 'classnames';
import { getImgBubbleSize, getBubbleImgSizeAndOtFromUrl, getImgWrapSize } from './sizeHelper';
import Img from '../Img';
import MessageBox from '../messageBox';
import PreviewPanel from '../previewPanel';

export default class ImageMessage extends React.Component {
    constructor(props) {
        super(props);
        this.orginalSrc = '';
        this.sizeLimit = {};
        this.isUnmounted = false;
        const { message } = props;
        try {
            this.orginalSrc = message.body.normal.trim();
        } catch (e) {
            this.orginalSrc = '';
        }

        const { width: specWidth, height: specHeight, maxWidth = 320, minWidth = 160, maxHeight = 320, minHeight = 160 } = this.props;

        this.sizeLimit = {
            maxWidth, minWidth, maxHeight, minHeight
        }
        //用户直接指定尺寸
        if (specHeight || specWidth) {
            this.state = {
                width: specWidth,
                height: specHeight,
                orientation: 1,
                renderImgUrl: this.orginalSrc
            }
        } else {
            const { width, height, orientation = 1 } = getBubbleImgSizeAndOtFromUrl({
                url: this.orginalSrc,
                ...this.sizeLimit
            });
            if (width && height) {
                this.state = {
                    width,
                    height,
                    orientation,
                    renderImgUrl: this.orginalSrc,
                };
            } else {
                this.state = {
                    width: 0,
                    height: 0,
                    orientation,
                    renderImgUrl: this.orginalSrc,
                };
                this.updateSize(this.orginalSrc);
            }
        }

        this.state = { ...this.state, loaded: false, loadError: false, visible: false };
    }

    //对于没有尺寸的图片获取到尺寸后重新赋值
    updateSize(url) {
        const { width, height } = this.state;
        if (width && height) {
            return;
        }

        return new Promise((resolve) => {
            const img = new Image();
            img.onload = () => {
                const { width, height } = getImgBubbleSize({ width: img.width, height: img.height, ...this.sizeLimit });
                !this.isUnmounted && this.setState({
                    width,
                    height
                });
                resolve();
            };
            img.onerror = () => {//这里图片取错什么都不做，由Img组件的onError重新获取url。
                resolve();
            };
            img.src = url;
        });
    };

    onMsgClick(e, type) {
        const { loadError } = this.state;
        if (loadError) {
            this.reloadImg();
        } else {
            const { onClick, message } = this.props;
            this.setState({ previewPanelvisible: true });
            onClick && onClick(e, message);
        }
    }

    //重试加载图片
    reloadImg() {
        this.setState({
            loaded: false,
            loadError: false
        })
    }

    componentWillUnMount(){
        this.isUnmounted = true;
    }

    onImgLoaded() {
        const { renderImgUrl } = this.state;
        if (renderImgUrl) {
            !this.isUnmounted && this.setState({
                loaded: true
            });
        }
    }

    onImgloadError() {
        const { renderImgUrl } = this.state;
        if (renderImgUrl) {
            !this.isUnmounted && this.setState({
                loaded: true,
                loadError: true
            });
        }
    }

    render() {
        const { className, onContextMenu } = this.props;
        const { renderImgUrl, width, height, orientation = 1, loaded, loadError, previewPanelvisible } = this.state;

        let { wrapHeight, wrapWidth } = getImgWrapSize({ width, height, orientation, ...this.sizeLimit });

        const wrapperClassName = classnames({
            'message-image-orientation-wrap': true,
            ['orientation-' + orientation]: true
        });
        return (
            <React.Fragment>
                <MessageBox
                    onContextMenu={onContextMenu}
                    className={`message-body-image ${className || ''}`}
                    onClick={(e) => {
                        this.onMsgClick(e, 'message');
                    }}
                >
                    {(!loaded || loadError) && (
                        <div className='msg-image-placeholder-wrapper'>
                            {loadError ? (
                                <div className='msg-image-placeholder  img-load-error'></div>
                            ) : (
                                    <div className='msg-image-placeholder  img-loading'></div>
                                )}
                        </div>
                    )}
                    <div
                        style={{
                            width: wrapWidth + 'px',
                            height: wrapHeight + 'px'
                        }}
                        className={wrapperClassName}>
                        {!loadError && (
                            <Img
                                className={'message-body-image-image'}
                                src={renderImgUrl}
                                onError={this.onImgloadError.bind(this)}
                                onLoad={this.onImgLoaded.bind(this)}
                                style={{
                                    width: width + 'px',
                                    height: height + 'px',
                                    top: (wrapHeight - height) / 2 + 'px',//用于图片和容器中心点对齐
                                    left: (wrapWidth - width) / 2 + 'px',//用于图片和容器中心点对齐
                                    visibility: `${loaded ? 'visible' : 'hidden'}`
                                }}
                            />
                        )}
                    </div>
                </MessageBox>
                <PreviewPanel
                    source={renderImgUrl}
                    visible={previewPanelvisible}
                    type='image'
                    onClose={() => {
                        this.setState({ previewPanelvisible: false });
                    }}
                />
            </React.Fragment>
        );
    }
}
