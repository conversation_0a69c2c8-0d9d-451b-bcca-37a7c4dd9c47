.message-body-image {
    flex:0 1 auto;
    position: relative;
    cursor: pointer;
    background:rgb(242,242,242);
    .msg-image-placeholder-wrapper{
        position: absolute;
        width:100%;
        height:100%;
        overflow: hidden;
        background: #f2f2f2;
        box-sizing: border-box;
        .msg-image-placeholder{
            position: absolute;
            width:100%;
            height:100%;
            left:0; 
            right:0; 
            top:0; 
            bottom:0;
            margin:auto;
            &.img-loading{
                max-width: 150px;
                max-height: 150px;
                background: url(../../../../assets/img/defaultImg.png) center no-repeat;
            }
            &.img-load-error{
              max-width: 70px;
              max-height: 70px;
              background: url(../../../../assets/img/errorImg.png) center/contain no-repeat;
            }
        }
    }

    .message-image-orientation-wrap {
        // min-width: 40px;
        // min-height: 40px;
        // max-width: 370px;
        // max-height: 300px;
        position: relative;
        overflow: hidden;
        .message-body-image-image {
            position: absolute;
            transform-origin: 50% 50%;
            box-sizing: border-box;
            width: auto;
        }

        &.orientation-2 {
            .message-body-image-image {
                transform: rotateX(180deg);
            }
        }

        &.orientation-3 {
            .message-body-image-image {
                transform: rotate(180deg);
            }
        }

        &.orientation-4 {
            .message-body-image-image {
                transform: rotate(180deg) rotateX(180deg);
            }
        }

        &.orientation-5 {
            .message-body-image-image {
                transform: rotate(90deg) rotateX(180deg);
            }
        }

        &.orientation-6 {
            .message-body-image-image {
                transform: rotate(90deg);
            }
        }

        &.orientation-7 {
            .message-body-image-image {
                transform: rotate(-90deg) rotateX(180deg);
            }
        }

        &.orientation-8 {
            .message-body-image-image {
                transform: rotate(-90deg);
            }
        }
    }
}