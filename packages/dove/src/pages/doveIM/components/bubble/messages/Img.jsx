import * as React from 'react';

function noop() {
};

export default class Img extends React.Component {

    constructor(props) {
        super(props);
        this.state = {
            src: props.placeholderSrc || props.src,
        };
        this.unmounted = undefined;
    }

    dealWithSrc(forceSrc) {
        let {src, loadingSrc, errorSrc, onError, onLoad} = this.props;
        
        if(forceSrc != undefined){
            src = forceSrc;
        }
        
        if (loadingSrc) {
            this.setState({
                src: loadingSrc
            })
        }
        const img = new Image();
        img.onload = () => {
            if (!this.unmounted) {
                this.setState({src: src});
                onLoad && onLoad();
            }
        };
        
        img.onerror = () => {
            if (!this.unmounted) {
                if (errorSrc) {
                    this.setState({
                        src: errorSrc
                    })
                }
                onError && onError();
            }
        };
        
        img.src = src || "";//no src will trigger onError,just like browser default
        
        if (!src) {
            this.setState({//no src no default img,just like browser default
                src: ""
            });
        }
    }
    
    componentDidMount() {
        this.dealWithSrc();
    }
    
    componentWillReceiveProps(nextProps) {
        if (nextProps.src !== this.props.src) {
            this.dealWithSrc(nextProps.src || "");
        }
    }
    
    componentDidUpdate() {
    
    }
    
    componentWillUnmount() {
        this.unmounted = true;
    }
    
    render() {
        const {placeholderSrc, loadingSrc, errorSrc,...props} = this.props;
        return (
            <img
                {...props}
                src={this.state.src}
                onError={noop}
                onLoad={noop}
                ref={noop}
            />
        )
    }
}