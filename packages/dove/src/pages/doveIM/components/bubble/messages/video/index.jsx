import * as React from 'react';
import PreviewPanel from '../previewPanel';
import { PlayCircleOutlined, DownloadOutlined } from '@ant-design/icons';
import MessageBox from '../messageBox';
import Img from '../Img';
import './index.scss';

export default class VideoMessage extends React.Component {

    constructor(props) {
        super(props);
        this.state = {
            visible: false
        }
    }

    handleDownload() {
        // 下载视频
        location.href = this.props.message.body.videoUrl;
    }

    handlePlay() {
        this.setState({visible: true});
    }

    render() {
        return (
            <MessageBox>
                <div className='message-video'>
                    <Img src={this.props.message.body.screenshotUrl} style={{
                                'maxWidth': 270 + 'px'
                            }}/>
                    <PlayCircleOutlined className='message-video-play' onClick={this.handlePlay.bind(this)}/>
                    <DownloadOutlined className='message-video-download' onClick={this.handleDownload.bind(this)}/>
                </div>
                <PreviewPanel
                    source={this.props.message.body.videoUrl}
                    visible={this.state.visible}
                    type='video'
                    onClose={() => {
                    this.setState({ visible: false });
                }}/>
            </MessageBox>
        );
    }
}
