import React, { Component } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import './index.scss';

export default class PreviewPanel extends Component {
    constructor(props) {
        super(props);
        this.state = {
          visible: false
        }
    }

    componentWillReceiveProps(nextProps) {
        if (nextProps.visible !== this.props.visible) {
            this.setState({visible: nextProps.visible});
        }
    }

    handleClose() {
        this.props.onClose();
    }

    render() {
        return this.state.visible ? (
            <div className='preview-panel'>
              <div className='preview-panel-main'>
                { this.props.type === 'video' ? (
                  <div className='preview-panel-vedio'>
                    <video src={this.props.source} controls="controls">
                      your browser does not support the video tag
                    </video>
                  </div>
                ) : (
                  <div className='preview-panel-image'>
                    <img src={this.props.source}></img>
                  </div>
                )}
              </div>
              <div className='preview-panel-close'>
                <CloseOutlined onClick={(e) => {
                  this.handleClose()
                }}/>
              </div>
            </div>
        ) : null;
    }
}
