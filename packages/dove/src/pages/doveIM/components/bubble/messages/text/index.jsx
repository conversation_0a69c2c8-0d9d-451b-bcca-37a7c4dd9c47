import * as React from 'react';
import classNames from 'classnames';
import parseMsgText from './textParse';
import {createMarkup} from '../../../utils/handleUrl';
import MessageBox from '../messageBox';
import './index.scss';

export default class TextMessage extends React.Component {

    constructor(props) {
        super(props);
    }

    handleClick(e) {
        const node = e.target;
        if (node.nodeName === 'A') {
            const {message,onLinkClick} = this.props;
            const url = node.href;
            if(onLinkClick){
                onLinkClick(e,url,message);
            }
        }
    }

    render() {
        const {message, className = '',onContextMenu} = this.props;

        const commonText = parseMsgText(message.body.text);

        const classes = classNames({
            'message-body-text': true,
            [className]: className
        });

        return (
            <MessageBox 
                 className={classes}
                 onClick={this.handleClick}
                 onContextMenu={onContextMenu}
            >
                <div
                  dangerouslySetInnerHTML={createMarkup(commonText)}
                />
            </MessageBox>
        );
    }
}
