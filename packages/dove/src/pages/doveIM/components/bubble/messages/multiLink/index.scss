.message-multilink-single {
  padding: 10px 10px 0 10px;
  cursor: pointer;
}

.message-multilink-single-title {
  margin-bottom: 10px;
}

.message-multilink-single-keyword {
  color: rgba(0,0,0,0.38);
  font-size: 12px;
  margin: 10px 0;
}

.message-multilink-single-image {
  width: 100%;
}


.message-multilink {
  border: 1px #EEE solid;
  background-color: #fff !important;
  img: {
    width: 100%;
  }
  // 多消息第一个
  .message-multilink-multi {
    display: block;
    position: relative;
    padding: 10px 9px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }
  .message-multilink-multi-container {
    position: relative;
    padding: 0 0 10px 0;
    &:hover {
      cursor: pointer;
    }
  }
  .message-multilink-multi-image {
    display: inline-block;
    height: 108px;
    width: 252px;
    margin: 0;
    vertical-align: top;
  }
  .message-multilink-multi-title {
    box-sizing: border-box;
    display: block;
    position: absolute;
    bottom: 9px;
    height: 30px;
    width: 252px;
    line-height: 30px;
    padding: 0 10px;
    background-color: rgba(0,0,0,0.7);
    color: #fff;
    font-size: 14px;
    display: -webkit-box;
    display: -moz-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    word-break: break-all;
  }
  // 多消息其他
  .message-multilink-other {
    display: block;
    width: 252px;
    height: 40px;
    padding: 10px 0;
    line-height: 20px;
    border-top: 1px solid rgba(0,0,0,0.05);
    font-size: 14px;
    div {
      width: 100%;
      height: 100%;
      display: -ms-flexbox;
      display: flex;
      -ms-flex-align: center;
      align-items: center;
      -ms-flex-pack: justify;
      justify-content: space-between;
      -ms-flex-direction: row-reverse;
      flex-direction: row-reverse;
      img {
        float: right;
        left: 200px;
        width: 40px;
        height: 40px;
        vertical-align: top;
        padding: 10px;
      }
      span {
        display: -webkit-box;
        display: -moz-box;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        word-break: break-all;
        width: 201px;
        color: rgba(0,0,0,0.87);
      }
    }
  }

}