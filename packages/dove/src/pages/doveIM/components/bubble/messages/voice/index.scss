$color-lightest: #123;
$color-base: rgba(0,0,0,0.6);
$font-size-xlarge: 12px;
$primary: #222;
$font-size-small: 12px;


.ico-voice {
  min-width: 80px;
  max-width: 220px;
  height: 36px;
  background-color:$color-lightest;
  background-repeat:repeat;
  border-radius: 3px;
  line-height: 36px;
  color: $color-base;
  cursor: pointer;
  .dxicon {
      margin-top: -2px;
      font-size: $font-size-xlarge;
  }
  &.isplay {
      .dxicon, .audio-icon.dxicon {
          color: $primary;
      }
  }
  .left-audio-icon {
      margin-left: 10px;
  }
  .audio-time {
      float: right;
      margin: 0 10px 0 0;
      font-size: $font-size-small;
  }
  &.collect {
      background: none;
  }
}