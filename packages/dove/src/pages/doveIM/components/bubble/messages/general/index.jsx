import * as React from 'react';
import MessageBox from '../messageBox';
import './index.scss';

// const generalMessage = {
//   linkUrl: "http://doveimapi.waimai.test.sankuai.com/doveimp/share/r/page?id=3832&doveimCustomMsgId=1955&doveimSendMethod=1",
//   poiAvatarUrl: "http://p0.meituan.net/waimaipoi/7dddd6b6a179e2223ba98b28be0595a196325.jpg",
//   poiId: "611529",
//   poiName: "洪门看楷涩臣尹_使用中勿动",
//   poiType: 1,
//   shareDesc: "巴拉巴",
//   shareImgUrl: "https://s3plus-img.meituan.net/v1/mss_b2337692cf044b7aac9164ce94e9f90d/doveim-public/doveim_file_2c135271dd9e72694ef85c8a637f8631.jpg",
//   shareName: "hello，群发一下",
//   shareRecordId: 3832,
//   shareTitle: "业务经理分享给您一个通知",
//   type: 3
// }

export default class GeneralMessage extends React.Component {

    constructor(props) {
        super(props);
    }

    render() {
        const generalMessage = JSON.parse(this.props.message.body.data);
        const doveimCustomMsgIdMatches = generalMessage.linkUrl.match(/doveimCustomMsgId=(\d*)/);
        const doveimCustomMsgId = doveimCustomMsgIdMatches ? doveimCustomMsgIdMatches[1] : undefined;
        const linkUrl = `/page/dove/generalDetailPage?messageId=${doveimCustomMsgId}`;
        return doveimCustomMsgId ? (
            <MessageBox className='message-general' onClick={() => {
              window.open(linkUrl);
            }}>
                <div className='general-message'>{generalMessage.shareTitle}</div>
                <div className='general-content'>
                  <img className='general-image' src={generalMessage.shareImgUrl} />
                  <div className='general-text'>
                    <div className='general-text-title'>{generalMessage.shareName}</div>
                    <div className='general-text-desc'>{generalMessage.shareDesc}</div>
                  </div>
                </div>
            </MessageBox>
        ) : '[消息类型无法展示，请在移动端进行查看]';
    }
}
