import * as React from 'react';
import MessageBox from '../messageBox';
import './index.scss';

export default class MultiLinkMessage extends React.Component {

    constructor(props) {
        super(props);
    }

    render() {
        const num = this.props.message.body.num;
        const content = this.props.message.body.content
        return (
            <MessageBox className='message-multilink'>
                { num === 1 ? (
                  <div className='message-multilink-single' onClick={() => {window.open(content[0].link)}}>
                    <div className='message-multilink-single-title'>{ content[0].title }</div>
                    <img className='message-multilink-single-image' src={ content[0].image }/>
                    <div className='message-multilink-single-keyword'>{ content[0].keyword }</div>
                  </div>
                ) : (
                  <div className='message-multilink-multi'>
                    <div className='message-multilink-multi-container' onClick={() => {window.open(content[0].link)}}>
                      <img className='message-multilink-multi-image' src={content[0].image}/>
                      <div className='message-multilink-multi-title'>{content[0].title}</div>
                    </div>
                    {content.slice(1).map((item, key) => (
                      <a className='message-multilink-other' href={item.link} key={key} target='_blank'>
                        <div>
                          <img src={item.image}/>
                          <span>{item.title}</span>
                        </div>
                      </a> 
                    ))}
                  </div>
                )}
            </MessageBox>
        );
    }
}
