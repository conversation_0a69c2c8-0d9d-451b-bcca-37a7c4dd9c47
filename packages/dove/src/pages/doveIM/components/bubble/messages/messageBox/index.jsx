import * as React from 'react';
import classNames from 'classnames';

export default class MessageBox extends React.Component {

    constructor(props) {
        super(props);
    }

    onMsgContextMenu(e) {
        const { onContextMenu } = this.props;
        if (onContextMenu) {
            onContextMenu(e);
        }
    }

    render() {
        const { className = '', children, ...restProps } = this.props;

        const classes = classNames({
            'message-body': true,
        }, className);

        return (
            <div
                {...restProps}
                className={classes}
                onContextMenu={this.onMsgContextMenu.bind(this)}
            >
                <div className="message-body-corner">
                    <div className='message-body-corner-inner'></div>
                </div>
                {children}
            </div>
        );
    }
}
