import { Component } from 'react';
import Avatar from '~/components/dx-web-sdk-ui/avatar';
import { connect } from 'react-redux';
import {
    TextMessage,
    ImageMessage,
    FileMessage,
    LinkMessage,
    ErrorMessage,
    VoiceMessage,
    VideoMessage,
    MultiLinkMessage,
    GeneralMessage,
} from '../messages';
import { MESSAGE_TYPE, MESSAGE_STATUS } from '../../../data/constants';
import EventMsg from '../bubbleList/eventMsg';
import BubbleLink from './BubbleLink';
import './index.scss';

const mapStateToProps = state => {
    return {
        sts: state.session.get('sts'),
    };
};

class BubbleItem extends Component {
    constructor(props) {
        super(props);
        this.state = {
            renderError: false,
        };
    }

    onMessageErrorClick() {
        const { onMessageErrorClick, message } = this.props;
        if (onMessageErrorClick && typeof onMessageErrorClick === 'function') {
            onMessageErrorClick(message);
        }
    }

    onMessageContextMenu(e) {
        const { message, onContextMenu } = this.props;
        if (e.target && onContextMenu) {
            onContextMenu(e, message);
        }
    }

    saveItem(element) {
        const { itemRef, message } = this.props;
        if (itemRef && element) {
            itemRef(element, message);
        }
    }

    componentDidCatch(error, info) {
        const { onMessageRenderError, message } = this.props;
        if (onMessageRenderError) {
            onMessageRenderError(message, error, info);
        }
        this.setState({
            renderError: true,
        });
    }

    renderErrorMessage(messageProps) {
        const { message, renderErrorMessage } = this.props;
        function defaultRenerErrorMsg() {
            return <ErrorMessage {...messageProps} />;
        }
        if (renderErrorMessage) {
            return renderErrorMessage(message, defaultRenerErrorMsg);
        } else {
            return defaultRenerErrorMsg();
        }
    }

    renderMessage() {
        const {
            message,
            onAvatarClick,
            renderCustomMsg,
            onContextMenu,
            className,
            vcard,
            typedMsgProps = {},
            ...restProps
        } = this.props;
        const { renderError } = this.state;
        const messageProps = {
            message,
            onContextMenu: this.onMessageContextMenu.bind(this),
            ...restProps,
        };
        if (renderError) {
            return this.renderErrorMessage(messageProps);
        }
        switch (message.type) {
            case MESSAGE_TYPE.MSG_TYPE_TEXT: {
                return (
                    <TextMessage
                        {...messageProps}
                        {...typedMsgProps[message.type]}
                    ></TextMessage>
                );
            }
            case MESSAGE_TYPE.MSG_TYPE_IMAGE: {
                return (
                    <ImageMessage
                        {...messageProps}
                        {...typedMsgProps[message.type]}
                    ></ImageMessage>
                );
            }
            case MESSAGE_TYPE.MSG_TYPE_FILE: {
                return (
                    <FileMessage
                        {...messageProps}
                        {...typedMsgProps[message.type]}
                    ></FileMessage>
                );
            }
            case MESSAGE_TYPE.MSG_TYPE_LINK: {
                return (
                    <LinkMessage
                        {...messageProps}
                        {...typedMsgProps[message.type]}
                    ></LinkMessage>
                );
            }
            case MESSAGE_TYPE.MSG_TYPE_VOICE: {
                return (
                    <VoiceMessage
                        {...messageProps}
                        {...typedMsgProps[message.type]}
                    ></VoiceMessage>
                );
            }
            case MESSAGE_TYPE.MSG_TYPE_VIDEO: {
                return (
                    <VideoMessage
                        {...messageProps}
                        {...typedMsgProps[message.type]}
                    ></VideoMessage>
                );
            }
            case MESSAGE_TYPE.MSG_TYPE_MULTI_LINK: {
                return (
                    <MultiLinkMessage
                        {...messageProps}
                        {...typedMsgProps[message.type]}
                    ></MultiLinkMessage>
                );
            }
            case MESSAGE_TYPE.MSG_TYPE_GENERAL: {
                // return <GeneralMessage {...messageProps} {...typedMsgProps[message.type]}></GeneralMessage>
                return (
                    <GeneralMessage
                        {...messageProps}
                        {...typedMsgProps[message.type]}
                    ></GeneralMessage>
                );
            }
            default: //todo 支持业务方自行实现
                if (renderCustomMsg) {
                    return renderCustomMsg(message);
                } else {
                    return '[消息类型无法展示，请在移动端进行查看]';
                }
        }
    }

    renderEventMessage = () => {
        const { message } = this.props;

        if (message.type !== MESSAGE_TYPE.MSG_TYPE_EVENT) {
            return null;
        }
        const regex = /(.*?)\[(.*?)\|(.*?)\]/;
        const { text = '', link = '' } = message.body || {};
        const match = text.match(regex);

        let result = {
            text: message.body.text,
            linkText: '',
            link,
        };

        if (match) {
            result.text = match[1];
            result.linkText = match[2];
            result.link = match[3];
        }

        return (
            <EventMsg key={message.mid || message.uuid}>
                <div className="withdraw-msg-tip">
                    {result.text}
                    <BubbleLink {...result} />
                </div>
            </EventMsg>
        );
    };

    render() {
        const { myUid, message, vcard, onAvatarClick, className, sts } =
            this.props;
        const isMe = message.from === myUid;
        const messageStatus = message.status;
        const isMsgErr =
            messageStatus === MESSAGE_STATUS.STATUS_TIMEOUT ||
            message.status === MESSAGE_STATUS.STATUS_SEND_ERR;

        if (message.type === MESSAGE_TYPE.MSG_TYPE_EVENT) {
            return this.renderEventMessage();
        }

        return (
            <div
                ref={this.saveItem.bind(this)}
                className={`${className || ''} bubble-item-wrap ${
                    isMe ? 'me' : ''
                }`}
            >
                <div className="bubble-item">
                    <div className="bubble-item-avatar">
                        <Avatar
                            className="bubble-item-avatar-img"
                            vcard={vcard}
                            onClick={() => {
                                if (typeof onAvatarClick === 'function') {
                                    onAvatarClick({ isMe, from: message.from });
                                }
                            }}
                        />
                    </div>
                    <div className="bubble-item-content">
                        {!isMe && (
                            <div className="bubble-item-sender">
                                {(vcard && vcard.name) ||
                                    message.fromName ||
                                    message.from}
                            </div>
                        )}
                        <div className="bubble-item-msg">
                            {this.renderMessage()}
                            {!!sts && !!isMe && (
                                <div className="message-read-status">
                                    {sts > message.svrTime ? (
                                        '已读'
                                    ) : (
                                        <span>未读</span>
                                    )}
                                </div>
                            )}
                            <div className="bubble-item-msg-send-status">
                                {messageStatus ===
                                    MESSAGE_STATUS.STATUS_SENT && (
                                    <div className="message-loading-icon">
                                        ...
                                    </div>
                                )}
                                {isMsgErr && (
                                    <div
                                        onClick={this.onMessageErrorClick.bind(
                                            this,
                                        )}
                                        className="message-error-icon"
                                    ></div>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

export default connect(mapStateToProps)(BubbleItem);
