import { setThemeModalOpen } from '../../../redux/actions/themeChat';
import { connect } from 'react-redux';
import './index.scss';

const mapStateToProps = () => ({});

const mapDispatchToProps = {
    setThemeModalOpen,
};

const REBUILD_TEXT = 'rebuildSession';

interface BubbleLink {
    link?: string;
    linkText?: string;
    setThemeModalOpen: (v: boolean) => void;
}

const BubbleLink = (props: BubbleLink) => {
    if (!props.link || !props.linkText) {
        return null;
    }

    if (props.link === REBUILD_TEXT) {
        return (
            <a onClick={() => props.setThemeModalOpen(true)}>
                {props.linkText}
            </a>
        );
    }

    return <a href={props.link}>{props.linkText}</a>;
};

export default connect(mapStateToProps, mapDispatchToProps)(BubbleLink);
