.bubble-item-wrap {
  padding: 0 56px 0 10px;
  position: relative;
  box-sizing: border-box;
  margin: 12px 0;
  justify-content: flex-start;

  &.me {
    padding: 0 10px 0 56px;
    .bubble-item {
        flex-direction: row-reverse;  
        .bubble-item-content{
          margin-left:0;
          margin-right: 10px;
          flex-direction: row-reverse;
          .bubble-item-msg{
            flex-direction: row-reverse;
            .message-read-status{
              font-size: 10px;
              padding: 28px 6px 0px;
              span {
                color: #f43530;
              }
            }
            .message-body{
              background-color: #d0e7ff;
              border: 1px solid #c9dfff;
              .message-body-corner{
                position: absolute;
                right:-4px;
                left:auto;
                top:12px;
                width:0;
                height:0;
                border-top:6px solid transparent;
                border-left:4px solid #c9dfff;
                border-bottom:6px solid transparent;
                border-right: 0px solid transparent;
                .message-body-corner-inner{
                  position: absolute;
                  right:1px;
                  left:auto;
                  top:-6px;
                  width:0;
                  height:0;
                  border-top:6px solid transparent;
                  border-left:4px solid #d0e7ff;
                  border-bottom:6px solid transparent;
                  border-right: 0px solid transparent;
                }
              }
            }
          }
        }
    }
  }

  .bubble-item {
    display: flex;
    .bubble-item-avatar {
      width: 36px;
      height: 36px;
      flex:0 0 36px;
      vertical-align: top;
      box-sizing: border-box;
      border-radius: 3px;
      overflow: hidden;
      &:hover {
        cursor: pointer;
      }
      .bubble-item-avatar-img {
        width: 100%;
        height: 100%;
      }
    }

      .bubble-item-content {
        width: 100%;
        margin-left: 10px;
        position: relative;
        flex: 1 1 auto;
        min-width: 0; //为了解决flex下空白字符折行问题
        justify-content: flex-start;
        display:flex;
        flex-wrap: wrap;
        .bubble-item-sender {
          margin: 0 0 3px;
          height: 16px;
          overflow: hidden;
          color: #596e8f;
          padding-right: 4px;
          font-size: 12px;
          line-height: 16px;
          position: relative;
          white-space: pre;
          width: 100%;
        }

        .bubble-item-msg {
          width: 100%;
          display:flex;
          .message-body{
            background-color: #fff;
            border: 1px solid #eee;
            border-radius: 3px;
            position: relative;
            .message-body-corner{
              position: absolute;
              left:-4px;
              right:auto;
              top:12px;
              width:0;
              height:0;
              border-top:6px solid transparent;
              border-right:4px solid #eee;
              border-bottom:6px solid transparent;
              border-left:0px solid transparent;
              .message-body-corner-inner{
                position: absolute;
                left:1px;
                right:auto;
                top:-6px;
                width:0;
                height:0;
                border-top:6px solid transparent;
                border-right:4px solid #fff;
                border-bottom:6px solid transparent;
                border-left:0px solid transparent;
              }
            }
          }
          .bubble-item-msg-send-status{
            margin: 12px;
            width:16px;
            height:16px;
            .message-loading-icon{
              width:16px;
              height:16px;
            }
            .message-error-icon{
              cursor:pointer;
              width:16px;
              height:16px;
              background-image:url(./sendError.png);
              background-size: cover;
            }
          }
        }
      }
  }
}

@media screen and (min-width: 480px){
  .bubble-item-msg{
    .message-body{
      max-width: 270px;
      &.message-body-image{
        max-width:none;
      }
    }
  }
}
