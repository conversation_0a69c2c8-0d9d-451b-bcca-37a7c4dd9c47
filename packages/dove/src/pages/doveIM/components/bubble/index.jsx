import { Component } from 'react';
import { connect } from 'react-redux';
import B<PERSON>bleList from './bubbleList';
import { getVcard } from '../../redux/reducers/vcard';
import { getHistoryMessages } from '../../redux/actions/message';
import { MethodType, apiCaller } from '@mfe/cc-api-caller-pc';


const mapStateToProps = (state) => {
    return {
        messageArray: state.message.get('messageArray'),
        currentUid: state.session.get('currentUid'),
        sessionType: state.session.get('sessionType'),
        vcardMap: state.vcard.get('vcardMap'),
        myUid: state.info.get('uid'),
    }
  }

const mapDispatchToProps = {
  getVcard,
  getHistoryMessages
}

class List extends Component {
    constructor(props) {
        super(props);
        this.getVcardInfo(props.messageArray);
        this.state = {
            isEnd: false
        }
    }

    getVcardInfo (messageArray) {
        const {
            getVcard
        } = this.props;
        const uidMap = {};
        const pubIdMap = {};
        messageArray.forEach(message => {
            if (message.sessionType !== 'pubchat') {
                uidMap[message.from] = true;
            } else {
                pubIdMap[message.from] = true;
            }
        });
        getVcard(Object.keys(uidMap), Object.keys(pubIdMap));
    };

    componentDidMount() {
        this.bubbleList && this.bubbleList.scrollToBottom();
    }

    componentDidUpdate(prevProps) {
        if (prevProps.currentUid !== this.props.currentUid) {
            this.bubbleList && this.bubbleList.scrollToBottom();
        }
        //新消息的情况
        if (prevProps.messageArray && this.props.messageArray && prevProps.messageArray !== this.props.messageArray) {
            const prevList = prevProps.messageArray.toJS() || [];
            const thisList = this.props.messageArray.toJS() || [];
            const prevLastMsg = prevList[prevList.length - 1];
            const thisLastMsg = thisList[thisList.length - 1];
            if (prevLastMsg !== thisLastMsg) {
                this.bubbleList && this.bubbleList.scrollToBottom();
            }
        }
    }

    componentWillReceiveProps(nextProps) {
        if (nextProps.messageArray !== this.props.messageArray) {
            this.getVcardInfo(nextProps.messageArray);
        }
    }

    async loadHistory(sessionId, sessionType) {
        const { getHistoryMessages } = this.props;
        const result = await getHistoryMessages(sessionId, sessionType);
        if (!(result && result.next > 0)) {
            this.setState({
                isEnd: true
            })
        }
    };

    saveList (e) {
        this.bubbleList = e;
    }

    render() {
        const {
            messageArray,
            vcardMap,
            currentUid,
            myUid,
            sessionType
        } = this.props;
        const bubbleItemProps = {
            onAvatarClick: (detail) => {
                const { isMe, from } = detail;
                if (isMe) return;
                apiCaller.send('/impc/poi/r/getWmPoiIdByPubId', {pubId: from}, {method: MethodType.GET})
                    .then(res => {
                        if (res.code !== 0) {
                            return;
                        }
                        const { poiId } = res.data;
                        window.open(`https://igate.waimai.meituan.com/igate/customer/customer.html?ctype=1&customerId=${poiId}`);
                    })
                    .catch(e => {
                        console.warn(e);
                    });
            },
            typedMsgProps : {
                image: {
                    maxWidth: 320,
                    minWidth: 160,
                    maxHeight: 320,
                    minHeight: 160
                }
            }
        }
        return (
            <BubbleList 
                key={currentUid}
                messageArray={messageArray.toJS()}
                vcardMap={vcardMap.toJS()}
                sessionId={currentUid}
                sessionType={sessionType}
                myUid={myUid}
                getHistoryMessages={this.loadHistory.bind(this)}
                hasMore={!this.state.isEnd}
                ref={this.saveList.bind(this)}
                bubbleItemProps={bubbleItemProps}
            />
        );
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(List);
