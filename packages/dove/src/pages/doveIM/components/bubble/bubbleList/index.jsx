import './index.scss';
import react, { useEffect, useRef } from 'react';
import { usePrevious } from 'ahooks';
import DynamicList from '~/components/dx-web-sdk-ui/dynamicList';
import BubbleItem from '../bubbleItem';
import { formatMessageTime, isCancelMessage } from '../../utils/messageUtil';
import EventMsg from './eventMsg';
import { myGlobalUid } from '../../../redux/actions/info';

const FIVE_MINUTES = 5 * 60 * 1000;

const BubbleList = props => {
    const wrapperElem = useRef();
    const scrollInfo = useRef({});
    const preMessageArray = usePrevious(props.messageArray);

    // 初次加载消息后自动滚动到底部
    useEffect(() => {
        if (!preMessageArray?.lenght && props.messageArray?.length) {
            scrollToBottom();
        }
    }, [preMessageArray, props.messageArray]);

    const scrollToBottom = () => {
        if (wrapperElem.current) {
            wrapperElem.current.scrollTop = wrapperElem.current.scrollHeight;
        }
    };

    const loadHistory = async () => {
        //保持滚动位置
        scrollInfo.current.height = wrapperElem.current.scrollHeight;
        scrollInfo.scrollTop = wrapperElem.current.scrollTop;
        const { getHistoryMessages, sessionId, sessionType } = props;
        await getHistoryMessages(sessionId, sessionType);
    };

    useEffect(() => {
        if (!wrapperElem.current || !scrollInfo.current.height) {
            return;
        }
        const newHeight = wrapperElem.current.scrollHeight;
        wrapperElem.current.scrollTop =
            newHeight -
            scrollInfo.current.height +
            scrollInfo.current.scrollTop;
        scrollInfo.current = {};
    }, [props.messageArray]);

    const renderMessageList = () => {
        const {
            messageArray,
            myUid = myGlobalUid,
            vcardMap,
            bubbleItemProps,
        } = props;

        let lastMsgTime = 0;
        const result = messageArray.reduce((res, message, index) => {
            if (!lastMsgTime || lastMsgTime + FIVE_MINUTES < message.time) {
                res.push(
                    <EventMsg
                        key={(message.mid || message.uuid) + '-' + message.time}
                    >
                        {formatMessageTime(message.time)}
                    </EventMsg>,
                );
            }

            lastMsgTime = message.time;

            const vcard = vcardMap[message.from];

            if (isCancelMessage(message)) {
                const isMe = message.from === myUid;
                const name = isMe
                    ? '你'
                    : (vcard && vcard.name) || message.fromName || message.from;
                res.push(
                    <EventMsg key={message.mid || message.uuid}>
                        <div className="withdraw-msg-tip">{`${name}撤回了一条消息`}</div>
                    </EventMsg>,
                );
                return res;
            } else {
                res.push(
                    <BubbleItem
                        key={message.mid || message.uuid || index}
                        message={message}
                        myUid={myUid}
                        vcard={vcard}
                        {...bubbleItemProps}
                    />,
                );
                return res;
            }
        }, []);

        return result;
    };

    const { wrapperClassName, bodyClassName, sessionId } = props;

    if (!sessionId) {
        return null;
    }
    return (
        <DynamicList
            // session切换则更新动态列表实例
            key={sessionId}
            hasLatter={false}
            hasEarlier={props.hasMore}
            loadEarlier={loadHistory}
            wrapperRef={e => (wrapperElem.current = e)}
            wrapperClassName={wrapperClassName}
            bodyClassName={bodyClassName}
        >
            {renderMessageList()}
        </DynamicList>
    );
};
export default BubbleList;
