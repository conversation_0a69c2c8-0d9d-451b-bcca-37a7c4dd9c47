import { ThemeChatStatus } from '@src/pages/types';
import { Button } from 'antd';
import { connect } from 'react-redux';
import Sender from './sender';
import { setThemeModalOpen } from '../redux/actions/themeChat';

const mapStateToProps = state => ({
    doveThemeChat: state.themeChat.get('doveThemeChat'),
    currentAuth: state.session.get('currentAuth'),
});

const mapDispatchToProps = {
    setThemeModalOpen,
};

interface SenderWrapper {
    currentAuth: boolean;
    setThemeModalOpen: (v: boolean) => void;
    doveThemeChat: {
        status: number;
    };
}
const SenderWrapper = (props: SenderWrapper) => {
    const createThemeChat = () => {
        props.setThemeModalOpen(true);
    };

    if (!props.currentAuth) {
        return <div className="dove-web-tips">您无权限对该商家发起会话</div>;
    }

    switch (props.doveThemeChat?.status) {
        case ThemeChatStatus.FRESH:
            return (
                <div className="dove-web-tips dove-web-button">
                    <Button type="primary" onClick={createThemeChat} block>
                        发起会话
                    </Button>
                </div>
            );
            break;
        case ThemeChatStatus.CHATTING:
        case ThemeChatStatus.USE_LEGACY:
        default:
            return (
                <div className="web-demo-sender">
                    <Sender />
                </div>
            );
    }
};

export default connect(mapStateToProps, mapDispatchToProps)(SenderWrapper);
