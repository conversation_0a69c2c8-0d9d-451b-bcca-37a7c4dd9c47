import * as React from 'react';
import './index.scss';
import { connect } from 'react-redux';
import classNames from 'classnames';
import Avatar from '~/components/dx-web-sdk-ui/avatar';
import parseTime from '../../utils/parseTime';
import { MESSAGE_TYPE, SESSION_TYPE } from '../../../data/constants';
import { isCancelMessage } from '../../utils/messageUtil';
import { simpleParseMsg } from './parseMsgPreview';
import NoDisturbIcon from '../../icon/NoDisturbIcon';

const mapStateToProps = state => {
    return {
        draft: state.draft.get('draft'),
        myUid: state.info.get('uid'),
    };
};

class SessionItem extends React.Component {
    constructor(props) {
        super(props);
    }

    onClick(e) {
        const { onClick, session } = this.props;
        if (onClick) {
            onClick(e, session);
        }
    }

    onContextMenu(e) {
        const { onContextMenu, session } = this.props;
        if (onContextMenu) {
            onContextMenu(e, session);
        }
    }

    renderSessionName() {
        const { vcard, session } = this.props;
        return (vcard && vcard.name) || session.uid;
    }

    renderSessionTime() {
        const { session } = this.props;
        return getSessionTime(session.time);
    }

    renderSessionPreview() {
        return (
            <span
                dangerouslySetInnerHTML={{ __html: this.renderPreview() }}
            ></span>
        );
    }

    renderDraft() {
        return;
    }

    renderPreview() {
        const { session, myUid, vcard, renderPreview, draft } = this.props;
        const { message } = session;
        if (renderPreview) {
            return renderPreview(session, vcard);
        } else if (draft[session.uid]) {
            return `<span class='session-item-draft'>[草稿]</span>${
                draft[session.uid]
            }`;
        } else {
            let name = '';
            if (
                session.type === SESSION_TYPE.GROUP_CHAT && //群聊
                !isCancelMessage(message) && //非撤回消息
                message.type !== MESSAGE_TYPE.MSG_TYPE_EVENT //非event消息
            ) {
                name = vcard ? vcard.name : session.uid + '：';
            }
            if (isCancelMessage(message)) {
                const isMe = message.from === myUid;
                name = isMe
                    ? '你'
                    : (vcard && vcard.name) || message.fromName || message.from;
            }
            const content = simpleParseMsg(message);
            return `${name}${content}`;
        }
    }

    render() {
        const { session, vcard, className, isMute } = this.props;
        const warpClz = classNames('session-item', className);
        return (
            <div
                className={warpClz}
                onClick={this.onClick.bind(this)}
                onContextMenu={this.onContextMenu.bind(this)}
            >
                <div className="session-item-avatar-box">
                    <Avatar
                        vcard={vcard}
                        className={'session-item-avatar'}
                    ></Avatar>
                    <div className="session-item-tip-box">
                        {!!session.unreads &&
                            (isMute ? (
                                <span className="session-item-unread-tip"></span>
                            ) : (
                                <span className="session-item-unread">
                                    {session.unreads > 99
                                        ? '99+'
                                        : session.unreads}
                                </span>
                            ))}
                    </div>
                </div>
                <div className="session-item-profile-box">
                    <div className="session-item-profile-main">
                        <div className="session-item-name">
                            {this.renderSessionName()}
                        </div>
                        <div className="session-item-time">
                            {this.renderSessionTime()}
                        </div>
                    </div>
                    <div className="session-item-profile-sub">
                        <div className="session-item-preview">
                            {this.renderSessionPreview()}
                        </div>

                        {isMute && (
                            <NoDisturbIcon  />
                        )}
                    </div>
                </div>
            </div>
        );
    }
}

function getSessionTime(time) {
    const bigTime = 35 * 365 * 24 * 60 * 60 * 1000; //2005年时间，判断当前会话是否有聊天记录
    let showTime;
    if (time < bigTime) {
        showTime = '';
    } else {
        showTime = parseTime('LITERARY', time);
    }
    if (showTime.indexOf('-') === 4) {
        showTime = showTime.split(' ')[0];
    }
    return showTime;
}

export default connect(mapStateToProps)(SessionItem);
