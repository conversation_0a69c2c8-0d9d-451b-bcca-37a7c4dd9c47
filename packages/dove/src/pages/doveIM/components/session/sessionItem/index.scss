@import "../../../assets/style/index.scss";

.session-item-active {
  background-color: rgba(0, 0, 0, 0.1);
  background-clip: border-box !important;
}

.session-item-draft {
  color:#FF5D4A;
}

.session-item {
  display: flex;
  padding: 15px 16px;
  padding-left: 0;
  cursor: pointer;
  background-clip: content-box;
  .session-item-avatar-box {
    position: relative;
    flex: 0 0 52px;
    height: 40px;
    width: 40px;
    .session-item-avatar {
      height: 40px;
      width: 40px;
      border-radius: 3px;
    }
    .session-item-tip-box {
      position: absolute;
      height: 16px;
      left: 28px;
      top: -8px;
      display: inline-block;
      text-align: center;
      font-size: 12px;
      width: 24px;
      .session-item-unread-tip {
        width: 8px;
        height: 8px;
        padding: 0;
        border-radius: 4px;
        background: #f43530;
        display: inline-block;
        overflow: hidden;//bfc
      }
      .session-item-unread {
        display: inline-block;
        color: #fff;
        background: #f43530;
        vertical-align: top;
        min-width: 16px;
        border-radius: 8px;
        line-height: 16px;
        padding: 0 3px;
        box-sizing: border-box;
        @include dx-font; //大象字体，为了跟大象的垂直对齐看起来一致
      }
    }
  }
  .session-item-profile-box {
    flex: 1 1 auto;
    overflow: hidden;
    height: 40px;
    .session-item-profile-main {
      display: flex;
      justify-content: space-between;
      line-height: 18px;
      height: 18px;
      .session-item-name {
        line-height: 18px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.87);
        flex: auto;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: pre;
        margin-right: 3px;
      }
      .session-item-time {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.38);
        flex: none;
        font-size: 12px;
      }
    }
    .session-item-profile-sub {
      display: flex;
      justify-content: space-between;
      margin-top: 5px;
      height: 18px;
      line-height: 18px;
      .session-item-preview {
        line-height: 18px;
        flex: auto;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.6);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: pre;
        margin-right: 5px;
        @include dx-font; //大象字体
        //表情大小
        .msg-emotion{
            width:18px;
            height:18px;
        }
      }
      .session-mute-tip {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.24);
        line-height: 18px;
        flex: none;
      }
    }
  }
}
