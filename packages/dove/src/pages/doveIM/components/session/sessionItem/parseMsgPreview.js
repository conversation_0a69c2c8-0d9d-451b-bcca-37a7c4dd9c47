import { filterUrl } from "../../utils/handleUrl";
import { escapeHtml, parseCustomContent } from "../../utils/htmlRelated";
import { replaceFace } from "../../utils/handleText";
import { QUOTED_MSG_REG, MESSAGE_TYPE } from "../../../data/constants";
import { isCancelMessage } from "../../utils/messageUtil";

function getMsgExtension(message) {
    if (message.extension) {
        try {
            const extension = JSON.parse(message.extension);
            return extension;
        } catch (e) {
            return {};
        }
    } else {
        return void 0;
    }
}

export function simpleParseMsg(msg, isNotification = false) {
    try {
        const msgType = msg.type;
        const body = msg.body || {};
        let str = "";
        if (isCancelMessage(msg)) {
            return '撤回了一条消息';
        }

        switch (msgType) {
            case MESSAGE_TYPE.MSG_TYPE_TEXT:
            case MESSAGE_TYPE.MSG_TYPE_EVENT:
                QUOTED_MSG_REG.lastIndex = 0;
                const isQuoted = QUOTED_MSG_REG.test(body.text);
                let inputText = body.text;
                if (isQuoted) {
                    const result = QUOTED_MSG_REG.exec(body.text);
                    inputText = result[4];
                }

                str = filterUrl(inputText.replace(/^[\n\s]+/, "")).replace(
                    /<a[^>]*>([^<]+)<\/a>/g,
                    "$1"
                );

                const groupNoticeReg = /(.*[发布|更改|更新]了群公告).*/;
                const mergeMsgReg = /你邀请(.*)加入了群聊/;

                if (groupNoticeReg.test(str)) {
                    const matches = groupNoticeReg.exec(str);
                    return `${matches[1]}`;
                }

                if (mergeMsgReg.test(str)) {
                    const matches = mergeMsgReg.exec(str);
                    return `你邀请${matches[1]}加入了群聊`;
                }

                if (str.indexOf("\n") !== -1) {
                    str = str.split("\n")[0] + "...";
                }
                if (isNotification) {
                    return str;
                } else {
                    return replaceFace(str);
                }
            case MESSAGE_TYPE.MSG_TYPE_IMAGE:
                return "[图片]";

            case MESSAGE_TYPE.MSG_TYPE_VOICE:
                return "[语音]";

            case MESSAGE_TYPE.MSG_TYPE_CALENDAR:
                return "[日历消息]";

            case MESSAGE_TYPE.MSG_TYPE_LINK: {
                const extension = getMsgExtension(msg);
                if (extension && extension.isMergeMessage) {
                    return escapeHtml(body.title);
                } else {
                    return "[链接]" + escapeHtml(body.title);
                }
            }

            case MESSAGE_TYPE.MSG_TYPE_MULTI_LINK:
                return "[链接]" + escapeHtml(body.content[0].title);

            case MESSAGE_TYPE.MSG_TYPE_FILE: {
                const extension = getMsgExtension(msg);
                if (extension && extension.description) {
                    str = filterUrl(extension.description).replace(
                        /<a[^>]*>([^<]+)<\/a>/g,
                        "$1"
                    );
                    if (isNotification) {
                        return str;
                    } else if (str != "undefined") {
                        return replaceFace(str);
                    } else {
                        return "[文件]";
                    }
                } else {
                    return "[文件]";
                }
            }
            case MESSAGE_TYPE.MSG_TYPE_VCARD:
                return "[名片]";

            case MESSAGE_TYPE.MSG_TYPE_GVCARD:
                return "[群名片]";

            case MESSAGE_TYPE.MSG_TYPE_GPS:
                return "[位置]";

            case MESSAGE_TYPE.MSG_TYPE_EMOTION:
                return "[表情]";

            case MESSAGE_TYPE.MSG_TYPE_NEWEMOTION:
                return "[表情]";

            case MESSAGE_TYPE.MSG_TYPE_CUSTOM:
                let escapeStyleContent =
                    (body.content &&
                        parseCustomContent({
                            content: body.content,
                            isShowStyle: false,
                        })) ||
                    body.content;
                let content =
                    (escapeStyleContent ? ":" : "") + escapeStyleContent;
                return filterUrl(
                    "[" + body.templateName + "]" + body.contentTitle + content
                ).replace(/<a[^>]*>([^<]+)<\/a>/g, "$1");

            case MESSAGE_TYPE.MSG_TYPE_NOTICE:
                return "[公告]" + escapeHtml(body.title);
            case MESSAGE_TYPE.MSG_TYPE_RED_PACKET:
                return `[红包]${body.greetings || ""}`;
            case MESSAGE_TYPE.MSG_TYPE_PCALL:
                return "[语音通话]";
            case MESSAGE_TYPE.MSG_TYPE_VIDEO:
                return "[小视频]";
            case MESSAGE_TYPE.MSG_TYPE_GENERAL:
                return "[群发消息]";
            // case MESSAGE_TYPE.MSG_TYPE_GENERAL: {
            //语音会议，群投票
            // let { type, data } = body;
            // switch (type) {
            //     case DATA_CONSTS.GENERAL_TYPE.TYPE_CALL_MEETING: {
            //         let text = body.data.text;
            //         let isQuoted = QUOTED_MSG_REG.test(text);
            //         let result;
            //         let inputText = text;

            //         if (isQuoted) {
            //             result = QUOTED_MSG_REG.exec(text);
            //             inputText = result[4];
            //         }

            //         str = filterUrl(inputText.replace(/^[\n\s]+/, '')).replace(/<a[^>]*>([^<]+)<\/a>/g, '$1');

            //         return `[语音会议]${str}`;
            //     }
            //     case DATA_CONSTS.GENERAL_TYPE.TYPE_GROUP_VOTE: {
            //         let { contentTitle } = data ? (JSON.parse(data) || {}) : {};
            //         return `[应用]${contentTitle || ''}`;
            //     }
            //     default: {
            //         return '暂不支持此消息，请在最新App版本中查看';
            //     }
            //     }
            // }
            default:
                return "[不支持的消息]";
        }
    } catch (e) {
        return "[不支持的消息]";
    }
}
