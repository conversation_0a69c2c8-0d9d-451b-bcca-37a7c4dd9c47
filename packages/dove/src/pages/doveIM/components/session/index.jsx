import React, { Component } from 'react';
import { connect } from 'react-redux';
import SessionList from './sessionList';
import { getVcard } from '../../redux/reducers/vcard';
import { selectSession } from '../../redux/actions/session';
import { UNREADS_TITLE, NOUNREADS_TITLE } from '../../data/constants';
import './index.scss';

const mapStateToProps = state => {
    return {
        sessionArray: state.session.get('sessionList'),
        vcardMap: state.vcard.get('vcardMap'),
        currentUid: state.session.get('currentUid'),
    };
};

const mapDispatchToProps = {
    getVcard,
    selectSession,
};

class List extends Component {
    constructor(props) {
        super(props);
        this.getVcardInfo(props.sessionArray);
    }

    getVcardInfo(sessionArray) {
        sessionArray = this.getSessionArray(sessionArray);
        const { getVcard } = this.props;
        const pubIdMap = {};
        sessionArray.forEach(session => {
            pubIdMap[session.pubId] = true;
        });
        getVcard(Object.keys(pubIdMap));
    }

    componentWillReceiveProps(nextProps) {
        if (nextProps.sessionArray !== this.props.sessionArray) {
            this.getVcardInfo(nextProps.sessionArray);
        }
    }

    getSessionArray(sessionArray) {
        const toGetArray = sessionArray || this.props.sessionArray;
        let unreads = 0;
        const dealedSessionArray = Object.entries(toGetArray.toJS())
            .map(([, entry]) => {
                unreads += entry.unreads || 0;
                return entry;
            })
            .sort((itemA, itemB) => itemB.time - itemA.time);
        document.title = unreads ? UNREADS_TITLE : NOUNREADS_TITLE;
        // 找到注入的元素
        (function (e, t) {
            var a = document.querySelectorAll(
                '.bellwether-sidebar_menu__item' + ' a[href *= "' + t + '"]',
            );
            a.forEach(function (t) {
                var a = document.createDocumentFragment(),
                    r = t.querySelector(
                        '.new_bellwether-sidebar_parent-menu__polling',
                    );
                r && r.remove();
                var i = document.createElement('template');
                if (!e) return;
                if (e > 99) e = '99+';
                (i.innerHTML =
                    '<span class="new_bellwether-sidebar_parent-menu__polling">' +
                    e +
                    '</span>'),
                    (a = i.content),
                    t.appendChild(a);
            });
        })(unreads, '/doveIM.html');
        return dealedSessionArray;
    }

    clickSessionItem(e, session) {
        const { selectSession } = this.props;
        selectSession(session.pubId, session.type, session, true);
    }

    render() {
        const { vcardMap } = this.props;
        const sessionArray = this.getSessionArray();
        return sessionArray.length ? (
            <SessionList
                sessionArray={this.getSessionArray()}
                vcardMap={vcardMap.toJS()}
                className={`web-demo-session-list ${this.props.className}`}
                sessionItemProps={{
                    onClick: this.clickSessionItem.bind(this),
                }}
            />
        ) : (
            <div className={`session-empty ${this.props.className}`}>
                最近30天内没有您的会话
            </div>
        );
    }
}

export default connect(mapStateToProps, mapDispatchToProps)(List);
