import './index.scss';
import { connect } from 'react-redux';
import * as React from 'react';
import { List } from 'antd';
import SessionItem from '../sessionItem';
import VirtualList from 'rc-virtual-list';

const mapStateToProps = state => {
    return {
        currentUid: state.session.get('currentUid'),
    };
};

class SessionList extends React.Component {
    constructor(props) {
        super(props);
    }

    render() {
        const {
            className,
            vcardMap,
            sessionArray,
            sessionItemProps = {},
        } = this.props;
        // const wrapperClz = classNames('session-list', className);
        return (
            <List style={{ width: 270 }}>
                <VirtualList
                    data={sessionArray}
                    itemHeight={70}
                    itemKey={v => v.pubId || v.uid}
                    height={
                        document.getElementsByClassName(
                            'ant-tabs-content-holder',
                        )?.[0]?.scrollHeight || 700
                    }
                >
                    {session => {
                        let sessionProps;
                        if (typeof sessionItemProps === 'function') {
                            sessionProps = sessionItemProps(session);
                        } else {
                            sessionProps = sessionItemProps;
                        }
                        return (
                            <SessionItem
                                className={
                                    this.props.currentUid === session.pubId
                                        ? 'session-item-active'
                                        : ''
                                }
                                key={session.uid}
                                session={session}
                                // 用 pubId 替换 uid，因为cschat会话类型是通过 pubId 获取的名片信息
                                vcard={vcardMap[session.pubId]}
                                {...sessionProps}
                            />
                        );
                    }}
                </VirtualList>
            </List>
        );
    }
}

export default connect(mapStateToProps)(SessionList);
