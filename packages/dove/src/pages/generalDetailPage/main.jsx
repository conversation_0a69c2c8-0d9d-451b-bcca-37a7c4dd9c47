/**
 * @description [ 信鸽 IM PC ]
 * <AUTHOR> <EMAIL> ]
 * @date        [ 2020-08-03 ]
 */
import React from 'react';
import './index.scss';
import { MethodType, apiCaller } from '@mfe/cc-api-caller-pc';
import { render } from '@src/module/root';
// import Owl from '@dp/owl';

// // 接入 OWL
// Owl.start({
//     project: 'com.sankuai.waimaicrmshareservice.fe.dove',
//     pageUrl: 'doveIM',
//     devMode: process.env.ONLINE_ENV !== 'prod',
//     resource: {
//         sampleApi: 1,
//     },
//     page: {
//         sample: 1,
//     },
// });

const messageMatches = location.search.match(/messageId=(\d*)/);
const messageId = messageMatches ? messageMatches[1] : 0;

class Root extends React.Component {
    constructor(props) {
        super();
        this.handleGeneralDetail();
        this.state = {
            title: '',
            content: '',
            imageList: [],
            ctimeStr: '',
        };
    }

    handleGeneralDetail() {
        apiCaller
            .send(
                `/impc/customMsg/share/r/getToken`,
                {},
                { method: MethodType.GET },
            )
            .then(res => {
                if (res.code !== 0) {
                    return;
                }
                const { account } = res.data;
                console.log('account', account);
                apiCaller
                    .send(
                        `/impc/customMsg/r/getById`,
                        {
                            doveimSendMethod: 1,
                            doveimShareAppId: 35,
                            doveimShareAccount: account,
                            doveimCustomMsgId: messageId,
                        },
                        { method: MethodType.GET },
                    )
                    .then(res => {
                        if (res.code !== 0) {
                            return;
                        }
                        const { title, content, imageList, ctimeStr } =
                            res.data;
                        this.setState({ title, content, imageList, ctimeStr });
                    });
            });
    }

    render() {
        const { title, content, imageList, ctimeStr } = this.state;
        // const title = "hello，群发一下";
        // const content = "巴拉巴";
        // const imageList = [
        //         "https://s3plus-img.meituan.net/v1/mss_b2337692cf044b7aac9164ce94e9f90d/doveim-public/doveim_file_2c135271dd9e72694ef85c8a637f8631.jpg"
        //     ]
        // const ctimeStr = "2020.08.28 15:55";
        return (
            <div>
                <h2>{title}</h2>
                <span className="general-detail-time">{ctimeStr}</span>
                <p className="general-detail-content">{content}</p>
                {imageList.map((image, i) => (
                    <img className="general-detail-image" src={image} key={i} />
                ))}
            </div>
        );
    }
}

render(<Root />, '通知详情');
