import { message } from 'antd';
import { apiCaller } from '@mfe/cc-api-caller-pc';

const T = (type, r) => title => {
    //   MRCToast({
    //     type,
    //     title,
    //   });
    message[type](title);
    return r;
};

export const get = (url, params = {}) => apiCaller.get(url, params);
export const post = (url, data) => apiCaller.post(url, data);
export const Toast = {
    success: T('success', true),
    warn: T('warn', false),
    error: T('error', false),
};

/**
 * 兼容闪购业务的虚拟根节点情况
 * @param {string} orgIds
 */
export const getOrgIds = orgIds => {
    // 如果是线上和st环境并且选择了闪购业务（-12），则返回55475,55476,55477这三个orgId
    if (
        ['igate.waimai.st.sankuai.com', 'igate.waimai.meituan.com'].indexOf(
            location.host,
        ) !== -1
    ) {
        if (String(orgIds) === '-12') {
            return '55475_55476_55477';
        }
    }
    return orgIds;
};

/**
 * 是否是配送用户
 */
export const isPeisong = () => {
    return new Promise(resolve => {
        get('/doveqc/callRecord/r/isPeiSongUser')
            .then(res => {
                if (res && res.code === 0 && res.data) {
                    resolve(res.data.isPeiSongUser);
                } else {
                    resolve(false);
                }
                resolve(undefined);
            })
            .catch(err => {
                resolve(false);
                T(
                    'error',
                    false,
                )((err && (err.msg || err.message)) || '请求出错，请重试');
            });
    });
};
