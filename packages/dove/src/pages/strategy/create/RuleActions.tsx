import React, { useContext } from 'react';
import {
    Row,
    Col,
    Button,
    Radio,
    TimePicker,
    Select,
    InputNumber,
    Popover,
} from 'antd';
import { executeTypeOpts, restOpts, delayTypeOptions } from '../const';
import LeadDetailContext from './globalContext';
import {
    CloseOutlined,
    PlusOutlined,
    QuestionCircleOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Option } = Select;

const formatTime = (t: string, format = 'HH:mm:ss') =>
    t ? dayjs(t, format) : undefined;

interface ActionInfo {
    executeTime: {
        delayTime: number;
        delayTimeUnit: number;
        executeType: number;
        fixedTime: string;
    };
    restConfig: {
        restBeginTime: string;
        restDelayTime: string;
        restEndTime: string;
        restStrategy: number;
    };
    templateList: Partial<{ type: number; template: number; id: number }>[];
}

interface RuleActions extends ActionInfo {
    updateActionInfo: (v: Partial<ActionInfo>) => void;
}

const RuleActions = (props: RuleActions) => {
    const { actionTemplateOpts } = useContext(LeadDetailContext);

    const updateExcuteTime = (payload: Partial<ActionInfo['executeTime']>) => {
        props.updateActionInfo({
            executeTime: {
                ...props.executeTime,
                ...payload,
            },
        });
    };

    const updateRestConfig = (payload: Partial<ActionInfo['restConfig']>) => {
        props.updateActionInfo({
            restConfig: {
                ...props.restConfig,
                ...payload,
            },
        });
    };

    const updateTemplate = (
        payload: Partial<ActionInfo['templateList'][number]>,
        index,
    ) => {
        props.updateActionInfo({
            templateList: props.templateList.map((one, i) =>
                i === index ? { ...one, ...payload } : one,
            ),
        });
    };

    return (
        <>
            <Row className="margin-bottom10">
                <Col span={12} className="display-flex-center height36">
                    <span className="headline required">动作</span>
                </Col>
                <Col span={12}>
                    <div className="flont-right">
                        <Button
                            type="primary"
                            onClick={() => {
                                props.updateActionInfo({
                                    templateList: [
                                        ...props.templateList,
                                        {
                                            id:
                                                Number(
                                                    props.templateList?.at(-1)
                                                        ?.id,
                                                ) + 1,
                                            type: undefined,
                                            template: undefined,
                                        },
                                    ],
                                });
                            }}
                        >
                            <PlusOutlined />
                        </Button>
                    </div>
                </Col>
            </Row>
            <div className="display-frame margin-bottom10">
                {props.templateList.map((value, i) => {
                    const templateOpts =
                        actionTemplateOpts.filter(
                            v => v.value === value.type,
                        )?.[0]?.templates || [];
                    return (
                        <div key={value.id} className="display-frame-content">
                            <Row className="display-flex">
                                <p className="flex-basis70">动作{i + 1}：</p>
                                <Radio.Group
                                    value={value.type}
                                    onChange={e => {
                                        updateTemplate(
                                            {
                                                type:
                                                    e.target.value || undefined,
                                                template: undefined,
                                            },
                                            i,
                                        );
                                    }}
                                >
                                    {actionTemplateOpts.map(v => (
                                        <Radio value={v.value} key={v.value}>
                                            {v.label}
                                        </Radio>
                                    ))}
                                </Radio.Group>
                                {props.templateList.length !== 1 && ( // 只有一条数据不显示 删除功能
                                    <div
                                        className="position-right"
                                        onClick={() => {
                                            props.updateActionInfo({
                                                templateList:
                                                    props.templateList.filter(
                                                        (_, ti) => ti !== i,
                                                    ),
                                            });
                                        }}
                                    >
                                        <CloseOutlined />
                                    </div>
                                )}
                            </Row>
                            <Row className="display-flex flex-align-center">
                                <span className="flex-basis70"></span>
                                <span>动作模板</span>
                                <Select
                                    value={value.template}
                                    className="action-template-select"
                                    placeholder="请选择动作模版"
                                    onChange={e => {
                                        updateTemplate(
                                            {
                                                template:
                                                    Number(e) || undefined,
                                            },
                                            i,
                                        );
                                    }}
                                >
                                    {templateOpts.map(it => (
                                        <Option key={it.label} value={it.value}>
                                            {it.label}
                                        </Option>
                                    ))}
                                </Select>
                            </Row>
                        </div>
                    );
                })}
            </div>
            <Row className="margin-bottom10">
                <Col className="display-flex">
                    <span className="headline required">动作执行时间</span>
                    <Radio.Group
                        value={props.executeTime.executeType}
                        onChange={e => {
                            updateExcuteTime({
                                executeType:
                                    Number(e.target.value) || undefined,
                            });
                        }}
                    >
                        {executeTypeOpts.map(v => (
                            <Radio value={v.value} key={v.value}>
                                {v.label}
                            </Radio>
                        ))}
                    </Radio.Group>
                </Col>
            </Row>
            {props.executeTime.executeType === 1 && (
                <Row className="margin-bottom10">
                    <Col span={24} className="display-flex">
                        <span className="headline"></span>
                        <TimePicker
                            style={{ width: '15%' }}
                            allowClear
                            value={formatTime(props.executeTime.fixedTime)}
                            onChange={(_, t) => {
                                updateExcuteTime({
                                    fixedTime: t || undefined,
                                });
                            }}
                        />
                    </Col>
                </Row>
            )}
            {props.executeTime.executeType === 2 && (
                <Row className="margin-bottom10">
                    <Col span={24} className="display-flex flex-align-center">
                        <span className="headline"></span>
                        <InputNumber
                            min={1}
                            max={300}
                            precision={0}
                            value={props.executeTime.delayTime}
                            onChange={e => {
                                updateExcuteTime({
                                    delayTime: Number(e) || undefined,
                                });
                            }}
                        />
                        <Select
                            className="action-template-select"
                            style={{
                                width: '10%',
                                margin: '0 5px',
                            }}
                            defaultValue={1}
                            value={props.executeTime.delayTimeUnit}
                            onChange={e => {
                                updateExcuteTime({
                                    delayTimeUnit: e || undefined,
                                });
                            }}
                        >
                            {delayTypeOptions.map(it => (
                                <Option key={it.label} value={it.value}>
                                    {it.label}
                                </Option>
                            ))}
                        </Select>
                        <span>后触发</span>
                    </Col>
                </Row>
            )}
            {props.executeTime.executeType === 3 && <div />}
            <Row className="margin-bottom10">
                <Col span={24} className="display-flex flex-align-center">
                    <span className="headline">免打扰时间</span>
                    <span>每天</span>
                    <TimePicker
                        style={{
                            width: '15%',
                            margin: '0 10px',
                        }}
                        allowClear
                        value={formatTime(props.restConfig.restBeginTime)}
                        onChange={(_, t) => {
                            updateRestConfig({
                                restBeginTime: t || undefined,
                            });
                        }}
                    />
                    <span>至</span>
                    <TimePicker
                        style={{
                            width: '15%',
                            margin: '0 10px',
                        }}
                        allowClear
                        value={formatTime(props.restConfig.restEndTime)}
                        onChange={(_, t) => {
                            updateRestConfig({
                                restEndTime: t || undefined,
                            });
                        }}
                    />
                    <span>为免打扰</span>
                </Col>
            </Row>
            <Row className="margin-bottom10">
                <Col span={12} className="display-flex-center height32">
                    <span className="headline">
                        <span>免打扰策略&nbsp;</span>
                        <Popover
                            title={null}
                            content={
                                <p style={{ maxWidth: 300 }}>
                                    规则不执行，即为如果规则触达时间命中免打扰时间端，规则就流失掉不会执行；延迟固定时间执行，即为规则会延迟到第二天固定时间执行。
                                </p>
                            }
                        >
                            <QuestionCircleOutlined />
                        </Popover>
                    </span>
                    <Radio.Group
                        className="display-flex-center"
                        value={props.restConfig.restStrategy}
                        onChange={e => {
                            updateRestConfig({
                                restStrategy: e.target.value || undefined,
                            });
                        }}
                    >
                        {restOpts.map(v => (
                            <Radio value={v.value} key={v.value}>
                                {v.label}
                            </Radio>
                        ))}
                    </Radio.Group>
                </Col>
            </Row>
            {props.restConfig.restStrategy === 1 && null}
            {props.restConfig.restStrategy === 2 && (
                <Row className="margin-bottom10">
                    <Col span={24} className="display-flex flex-align-center">
                        <span className="headline"></span>
                        <span>第二天</span>
                        <TimePicker
                            style={{
                                width: '15%',
                                margin: '0 10px',
                            }}
                            allowClear
                            value={formatTime(props.restConfig.restDelayTime)}
                            onChange={(_, t) => {
                                updateRestConfig({
                                    restDelayTime: t || undefined,
                                });
                            }}
                        />
                        <span>执行</span>
                    </Col>
                </Row>
            )}
        </>
    );
};

export default RuleActions;
