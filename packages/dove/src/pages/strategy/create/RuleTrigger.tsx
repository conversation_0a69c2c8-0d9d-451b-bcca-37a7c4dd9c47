import React from 'react';
import { Row, Col, Radio, TimePicker, Select, InputNumber } from 'antd';
import { periodOptions, triggerOpts } from '../const';
import { TriggerType } from '../types';
import dayjs from 'dayjs';
const { Option } = Select;

const formatTime = (t: string, format = 'HH:mm:ss') =>
    t ? dayjs(t, format) : undefined;

interface TriggerInfo {
    intervalMinutes: number;
    timerTime: string;
    type: number;
    weekDays: number[];
}

interface RuleTrigger {
    triggerInfo: TriggerInfo;
    triggerSupported?: number[];
    readonly?: boolean;
    onChange?: (v: TriggerInfo) => void;
}

const ReadonlyRuleTrigger = (props: TriggerInfo) => {
    const periodTypeName =
        periodOptions.find(one => props.weekDays[0] === one.value)?.label ||
        '-';

    const triggerName =
        triggerOpts.find(one => one.value === props.type)?.label || '-';
    switch (props.type) {
        case TriggerType.PERIOD:
            return (
                <span>{`${triggerName}（每 ${props.intervalMinutes} 分钟触发一次）`}</span>
            );
        case TriggerType.TIMING:
            return (
                <span>
                    {`${triggerName}（每 ${periodTypeName} ${props.timerTime} 触发）`}
                </span>
            );
        case TriggerType.OBJ_CREATE:
        case TriggerType.OBJ_DEL:
        case TriggerType.OBJ_UPDATE:
            return <span>{triggerName}</span>;
        default:
            return <span>未知</span>;
    }
};

const RuleTrigger = (props: RuleTrigger) => {
    const onUpdate = (payload: Partial<TriggerInfo>) => {
        props.onChange?.({ ...props.triggerInfo, ...payload });
    };

    const options = triggerOpts.filter(it =>
        props.triggerSupported?.includes(it.value),
    );

    if (props.readonly) {
        return (
            <p className="margin-bottom5">
                <span className="fixed-width200">触发时机：</span>
                <ReadonlyRuleTrigger {...props.triggerInfo} />
            </p>
        );
    }

    return (
        <>
            <Row className="margin-bottom10">
                <Col span={16} className="display-flex-center height32">
                    <span className="headline required">触发时机：</span>
                    <Radio.Group
                        className="display-flex-center"
                        value={props.triggerInfo.type}
                        onChange={e => {
                            onUpdate({
                                type: e.target.value || undefined,
                                weekDays: [],
                                intervalMinutes: undefined,
                                timerTime: undefined,
                            });
                        }}
                    >
                        {options.map(v => (
                            <Radio value={v.value} key={v.value}>
                                {v.label}
                            </Radio>
                        ))}
                    </Radio.Group>
                </Col>
            </Row>
            <Row className="margin-bottom10">
                {props.triggerInfo.type === 1 && (
                    <Col span={12} className="display-flex flex-align-center">
                        <span className="headline"></span>
                        <span>每</span>
                        <InputNumber
                            style={{
                                margin: '0 10px',
                            }}
                            placeholder="分钟"
                            min={1}
                            max={300}
                            precision={0}
                            value={props.triggerInfo.intervalMinutes}
                            onChange={e => {
                                onUpdate({
                                    intervalMinutes: Number(e) || undefined,
                                });
                            }}
                        />
                        <span>分钟触发1次</span>
                    </Col>
                )}
                {props.triggerInfo.type === 2 && (
                    <Col span={16} className="display-flex flex-align-center">
                        <span className="headline"></span>
                        <span>每</span>
                        <Select
                            style={{
                                width: '20%',
                                marginLeft: 5,
                            }}
                            placeholder="请选择"
                            value={props.triggerInfo.weekDays?.[0]}
                            onChange={e => {
                                onUpdate({
                                    weekDays: [e] || undefined,
                                });
                            }}
                        >
                            {periodOptions.map(it => (
                                <Option key={it.label} value={it.value}>
                                    {it.label}
                                </Option>
                            ))}
                        </Select>
                        <TimePicker
                            style={{
                                width: '30%',
                                margin: '0 10px',
                            }}
                            className="time-picker-name"
                            format="HH:mm"
                            value={formatTime(
                                props.triggerInfo.timerTime,
                                'HH:mm',
                            )}
                            onChange={(_, t) => {
                                onUpdate({
                                    timerTime: t || undefined,
                                });
                            }}
                        />
                        <span>触发</span>
                    </Col>
                )}
            </Row>
        </>
    );
};

export default RuleTrigger;
