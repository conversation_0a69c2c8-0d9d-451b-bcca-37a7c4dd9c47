.page-wrap {
    padding: 20px 30px;

    //
    // 公共样式区
    .display-flex {
        display: flex;
    }

    .display-flex-center {
        display: flex;
        align-items: center;
    }

    .flex-align-center {
        align-items: center;
    }

    .flex-basis70 {
        flex-basis: 70px;
    }

    .height32 {
        height: 32px;
    }

    .height36 {
        height: 36px;
    }

    .margin-left10 {
        margin-left: 10px;
    }

    .margin-bottom5 {
        margin-bottom: 5px;
    }

    .margin-bottom10 {
        margin-bottom: 10px;
    }

    .flont-right {
        float: right;
    }

    .position-right {
        position: absolute;
        top: 0;
        right: 0;
    }

    //
    // 业务组件样式区
    .rule-name {
        padding: 12px 15px;
    }

    .rule-content {
        background-color: #f0f0f0;
        padding: 12px 15px;
        border-radius: 5px;
    }

    .headline {
        width: 100px;
        display: inline-block;
        white-space: nowrap;
    }

    .required::before {
        display: inline-block;
        margin-right: 2px;
        color: #f5222d;
        font-size: 14px;
        font-family: Sim<PERSON><PERSON>, sans-serif;
        line-height: 1;
        content: '*';
    }

    .rule-dividing-line {
        color: #b1b1b1;
        font-weight: 400;
        font-size: 14px;
    }

    // antd分割线颜色
    .ant-divider-with-text-center::before,
    .ant-divider-with-text-center::after {
        border-top: 1px dashed #a2a4b3 !important;
    }

    // 展示框
    .display-frame {
        position: relative;

        // 展示框内容
        .display-frame-content {
            margin: 10px 0 10px 100px;
            padding: 10px 8px;
            border-radius: 8px;
            background-color: #ffffff;
        }

        // 单选框
        .display-frame-and-or {
            position: absolute;
            top: calc(50% - 35px);
            z-index: 10;
        }

        // 单选框线
        .display-frame-line {
            position: absolute;
            height: calc(100% - 70px);
            width: calc(100px - 26px);
            border-width: 2px 0px 2px 2px;
            border-style: solid;
            border-color: #666;
            border-radius: 5px 0px 0px 5px;
            left: 26px;
            top: 35px;
            z-index: 4;
        }

        // 单选框线 遮蔽
        .display-frame-line-shelter {
            position: absolute;
            top: calc(50% - 35px);
            z-index: 7;
            height: 72px;
            background-color: #ffffff;
        }

        // 竖向 单选框
        .and-or {
            display: flex;
            flex-flow: column;

            .ant-radio-button-wrapper {
                &:first-child {
                    border-radius: 4px 4px 0 0;
                }
                &:last-child {
                    border-radius: 0 0 4px 4px;
                }

                &:not(:first-child) {
                    border-left: 1px solid #d9d9d9;
                    &::before {
                        content: unset;
                    }
                }
            }
        }
    }

    // 动作模板下拉框
    .action-template-select {
        width: calc(60% + 20px);
        margin-left: 20px;
    }

    // 规则完成展示
    .rule-completion {
        background-color: #f0f0f0;
        margin-bottom: 10px;
        padding: 12px 15px 5px 15px;
        border-radius: 5px;
    }

    .fixed-width200 {
        width: 100px;
        display: inline-block;
    }

    .divider-no-title {
        margin: 10px 0;
        border-color: #a2a4b3;
    }

    .new-rule-butt {
        width: 400px;
        border-radius: 10px;
        font-size: 14px;
        line-height: 28px;
    }

    // 公共 label
    label {
        margin-bottom: 0;
    }

    .form-control {
        height: 32px;
    }
}

h5.title::before {
    display: none;
}

.msg-title-1 {
    display: list-item;
    font-weight: 500;
}

.msg-title {
    margin-top: 10px;
    .msg-title-2 {
        display: list-item;
    }

    span {
        font-weight: 500;
    }

    p {
        margin-bottom: 5px;
    }
}
