import React from 'react';
import {
    Row,
    Col,
    Tooltip,
    Button,
    Input,
    Radio,
    Select,
    InputNumber,
} from 'antd';
import {
    conditionRelationOpts,
    filterOpts,
    operatorOpts,
    delayTypeOptions,
} from '../const';
import { OperatorType } from '../types';
import { CloseCircleOutlined, PlusOutlined } from '@ant-design/icons';
import CitySelector from '@src/components/CitySelector';
const { Option } = Select;

interface RuleConditions {
    conditionList: any[];
    conditionRelation: number;
    fieldList: any[];
    updateConditionInfo: (
        p: Partial<{ conditionList: any[]; conditionRelation: number }>,
    ) => void;
}

const MULTIPLE_VALUE_OPERATOR = [OperatorType.IN, OperatorType.NOT_IN];

const RuleConditions = (props: RuleConditions) => {
    const timeTypeOptions = props.fieldList.filter(v => [3].includes(v.type)); // 按时间过滤 (3 时间 4 可选项 1 字符串 2 数字)
    const eventAttributeOptions = props.fieldList.filter(v =>
        [1, 2, 4, 5].includes(v.type),
    ); // 按属性过滤
    const addCondition = payload => {
        props.updateConditionInfo({
            conditionList: [...props.conditionList, payload],
        });
    };

    const removeCondition = index => {
        props.updateConditionInfo({
            conditionList: props.conditionList.filter((_, i) => i !== index),
        });
    };

    const updateCondition = (payload, index) => {
        props.updateConditionInfo({
            conditionList: props.conditionList.map((one, i) =>
                i === index ? { ...one, ...payload } : one,
            ),
        });
    };

    return (
        <>
            <Row className="margin-bottom10">
                <Col span={12} className="display-flex-center height36">
                    <span className="headline">
                        <span>过滤条件&nbsp;</span>
                    </span>
                </Col>
                <Col span={12}>
                    <div className="flont-right">
                        <Button
                            type="primary"
                            icon={<PlusOutlined />}
                            onClick={() => {
                                addCondition({
                                    id: props.conditionList.at(-1).id + 1,
                                    filterType: 1,
                                    fieldName: undefined,
                                    operator: undefined,
                                    value: '1',
                                });
                            }}
                        ></Button>
                    </div>
                </Col>
            </Row>
            <div className="display-frame margin-bottom10">
                {props.conditionList.length > 1 && ( // 只有一条数据不显示 且、或
                    <>
                        <div className="display-frame-and-or">
                            <Tooltip title="暂不支持切换，敬请期待">
                                <Radio.Group
                                    value={props.conditionRelation}
                                    className="and-or"
                                >
                                    {conditionRelationOpts.map(v => (
                                        <Radio.Button
                                            value={v.value}
                                            key={v.value}
                                        >
                                            {v.label}
                                        </Radio.Button>
                                    ))}
                                </Radio.Group>
                            </Tooltip>
                        </div>
                        <div className="display-frame-line"></div>
                        <div className="display-frame-line-shelter"></div>
                    </>
                )}
                {props.conditionList.map((value, i) => {
                    const {
                        operator = [],
                        option = [],
                        type = 4,
                    } = props.fieldList.find(
                        v => v.fieldName === value.fieldName,
                    ) || {};

                    const currentOperators = operatorOpts.filter(v =>
                        operator.includes(v.value),
                    );
                    return (
                        <div key={value.id} className="display-frame-content">
                            <Row className="display-flex">
                                <p className="flex-basis70">条件{i + 1}：</p>
                                <Radio.Group
                                    value={value.filterType}
                                    onChange={e => {
                                        updateCondition(
                                            {
                                                filterType:
                                                    Number(e.target.value) ||
                                                    undefined,
                                                fieldName: undefined,
                                                operator: undefined,
                                                value:
                                                    Number(e.target.value) === 1
                                                        ? '1'
                                                        : undefined,
                                                timeUnit:
                                                    Number(e.target.value) === 1
                                                        ? 1
                                                        : undefined,
                                            },
                                            i,
                                        );
                                    }}
                                >
                                    {filterOpts.map(v => (
                                        <Radio value={v.value} key={v.value}>
                                            {v.label}
                                        </Radio>
                                    ))}
                                </Radio.Group>
                                {props.conditionList.length !== 1 && ( // 只有一条数据不显示 删除功能
                                    <div
                                        className="position-right"
                                        onClick={() => {
                                            removeCondition(i);
                                        }}
                                    >
                                        <CloseCircleOutlined />
                                    </div>
                                )}
                            </Row>
                            {value.filterType === 1 && (
                                <Row className="display-flex flex-align-center">
                                    <span className="flex-basis70"></span>
                                    <span>
                                        当前时间&nbsp;&nbsp;-&nbsp;&nbsp;
                                    </span>
                                    <Select
                                        value={value.fieldName}
                                        style={{
                                            width: '20%',
                                        }}
                                        placeholder="请选择时间属性"
                                        onChange={e => {
                                            updateCondition(
                                                {
                                                    fieldName: e || undefined,
                                                    operator: undefined,
                                                },
                                                i,
                                            );
                                        }}
                                    >
                                        {timeTypeOptions.map(it => (
                                            <Option
                                                key={it.fieldName}
                                                value={it.fieldName}
                                            >
                                                {it.name}
                                            </Option>
                                        ))}
                                    </Select>
                                    <Select
                                        value={value.operator}
                                        style={{
                                            width: '20%',
                                            margin: '0 10px',
                                        }}
                                        placeholder="请选择操作符"
                                        onChange={e => {
                                            updateCondition(
                                                {
                                                    operator:
                                                        Number(e) || undefined,
                                                },
                                                i,
                                            );
                                        }}
                                    >
                                        {currentOperators.map(it => (
                                            <Option
                                                key={it.value}
                                                value={it.value}
                                            >
                                                {it.label}
                                            </Option>
                                        ))}
                                    </Select>
                                    <InputNumber
                                        min={1}
                                        max={3000}
                                        precision={0}
                                        value={Number(value.value)}
                                        onChange={e => {
                                            updateCondition(
                                                {
                                                    value: `${e}` || undefined,
                                                },
                                                i,
                                            );
                                        }}
                                    />
                                    <Select
                                        style={{
                                            width: 200,
                                            margin: '0 10px',
                                        }}
                                        placeholder="请选择时间单位"
                                        value={value.timeUnit}
                                        onChange={e => {
                                            updateCondition(
                                                {
                                                    timeUnit: e,
                                                },
                                                i,
                                            );
                                        }}
                                    >
                                        {delayTypeOptions.map(it => (
                                            <Option
                                                key={it.label}
                                                value={it.value}
                                            >
                                                {it.label}
                                            </Option>
                                        ))}
                                    </Select>
                                </Row>
                            )}
                            {value.filterType === 2 && (
                                <Row className="display-flex flex-align-center">
                                    <span className="flex-basis70"></span>
                                    <Select
                                        value={value.fieldName}
                                        style={{
                                            width: '20%',
                                        }}
                                        placeholder="请选择事件属性"
                                        onChange={e => {
                                            updateCondition(
                                                {
                                                    fieldName: e || undefined,
                                                    operator: undefined,
                                                    value:
                                                        e === 2
                                                            ? '1'
                                                            : undefined,
                                                },
                                                i,
                                            );
                                        }}
                                    >
                                        {eventAttributeOptions.map(it => (
                                            <Option
                                                key={it.fieldName}
                                                value={it.fieldName}
                                            >
                                                {it.name}
                                            </Option>
                                        ))}
                                    </Select>
                                    <Select
                                        style={{
                                            width: '20%',
                                            margin: '0 10px',
                                        }}
                                        placeholder="请选择操作符"
                                        value={value.operator}
                                        onChange={e => {
                                            const isSingleValue =
                                                !MULTIPLE_VALUE_OPERATOR.includes(
                                                    e,
                                                );

                                            const valueObj =
                                                isSingleValue && value.value
                                                    ? {
                                                          value: value.value.split(
                                                              ',',
                                                          )[0],
                                                      }
                                                    : undefined;

                                            updateCondition(
                                                {
                                                    ...valueObj,
                                                    operator: e,
                                                },
                                                i,
                                            );
                                        }}
                                    >
                                        {currentOperators.map(it => (
                                            <Option
                                                key={it.value}
                                                value={it.value}
                                            >
                                                {it.label}
                                            </Option>
                                        ))}
                                    </Select>
                                    {type === 1 && (
                                        <Input
                                            className="height32"
                                            placeholder="请输入属性值"
                                            value={value.value}
                                            onChange={e => {
                                                updateCondition(
                                                    {
                                                        value:
                                                            e?.target?.value ||
                                                            undefined,
                                                    },
                                                    i,
                                                );
                                            }}
                                        />
                                    )}
                                    {type === 2 && (
                                        <InputNumber
                                            style={{
                                                margin: '0 10px',
                                            }}
                                            precision={0}
                                            value={value.value}
                                            placeholder="请输入属性值"
                                            onChange={e => {
                                                updateCondition(
                                                    {
                                                        value:
                                                            Number(e) ||
                                                            undefined,
                                                    },
                                                    i,
                                                );
                                            }}
                                        />
                                    )}
                                    {type === 4 && (
                                        <Select
                                            style={{
                                                width: '25%',
                                                margin: '0 10px',
                                            }}
                                            mode={
                                                MULTIPLE_VALUE_OPERATOR.includes(
                                                    value.operator,
                                                )
                                                    ? 'multiple'
                                                    : undefined
                                            }
                                            maxTagCount={2}
                                            placeholder="请选择属性值"
                                            value={
                                                value.value
                                                    ?.split(',')
                                                    .map(Number) || undefined
                                            }
                                            onChange={e => {
                                                updateCondition(
                                                    {
                                                        value:
                                                            e.toString() ||
                                                            undefined,
                                                    },
                                                    i,
                                                );
                                            }}
                                        >
                                            {option.map(it => (
                                                <Option
                                                    key={it.code}
                                                    value={it.code}
                                                >
                                                    {it.name}
                                                </Option>
                                            ))}
                                        </Select>
                                    )}
                                    {type === 5 ? (
                                        <CitySelector
                                            maxLevel={3}
                                            value={
                                                value.value
                                                    ?.split(',')
                                                    .map(Number) || undefined
                                            }
                                            onChange={e => {
                                                updateCondition(
                                                    {
                                                        value:
                                                            e.toString() ||
                                                            undefined,
                                                    },
                                                    i,
                                                );
                                            }}
                                        />
                                    ) : null}
                                </Row>
                            )}
                        </div>
                    );
                })}
            </div>
        </>
    );
};

export default RuleConditions;
