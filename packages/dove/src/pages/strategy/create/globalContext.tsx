/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';

interface globalContextType {
    initialValue: any;
    filterCondition: (a: any[]) => any[];
    checkTheRequiredInput: (v) => false | any[];
    ruleList: any[];
    activeRuleList: any[];
    completeRuleList: any[];
    setRuleList: (v) => void;
    ruleObjectOpts: {
        fieldList: {
            name: string;
            operator: number[];
            option: { code: number; name: string }[];
            type: number;
            fieldName: number;
        }[];
        objKey: string;
        triggerSupported: number[];
        visitBizInfo: {
            name: string;
            option: { code: string; name: 'string' }[];
            fieldName: string;
        };
    }[];
    actionTemplateOpts: {
        templates: { value: number; label: string }[];
        label: string;
        value: number;
    }[];
    newRule: (v) => void;
}

const data: globalContextType = {
    initialValue: {},
    filterCondition: a => {
        return [];
    },
    checkTheRequiredInput: a => {
        return false;
    },
    ruleList: [],
    activeRuleList: [],
    completeRuleList: [],
    setRuleList: a => {},
    ruleObjectOpts: [],
    actionTemplateOpts: [],
    newRule: a => {},
};

const LeadDetailContext = React.createContext(data);
export default LeadDetailContext;
