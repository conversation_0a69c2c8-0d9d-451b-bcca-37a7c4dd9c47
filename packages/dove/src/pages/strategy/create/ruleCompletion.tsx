import React, { useContext } from 'react';
import { Divider, message, Button } from 'antd';
import LeadDetailContext from './globalContext';
import {
    conditionRelationOpts,
    filterOpts,
    operatorOpts,
    executeTypeOpts,
    restOpts,
    delayTypeOptions,
} from '../const';
import RuleTrigger from './RuleTrigger';

// 已完成的规则信息展示
export default function ruleCompletion() {
    const {
        filterCondition,
        completeRuleList,
        ruleList,
        setRuleList,
        ruleObjectOpts,
        actionTemplateOpts,
        newRule,
    } = useContext(LeadDetailContext);

    const deleteRule = uniqId => {
        if (ruleList.length <= 1) {
            message.error('至少保留一条规则');
            return;
        }

        setRuleList(ruleList.filter(one => one.uniqId !== uniqId));
    };

    const expansionRule = uniqId => {
        setRuleList(
            ruleList.map(one => {
                if (one.uniqId !== uniqId) {
                    return one;
                }

                return {
                    ...one,
                    active: true,
                };
            }),
        );
    };

    if (!completeRuleList.length || !ruleObjectOpts.length) {
        return null;
    }

    return (
        <div className="rule-completion">
            {completeRuleList.map((val, index) => {
                const ruleObjectOpt = ruleObjectOpts.filter(
                    v => v.objKey === val.objInfo.objClassName,
                )[0];
                const fieldList = ruleObjectOpt?.fieldList || [];

                const ruleObjectName =
                    ruleObjectOpts.filter(
                        v => v.objKey === val.objInfo.objClassName,
                    )[0]?.objKey || ''; //

                const executeTimeName =
                    executeTypeOpts.filter(
                        v => v.value === val.actionInfo.executeTime.executeType,
                    )[0]?.label || '';
                const andOrName =
                    conditionRelationOpts.filter(
                        v => v.value === val.conditionInfo.conditionRelation,
                    )[0]?.label || ''; // 且或
                const restConfigName =
                    restOpts.filter(
                        v => v.value === val.actionInfo.restConfig.restStrategy,
                    )[0]?.label || '';
                const delayTypeName =
                    delayTypeOptions.filter(
                        v =>
                            v.value ===
                            val.actionInfo.executeTime.delayTimeUnit,
                    )[0]?.label || '';

                const condition = filterCondition(
                    val.conditionInfo.conditionList,
                ); // 过滤出信息完整的条件

                return (
                    <div key={val.uniqId}>
                        <p className="margin-bottom5">
                            <span className="fixed-width200">规则id：</span>
                            <span>{val.id || '-'}</span>
                            <span className="flont-right">
                                <Button
                                    onClick={() => {
                                        deleteRule(val.uniqId);
                                    }}
                                >
                                    删除
                                </Button>
                                <Button
                                    className="margin-left10"
                                    onClick={() => {
                                        newRule(val);
                                        message.success('复制成功');
                                    }}
                                >
                                    复制
                                </Button>
                                <Button
                                    type="primary"
                                    className="margin-left10"
                                    onClick={() => expansionRule(val.uniqId)}
                                >
                                    展开
                                </Button>
                            </span>
                        </p>
                        <p className="margin-bottom5">
                            <span className="fixed-width200">
                                规则{index + 1}：
                            </span>
                            <span>{val.name || '-'}</span>
                        </p>
                        <p className="margin-bottom5">
                            <span className="fixed-width200">规则对象：</span>
                            <span>{ruleObjectName || '-'}</span>
                        </p>
                        <div className="display-flex">
                            <span className="fixed-width200">过滤条件：</span>
                            <div style={{ width: '80%' }}>
                                {condition.length === 0
                                    ? '-'
                                    : condition.map((value, ind) => {
                                          const {
                                              name = '',
                                              option = [],
                                              type = 4,
                                          } = fieldList.filter(
                                              v =>
                                                  v.fieldName ===
                                                  value.fieldName,
                                          )?.[0] || {};

                                          const filterName =
                                              filterOpts.filter(
                                                  v =>
                                                      v.value ===
                                                      value.filterType,
                                              )[0]?.label || '';
                                          const operator =
                                              operatorOpts.filter(
                                                  v =>
                                                      v.value ===
                                                      value.operator,
                                              )[0]?.title || '';
                                          const unitName =
                                              delayTypeOptions.find(
                                                  it =>
                                                      it.value ===
                                                      value.timeUnit,
                                              )?.label || value.timeUnit;

                                          let va = '';
                                          if (type === 4) {
                                              const attributeValueNameList: any[] =
                                                  [];
                                              (value.value || '')
                                                  .split(',')
                                                  .forEach(ele => {
                                                      const attributeList =
                                                          option.filter(
                                                              v =>
                                                                  v.code ===
                                                                  Number(ele),
                                                          );
                                                      if (
                                                          attributeList.length
                                                      ) {
                                                          attributeValueNameList.push(
                                                              attributeList[0]
                                                                  .name,
                                                          );
                                                      }
                                                  });
                                              va =
                                                  attributeValueNameList.join(
                                                      ',',
                                                  );
                                          } // 为数组
                                          if (type === 2) va = value.value; // 为数值

                                          return (
                                              <div key={value.id}>
                                                  <p className="margin-bottom5">
                                                      <span>
                                                          条件{ind + 1}：
                                                      </span>
                                                      <span>
                                                          {value.filterType ===
                                                              1 &&
                                                              ` ${filterName}（当前时间 - ${name}）${operator} ${value.value} ${unitName}`}
                                                          {value.filterType ===
                                                              2 &&
                                                              ` ${filterName}（${name} ${operator} ${va}）`}
                                                      </span>
                                                  </p>
                                                  {val.conditionInfo
                                                      .conditionList.length !==
                                                      ind + 1 && (
                                                      <span>{andOrName}</span>
                                                  )}
                                              </div>
                                          );
                                      })}
                            </div>
                        </div>
                        <RuleTrigger triggerInfo={val.triggerInfo} readonly />
                        <div className="display-flex">
                            <span className="fixed-width200">动作：</span>
                            <div style={{ width: '80%' }}>
                                {val.actionInfo.templateList.map(
                                    (value, ind) => {
                                        const actonTemplate =
                                            actionTemplateOpts.filter(
                                                v => v.value === value.type,
                                            )[0];
                                        const actionTypeName =
                                            actonTemplate?.label || '';
                                        const templatesTypeName =
                                            actonTemplate?.templates.filter(
                                                v => v.value === value.template,
                                            )[0]?.label || '';

                                        return (
                                            <p
                                                className="margin-bottom5"
                                                key={value.id}
                                            >
                                                <span>动作{ind + 1}：</span>
                                                <span>{`${actionTypeName}（模板：${
                                                    templatesTypeName || '-'
                                                }）`}</span>
                                            </p>
                                        );
                                    },
                                )}
                            </div>
                        </div>
                        <p className="margin-bottom5">
                            <span className="fixed-width200">
                                动作执行时间：
                            </span>
                            <span>
                                {val.actionInfo.executeTime.executeType === 1 &&
                                    val.actionInfo.executeTime.fixedTime &&
                                    `${executeTimeName}（${val.actionInfo.executeTime.fixedTime}）`}
                                {val.actionInfo.executeTime.executeType === 2 &&
                                    val.actionInfo.executeTime.delayTime &&
                                    `${executeTimeName} (${val.actionInfo.executeTime.delayTime} ${delayTypeName} 后执行）`}
                                {val.actionInfo.executeTime.executeType === 3 &&
                                    `${executeTimeName}`}
                            </span>
                        </p>
                        {val.actionInfo.restConfig && (
                            <p className="margin-bottom5">
                                <span className="fixed-width200">
                                    免打扰时间：
                                </span>
                                <span>
                                    {val.actionInfo.restConfig.restBeginTime &&
                                    val.actionInfo.restConfig.restEndTime
                                        ? `每天 ${val.actionInfo.restConfig.restBeginTime} 至 ${val.actionInfo.restConfig.restEndTime} 为免打扰`
                                        : '-'}
                                </span>
                            </p>
                        )}
                        {val.actionInfo.restConfig && (
                            <p className="margin-bottom5">
                                <span className="fixed-width200">
                                    免打扰策略：
                                </span>
                                <span>
                                    {val.actionInfo.restConfig.restStrategy ===
                                        1 && `${restConfigName}`}
                                    {val.actionInfo.restConfig.restStrategy ===
                                        2 &&
                                    val.actionInfo.restConfig.restDelayTime
                                        ? `${restConfigName}（第二天 ${val.actionInfo.restConfig.restDelayTime} 执行）`
                                        : ''}
                                </span>
                            </p>
                        )}
                        {completeRuleList.length !== index + 1 && (
                            <Divider dashed className="divider-no-title" />
                        )}
                    </div>
                );
            })}
        </div>
    );
}
