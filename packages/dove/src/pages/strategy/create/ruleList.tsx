import React, { useEffect, useContext } from 'react';
import {
    Divider,
    Row,
    Col,
    message,
    Button,
    Input,
    Modal,
    Popover,
    Select,
} from 'antd';
import LeadDetailContext from './globalContext';
import RuleConditions from './RuleConditions';
import RuleActions from './RuleActions';
import DefaultFirstSelect from '~/components/DefaultFirstSelect';
import RuleTrigger from './RuleTrigger';
import { QuestionCircleOutlined } from '@ant-design/icons';

export default function rule() {
    const {
        initialValue,
        checkTheRequiredInput,
        completeRuleList,
        ruleList,
        activeRuleList,
        setRuleList,
        ruleObjectOpts,
    } = useContext(LeadDetailContext);

    const deleteRule = uniqId => {
        if (ruleList.length <= 1) {
            message.error('至少保留一条规则');
            return;
        }

        setRuleList(ruleList.filter(one => one.uniqId !== uniqId));
    };

    // 收起
    const confirmSubmissionRule = value => {
        // 校验必输项
        const check = checkTheRequiredInput(value);
        if (check) {
            Modal.warn({
                title: '必输项校验失败，无法收起',
                content: (
                    <div className="msg-title">
                        {check.map(v => (
                            <p key={v}>{v}</p>
                        ))}
                    </div>
                ),
            });
            return;
        }

        setRuleList(
            ruleList.map(one => {
                if (one.uniqId !== value.uniqId) {
                    return one;
                }

                return {
                    ...one,
                    active: false,
                };
            }),
        );
    };

    const updateRule = (payload, uniqId) => {
        setRuleList(
            ruleList.map(one => {
                if (one.uniqId !== uniqId) {
                    return one;
                }

                return {
                    ...one,
                    ...payload,
                };
            }),
        );
    };

    const updateConditionInfo = (conditionInfo, uniqId) => {
        updateRule(
            {
                conditionInfo,
            },
            uniqId,
        );
    };

    const updateActionInfo = (actionInfo, uniqId) => {
        updateRule(
            {
                actionInfo,
            },
            uniqId,
        );
    };

    useEffect(() => {
        //
    }, []);

    if (!activeRuleList.length || !ruleObjectOpts.length) {
        return null;
    }

    console.log(activeRuleList);

    return (
        <div>
            {activeRuleList.map((val, index) => {
                if (!val.conditionInfo) {
                    val.conditionInfo = initialValue.conditionInfo;
                }
                if (!(val.conditionInfo?.conditionList?.length > 0)) {
                    val.conditionInfo.conditionList =
                        initialValue.conditionInfo.conditionList;
                }
                if (!val.objInfo) {
                    val.objInfo = initialValue.objInfo;
                }
                if (!val.triggerInfo) {
                    val.triggerInfo = initialValue.triggerInfo;
                }
                if (!val.actionInfo) {
                    val.actionInfo = initialValue.actionInfo;
                }
                if (!val.actionInfo.executeTime) {
                    val.actionInfo.executeTime =
                        initialValue.actionInfo.executeTime;
                }
                if (!val.actionInfo.templateList.length) {
                    val.actionInfo.templateList =
                        initialValue.actionInfo.templateList;
                }

                const ruleObjectOpt = ruleObjectOpts.filter(
                    v => v.objKey === val.objInfo.objClassName,
                )[0];
                const fieldList = ruleObjectOpt?.fieldList || [];

                return (
                    <div
                        className="rule-content margin-bottom10"
                        key={val.uniqId}
                    >
                        <Row className="margin-bottom10">
                            <Col span={12}>
                                <span className="headline required">
                                    {`规则${
                                        completeRuleList.length + index + 1
                                    }：`}
                                </span>
                                <Input
                                    className="height32"
                                    style={{ width: '80%' }}
                                    placeholder="请输入4-20字的规则名称"
                                    maxLength={20}
                                    value={val.name}
                                    onChange={e => {
                                        updateRule(
                                            { name: e?.target?.value },
                                            val.uniqId,
                                        );
                                    }}
                                />
                            </Col>
                            <Col span={12}>
                                <div className="flont-right">
                                    <Button
                                        onClick={() => {
                                            deleteRule(val.uniqId);
                                        }}
                                    >
                                        删除
                                    </Button>
                                    <Button
                                        type="primary"
                                        className="margin-left10"
                                        onClick={() =>
                                            confirmSubmissionRule(val)
                                        }
                                    >
                                        收起
                                    </Button>
                                </div>
                            </Col>
                        </Row>
                        <Row className="margin-bottom10">
                            <Col
                                span={16}
                                className="display-flex flex-align-center"
                            >
                                <span className="headline required">
                                    <span>规则对象&nbsp;</span>
                                    <Popover
                                        title={null}
                                        content="是一个实体，包含多种对象属性"
                                    >
                                        <QuestionCircleOutlined />
                                    </Popover>
                                </span>
                                <DefaultFirstSelect
                                    style={{ width: '60%' }}
                                    placeholder="请选择对象"
                                    value={val.objInfo.objClassName}
                                    onChange={e => {
                                        updateRule(
                                            {
                                                objInfo: {
                                                    ...val.objInfo,
                                                    objClassName:
                                                        e || undefined,
                                                },
                                                conditionInfo: {
                                                    ...val.conditionInfo,
                                                    conditionList: [],
                                                },
                                            },
                                            val.uniqId,
                                        );
                                    }}
                                    options={ruleObjectOpts.map(it => ({
                                        label: it.objKey,
                                        value: it.objKey,
                                    }))}
                                />
                            </Col>
                        </Row>
                        <Row>
                            {ruleObjectOpts.find(v => v.objKey === '拜访')
                                ?.visitBizInfo?.option?.length &&
                            val.objInfo.objClassName === '拜访' ? (
                                <Col
                                    span={16}
                                    className="display-flex flex-align-center"
                                >
                                    <span className="headline required">
                                        <span>
                                            {
                                                ruleObjectOpts.find(
                                                    v => v.objKey === '拜访',
                                                )?.visitBizInfo.name
                                            }
                                            &nbsp;
                                        </span>
                                    </span>
                                    <Select
                                        mode="multiple"
                                        style={{ width: '60%' }}
                                        placeholder="请选择触发范围"
                                        value={val.visitBizIds
                                            ?.split(',')
                                            .map(Number)}
                                        onChange={v => {
                                            updateRule(
                                                {
                                                    ...val.objInfo,
                                                    visitBizIds:
                                                        // 为空则返回null，不然空字符串会展示"0"
                                                        v.join(',') || null,
                                                },
                                                val.uniqId,
                                            );
                                        }}
                                        options={
                                            ruleObjectOpts
                                                .find(v => v.objKey === '拜访')
                                                ?.visitBizInfo?.option?.map(
                                                    it => ({
                                                        label: it.name,
                                                        value: it.code,
                                                    }),
                                                ) || []
                                        }
                                    />
                                </Col>
                            ) : null}
                        </Row>
                        <Divider dashed>
                            <span className="rule-dividing-line">
                                过滤器设置
                            </span>
                        </Divider>

                        <RuleTrigger
                            triggerInfo={val.triggerInfo}
                            triggerSupported={ruleObjectOpt?.triggerSupported}
                            onChange={triggerInfo =>
                                updateRule({ triggerInfo }, val.uniqId)
                            }
                        />

                        <RuleConditions
                            {...val.conditionInfo}
                            fieldList={fieldList}
                            updateConditionInfo={c =>
                                updateConditionInfo(
                                    {
                                        ...val.conditionInfo,
                                        ...c,
                                    },
                                    val.uniqId,
                                )
                            }
                        />

                        <Divider dashed>执行器设置</Divider>

                        <RuleActions
                            {...val.actionInfo}
                            updateActionInfo={a =>
                                updateActionInfo(
                                    {
                                        ...val.actionInfo,
                                        ...a,
                                    },
                                    val.uniqId,
                                )
                            }
                        />
                    </div>
                );
            })}
        </div>
    );
}
