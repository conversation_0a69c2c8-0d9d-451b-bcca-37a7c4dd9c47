import React, { useEffect, useState, useMemo } from 'react';
import { Row, Col, message, Button, Input, Modal, Spin } from 'antd';
import queryString from 'query-string';
import lodash_ from 'lodash';
import RuleCompletion from './ruleCompletion';
import RuleList from './ruleList';
import { get, post } from '../util';
import LeadDetailContext from './globalContext';
import './index.scss';
import { bellwetherLinkParse } from '@mfe/bellwether-route';
import { PlusOutlined } from '@ant-design/icons';

const createInitialValue = () => ({
    active: true,
    // id: 100, // 规则ID（有就传
    name: undefined,
    objInfo: {
        // 对象
        objClassName: undefined,
        object: undefined,
    },
    triggerInfo: {
        // 触发时机
        type: 1, // 1-周期触发 2-周期定时触发 3-对象数据新增 4-对象数据更新 5-对象数据删除
        timerTime: undefined, // 周期定时触发 执行的时间
        intervalMinutes: undefined, // 周期触发，间隔分钟数
        weekDays: [],
    },
    conditionInfo: {
        conditionList: [
            // 条件
            {
                id: 1,
                filterType: 1, // 1-按时间过滤 2-按属性值过滤
                fieldName: undefined,
                operator: undefined, // 1-等于 2-大于 3-小于 4-大于等于 5-小于等于
                value: '1', //
                timeUnit: 1, // 1-小时 2-分钟
            },
        ],
        conditionRelation: 1, // 1-且 2-或
    },
    actionInfo: {
        templateList: [
            {
                id: 1,
                type: undefined, // 1-自动外呼 2-短信 3-商家的PUSH 4-微信公众号
                template: undefined,
            },
        ],
        executeTime: {
            executeType: 1, // 执行类型 1-固定时间 2-延时触发 3-实时触发 （新增）
            fixedTime: undefined, // 固定时间触发
            delayTime: 1, // 延时触发
            delayTimeUnit: 1, // 1-天，2-小时，3-分钟
        },
        restConfig: {
            restBeginTime: undefined, // 免打扰时间
            restEndTime: undefined, // 免打扰时间
            restStrategy: 1, // 1-规则不执行 2-延迟固定时间执行
            restDelayTime: undefined, // 免打扰延迟固定时间
        },
    },
});

const sanitizeRule = rule => {
    const initialValue = createInitialValue();

    return {
        ...initialValue,
        ...rule,
    };
};

export default function strategyAdd() {
    const { strategyId } = useMemo(
        () => queryString.parse(window.location.search.substr(1)),
        [],
    ); // strategyId ？ 列表粘贴过来的页面 : 新建策略页
    const [policyName, setPolicyName] = useState<string | undefined>(undefined); // 策略名称

    const [policyDetails, setPolicyDetails] = useState<any>(undefined); // 编辑页查询策略详情
    const [ruleList, setRuleList] = useState<
        {
            active?: boolean;
            uniqId?: number;
            conditionInfo?: any;
            id?: number;
        }[]
    >([]); // 未收起规则

    const [ruleObjectOpts, setRuleObjectOpts] = useState([]); // 对象列表、及过滤条件
    const [actionTemplateOpts, setActionTemplateOptsOpts] = useState([]); // 动作对象列表 及 模板

    const [loadVisible, setLoadVisible] = useState(false); // 是否全局loading

    const completeRuleList = ruleList.filter(one => !one.active);
    const activeRuleList = ruleList.filter(one => one.active);
    // 获取对象列表
    const getObjectList = () => {
        const par = {};
        get('/strategy/config/objMeta', par)
            .then(res => {
                if (res.code !== 0) {
                    return;
                }
                setRuleObjectOpts(res.data);
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
            });
    };

    // 获取动作模板列表
    const getActionTemplate = () => {
        const par = {};
        get('/strategy/actionTemp/search', par)
            .then(res => {
                if (res.code !== 0) {
                    return;
                }
                const opts = res.data.map(v => ({
                    templates: v.templates.map(val => ({
                        label: val.desc,
                        value: val.templateId,
                    })),
                    label: v.actionName,
                    value: v.actionType,
                }));
                setActionTemplateOptsOpts(opts);
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
            });
    };

    // 查询策略信息
    const queryingPolicyInformation = () => {
        const par = {
            strategyId,
        };
        get('/strategy/strategy/detail', par)
            .then(res => {
                if (res.code !== 0) {
                    return;
                }
                const { name, ruleList, status } = res.data;
                setPolicyName(name); // 保存策略名
                setPolicyDetails(res.data); // 保存查询的策略详情
                const sanitizedRuleList = ruleList.map(sanitizeRule).map(
                    item =>
                        status === 6
                            ? { ...item, active: true, uniqId: item.id }
                            : item, // status为草稿的话，默认展开所有草稿
                );
                setRuleList(sanitizedRuleList);
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
            });
    };

    // 过滤出全部填写数据的动作
    const filterAction = list => {
        return list.filter(v => v.type && v.template);
    };

    // 校验必输项
    const checkTheRequiredInput = value => {
        const value2 = JSON.stringify(value);
        const val = JSON.parse(value2);
        const ruleName = val.name;
        const ruleObject = val.objInfo.objClassName;
        const { type, timerTime, intervalMinutes, weekDays } = val.triggerInfo;
        const actionList = filterAction(val.actionInfo.templateList);
        const { executeType, fixedTime, delayTime, delayTimeUnit } =
            val.actionInfo.executeTime;

        const { restBeginTime, restEndTime } = val.actionInfo.restConfig;
        // 必输项校验
        const condition1 = !!ruleName;
        const condition1_1 =
            condition1 && ruleName.length >= 4 && ruleName.length <= 20;
        const condition2 = !!ruleObject;
        const condition3 =
            (type === 1 && !!intervalMinutes) ||
            (type === 2 && !!weekDays.length && !!timerTime) ||
            (!!type && type !== 1 && type != 2);
        // 触发时机
        const condition4 = !!actionList.length; // 动作
        const condition5 =
            (executeType === 1 && !!fixedTime) ||
            (executeType === 2 && !!delayTime && !!delayTimeUnit) ||
            executeType === 3; // 动作执行时间

        const restTimeSelected = [restBeginTime, restEndTime].filter(
            Boolean,
        ).length;
        const condition6 = restTimeSelected !== 1;
        const condition7 =
            restTimeSelected === 0 || restBeginTime !== restEndTime;

        const msg: string[] = [];
        !condition1 && msg.push('规则名称未输入');
        !condition1_1 && msg.push('规则名称长度必须在4-20个字符');
        !condition2 && msg.push('规则对象未选择');
        !condition3 && msg.push('触发时机信息不完整');
        !condition4 && msg.push('动作信息不完整');
        !condition5 && msg.push('动作执行时间信息不完整');
        !condition6 && msg.push('免打扰时间信息不完整');
        !condition7 && msg.push('免打扰时间开始时间和结束时间不能相同');
        if (
            !condition1 ||
            !condition2 ||
            !condition3 ||
            !condition4 ||
            !condition5 ||
            !condition6 ||
            !condition7
        ) {
            return msg;
        }
        return false;
    };

    // 过滤出全部填写数据的过滤条件
    const filterCondition = list => {
        if (!list || list.length === 0) return [];
        return list.filter(v => {
            const { filterType, fieldName, operator, value } = v;
            return (
                (filterType === 1 && !!fieldName && !!operator && !!value) ||
                (filterType === 2 && !!fieldName && !!operator && !!value)
            );
        });
    };

    // 保存草稿弹框
    const saveDraftModal = () => {
        // 草稿需要校验名称
        if (!policyName) {
            message.error('请输入策略名称');
            return;
        }
        // 未填写完成也支持保存
        Modal.confirm({
            title: '保存草稿',
            content: '确定保存草稿吗？',
            onOk: () => {
                saveDraft();
            },
        });
    };

    // 保存草稿
    const saveDraft = () => {
        setLoadVisible(true);
        const ruleListNew = ruleList.map(v => {
            const obj = { ...v };
            delete obj.active;
            delete obj.uniqId;

            obj.conditionInfo.conditionList = filterCondition(
                obj.conditionInfo.conditionList,
            );

            return obj;
        });

        const par: {
            id?: number;
            name: string;
            ruleList: any[];
            isDraft: true; //是否是保存草稿
        } = {
            name: policyName as string,
            ruleList: ruleListNew, // 已提交的规则信息 + 未完成的
            isDraft: true, //是否是保存草稿
        };

        if (policyDetails) {
            par.id = policyDetails.id;
        }

        post('/strategy/strategy/save', par)
            .then(res => {
                if (res.code !== 0) {
                    return;
                }

                message.success('操作成功');
                setPolicyDetails(x => ({ ...x, id: res.data }));
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
            })
            .finally(() => {
                setLoadVisible(false);
            });
    };

    // 生成策略
    const generationStrategy = () => {
        // 校验必输项
        const checks: { id: number; check: string[] }[] = [];
        ruleList.map((v, index) => {
            const check = checkTheRequiredInput(v);
            if (check) {
                const len = completeRuleList.length;
                checks.push({ id: len + index + 1, check });
            }
        });

        const strategyNameMsg = !policyName
            ? '策略名称未输入'
            : policyName.length < 4 || policyName.length > 20
            ? '策略名称长度需要在4-20字'
            : '';

        if (checks.length > 0 || !policyName) {
            Modal.warn({
                title: '必输项校验失败',
                content: (
                    <div>
                        {strategyNameMsg ? (
                            <span className="msg-title-1">
                                {strategyNameMsg}
                            </span>
                        ) : null}
                        {checks.map(v => (
                            <div key={v.id} className="msg-title">
                                {checks.length > 0 && (
                                    <>
                                        <span className="msg-title-2">{`规则${v.id}`}</span>
                                        {v.check.map(v => (
                                            <p key={v}>{v}</p>
                                        ))}
                                    </>
                                )}
                            </div>
                        ))}
                    </div>
                ),
            });
        } else {
            saveStrategy();
        }
    };

    // 保存策略
    const saveStrategy = () => {
        setLoadVisible(true);
        //
        const ruleListNew = ruleList.map(v => {
            const obj = { ...v };
            delete obj.active;
            delete obj.uniqId;

            obj.conditionInfo.conditionList = filterCondition(
                obj.conditionInfo.conditionList,
            );

            return obj;
        });
        const par: {
            id?: number;
            name: string;
            ruleList: any[];
        } = {
            name: policyName as string,
            ruleList: ruleListNew, // 已提交的规则信息 + 未完成的
        };
        if (policyDetails) {
            par.id = policyDetails.id;
        }
        post('/strategy/strategy/save', par)
            .then(res => {
                if (res.code !== 0) {
                    return;
                }

                message.success('操作成功');
                location.href = bellwetherLinkParse('list');
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
            })
            .finally(() => {
                setLoadVisible(false);
            });
    };

    // 新建规则
    const newRule = (copyVal = undefined) => {
        // 新增复制的规则 || 新增空规则
        const nextValue = copyVal
            ? lodash_.cloneDeep(copyVal)
            : createInitialValue();
        // aa.id = (ruleList.at(-1)?.id || un) + 100
        ruleList.push({ ...nextValue, id: undefined, uniqId: Math.random() });
        setRuleList([...ruleList]);
    };

    useEffect(() => {
        getObjectList();
        getActionTemplate();
        if (strategyId) {
            // 查询策略信息 并 反显
            queryingPolicyInformation();
        } else {
            // 新建页
            newRule(undefined);
        }
    }, []);

    const provider = {
        initialValue: createInitialValue(),
        filterCondition,
        checkTheRequiredInput,
        completeRuleList,
        activeRuleList,
        ruleList: ruleList,
        setRuleList,
        ruleObjectOpts,
        actionTemplateOpts,
        newRule,
    };

    return (
        <LeadDetailContext.Provider value={provider}>
            <div className="page-wrap">
                <Spin spinning={loadVisible}>
                    <Row className="display-flex rule-name">
                        <Col span={12}>
                            <span className="headline required">策略名称</span>
                            <Input
                                className="height32"
                                style={{ width: '80%' }}
                                placeholder="请输入4-20字的策略名称"
                                maxLength={20}
                                value={policyName}
                                onChange={e => {
                                    const val = e?.target?.value || undefined;
                                    setPolicyName(val);
                                }}
                            />
                        </Col>
                        <Col span={12}>
                            <div className="flont-right">
                                <Button onClick={saveDraftModal}>
                                    保存草稿
                                </Button>
                                <Button
                                    type="primary"
                                    className="margin-left10"
                                    onClick={generationStrategy}
                                >
                                    生成策略
                                </Button>
                            </div>
                        </Col>
                    </Row>
                    {/* 收起 规则信息 */}
                    {completeRuleList.length > 0 && <RuleCompletion />}

                    {/* 展开 规则信息 */}
                    {activeRuleList.length > 0 && <RuleList />}

                    <Row className="display-flex rule-name">
                        <Col span={24}>
                            <div>
                                <Button
                                    type="primary"
                                    className="new-rule-butt"
                                    icon={<PlusOutlined />}
                                    onClick={() => newRule(undefined)}
                                >
                                    <span>新建规则</span>
                                </Button>
                            </div>
                        </Col>
                    </Row>
                </Spin>
            </div>
        </LeadDetailContext.Provider>
    );
}
