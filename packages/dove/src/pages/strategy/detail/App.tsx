import React, { useEffect, useMemo, useState } from 'react';
import { Tabs, message, Spin } from 'antd';
import queryString from 'query-string';
import DetailInfo from './DetailInfo';
import DetailAction from './DetailAction';
import { get } from '../util';
import './index.scss';
import { bellwetherLinkParse } from '@mfe/bellwether-route';

const { TabPane } = Tabs;

export default function strategyDetails() {
    const { strategyId, tab = '1' } = useMemo(
        () => queryString.parse(window.location.search.substr(1)),
        [],
    );
    const [loadVisible, setLoadVisible] = useState(false); // 是否全局loading
    const [ruleObjectOpts, setRuleObjectOpts] = useState([]); // 对象列表、及过滤条件
    const [actionTemplateOpts, setActionTemplateOptsOpts] = useState([]); // 动作对象列表 及 模板

    const [information, setInformation] = useState({
        id: 11,
        name: '-',
        creatorName: '-', // 创建人
        createTime: '-', // 创建时间
        updateTime: '-', // 最后编辑时间
        status: 1, // 策略状态 1-策略发布 2-审批中 3-审批失败 4-进行中 5-暂停中 6-草稿
        ruleList: [
            {
                id: undefined, // 规则ID（有就传
                name: '-',
                objInfo: {
                    // 对象
                    objClassName: '-',
                },
                triggerInfo: {
                    // 触发时机
                    type: 1, // 1-周期触发 2-周期定时触发 3-对象数据新增 4-对象数据更新 5-对象数据删除
                    timerTime: undefined, // 周期定时触发 执行的时间
                    intervalMinutes: undefined, // 周期触发，间隔分钟数
                    weekDays: [],
                },
                conditionInfo: {
                    conditionList: [],
                    conditionRelation: 1, // 1-且 2-或
                },
                actionInfo: {
                    templateList: [],
                    executeTime: {
                        executeType: 1, // 执行类型 1-固定时间 2-延时触发 3-实时触发 （新增）
                        fixedTime: undefined, // 固定时间触发
                        delayTime: undefined, // 延时触发
                        delayTimeUnit: undefined, // 1-天，2-小时，3-分钟
                    }, // 动作执行时间
                    restConfig: {
                        restBeginTime: undefined, // 免打扰时间
                        restEndTime: undefined, // 免打扰时间
                        restStrategy: undefined, // 1-规则不执行 2-延迟固定时间执行
                        restDelayTime: undefined, // 免打扰延迟固定时间
                    }, // 免打扰时间 免打扰策略
                },
            },
        ],
    });

    const callback = key => {
        location.href = bellwetherLinkParse(
            `detail?tab=${key}&strategyId=${strategyId}`,
        );
    };

    // 获取对象列表
    const getObjectList = () => {
        const par = {};
        get('/strategy/config/objMeta', par)
            .then(res => {
                if (res.code !== 0) {
                    return;
                }
                setRuleObjectOpts(res.data);
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
            });
    };

    // 获取动作模板列表
    const getActionTemplate = () => {
        const par = {};
        get('/strategy/actionTemp/search', par)
            .then(res => {
                if (res.code !== 0) {
                    return;
                }

                const opts = res.data.map(v => ({
                    templates: v.templates.map(val => ({
                        label: val.desc,
                        value: val.templateId,
                    })),
                    label: v.actionName,
                    value: v.actionType,
                }));
                setActionTemplateOptsOpts(opts);
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
            });
    };

    // 查询详情
    const queryDetails = () => {
        setLoadVisible(true);
        const par = {
            strategyId,
        };
        get('/strategy/strategy/detail', par)
            .then(res => {
                if (res.code !== 0 || !res.data) {
                    return;
                }

                setInformation(res.data);
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
            })
            .finally(() => {
                setLoadVisible(false);
            });
    };

    useEffect(() => {
        getObjectList();
        getActionTemplate();
        queryDetails();
    }, []);

    return (
        <div className="page-wrap">
            <Spin spinning={loadVisible}>
                <Tabs activeKey={tab as any} onChange={callback}>
                    <TabPane tab="策略详情" key="1">
                        <DetailInfo
                            information={information}
                            ruleObjectOpts={ruleObjectOpts}
                            actionTemplateOpts={actionTemplateOpts}
                        />
                    </TabPane>
                    <TabPane tab="动作记录" key="2">
                        <DetailAction
                            information={information}
                            setLoadVisible={setLoadVisible}
                            actionTemplateOpts={actionTemplateOpts}
                        />
                    </TabPane>
                </Tabs>
            </Spin>
        </div>
    );
}
