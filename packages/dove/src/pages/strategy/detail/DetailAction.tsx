import React, { useEffect, useMemo, useState } from 'react';
import {
    message,
    Button,
    Input,
    Row,
    Col,
    Select,
    Table,
    DatePicker,
} from 'antd';
import queryString from 'query-string';
import { post } from '../util';
import './index.scss';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

export default function StrategyAction({
    information,
    setLoadVisible,
    actionTemplateOpts,
}) {
    const dateFormat = 'YYYY-MM-DD HH:mm:ss';
    const ruleNodeOpts = information.ruleList.map(v => ({
        label: v.name,
        value: v.id,
    }));
    const { strategyId, tab } = useMemo(
        () => queryString.parse(window.location.search.substr(1)),
        [],
    );

    const [merchant, setMerchant] = useState(undefined);
    const [ruleNode, setRuleNode] = useState<number | undefined>(undefined);
    const [actionsTime, setActionsTime] = useState([
        dayjs().subtract(7, 'days').startOf('day').format(dateFormat),
        dayjs().endOf('day').format(dateFormat),
    ]);
    const [dataSource, setDataSource] = useState([]);
    // const [selectedRowKeys, setSelectedRowKeys] = useState([]);

    const [queryParams, setQueryParams] = useState({
        objectId: merchant,
        ruleId: ruleNode,
        startTime: actionsTime[0],
        endTime: actionsTime[1],
        strategyId,
        page: 1, // 页码
        pageSize: 20, // 每页数量
    }); // 用于导出参数
    const [listParams, setListParams] = useState({
        page: 0,
        pageSize: 0,
        total: 0,
    }); // 用于表格分页

    const getColumns = () => {
        return [
            {
                title: '动作类型',
                dataIndex: 'actionType',
                render: actionType => {
                    return actionTemplateOpts.filter(
                        v => v.value === actionType,
                    )[0]?.label;
                },
            },
            { title: '动作记录ID', dataIndex: 'id' },
            { title: '规则节点', dataIndex: 'ruleId' },
            { title: '规则名', dataIndex: 'ruleName' },
            { title: '动作执行时间', dataIndex: 'executeTime', width: '10%' },
            { title: '对象Id', dataIndex: 'objectId' },
            { title: '对象名称', dataIndex: 'objectName' },
            { title: '用户意向', dataIndex: 'intention' },
            { title: '标签信息', dataIndex: 'tags' },
            { title: '按键信息', dataIndex: 'keyPressList' },
            { title: '未接原因', dataIndex: 'hangupReason' },
            {
                title: '通话时长',
                dataIndex: 'talkingTimeLen',
                render: s => (s != null ? `${s}秒` : '-'),
            },
            {
                title: '电话录音',
                dataIndex: 'audioUrl',
                width: 320,
                render: url => {
                    return url ? <audio src={url} controls /> : '-';
                },
            },
            // { title: '是否触达', dataIndex: ' ' },
            // { title: '是否点击', dataIndex: ' ' },
        ];
    };

    const screeningPar = pagination => {
        const par = {
            objectId: merchant,
            ruleId: ruleNode,
            startTime: actionsTime[0],
            endTime: actionsTime[1],
            strategyId: Number(strategyId),
            page: pagination.page, // 页码
            pageSize: pagination.pageSize, // 每页数量
        };
        query(par);
        // @ts-ignore
        setQueryParams(par);
    };

    const query = par => {
        setLoadVisible(true);
        post('/strategy/strategy/action/r/list', par)
            .then(res => {
                if (res.code !== 0) {
                    return;
                }

                const { total = 0, actionDtoList = [] } = res.data;
                setDataSource(actionDtoList);
                setListParams({
                    page: par.page,
                    pageSize: par.pageSize,
                    total,
                });
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
            })
            .finally(() => {
                setLoadVisible(false);
            });
    };

    const derive = () => {
        setLoadVisible(true);
        //
        post('/strategy/strategy/action/r/export', queryParams)
            .then(res => {
                if (res.code !== 0) {
                    return;
                }

                message.success('导出成功，请等待大象通知下载文件');
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
            })
            .finally(() => {
                setLoadVisible(false);
            });
    };

    useEffect(() => {
        screeningPar({ page: 1, pageSize: 20 });
    }, [tab]);

    return (
        <div>
            <div>
                <Row>
                    <Col span={6}>
                        <p>对象：</p>
                        <div>
                            <Input
                                value={merchant}
                                allowClear
                                style={{ width: '90%' }}
                                placeholder="请选择对象id"
                                onChange={v => {
                                    // @ts-ignore
                                    setMerchant(v.target.value || undefined);
                                }}
                            />
                        </div>
                    </Col>
                    <Col span={6}>
                        <p>规则节点：</p>
                        <div>
                            <Select
                                value={ruleNode}
                                allowClear
                                style={{ width: '90%' }}
                                placeholder="请选择策略下规则"
                                onChange={v => {
                                    setRuleNode(v);
                                }}
                            >
                                {ruleNodeOpts.map(val => (
                                    <Option key={val.label} value={val.value}>
                                        {val.label}
                                    </Option>
                                ))}
                            </Select>
                        </div>
                    </Col>
                    <Col span={6}>
                        <p>动作时间：</p>
                        <div>
                            <RangePicker
                                allowClear={false}
                                value={[
                                    dayjs(actionsTime[0], dateFormat),
                                    dayjs(actionsTime[1], dateFormat),
                                ]}
                                showTime={{ format: 'HH:mm:ss' }}
                                format={dateFormat}
                                onChange={(v, s) => {
                                    setActionsTime(s);
                                }}
                            />
                        </div>
                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <div className="operation-area">
                            <Button
                                type="primary"
                                onClick={() => {
                                    screeningPar({ page: 1, pageSize: 20 });
                                }}
                            >
                                查询
                            </Button>
                            <Button
                                disabled={!dataSource.length}
                                onClick={derive}
                            >
                                导出
                            </Button>
                        </div>
                    </Col>
                </Row>
            </div>
            <div>
                <Table
                    // rowSelection={{
                    //     selectedRowKeys: selectedRowKeys,
                    //     onChange: rows => {
                    //         setSelectedRowKeys(rows);
                    //     },
                    // }}
                    rowKey="id"
                    dataSource={dataSource}
                    columns={getColumns()}
                    scroll={{ x: 1500 }}
                    pagination={{
                        onChange: (page, pageSize) => {
                            screeningPar({ page, pageSize });
                        },
                        onShowSizeChange: (page, pageSize) => {
                            screeningPar({ page, pageSize });
                        },
                        current: listParams.page,
                        pageSize: listParams.pageSize,
                        total: listParams.total,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: total => `共 ${total} 条`,
                    }}
                />
            </div>
        </div>
    );
}
