.page-wrap {
    padding: 20px 30px;

    //
    // 公共样式区
    .display-flex {
        display: flex;
    }

    .title-1 {
        display: inline-block;
        width: 100px;
    }

    .title-2 {
        display: inline-block;
        width: 80px;
    }

    .title-3 {
        display: inline-block;
        width: 60px;
    }

    .title-4 {
        margin: 0 0 0 40px;
    }

    .title-5 {
        display: inline-block;
        margin-bottom: 10px;
    }

    .value-1 {
        color: #aaa;
    }

    .margin-bottom5 {
        margin-bottom: 5px;
    }

    //
    .operation-area {
        display: flex;
        justify-content: flex-end;

        button {
            margin: 20px 0 20px 20px;
        }
    }
}
