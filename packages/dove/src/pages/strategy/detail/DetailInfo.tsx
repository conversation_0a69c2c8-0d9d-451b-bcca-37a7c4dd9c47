import React, { useEffect, useState } from 'react';
import {
    policyState,
    conditionRelationOpts,
    operatorOpts,
    triggerOpts,
    executeTypeOpts,
    delayTypeOptions,
    restOpts,
    filterOpts,
    periodOptions,
} from '../const';
import './index.scss';
import { TriggerType } from '../types';
import { getCityNameFromId } from '@src/components/CitySelector';

const Condition = ({
    val,
    fieldList,
    conditionList,
    conditionRelationName,
    i,
}) => {
    const { filterType, fieldName, operator, value, timeUnit } = val;
    // console.log('--条件-->', val)
    const {
        name = '',
        option = [],
        type = 4,
    } = fieldList.filter(v => v.fieldName === fieldName)?.[0] || {};
    const unitName =
        delayTypeOptions.find(it => it.value === timeUnit)?.label || timeUnit;

    const operatorName = operatorOpts.find(v => v.value === operator)?.title; // 操作符名称
    const filterName = filterOpts.find(v => v.value === filterType)?.label; // 过滤条件名称

    let va = '';
    if (type === 4) {
        const attributeValueNameList: any[] = [];
        (value || '').split(',').forEach(ele => {
            const attributeList = option.filter(v => v.code === Number(ele));
            if (attributeList.length) {
                attributeValueNameList.push(attributeList[0].name);
            }
        });
        va = attributeValueNameList.join(',');
    } // 为数组
    if (type === 2) va = value; // 为数值

    const [cityValue, setCityValue] = useState('');
    useEffect(() => {
        (async () => {
            if (fieldName === 'locationIds') {
                setCityValue(await getCityNameFromId(value));
            }
        })();
    }, []);

    return (
        <div key={i}>
            <p className="title-3 margin-bottom5">条件{i + 1}：</p>
            <span>
                {filterType === 1 &&
                    `${filterName}（当前时间 - ${name} ${operatorName} ${value} ${unitName}）`}
                {filterType === 2 &&
                    `${filterName}（${name} ${operatorName} ${
                        va || cityValue
                    }）`}
            </span>
            {conditionList.length !== i + 1 && (
                <p className="margin-bottom5">{conditionRelationName}</p>
            )}
        </div>
    );
};

export default function strategyDetails({
    information,
    ruleObjectOpts,
    actionTemplateOpts,
}) {
    const { creatorName, createTime, updateTime, status, ruleList } =
        information;
    const statusName =
        policyState.filter(v => v.value === status)[0]?.label || '-'; // 策略状态名称

    // 过滤条件
    const showFiltrationCondition = (conditionInfo, fieldList) => {
        if (!conditionInfo) return null;

        const { conditionList = [], conditionRelation } = conditionInfo;
        const conditionRelationName = conditionRelationOpts.filter(
            v => v.value === conditionRelation,
        )[0]?.label; // 且 、 或

        return conditionList.map((val, i) => (
            <Condition
                fieldList={fieldList}
                val={val}
                i={i}
                conditionList={conditionList}
                conditionRelationName={conditionRelationName}
            />
        ));
    };

    // 触发时机
    const showTriggerTiming = triggerInfo => {
        if (!triggerInfo) return null;

        const { type, timerTime, weekDays, intervalMinutes } = triggerInfo;
        const triggerName = triggerOpts.filter(v => v.value === type)[0]?.label; // 触发时机名称
        const periodTypeName =
            periodOptions.filter(v => v.value === weekDays[0])[0]?.label ||
            '天'; // 触发时机 周期单位（没有值，则为每天

        const getExtra = () => {
            switch (type) {
                case TriggerType.PERIOD:
                    return intervalMinutes
                        ? `（每${intervalMinutes}分钟触发一次）`
                        : '';
                case TriggerType.TIMING:
                    return timerTime
                        ? `（每${periodTypeName}${timerTime}触发）`
                        : '';
            }
            return '';
        };

        const extra = getExtra();

        return (
            <span className="value-1">
                {triggerName}
                {extra}
            </span>
        );
    };

    // 动作
    const showAction = actionInfo => {
        if (!actionInfo) return null;

        const { templateList = [] } = actionInfo;
        return templateList.map((val, i) => {
            const { type, template } = val;
            const temp0 =
                actionTemplateOpts.filter(v => v.value === type)?.[0] || [];

            const typeName = temp0?.label; // 动作类型
            const templateName =
                temp0.templates?.filter(v => v.value === template)[0]?.label ||
                ''; // 模板

            return (
                <div key={i}>
                    <p className="title-3 margin-bottom5">动作{i + 1}：</p>
                    <span>{`${typeName}（${templateName}）`}</span>
                </div>
            );
        });
    };

    // 动作执行时间
    const showExecuteTime = executeTime => {
        if (!executeTime) return '-';

        const { executeType, fixedTime, delayTime, delayTimeUnit } =
            executeTime;
        const executeName = executeTypeOpts.filter(
            v => v.value === executeType,
        )[0]?.label; // 执行类型名称
        const delayUnitName = delayTypeOptions.filter(
            v => v.value === delayTimeUnit,
        )[0]?.label; // 延时单位名称

        return (
            <span className="value-1">
                {executeType === 1 && fixedTime
                    ? `${executeName}（${fixedTime}）`
                    : ''}
                {executeType === 2 && delayTime && delayUnitName
                    ? `${executeName} (${delayTime} ${delayUnitName} 后执行）`
                    : ''}
                {executeType === 3 && `${executeName}`}
            </span>
        );
    };

    // 规则列表
    const showRuleList = (ruleList: any[] = []) => {
        return ruleList.map((val, index) => {
            const {
                id,
                name,
                objInfo,
                visitBizIds,
                triggerInfo,
                conditionInfo,
                actionInfo,
            } = val;
            const { executeTime, restConfig } = actionInfo;
            const { restBeginTime, restEndTime, restStrategy, restDelayTime } =
                restConfig;
            const restName = restOpts.filter(v => v.value === restStrategy)[0]
                ?.label; // 延时单位名称

            const ruleObjectOpt = ruleObjectOpts.filter(
                v => v.objKey === val.objInfo?.objClassName,
            )[0];
            const fieldList = ruleObjectOpt?.fieldList || [];
            //

            return (
                <div key={id}>
                    <p>
                        <span className="title-2">{`规则${index + 1}：`}</span>
                        <span>{name}</span>
                    </p>
                    <p>
                        <span className="title-2">规则对象：</span>
                        <span>{objInfo?.objClassName || '-'}</span>
                    </p>
                    {objInfo?.objClassName === '拜访' ? (
                        <p>
                            <span className="title-2">触发范围：</span>
                            <span>
                                {visitBizIds
                                    ?.split(',')
                                    .map(v => {
                                        return ruleObjectOpt?.visitBizInfo.option.find(
                                            op => String(op.code) === String(v),
                                        ).name;
                                    })
                                    .join('、') || '-'}
                            </span>
                        </p>
                    ) : null}
                    <div className="display-flex">
                        <span className="title-2">规则id：</span>
                        <div>
                            <p>{id}</p>
                            <span className="title-1">过滤条件：</span>
                            {!(conditionInfo?.conditionList?.length > 0) && (
                                <span className="title-5">-</span>
                            )}
                            <div className="title-4 value-1">
                                {showFiltrationCondition(
                                    conditionInfo,
                                    fieldList,
                                )}
                            </div>
                            <p>
                                <span className="title-1">触发时机：</span>
                                {showTriggerTiming(triggerInfo)}
                            </p>

                            <span className="title-1">动作：</span>
                            {!(actionInfo?.templateList?.length > 0) && (
                                <span className="title-5">-</span>
                            )}
                            <div className="title-4 value-1">
                                {showAction(actionInfo)}
                            </div>
                            <p>
                                <span className="title-1">动作执行时间：</span>
                                {showExecuteTime(executeTime)}
                            </p>
                            <p>
                                <span className="title-1">免打扰时间：</span>
                                <span className="value-1">
                                    {restBeginTime && restEndTime
                                        ? `每天 ${restBeginTime} 至 ${restEndTime} 为免打扰`
                                        : '-'}
                                </span>
                            </p>
                            <p>
                                <span className="title-1">免打扰策略：</span>
                                <span className="value-1">
                                    {restStrategy === 1 && `${restName}`}
                                    {restStrategy === 2 && restDelayTime
                                        ? `${restName}（第二天 ${restDelayTime} 执行）`
                                        : ''}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            );
        });
    };

    useEffect(() => {
        // queryDetails();
    }, []);

    return (
        <div>
            <p>
                <span className="title-1">创建人：</span>
                <span>{creatorName}</span>
            </p>
            <p>
                <span className="title-1">创建时间：</span>
                <span>{createTime}</span>
            </p>
            <p>
                <span className="title-1">最后编辑时间：</span>
                <span>{updateTime}</span>
            </p>
            <p>
                <span className="title-1">策略状态：</span>
                <span>{statusName}</span>
            </p>
            <div className="display-flex">
                <span className="title-1">策略规则：</span>
                <div>{showRuleList(ruleList)}</div>
            </div>
        </div>
    );
}
