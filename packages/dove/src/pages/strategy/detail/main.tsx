import React from 'react';
import zhCN from 'antd/es/locale/zh_CN';
import { ConfigProvider } from 'antd';
import App from './App';
// import 'antd/dist/antd.less';
// import '~/overrides-antd.less';
import LayoutProvider from '~/components/Layout';
import { render } from '~/module/root';

const Main = () => (
    <ConfigProvider locale={zhCN}>
        <LayoutProvider>
            <App />
        </LayoutProvider>
    </ConfigProvider>
);

render(<Main />, '策略详情');
