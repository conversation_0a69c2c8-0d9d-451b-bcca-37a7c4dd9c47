import { TriggerType, OperatorType } from './types';

export const periodOptions = [
    { label: '天', value: 0 },
    { label: '周一', value: 1 },
    { label: '周二', value: 2 },
    { label: '周三', value: 3 },
    { label: '周四', value: 4 },
    { label: '周五', value: 5 },
    { label: '周六', value: 6 },
    { label: '周天', value: 7 },
];

export const policyState = [
    { label: '策略发布', value: 1 },
    { label: '审批中', value: 2 },
    { label: '审批失败', value: 3 },
    { label: '进行中', value: 4 },
    { label: '暂停中', value: 5 },
    { label: '草稿', value: 6 },
];

export const conditionRelationOpts = [
    { label: '且', value: 1 },
    { label: '或', value: 2 },
];

export const triggerOpts = [
    { label: '周期触发', value: TriggerType.PERIOD },
    { label: '周期定时触发', value: TriggerType.TIMING },
    { label: '对象数据新增', value: TriggerType.OBJ_CREATE },
    { label: '对象数据更新', value: TriggerType.OBJ_UPDATE },
    { label: '对象数据删除', value: TriggerType.OBJ_DEL },
];

export const filterOpts = [
    { label: '按时间过滤', value: 1 },
    { label: '按属性值过滤', value: 2 },
];

export const operatorOpts = [
    { title: '=', label: '等于', value: OperatorType.EQUAL },
    { title: '>', label: '大于', value: OperatorType.GT },
    { title: '<', label: '小于', value: OperatorType.LT },
    { title: '>=', label: '大于等于', value: OperatorType.GE },
    { title: '<=', label: '小于等于', value: OperatorType.LE },
    { title: 'in', label: '包括', value: OperatorType.IN },
    { title: 'not in', label: '不包括', value: OperatorType.NOT_IN },
];

export const executeTypeOpts = [
    { label: '固定时间执行', value: 1 },
    { label: '延迟执行', value: 2 },
    { label: '实时执行', value: 3 },
];

export const restOpts = [
    { label: '规则不执行', value: 1 },
    { label: '延迟固定时间执行', value: 2 },
];

export const delayTypeOptions = [
    { label: '小时', value: 1 },
    { label: '分钟', value: 2 },
];
