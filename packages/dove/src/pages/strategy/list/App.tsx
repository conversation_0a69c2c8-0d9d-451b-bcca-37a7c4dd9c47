/* eslint-disable @typescript-eslint/indent */
import React, { useEffect, useState } from 'react';
import { message, Table, Button, Modal, Popconfirm } from 'antd';
import './index.scss';
import { get, post } from '../util';
import { bellwetherLinkParse } from '@mfe/bellwether-route';
import { CloseCircleOutlined } from '@ant-design/icons';

const STATUS_APPROVAL = 2;
const STATUS_DOING = 4;
const STATUS_STOP = 5;
const STATUS_FAIL = 3;
const STATUS_DEPLOY = 1;
const STATUS_DRAFT = 6;

const STATUS_OPTION = [
    {
        label: '策略发布',
        value: STATUS_DEPLOY,
    },
    {
        label: '审批中',
        value: STATUS_APPROVAL,
    },
    {
        label: '进行中',
        value: STATUS_DOING,
    },
    {
        label: '暂停中',
        value: STATUS_STOP,
    },
    {
        label: '审批失败',
        value: STATUS_FAIL,
    },
    {
        label: '草稿',
        value: STATUS_DRAFT,
    },
];

export default function strategyRecordNew() {
    // const [selectedRowKeys, setSelectedRowKeys] = useState([]); // 多选框勾选项
    const [dataSource, setDataSource] = useState([]); // 表格数据项
    const [nowPage, setNowPage] = useState(1); // 当前页
    const [nowPageSize, setNowPageSize] = useState(1); // 每页条数
    const [nowTotal, setNowTotal] = useState(0); // 总数
    const [copyStrategyId, setCopyStrategyId] = useState(0); // 复制策略id

    const [loadVisible, setLoadVisible] = useState(false); // 是否全局loading

    const screeningPar = pagination => {
        const par = {
            // page: pageNo,
            // pageSize,
            pageNo: pagination.page, // 页码
            pageSize: pagination.pageSize, // 每页数量
        };
        getStrategyList(par);
    };

    const getStrategyList = par => {
        setLoadVisible(true);
        post('/strategy/strategy/search', par)
            .then(res => {
                if (res.code !== 0) {
                    return;
                }
                const { data = [], pageNum, pageSize, total } = res.data || {};
                setDataSource([]);
                setNowPage(pageNum);
                setNowPageSize(pageSize);
                setNowTotal(total);
                setDataSource(data);
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
            })
            .finally(() => {
                setLoadVisible(false);
            });
    };

    // const onSelectChange = selectedRowKeys => {
    //     setSelectedRowKeys(selectedRowKeys);
    // };

    const deleteRecord = item => {
        setLoadVisible(true);
        const { strategyId } = item;
        Modal.confirm({
            icon: (
                <CloseCircleOutlined style={{ fontSize: 20, color: 'red' }} />
            ),
            title: '确认删除',
            content: <div>删除后信息将无法恢复</div>,
            okText: '确认',
            cancelText: '取消',
            onOk: () => {
                get('/strategy/strategy/del', { strategyId })
                    .then(res => {
                        if (res.code !== 0) {
                            return;
                        }

                        message.success('删除成功！');
                        Modal.destroyAll();
                        // getStrategyList();
                        screeningPar({ page: 1, pageSize: 20 });
                    })
                    .finally(() => {
                        setLoadVisible(false);
                    });
            },
        });
    };

    const handleAction = (type, item) => {
        setLoadVisible(true);
        const { strategyId } = item;
        let url = '';

        if (type === 'stop') {
            url = '/strategy/strategy/suspended';
        }
        if (type === 'run') {
            url = '/strategy/strategy/open';
        }

        get(url, { strategyId })
            .then(res => {
                if (res.code !== 0) {
                    return;
                }

                message.success(`${type == 'stop' ? '暂停' : '启动'}成功！`);
                // getStrategyList();
                screeningPar({ page: 1, pageSize: 20 });
            })
            .finally(() => {
                setLoadVisible(false);
            });
    };

    const generateStrategyId = id => {
        setLoadVisible(true);
        const par = {
            strategyId: id,
        };
        get('/strategy/strategy/copy', par)
            .then(res => {
                if (res.code !== 0) {
                    return;
                }

                setCopyStrategyId(res.data.strategyId);
                //
                screeningPar({ page: 1, pageSize: 20 });
                message.success('已复制成功');
            })
            .catch(err => {
                message.error(
                    (err && (err.msg || err.message)) || '请求出错，请重试',
                );
            })
            .finally(() => {
                setLoadVisible(false);
            });
    };

    const getColumns = () => {
        return [
            {
                title: '策略名称',
                dataIndex: 'strategyName',
            },
            {
                title: '状态',
                dataIndex: 'strategyStatus',
                render: strategyStatus => {
                    const str = STATUS_OPTION.filter(
                        i => i.value === strategyStatus,
                    );
                    const title = str[0] && str[0].label;
                    return <div>{title}</div>;
                },
            },
            {
                title: '最后编辑时间',
                dataIndex: 'lastEditTime',
            },
            {
                title: '创建时间',
                dataIndex: 'dateCreated',
            },
            {
                title: '创建人',
                dataIndex: 'creatorName',
                render: (creatorName, item) => {
                    return (
                        <div>
                            {creatorName}({item.creatorMis})
                        </div>
                    );
                },
            },
            {
                title: '操作',
                dataIndex: 'strategyId',
                render: (strategyId, item) => {
                    return (
                        <div>
                            {[STATUS_DRAFT].includes(item.strategyStatus) && (
                                <Button
                                    type="link"
                                    style={{ padding: 0, marginRight: 10 }}
                                    onClick={() => {
                                        location.href = bellwetherLinkParse(
                                            `create?strategyId=${
                                                copyStrategyId || strategyId
                                            }`,
                                        );
                                    }}
                                >
                                    编辑
                                </Button>
                            )}
                            <a
                                href={bellwetherLinkParse(
                                    `detail?tab=1&strategyId=${
                                        copyStrategyId || strategyId
                                    }`,
                                )}
                                style={{ marginRight: 10 }}
                            >
                                查看
                            </a>
                            {[STATUS_STOP].includes(item.strategyStatus) ? (
                                <Popconfirm
                                    title="确定要开启吗？"
                                    onConfirm={() => {
                                        handleAction('run', item);
                                    }}
                                    okText="是"
                                    cancelText="否"
                                >
                                    <a style={{ marginRight: 10 }}>开启</a>
                                </Popconfirm>
                            ) : null}
                            {[STATUS_DOING].includes(item.strategyStatus) ? (
                                <Popconfirm
                                    title="确定要暂停吗？"
                                    onConfirm={() => {
                                        handleAction('stop', item);
                                    }}
                                    okText="是"
                                    cancelText="否"
                                >
                                    <a style={{ marginRight: 10 }}>暂停</a>
                                </Popconfirm>
                            ) : null}
                            {[STATUS_STOP, STATUS_FAIL, STATUS_DRAFT].includes(
                                item.strategyStatus,
                            ) ? (
                                <a
                                    onClick={() => {
                                        deleteRecord(item);
                                    }}
                                    style={{ marginRight: 10 }}
                                >
                                    删除
                                </a>
                            ) : null}
                            {[STATUS_STOP, STATUS_DOING].includes(
                                item.strategyStatus,
                            ) ? (
                                <a
                                    href={bellwetherLinkParse(
                                        `detail?tab=2&strategyId=${strategyId}`,
                                    )}
                                    style={{ marginRight: 10 }}
                                >
                                    数据
                                </a>
                            ) : null}
                            <Button
                                type="link"
                                style={{ padding: 0, marginRight: 10 }}
                                onClick={() => {
                                    // const name = dataSource[index].strategyName
                                    // dataSource[index].strategyName = `副本-${name}`
                                    // setDataSource(dataSource);
                                    // 生成草稿策略id
                                    generateStrategyId(strategyId);
                                }}
                            >
                                复制
                            </Button>
                        </div>
                    );
                },
            },
        ];
    };

    // const rowSelection = {
    //     selectedRowKeys: selectedRowKeys,
    //     onChange: onSelectChange,
    // };

    useEffect(() => {
        screeningPar({ page: 1, pageSize: 20 });
    }, []);

    return (
        <div className="page-wrap">
            <div className="search-name">
                <Button
                    type="primary"
                    onClick={() => {
                        location.href = bellwetherLinkParse('create');
                    }}
                >
                    新建策略
                </Button>
            </div>
            <div style={{ marginTop: 20 }}>
                <Table
                    loading={loadVisible}
                    // rowSelection={rowSelection}
                    rowKey={record => record.strategyId}
                    dataSource={dataSource}
                    columns={getColumns()}
                    // pagination={false}
                    pagination={{
                        onChange: (page, pageSize) => {
                            screeningPar({
                                page,
                                pageSize,
                            });
                        },
                        onShowSizeChange: (page, pageSize) => {
                            screeningPar({
                                page,
                                pageSize,
                            });
                        },
                        current: nowPage,
                        pageSize: nowPageSize,
                        total: nowTotal,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: total => `共 ${total} 条`,
                    }}
                />
            </div>
        </div>
    );
}
