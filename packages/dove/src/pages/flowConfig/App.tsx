import React, { useEffect, useState } from 'react';
import queryString from 'query-string';
import { FlowCanvas } from "./components/FlowCanvas";
import './index.css';
import './index.scss';

const FlowConfig: React.FC = () => {
    const [currentFlow, setCurrentFlow] = useState<any>({});
    const [unsavedChanges, setUnsavedChanges] = useState(false);
    const obj = queryString.parse(window.location.search.substr(1));
    
    return (
        <div className="flow-config-page">
            <FlowCanvas
                initialConfig={currentFlow.flowConfig || { nodes: [], edges: [] }}
                onChange={(flowConfig) => {
                    setCurrentFlow({ ...currentFlow, flowConfig });
                    setUnsavedChanges(true);
                }}
                readOnly={obj.contactId ? true : false}
            />
        </div>
    );
};

export default FlowConfig;
