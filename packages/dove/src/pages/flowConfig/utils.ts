import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";
import { DiagnosisOptions } from './constants';

export function cn(...inputs) {
  return twMerge(clsx(inputs))
}

const NodeKeysMap = {
  startNode: ['upstreamSystems'],
  agentNode: ['agentId', 'params'],
  delayNode: ['delayTime', 'unit'],
  enterpriseWechatNode: ['timeout'],
  telesalesNode: ['downstreamSystem'],
  branchNode: ['condition'],
  skillNode: ['toolName', 'params', 'url'],
  historyTalkIntentionNode: ['talkTimeMin', 'talkTimeMax', 'agentId'],
  poiDiagnosisNode: ['diagnosisScene'],
};

export const getNodeLabel = (type, name?) => {
  let result = '';
  if (name) {
    result = name;
  } else {
    switch (type) {
      case 'startNode':
        result = '开始';
        break;
      case 'branchNode':
        result = '条件分支';
        break;
      case 'agentNode':
        result = '调用agent';
        break;
      case 'delayNode':
        result = '执行节点';
        break;
      case 'enterpriseWechatNode':
        result = '企微添加好友';
        break;
      case 'dataSourceNode':
        result = '数据集读取';
        break;
      case 'skillNode':
        result = '技能调用';
        break;
      case 'telesalesNode':
        result = '电销介入';
        break;
      case 'historyTalkIntentionNode':
        result = '历史沟通意向';
        break;
      case 'poiDiagnosisNode':
        result = '商家诊断';
        break;
      case 'endNode':
        result = '结束';
        break;
    }
  }
  return result;
};

// 获取保存的config数据
export const getObjectByType = (obj, type, edges) => {
  if (!NodeKeysMap[type]) {
    return obj;
  } else {
    const result: any = {};
    const keys = NodeKeysMap[type];
    for (const key of keys) {
      result[key] = obj[key];
    }
    if (type === 'branchNode') {
      // 判断是否有否则的线；
      const elifEdge = edges.filter(item => item.sourceHandle?.includes('source-n') && item.source === obj.id);
      const elifNodeConfig = result?.condition?.filter(item => item.condition === 'ELSE')[0];
      // node里面是否有这个线的信息，没有的话生成一条加上；
      if (elifEdge && !elifNodeConfig) {
        result.condition.push({
          id: 'default',
          condition: 'ELSE'
        });
      }
    }
    if (type === 'delayNode' && !result.unit) {
      result.unit = 'hour';
    }
    return result;
  }
}

const getCondition = (conditionNode, edge) => {
  let result: any = null;
  const isSourceN = edge.sourceHandle?.includes('source-n');
  if (isSourceN) {
    result = {
      id: 'default',
      conditions: []
    };
  } else {
    // 如果序列：
    const sourceId = edge.sourceHandle?.split('source-y-')[1];
    result = conditionNode.config.condition.find(item => item.id === sourceId);
    const sourceYIndex = conditionNode.config.condition.findIndex(item => item.id === sourceId);
    // 如果是0则要确保是IF，避免一系列操作之后的condition丢失；
    if (sourceYIndex == 0 && result.condition === 'ELIF') {
      result.condition = 'IF';
    }
  }
  return result;
};

export const getEdges = (edges, nodes) => {
  const result: any = [];
  const conditionsNode = nodes.filter((node) => node.type === 'branchNode');
  for (const edge of edges) {
    // 再次在保存之前去除重复的边数据；
    const hasExist = result.filter(item => {
      return item.sourceHandle === edge.sourceHandle && item.targetHandle === edge.targetHandle;
    })[0];

    if (hasExist) {
      continue;
    }

    const newObj: any = {
      sourceId: edge.source,
      targetId: edge.target,
      sourceHandle: edge.sourceHandle,
      targetHandle: edge.targetHandle,
      type: edge.type || 1,
      id: edge.id
    };

    // 如果当前边是条件分支节点的边
    const currentConditionNode = conditionsNode.find(condition => condition.id === edge.source);
    if (conditionsNode.length > 0 && currentConditionNode) {
      const condition = getCondition(currentConditionNode, edge);
      newObj.condition = condition;
      // 处理所有的数据连线丢失问题；
      if (newObj.targetHandle !== newObj.targetId + '-target') {
        newObj.targetHandle = `${newObj.targetId}-target`;
      }
    }
    result.push(newObj);
  }
  return result;
};

// 得到元数据的label
export const getMetaLabel = (meta, data, type) => {
  let metaData: any = [];
  if (!meta) {
    return '';
  }
  if (type === 'startNode') {
    metaData = meta.upstreamSystems;
  } else if (type === 'agentNode' || type === 'historyTalkIntentionNode') {
    metaData = meta.agentList;
  } else if (type === 'telesalesNode') {
    metaData = meta.downstreamSystems;
  } else if (type === 'poiDiagnosisNode') {
    // todo: 添加诊断list
    // 暂时用agentList做效果
    metaData = DiagnosisOptions;
  }

  let result: any = [];
  // 开始节点的data是数组，另外两个是非数组
  // 对比metaData中的id和data中的各个id
  if (type === 'startNode' && data) {
    for (const item of data) {
      const metaItem: any = metaData.find((metaItem: any) => metaItem.id === item);
      if (metaItem) {
        result.push(metaItem.name);
      }
    }
  } else {
    const metaItem: any = metaData?.find((metaItem: any) => metaItem.id === data);
    if (metaItem) {
      result.push(metaItem.name);
    }
  }
  return result.join(', ');
}

export const generateId = (type) => {
  return `${type}-${Date.now()}`;
}

export const getUnitLabel = (unit) => {
  const UnitLabelMap = {
    'second': '秒',
    'minute': '分钟',
    'hour': '小时'
  };
  return UnitLabelMap[unit];
};

// 处理条件分支因为condtion变化导致的边异常；
export const clearConditionEdges = (node, edges) => {
  // 非条件分支节点则返回原值；
  if (node.type !== 'branchNode') {
    return edges;
  }
  // 判定condtion和边的数据量是否一致；
  // 如果一致则直接返回原值；
  const branchSourceEdges = edges.filter(item => item.source === node.id);
  if (branchSourceEdges.length === node.data?.condition?.length) {
    return edges;
  }
  try {
    const result: any = [];
    for (let i = 0; i < edges.length; i++) {
      const currentEdge = edges[i];
      // 不是当前条件分支的source边，直接注入
      if (currentEdge.source !== node.id) {
        result.push(currentEdge);
      } else {
        // 如果是条件分支的source边，就需要额外做判定；
        const currentEdgeCondition = node.data.condition.find((item, index) => {
          // 删除的condition的id，需要一些组合逻辑来判定
          if (currentEdge.sourceHandle?.includes('source-y')) {
            return currentEdge.id === item.id;
          } else {
            // 否则source-n的情况，直接返回true；
            return true;
          }
        });
        // 与当前节点数据condition做对比，如果id能一一对应，则保留
        if (currentEdgeCondition) {
          result.push(currentEdge);
        }
      }
    }
    return result;
  } catch (error) {
    console.log('clearConditionEdges error', error);
    // 如果发生错误，直接返回原值,作为兜底逻辑；
    return edges;
  }
};

export const invertBranchEdgeData = (connection, sourceNode) => {
  if (sourceNode.type !== 'branchNode') {
    return connection;
  }
  // 对条件分支处理，生成唯一的分支edgeId,
  if (connection.sourceHandle?.includes('source-y')) {
    const sourceId = connection.sourceHandle.split('source-y-')[1];
    connection.id = sourceId;
  }
};