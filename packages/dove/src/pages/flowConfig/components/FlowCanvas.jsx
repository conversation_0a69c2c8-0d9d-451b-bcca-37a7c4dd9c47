import { useState, useCallback, useMemo, useEffect, useRef } from "react";
import ReactFlow, {
  Background,
  Controls,
  MiniMap,
  addEdge,
  applyEdgeChanges,
  applyNodeChanges,
  useReactFlow
} from "reactflow";
import { cloneDeep, isEmpty } from 'lodash';
import { message, Button, Select, Spin, Alert } from 'antd';
import queryString from 'query-string';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import dayjs from 'dayjs';
import NodesDrop from "./NodesDrop";
import ConfigComponent from "./ConfigComponent";
import useFlowNodesHooks from './FlowNodes/useFlowNodesHooks';
import NodeTitle from "./NodeTitle";
import {
  StartNode,
  EndNode,
  BranchNode,
  TelesalesNode,
  AgentNode,
  EnterpriseWechatNode,
  DelayNode,
  DataSourceNode,
  SkillNode,
  HistoryTalkIntentionNode,
  PoiDiagnosisNode
} from './FlowNodes';
import "reactflow/dist/style.css";
import { getObjectByType, getEdges, getNodeLabel, generateId, clearConditionEdges, invertBranchEdgeData } from "../utils";
import { PlusCircle, Undo, Redo, ZoomIn, ZoomOut, ChevronLeft } from "lucide-react";

const defaultNodeFormData = {
  label: "",
  type: "",
  upstreamSystems: [],
  condition: [],
  agentId: null,
  delayTime: "",
  unit: "hours",
  dataSource: "",
  toolName: "",
  diagnosisScene: "",
  downstreamSystem: "",
  talkTimeMax: null,
  talkTimeMin: null,
  params: null,
  url: "",
  field: "",
};

// 自定义节点类型
const nodeTypes = {
  startNode: StartNode,
  endNode: EndNode,
  branchNode: BranchNode,
  agentNode: AgentNode,
  delayNode: DelayNode,
  dataSourceNode: DataSourceNode,
  skillNode: SkillNode,
  telesalesNode: TelesalesNode,
  enterpriseWechatNode: EnterpriseWechatNode,
  historyTalkIntentionNode: HistoryTalkIntentionNode,
  poiDiagnosisNode: PoiDiagnosisNode
};

function CustomControls(props) {
  const { visible, setVisible } = useFlowNodesHooks();
  const { setDraggingNode, addNode } = props;
  const { zoomIn, zoomOut, setViewport, getViewport } = useReactFlow(); // 使用 ReactFlow 的 hooks
  const [zoomLevel, setZoomLevel] = useState("100%"); // 当前缩放比例

  useEffect(() => {
    const updateZoomLevel = () => {
      const viewport = getViewport(); // 获取当前视图
      const scale = Math.round(viewport.zoom * 100); // 将缩放比例转换为百分比
      setZoomLevel(`${scale}%`);
    };

    // 监听画布缩放变化
    const interval = setInterval(updateZoomLevel, 100); // 定时更新缩放比例
    return () => clearInterval(interval);
  }, [getViewport]);

  const handleZoomChange = (value) => {
    setZoomLevel(value);
    const scale = parseInt(value) / 100; // 将百分比转换为缩放比例
    setViewport({ x: 0, y: 0, zoom: scale }); // 设置视图缩放
  };

  return (
    <div className="custom-controls flex items-center space-x-2 p-2 rounded-md absolute">
      <div className="flex items-center space-x-1 px-2 bg-white rounded-md">
        <ZoomOut className="h-4 w-4 cursor-pointer text-gray-600" onClick={zoomOut} />
        <Select
          value={zoomLevel}
          style={{ width: 'auto', minWidth: 40 }}
          onChange={handleZoomChange}
          options={[
            { value: '200%', label: '200%' },
            { value: '100%', label: '100%' },
            { value: '75%', label: '75%' },
            { value: '50%', label: '50%' },
            { value: '25%', label: '25%' }
          ]}
          popupMatchSelectWidth={false}
        />
        <ZoomIn className="h-4 w-4 cursor-pointer text-gray-600" onClick={zoomIn} />
      </div>

      <div className="flex items-center space-x-1 px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600">
        <PlusCircle className="h-4 w-4" />
        <NodesDrop
          type={'node'}
          setDraggingNode={setDraggingNode}
          addNode={addNode}
          visible={visible}
          setVisible={setVisible}
        />
      </div>
    </div>
  );
}

function getPosition(event, rect) {
  const x = event.clientX - rect.left; // 计算相对 x
  const y = event.clientY - rect.top; // 计算相对 y
  const position = {
    x,
    y
  };
  if (x < 0) {
    position.x = 0;
  }
  if (y < 0) {
    position.y = 0;
  }
  if (rect.left + rect.width < event.clientX) {
    position.x = rect.width;
  }
  if (rect.top + rect.height < event.clientY) {
    position.y = rect.height;
  }
  return position;
};

export const FlowCanvas = ({ initialConfig, onChange, readOnly = false }) => {
  const [messageApi, contextHolder] = message.useMessage();
  const warning = (message) => {
    messageApi.open({
      type: 'warning',
      content: message,
    });
  };

  const [processId, setProcessId] = useState(null);
  const [processBaseInfo, setProcessBaseInfo] = useState({});
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);
  const [selectedNode, setSelectedNode] = useState(null);
  const [selectedEdge, setSelectedEdge] = useState(null);
  const [nodeFormData, setNodeFormData] = useState({ ...defaultNodeFormData });
  const [hoveredNodeId, setHoveredNodeId] = useState(null);
  const [draggingNode, setDraggingNode] = useState(null);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [saveLoading, setSaveLoading] = useState(false);
  const reactFlowRef = useRef(null);
  const [metaData, setMetaData] = useState({
    upstreamSystems: [],
    downstreamSystems: [],
    agentList: [],
    hasInit: false,
  }); // 元数据

  const generateNodePositions = (nodes) => {
    const horizontalSpacing = 200; // 水平间距
    const verticalSpacing = 100; // 垂直间距
    const nodeHasPosition = nodes.every(node => node.position);
    if (!nodeHasPosition) {
      // 分层布局
      const layers = [];
      nodes.forEach((node) => {
        const layerIndex = node.type === 'startNode' ? 0 : node.type === 'endNode' ? layers.length : 1;
        if (!layers[layerIndex]) {
          layers[layerIndex] = [];
        }
        layers[layerIndex].push(node);
      });

      // 计算 position
      let yOffset = 50; // 初始 y 偏移
      layers.forEach((layer, layerIndex) => {
        let xOffset = 50; // 初始 x 偏移
        layer.forEach((node, nodeIndex) => {
          node.position = {
            x: xOffset + nodeIndex * horizontalSpacing,
            y: yOffset + layerIndex * verticalSpacing,
          };
        });
      });
    }

    return nodes;
  };

  const generateNodes = (nodes, detail) => {
    const newNodes = generateNodePositions(nodes);
    newNodes.forEach(node => {
      node.config.label = getNodeLabel(node.type, node.name);

      // 把错误数据的id修复
      if (node.type === 'startNode') {
        node.id = 'LangGraphStartNode';

        // 补充开始节点的特殊逻辑，upstreamSystems可能会持续改变，这里要动态变化。
        if (detail?.upstreamSystems) {
          node.config.upstreamSystems = detail.upstreamSystems;
        } else {
          if (!node.config.upstreamSystems) {
            node.config.upstreamSystems = [];
          }
        }
      }

      if (node.type === 'endNode') {
        node.id = 'LangGraphEndNode';
      }
      // config是和后端的交互，
      // data是前端的配置数据；
      node.config.id = node.id;
      node.name = node.config.label;
      node.data = node.config;
    });
    return newNodes;
  };

  const generateEdeges = (edges) => {
    const newEdges = edges.map(edge => {
      const newObj = {
        id: edge.id || `${edge.sourceId}-${edge.targetId}`,
        source: edge.source || edge.sourceId,
        target: edge.target || edge.targetId,
        sourceHandle: edge.sourceHandle || null,
        targetHandle: edge.targetHandle || null,
        condition: edge.condition || null,
        type: edge.type || 1,
        selected: false,
        style: { stroke: '#CCC' },
        markerEnd: {
          type: 'arrow',
        }
      };

      // 特殊处理，条件分支的id唯一化；
      if (edge.condition) {
        // 兼容以前的数据；
        if (edge.sourceHandle.includes('source-y')) {
          const splitData = edge.sourceHandle.split('-source-y-');
          const id = splitData[1];
          const conditionId = edge.condition.id || generateId(`branch`);
          // branch开头，才代表是新数据；
          if (!id.startsWith('branch')) {
            // 旧数据，是索引开头的，会有问题，这里按照新的逻辑组装sourceHandle；
            newObj.sourceHandle = splitData[0] + '-source-y-' + conditionId;
          }
          newObj.id = conditionId;
        } else {
          newObj.id = newObj.id + generateId('-source-n');
        }
      }
      
      // 处理所有的数据连线丢失问题；
      if (newObj.targetHandle !== newObj.target + '-target') {
        newObj.targetHandle = `${newObj.target}-target`;
      }
      return newObj;
    });
    return newEdges;
  };

  const getFlowInformation = async (id) => {
    try {
      const res = await apiCaller.post(
        // @ts-ignore
        '/xianfu/api-v2/ai-infra/schedule/process/detail',
        {
          id,
        },
      );
      if (res.code === 0) {
        const dsl = JSON.parse(res.data.dsl);
        const nodes = generateNodes(dsl.nodes || [], res.data);
        const edges = generateEdeges(dsl.edges || []);
        setProcessBaseInfo(dsl.processBaseInfo);
        setProcessId(res.data.id);
        setNodes(nodes);
        setEdges(edges);

        setHistory({
          list: [{ nodes, edges, selectedNode, selectedEdge, nodeFormData }],
          index: 0,
        });
      }
    } catch (error) {
      console.error('Search call record failed:', error);
    }
  };

  const getEditInformation = async (id) => {
    try {
      const res = await apiCaller.post(
        // @ts-ignore
        '/xianfu/api-v2/dove/scheduling/progress',
        {
          contactId: id,
        },
      );

      if (res.code === 0) {
        const dsl = JSON.parse(res.data.dsl);
        const nodes = generateNodes(dsl.nodes || [], res.data);
        const edges = generateEdeges(dsl.edges || []);
        const currentNode = nodes.find(node => node.id === res.data.currentNodeId);
        currentNode && (currentNode.selected = true);
        setProcessBaseInfo(dsl.processBaseInfo);
        setProcessId(res.data.id);
        setNodes(nodes);
        setEdges(edges);
      }
    } catch (error) {
      console.error('Search call record failed:', error);
    }
  };

  const getConfigMetaData = async (type) => {
    try {
      const res = await apiCaller.post(
        // @ts-ignore
        '/xianfu/api-v2/ai-infra/schedule/system/list',
        {
          type,
        },
      );

      if (res.code === 0) {
        return res.data || [];
      }
    } catch (error) {
      console.error('Search call record failed:', error);
    }
  };

  const getAgentList = async () => {
    try {
      const res = await apiCaller.post(
        // @ts-ignore
        '/xianfu/api-v2/dove/agent/query',
        {
          page: 1,
          pageSize: 1000,
          status: [1], // 只查询启用状态的 agent
          forUse: true,
        },
      );

      if (res.code === 0) {
        return res.data?.data || [];
      }
    } catch (error) {
      console.error('Search call record failed:', error);
    }
  };

  // 初始化配置数据，包括包含id时候的画布数据、配置器所需的元数据；
  const initConfig = async () => {
    const obj = queryString.parse(window.location.search.substr(1));
    if (obj.processId) {
      getFlowInformation(obj.processId);
    }

    if (obj.contactId) {
      getEditInformation(obj.contactId);
    }

    Promise.all([getConfigMetaData('upstreamSystem'), getConfigMetaData('downstreamSystem'), getAgentList()]).then(res => {
      const newMetaData = {
        upstreamSystems: res[0],
        downstreamSystems: res[1],
        agentList: res[2],
        hasInit: true,
      };
      setMetaData(newMetaData);
    });
  };

  useEffect(() => {
    initConfig();
  }, []);

  const handleMouseMove = (event) => {
    if (draggingNode && reactFlowRef.current) {
      const canvasRect = reactFlowRef.current.getBoundingClientRect();
      const position = getPosition(event, canvasRect);
      setMousePosition(position);
    }
  };

  const handleMouseUp = (event) => {
    if (draggingNode && reactFlowRef.current) {
      const canvasRect = reactFlowRef.current.getBoundingClientRect();
      const position = getPosition(event, canvasRect);
      addNodeToPosition(draggingNode.type, position); // 将节点放置到画布
      setDraggingNode(null); // 结束拖拽
    }
  };

  useEffect(() => {
    if (draggingNode) {
      window.addEventListener("mousemove", handleMouseMove);
      window.addEventListener("mouseup", handleMouseUp); // 监听全局 mouseup
    } else {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
    }
    return () => {
      window.removeEventListener("mousemove", handleMouseMove);
      window.removeEventListener("mouseup", handleMouseUp);
    };
  }, [draggingNode]);

  // ReactFlow 的事件处理
  const handleNodeMouseEnter = useCallback((event, node) => {
    setHoveredNodeId(node.id); // 设置 hover 的节点 ID
  }, []);

  const handleNodeMouseLeave = useCallback(() => {
    setHoveredNodeId(null); // 清除 hover 的节点 ID
  }, []);

  const showEdge = useMemo(() => {
    return edges.map(edge => ({
      ...edge,
      style: {
        stroke: edge.id === selectedEdge?.id || edge.source === hoveredNodeId || edge.target === hoveredNodeId ? '#3b82f6' : '#CCC',
      },
      markerEnd: {
        type: 'arrow',
        color: edge.id === selectedEdge?.id || edge.source === hoveredNodeId || edge.target === hoveredNodeId ? '#3b82f6' : '#CCC',
      },
    }));
  }, [edges, selectedNode, hoveredNodeId]);

  // 历史记录用于撤销/重做
  const [history, setHistory] = useState({
    list: [],
    index: -1,
  });

  // 保存当前状态到历史记录
  const saveToHistory = useCallback((newNodes, newEdges) => {
    if (readOnly) return; // 只读模式不保存历史

    // 如果当前不是最新状态，则删除后面的历史
    const newHistory = history.list.slice(0, history.index + 1);

    // 添加新状态
    newHistory.push({ nodes: newNodes, edges: newEdges, selectedNode, selectedEdge, nodeFormData });

    // 如果历史记录太长，则删除最早的
    if (newHistory.length > 20) {
      newHistory.shift();
    }

    setHistory({
      list: newHistory,
      index: newHistory.length - 1,
    });
  }, [history, selectedEdge, selectedNode, nodeFormData, readOnly]);

  // 撤销
  const handleUndo = useCallback(() => {
    if (readOnly) return; // 只读模式不允许撤销

    const historyIndex = history.index;
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      const {
        nodes: prevNodes,
        edges: prevEdges,
        selectedNode: prevSelectedNode,
        selectedEdge: prevSelectedEdge,
        nodeFormData: prevNodeFormData
      } = history.list[newIndex];

      setNodes(prevNodes);
      setEdges(prevEdges);
      setNodeFormData(prevNodeFormData);
      setSelectedEdge(prevSelectedEdge);
      setSelectedNode(prevSelectedNode);
      setHistory({
        ...history,
        index: newIndex,
      });
      if (onChange) {
        onChange({ nodes: prevNodes, edges: prevEdges });
      }
      temporarilySave(prevNodes, prevEdges);
    }
  }, [history, onChange, readOnly]);

  // 重做
  const handleRedo = useCallback(() => {
    if (readOnly) return; // 只读模式不允许重做
    const historyIndex = history.index;

    if (historyIndex < history.list.length - 1) {
      const newIndex = historyIndex + 1;
      const {
        nodes: nextNodes,
        edges: nextEdges,
        selectedNode: nextSelectedNode,
        selectedEdge: nextSelectedEdge,
        nodeFormData: nextNodeFormData
      } = history.list[newIndex];
      setNodes(nextNodes);
      setEdges(nextEdges);
      setNodeFormData(nextNodeFormData);
      setSelectedEdge(nextSelectedEdge);
      setSelectedNode(nextSelectedNode);
      setHistory({
        ...history,
        index: newIndex,
      });

      if (onChange) {
        onChange({ nodes: nextNodes, edges: nextEdges });
      }
      temporarilySave(nextNodes, nextEdges);
    }
  }, [history, onChange, readOnly]);

  // 节点变化处理
  const onNodesChange = useCallback(
    (changes) => {
      let newEdges = [...edges];
      if (readOnly) return; // 只读模式不允许修改节点
      // 删除节点时，移除与该节点相关的边，用键盘删除的快捷键，需要对edges做相应的处理
      const deletedNodeIds = changes
        .filter(change => change.type === 'remove') // 找到被删除的节点
        .map(change => change.id); // 获取被删除节点的 ID

      if (deletedNodeIds.length > 0) {
        const deletedNodes = nodes.filter(node => deletedNodeIds.includes(node.id));
        const invalidNode = deletedNodes.find(node => node.type === 'startNode' || node.type === 'endNode');

        if (invalidNode) {
          warning("开始节点和结束节点不允许删除");
          return;
        }
        newEdges = edges.filter(
          edge => !deletedNodeIds.includes(edge.source) && !deletedNodeIds.includes(edge.target)
        );
        setSelectedNode(null);
      }
      const newNodes = applyNodeChanges(changes, nodes);
      setNodes(newNodes);
      // 如果是选择节点，则设置当前选中节点
      const selectChange = changes.find(change => change.type === 'select' && change.selected);
      if (selectChange) {
        if (selectChange.selected) {
          const selectedNode = newNodes.find(node => node.id === selectChange.id);
          setSelectedNode(selectedNode);
          setNodeFormData({
            label: selectedNode.data.label || "",
            type: selectedNode.type,
            upstreamSystems: selectedNode.data.upstreamSystems || [],
            condition: selectedNode.data.condition || [],
            agentId: selectedNode.data.agentId || null,
            delayTime: selectedNode.data.delayTime || "",
            dataSource: selectedNode.data.dataSource || null,
            toolName: selectedNode.data.toolName || null,
            url: selectedNode.data.url || null,
            params: selectedNode.data.params || null,
            talkTimeMax: selectedNode.data.talkTimeMax || null,
            talkTimeMin: selectedNode.data.talkTimeMin || null,
            diagnosisScene: selectedNode.data.diagnosisScene || null,
            downstreamSystem: selectedNode.data.downstreamSystem || null
          });
        } else {
          setSelectedNode(null);
        }
        setSelectedEdge(null);
      }

      // 保存到历史记录
      if (changes.some(change => change.type !== 'select')) {
        if (onChange) {
          onChange({ nodes: newNodes, edges: newEdges });
        }
      }

      if (newNodes.length !== nodes.length || newEdges.length !== edges.length) {
        temporarilySave(newNodes, newEdges);
      }
    },
    [nodes, edges, onChange, saveToHistory, readOnly]
  );

  // 边变化处理
  const onEdgesChange = useCallback(
    (changes) => {
      if (readOnly) return; // 只读模式不允许修改边
      const deleteEdges = changes.filter(change => change.type === 'remove');
      // 有删除操作则把连线信息也干掉；
      if (deleteEdges.length > 0) {
        setSelectedEdge(null);
      }

      const newEdges = applyEdgeChanges(changes, edges);
      setEdges(newEdges);

      // 如果是选择边，则设置当前选中边
      const selectChange = changes.find(change => change.type === 'select' && change.selected);
      if (selectChange) {
        if (selectChange.selected) {
          const selectedEdge = newEdges.find(edge => edge.id === selectChange.id);
          setSelectedEdge(selectedEdge);
        } else {
          setSelectedEdge(null);
        }
        setSelectedNode(null);
      }

      // 保存到历史记录
      if (changes.some(change => change.type !== 'select')) {
        saveToHistory(nodes, newEdges);

        if (onChange) {
          onChange({ nodes, edges: newEdges });
        }
      }

      if (newEdges.length !== edges.length) {
        temporarilySave(nodes, newEdges);
      }
    },
    [nodes, edges, onChange, saveToHistory, readOnly]
  );

  // 连接边处理
  const onConnect = useCallback(
    (connection) => {
      if (readOnly) return; // 只读模式不允许连接边
      // 当前连线的起点
      const sourceNode = nodes.find((node) => node.id === connection.source);

      const connectionRules = {
        startNode: 1, // 开始节点1个
        agentNode: 1, // 调用agent节点1个
        branchNode: Infinity, // 条件分支节点多个
        delayNode: 1, // 执行节点1个
        enterpriseWechatNode: 1, // 监听企微添加好友节点1个
        dataSource: 1, // 数据源节点1个
        telesalesNode: 1, // 电销介入节点1个
        // 0604新增
        historyTalkIntentionNode: 1, // 历史沟通意向节点1个
        poiDiagnosisNode: 1, // 商家诊断节点1个
        skillNode: 1, // 技能调用节点1个
        endNode: 0, // 结束节点0个
      };

      // 检查当前 source 节点的连接数量
      const existingConnections = edges.filter((edge) => edge.source === connection.source);
      const maxConnections = connectionRules[sourceNode.type] || Infinity;
      if (existingConnections.length >= maxConnections) {
        warning(`"${sourceNode.data.label}" 只允许连接最多 ${maxConnections} 个目标节点`);
        // 清除拖拽状态
        setEdges((prevEdges) => prevEdges.filter((edge) => edge.source !== connection.source || edge.target !== null));
        return; // 阻止连接
      }

      if (sourceNode.type === 'branchNode') {
        // 当前条件分支的source连接节点，最多只能有一个
        const currentConditionHandle = existingConnections.filter(item => item.sourceHandle === connection.sourceHandle);
        if (currentConditionHandle.length > 0) {
          warning(`「条件分支」节点的每一个出口最多连接1个下游节点`);
          // 清除拖拽状态
          setEdges((prevEdges) => prevEdges.filter((edge) => edge.source !== connection.source || edge.target !== null));
          return; // 阻止连接
        }
      }
      const edgeData = {
        ...connection,
        style: { stroke: '#CCC' },
        markerEnd: {
          type: 'arrow',
        },
      };

      invertBranchEdgeData(edgeData, sourceNode);
      const newEdges = addEdge(edgeData, edges);
      setEdges(newEdges);
      saveToHistory(nodes, newEdges);

      if (onChange) {
        onChange({ nodes, edges: newEdges });
      }
      temporarilySave(nodes, newEdges);
    },
    [nodes, edges, onChange, saveToHistory, readOnly]
  );

  // 复制节点
  const handleCopyNode = () => {
    if (readOnly) return; // 只读模式不允许添加节点
    const type = selectedNode?.type;
    // 如果已经有了开始节点，则不允许添加新的开始节点
    if (type === 'startNode' && nodes.some(node => node.type === 'startNode')) {
      warning("只能有一个开始节点");
      return;
    }

    // 如果已经有了结束节点，则不允许添加新的结束节点
    if (type === 'endNode' && nodes.some(node => node.type === 'endNode')) {
      warning("只能有一个结束节点");
      return;
    }

    const newId = `node_${Date.now()}`;
    const newNode = cloneDeep(selectedNode);
    // 把所有地方的id全部统一成新的。
    newNode.id = newId;
    newNode.data.id = newId;
    newNode.config.id = newId;

    const flowRefRect = reactFlowRef.current.getBoundingClientRect();
    newNode.position = { x: flowRefRect.width / 2, y: flowRefRect.height / 2 };
    nodes.forEach(node => {
      node.selected = false; // 取消其他节点的选中状态
    });
    const newNodes = [...nodes, newNode];
    setNodes(newNodes);

    // 选中新节点
    setSelectedNode(newNode);
    setNodeFormData({
      label: newNode.data.label,
      type,
      upstreamSystems: newNode.data.upstreamSystems || [],
      condition: newNode.data.condition || [],
      agentId: newNode.data.agentId || null,
      delayTime: newNode.data.delayTime || "",
      dataSource: newNode.data.dataSource || null,
      downstreamSystem: newNode.data.downstreamSystem || null,
      field: newNode.data.field || "",
      toolName: newNode.data.toolName || null,
      url: newNode.data.url || null,
      params: newNode.data.params || null,
      talkTimeMax: newNode.data.talkTimeMax || null,
      talkTimeMin: newNode.data.talkTimeMin || null,
      diagnosisScene: newNode.data.diagnosisScene || null,
    });

    saveToHistory(newNodes, edges);
    if (onChange) {
      onChange({ nodes: newNodes, edges });
    }
  };

  // 删除选中的节点或边
  const handleDelete = () => {
    if (readOnly) return; // 只读模式不允许删除
    if (selectedNode) {
      if (selectedNode.type === 'startNode' || selectedNode.type === 'endNode') {
        warning("开始节点和结束节点不允许删除");
        return;
      }
      // 删除节点及相关的边
      const newNodes = nodes.filter(node => node.id !== selectedNode.id);
      const newEdges = edges.filter(
        edge => edge.source !== selectedNode.id && edge.target !== selectedNode.id
      );

      setNodes(newNodes);
      setEdges(newEdges);
      setSelectedNode(null);
      saveToHistory(newNodes, newEdges);

      if (onChange) {
        onChange({ nodes: newNodes, edges: newEdges });
      }
      temporarilySave(newNodes, newEdges);
    } else if (selectedEdge) {
      // 删除边
      const newEdges = edges.filter(edge => edge.id !== selectedEdge.id);

      setEdges(newEdges);
      setSelectedEdge(null);
      saveToHistory(nodes, newEdges);

      if (onChange) {
        onChange({ nodes, edges: newEdges });
      }
      temporarilySave(nodes, newEdges);
    }
  };

  // 更新节点属性
  const handleUpdateNode = (data) => {
    if (readOnly || !selectedNode) return; // 只读模式不允许更新节点
    const formData = data || nodeFormData;
    let currentNode = null;
    const newNodes = nodes.map(node => {
      if (node.id === selectedNode.id) {
        const newNode = {
          ...node,
          data: {
            ...node.data,
            label: formData.label,
            upstreamSystems: formData.upstreamSystems,
            condition: formData.condition,
            agentId: formData.agentId,
            delayTime: formData.delayTime,
            dataSource: formData.dataSource,
            downstreamSystem: formData.downstreamSystem,
            field: formData.field,
            // 2025.06.04新增
            diagnosisScene: formData.diagnosisScene,
            talkTimeMax: formData.talkTimeMax,
            talkTimeMin: formData.talkTimeMin,
            url: formData.url,
            toolName: formData.toolName,
            params: formData.params,
          },
        };
        // 更新节点名称
        newNode.name = newNode.data.label;
        currentNode = newNode;
        return newNode;
      }
      return node;
    });

    const newEdges = clearConditionEdges(currentNode, edges);
    if (newEdges.length !== edges.length) {
      setEdges(newEdges);
    }
    setNodes(newNodes);
    saveToHistory(newNodes, newEdges);

    if (onChange) {
      onChange({ nodes: newNodes, edges: newEdges });
    }

    temporarilySave(newNodes, newEdges);
  };

  const saveCanvas = async () => {
    try {
      if (saveLoading) return; // 防止重复点击
      const saveNodes = cloneDeep(nodes);
      saveNodes.forEach(node => {
        node.config = getObjectByType(node.data, node.type, edges);
        delete node.data;
      });
      const saveEdges = getEdges(edges, saveNodes);
      setSaveLoading(true);
      const res = await apiCaller.post(
        // @ts-ignore
        '/xianfu/api-v2/ai-infra/schedule/process/version',
        {
          processId,
          dsl: JSON.stringify({
            processBaseInfo,
            edges: saveEdges,
            nodes: saveNodes
          })
        }
      );

      if (res.code === 0) {
        console.log('保存成功');
        messageApi.success('保存成功');
      }
      setSaveLoading(false);
    } catch (error) {
      console.error('Search call record failed:', error);
    }
  };

  // 暂存
  const temporarilySave = async (newNodes, newEdges) => {
    try {
      const nodes = newNodes;
      const edges = newEdges;
      const saveNodes = cloneDeep(nodes);
      saveNodes.forEach(node => {
        node.config = getObjectByType(node.data, node.type, edges);
        delete node.data;
      });
      const saveEdges = getEdges(edges, saveNodes);
      const res = await apiCaller.post(
        // @ts-ignore
        '/xianfu/api-v2/ai-infra/schedule/process/draft',
        {
          processId,
          dsl: JSON.stringify({
            processBaseInfo,
            edges: saveEdges,
            nodes: saveNodes
          })
        }
      );

    } catch (error) {
      console.error('Search call record failed:', error);
    }
  };

  const addNode = (type, event) => {
    const canvasRect = reactFlowRef.current.getBoundingClientRect();
    const position = getPosition(event, canvasRect);
    addNodeToPosition(type, position);
  };

  const addNodeToPosition = (type, position) => {
    if (readOnly) return; // 只读模式不允许添加节点

    // 如果已经有了开始节点，则不允许添加新的开始节点
    if (type === 'startNode' && nodes.some(node => node.type === 'startNode')) {
      warning("只能有一个开始节点");
      return;
    }

    // 如果已经有了结束节点，则不允许添加新的结束节点
    if (type === 'endNode' && nodes.some(node => node.type === 'endNode')) {
      warning("只能有一个结束节点");
      return;
    }

    const newId = `node_${Date.now()}`;
    let nodeData = { label: "" };
    switch (type) {
      case 'startNode':
        nodeData = {
          label: "开始",
          upstreamSystems: processBaseInfo.upstreamSystems || []
        };
        break;
      case 'branchNode':
        nodeData = {
          label: "条件分支",
          condition: [
            { id: generateId('branch'), condition: "IF", operate: "AND", conditions: [] },
          ]
        };
        break;
      case 'agentNode':
        nodeData = { label: "调用agent", agentId: null, params: null };
        break;
      case 'delayNode':
        nodeData = { label: "执行节点", delayTime: "" };
        break;
      case 'enterpriseWechatNode':
        nodeData = { label: "企微添加好友", delayTime: "" };
        break;
      case 'dataSourceNode':
        nodeData = { label: "数据集读取", dataSource: null, field: "" };
        break;
      case 'skillNode':
        nodeData = { label: "技能调用", toolName: null, url: null, params: null };
        break;
      case 'telesalesNode':
        nodeData = { label: "电销介入", downstreamSystem: null };
        break;
      // 0604新增
      case 'historyTalkIntentionNode':
        nodeData = { label: "历史沟通意向", talkTimeMax: null, talkTimeMin: null, agentId: null };
        break;
      case 'poiDiagnosisNode':
        nodeData = { label: "商家诊断", diagnosisScene: null };
        break;
      case 'endNode':
        nodeData = { label: "结束" };
        break;
      default:
        nodeData = { label: type === 'startNode' ? "开始节点" : type };
    }

    nodeData.id = newId;
    const newNode = {
      id: newId,
      type,
      name: nodeData.label,
      position: position,
      data: nodeData,
      selected: true,
    };

    if (type === 'startNode') {
      newNode.id = 'LangGraphStartNode';
      nodeData.id = 'LangGraphStartNode';
    }

    if (type === 'endNode') {
      newNode.id = 'LangGraphEndNode';
      nodeData.id = 'LangGraphEndNode';
    }

    nodes.forEach(node => {
      node.selected = false; // 取消其他节点的选中状态
    });
    const newNodes = [...nodes, newNode];
    setNodes(newNodes);
    // 选中新节点
    setSelectedNode(newNode);
    setNodeFormData({
      label: newNode.data.label,
      type,
      upstreamSystems: newNode.data.upstreamSystems || [],
      condition: newNode.data.condition || [],
      agentId: newNode.data.agentId || null,
      delayTime: newNode.data.delayTime || "",
      dataSource: newNode.data.dataSource || null,
      downstreamSystem: newNode.data.downstreamSystem || null,
      field: newNode.data.field || "",
      toolName: newNode.data.toolName || null,
      url: newNode.data.url || null,
      params: newNode.data.params || null,
      talkTimeMax: newNode.data.talkTimeMax || null,
      talkTimeMin: newNode.data.talkTimeMin || null,
      diagnosisScene: newNode.data.diagnosisScene || null,
    });

    saveToHistory(newNodes, edges);
    if (onChange) {
      onChange({ nodes: newNodes, edges });
    }
    temporarilySave(newNodes, edges);
  };

  const showNodes = useMemo(() => {
    const newNodes = cloneDeep(nodes);
    return newNodes.map((node) => {
      const hasSource = edges.filter(edge => {
        return edge.source === node.id;
      })[0];
      if (hasSource) {
        node.data.hasSource = true;
      } else {
        delete node.data?.hasSource;
      }
      node.data.metaData = metaData;
      node.data.addNode = addNode;
      return node;
    });
  }, [edges, nodes, metaData]);

  const closeConfig = () => {
    const newNodes = nodes.map(node => {
      return {
        ...node,
        selected: false
      }
    });
    setNodes(newNodes);
    setSelectedNode(null);
  };

  const backPage = () => {
    if (!document.referrer) {
      const obj = queryString.parse(window.location.search.substr(1));
      if (obj.fromXFIframe) {
        delete obj.fromXFIframe;
      }
      const query = queryString.stringify(obj);
      let url = `${import.meta.env.DEV ? '' : '/page/dove'}/scheduleManagement/detail?${query}`;
      window.open(url, '_self');
    } else {
      window.history.back();
    }
  };

  return (
    <div className="h-full flex flex-col">
      {contextHolder}
      {!readOnly && (
        <div className="flow-config-header">
          <div className="left-section">
            <ChevronLeft size={20} onClick={backPage} />
            <div className="process-information">
              <div className="process-name">{processBaseInfo.processName || ''}</div>
              <div className="process-updatetime">{processBaseInfo.updateTime ? '最近更新: ' + dayjs(processBaseInfo.updateTime).format('YYYY-MM-DD') : ''}</div>
            </div>
          </div>
          <div className="flex space-x-2">
            <Button onClick={handleUndo} disabled={history.index <= 0}>
              <Undo className="h-4 w-4" />
              撤销
            </Button>
            <Button onClick={handleRedo} disabled={history.index >= history.list.length - 1}>
              <Redo className="h-4 w-4" />
              重做
            </Button>
            {/* <Button onClick={handleUndo} >
              <PlayIcon className="h-4 w-4" />
              试运行
            </Button> */}
            <Button onClick={saveCanvas} style={{ background: '#222', color: 'white' }} loading={saveLoading}>
              保存
            </Button>
          </div>
        </div>
      )}

      <div className="flex flex-1">
        {
          metaData.hasInit && (
            <div className="flex-1 rounded-md overflow-hidden" ref={reactFlowRef}>
              <ReactFlow
                nodes={showNodes}
                edges={showEdge}
                onNodesChange={readOnly ? undefined : onNodesChange}
                onEdgesChange={readOnly ? undefined : onEdgesChange}
                onNodeMouseEnter={handleNodeMouseEnter}
                onNodeMouseLeave={handleNodeMouseLeave}
                onConnect={readOnly ? undefined : onConnect}
                nodeTypes={nodeTypes}
                // fitView
                nodesDraggable={!readOnly}
                nodesConnectable={!readOnly}
                elementsSelectable={!readOnly}
                style={{ backgroundColor: '#F3F4F7' }}
              >
                <Background />
                {
                  !readOnly && (
                    <CustomControls
                      handleAddNode={addNodeToPosition}
                      setDraggingNode={setDraggingNode}
                      addNode={addNode}
                    />
                  )
                }
                {/* <MiniMap /> */}
                {draggingNode && (
                  <div
                    className='flow-node bg-white rounded-lg p-3 w-40 drag-status'
                    style={{
                      top: mousePosition.y,
                      left: mousePosition.x,
                    }}
                  >
                    <NodeTitle label={draggingNode.name} />
                  </div>
                )}
                {/* 企微添加好友也没有了 */}
                {selectedNode && !readOnly && selectedNode.type !== 'endNode' && selectedNode.type !== 'enterpriseWechatNode' && (
                  <ConfigComponent
                    selectedNode={selectedNode}
                    readOnly={readOnly}
                    nodeFormData={nodeFormData}
                    setNodeFormData={setNodeFormData}
                    handleUpdateNode={handleUpdateNode}
                    onClose={closeConfig}
                    copyNode={handleCopyNode}
                    removeNode={handleDelete}
                    metaData={metaData}
                    processId={processId}
                    nodes={nodes}
                    edges={edges}
                  />

                )}
              </ReactFlow>
            </div>
          )
        }

        {selectedEdge && !readOnly && (
          <Alert message="Delete键 删除线" type="info" showIcon className="alert-delete-tip" />
        )}
      </div>
      {
        !metaData.hasInit && (
          <Spin
            tip="加载中..."
            fullscreen
            delay={100}
          />
        )
      }
    </div>
  );
};
