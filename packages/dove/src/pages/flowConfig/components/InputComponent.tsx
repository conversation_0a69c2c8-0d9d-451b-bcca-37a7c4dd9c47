import React, { useEffect, useState } from 'react';
import { Input } from 'antd';

const InputComponent = (props: any) => {
    const { value, onChange, placeholder, style, className } = props;
    const [inputValue, setInputValue] = useState(value);
    const [isComposing, setIsComposing] = useState(false);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newValue = e.target.value;
        setInputValue(newValue);

        if (!isComposing) {
            onChange(newValue);
        }
    };

    const handleCompositionStart = () => {
        setIsComposing(true);
    };

    const handleCompositionEnd = (e) => {
        setIsComposing(false);
        const newValue = e.target.value;
        onChange(newValue); // 中文输入结束后触发 onChange
    };

    useEffect(() => {
        if (value !== inputValue) {
            setInputValue(value);
        }
    }, [value]);

    return (
        <Input
            value={inputValue}
            onChange={handleChange}
            onCompositionStart={handleCompositionStart}
            onCompositionEnd={handleCompositionEnd}
            placeholder={placeholder || ''}
            style={style || {}}
            className={className || ''}
        />
    );
};

export default InputComponent;
