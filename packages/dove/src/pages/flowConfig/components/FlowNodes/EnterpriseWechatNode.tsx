import React from 'react';
import <PERSON>act<PERSON><PERSON>, {
    Position,
    Handle,
} from "reactflow";
import use<PERSON>lowNodesHooks from './useFlowNodesHooks';
import NodeTitle from '../NodeTitle';
import DragIcon from '../DragIcon';

const EnterpriseWechatNode = ({ data, isConnectable, selected }) => {
  const { visible, setVisible } = useFlowNodesHooks();
  const addNode = data.addNode;
  return (
    <div className={`flow-node bg-white rounded-lg p-3 w-40 text-center ${selected ? 'selected outline outline-2 outline-blue-500' : ''}`}>
      <NodeTitle label={data.label} />
      {/* 本期没有这个配置，都是默认的 */}
      {/* <div className="flow-node-content">
        {data?.timeout || "未配置监听时长"}小时
      </div> */}
      <Handle type="target" position={Position.Left} id={`${data.id}-target`} className="edge-icon-default target">
        <DragIcon type='target' hasSource={data.hasSource} />
      </Handle>
      <Handle 
        type="source" 
        position={Position.Right} 
        id={`${data.id}-source`} 
        className="edge-icon-default"
        onClick={() => {
          setVisible(!visible);
        }}
      >
        <DragIcon 
          hasSource={data.hasSource} 
          visible={visible} 
          setVisible={setVisible} 
          addNode={addNode}
        />
      </Handle>
    </div>
  );
};

export default EnterpriseWechatNode;