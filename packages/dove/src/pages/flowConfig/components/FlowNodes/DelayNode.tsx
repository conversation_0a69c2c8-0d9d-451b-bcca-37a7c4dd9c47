import React from 'react';
import <PERSON><PERSON><PERSON><PERSON>, {
    Position,
    Handle,
} from "reactflow";
import useFlowNodesHooks from './useFlowNodesHooks';
import { getUnitLabel } from '../../utils';
import NodeTitle from '../NodeTitle';
import DragIcon from '../DragIcon';

const DelayNode = ({ data, isConnectable, selected }) => {
  const { visible, setVisible } = useFlowNodesHooks();
  const addNode = data.addNode;
  let showTime = '';
  if (data?.delayTime) {
    showTime = data?.delayTime + (getUnitLabel(data?.unit) || '小时');
  }
  return (
    <div className={`flow-node bg-white rounded-lg p-3 w-40 text-center ${selected ? 'selected outline outline-2 outline-blue-500' : ''}`}>
      <NodeTitle label={data.label} />
      <div className="flow-node-content">
        {showTime || "未配置执行时间"}
      </div>
      <Handle type="target" position={Position.Left} id={`${data.id}-target`} className="edge-icon-default target">
        <DragIcon type='target' hasSource={data.hasSource} />
      </Handle>
      <Handle 
        type="source" 
        position={Position.Right} 
        id={`${data.id}-source`} 
        className="edge-icon-default"
        onClick={() => {
          setVisible(!visible);
        }}
      >
        <DragIcon 
          hasSource={data.hasSource} 
          visible={visible} 
          setVisible={setVisible} 
          addNode={addNode}
        />
      </Handle>
    </div>
  );
};

export default DelayNode;