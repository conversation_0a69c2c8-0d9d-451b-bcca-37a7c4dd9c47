import React from 'react';
import <PERSON><PERSON><PERSON><PERSON>, {
    Position,
    <PERSON>le,
} from "reactflow";
import use<PERSON>lowNodesHooks from './useFlowNodesHooks';
import NodeTitle from '../NodeTitle';
import DragIcon from '../DragIcon';

const DataSourceNode = ({ data, isConnectable, selected }) => {
    const { visible, setVisible } = useFlowNodesHooks();
    const addNode = data.addNode;
    
    return (
        <div className={`flow-node bg-white rounded-lg p-3 w-40 text-center ${selected ? 'selected outline outline-2 outline-blue-500' : ''}`}>
            <NodeTitle label={data.label} />
            <div className="flow-node-content">
                {data?.dataSource || "未配置数据源"}
            </div>
            <div className="flow-node-content">
                {data?.field || "未配置字段"}
            </div>
            <Handle type="target" position={Position.Left} id={`${data.id}-target`} className="edge-icon-default target">
                <DragIcon type='target' hasSource={data.hasSource} />
            </Handle>
            <Handle
                type="source"
                position={Position.Right}
                id={`${data.id}-source`}
                className="edge-icon-default"
                onClick={() => {
                    setVisible(!visible);
                }}
            >
                <DragIcon 
                    hasSource={data.hasSource} 
                    visible={visible} 
                    setVisible={setVisible} 
                    addNode={addNode}
                />
            </Handle>
        </div>
    );
};

export default DataSourceNode;