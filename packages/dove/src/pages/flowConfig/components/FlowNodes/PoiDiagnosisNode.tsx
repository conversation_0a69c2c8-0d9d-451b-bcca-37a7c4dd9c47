// 商家诊断节点
import React from 'react';
import <PERSON>act<PERSON><PERSON>, {
    Position,
    Handle,
} from "reactflow";
import useFlowNodesHooks from './useFlowNodesHooks';
import { getMetaLabel } from '../../utils';
import NodeTitle from '../NodeTitle';
import DragIcon from '../DragIcon';

const PoiDiagnosisNode = ({ data, isConnectable, selected }) => {
  const { visible, setVisible } = useFlowNodesHooks();
  const addNode = data.addNode;
  let scene = '';

  if (data?.diagnosisScene) {
    scene = getMetaLabel(data.metaData, data.diagnosisScene, 'poiDiagnosisNode');
  }
  return (
    <div className={`flow-node diagnosis-node bg-white rounded-lg p-3 w-40 text-center ${selected ? 'selected outline outline-2 outline-blue-500' : ''}`}>
      <NodeTitle label={data.label} />
      <div className="flow-node-content">
        <div>诊断场景：{scene || "未配置诊断场景"}</div>
      </div>
      <Handle type="target" position={Position.Left} id={`${data.id}-target`} className="edge-icon-default target">
        <DragIcon type='target' hasSource={data.hasSource} />
      </Handle>
      <Handle 
        type="source" 
        position={Position.Right} 
        id={`${data.id}-source`} 
        className="edge-icon-default"
        onClick={() => {
          setVisible(!visible);
        }}
      >
        <DragIcon 
          hasSource={data.hasSource} 
          visible={visible} 
          setVisible={setVisible} 
          addNode={addNode}
        />
      </Handle>
    </div>
  );
};

export default PoiDiagnosisNode;