import React from 'react';
import <PERSON>act<PERSON><PERSON>, {
    Position,
    Handle,
} from "reactflow";
import { getMetaLabel } from '../../utils';
import useFlowNodesHooks from './useFlowNodesHooks';
import NodeTitle from '../NodeTitle';
import DragIcon from '../DragIcon';

const AgentNode = ({ data, isConnectable, selected }) => {
  const { visible, setVisible } = useFlowNodesHooks();
  const agentName = getMetaLabel(data.metaData, data.agentId, 'agentNode');
  const addNode = data.addNode;
  // 有可能没有匹配到的agent，脏数据
  return (
    <div className={`flow-node bg-white rounded-lg p-3 w-40 text-center ${selected ? 'selected outline outline-2 outline-blue-500' : ''}`}>
      <NodeTitle label={data.label} />
      <div className="flow-node-content">
        {agentName || "未选择agent"}
      </div>
      <Handle type="target" position={Position.Left} id={`${data.id}-target`} className="edge-icon-default target">
        <DragIcon type='target' hasSource={data.hasSource} />
      </Handle>
      <Handle 
        type="source" 
        position={Position.Right} 
        id={`${data.id}-source`} 
        className="edge-icon-default"
        onClick={() => {
          setVisible(!visible);
        }}
      >
        <DragIcon 
          hasSource={data.hasSource} 
          visible={visible} 
          setVisible={setVisible}
          addNode={addNode}
        />
      </Handle>
    </div>
  );
};

export default AgentNode;