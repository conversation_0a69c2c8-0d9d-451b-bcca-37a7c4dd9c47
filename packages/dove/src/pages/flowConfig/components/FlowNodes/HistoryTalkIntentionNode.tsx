// 历史沟通意向节点
import React from 'react';
import React<PERSON><PERSON>, {
    Position,
    Handle,
} from "reactflow";
import dayjs from 'dayjs';
import useFlowNodesHooks from './useFlowNodesHooks';
import { getMetaLabel } from '../../utils';
import NodeTitle from '../NodeTitle';
import DragIcon from '../DragIcon';

const HistoryTalkIntentionNode = ({ data, isConnectable, selected }) => {
  const { visible, setVisible } = useFlowNodesHooks();
  const addNode = data.addNode;
  let showTime = '';
  let showAgent = '';

  if (data?.talkTimeMax && data?.talkTimeMin) {
    showTime = '近' +  dayjs(data.talkTimeMax).diff(dayjs(data.talkTimeMin), 'day') + '天';
  }
  if (data?.agentId) {
    showAgent = getMetaLabel(data.metaData, data.agentId, 'historyTalkIntentionNode');
  }

  return (
    <div className={`flow-node intention-node bg-white rounded-lg p-3 w-40 text-center ${selected ? 'selected outline outline-2 outline-blue-500' : ''}`}>
      <NodeTitle label={data.label} />
      <div className="flow-node-content">
        <div>时间周期：{showTime || "未配置时间周期"}</div>
        <div>沟通agent：{showAgent || "未配置沟通agent"}</div>
      </div>
      <Handle type="target" position={Position.Left} id={`${data.id}-target`} className="edge-icon-default target">
        <DragIcon type='target' hasSource={data.hasSource} />
      </Handle>
      <Handle 
        type="source" 
        position={Position.Right} 
        id={`${data.id}-source`} 
        className="edge-icon-default"
        onClick={() => {
          setVisible(!visible);
        }}
      >
        <DragIcon 
          hasSource={data.hasSource} 
          visible={visible} 
          setVisible={setVisible} 
          addNode={addNode}
        />
      </Handle>
    </div>
  );
};

export default HistoryTalkIntentionNode;