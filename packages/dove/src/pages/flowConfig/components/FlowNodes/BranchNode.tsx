import React, { useState, useEffect } from 'react';
import <PERSON><PERSON><PERSON><PERSON>, {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
} from "reactflow";
import useFlowNodesHooks from './useFlowNodesHooks';
import addIcon from '../../../../assets/svg/addIcon.svg';
import NodeTitle from '../NodeTitle';
import DragIcon from '../DragIcon';

const BranchNode = ({ data, isConnectable, selected }) => {
  const { visible, setVisible } = useFlowNodesHooks();
  // 记录当前点击的source节点；
  const [currentId, setCurrentId] = useState('');
  const legalConditions = data.condition.filter(item => item.id !== 'default');
  const addNode = data.addNode;

  useEffect(() => {
    if (!visible && currentId) {
      setCurrentId('');
    }
  }, [visible]);

  return (
    <div className={`flow-node branch-node bg-white rounded-lg py-3 text-center ${selected ? 'selected outline outline-2 outline-blue-500' : ''}`}>
      <div className="px-3">
        <NodeTitle label={data.label} />
      </div>
      <div className="text-xs mt-1 px-3">
        {legalConditions?.map((condition, index) => {
          const curId = `${data.id}-source-y-${condition.id}`;
          return (
            <div key={index} className="bg-white rounded mt-1 condition-item">
              <div className={`relative px-1 flex ${legalConditions.length === 1 ? 'justify-end' : 'justify-between'} items-center condition-item-header`}>
                {
                  legalConditions?.length > 1 ? (
                    <div>优先级{index + 1}</div>
                  ) : null
                }
                <div className="header-label">
                  {index === 0 ? '如果' : '否则如果'}
                </div>

                <Handle
                  type="source"
                  position={Position.Right}
                  id={curId}
                  className="edge-icon-default"
                  onClick={() => {
                    setCurrentId(curId);
                    setVisible(!visible);
                  }}
                >
                  <DragIcon 
                    hasSource={data.hasSource} 
                    visible={visible && currentId === curId} 
                    setVisible={setVisible} 
                    addNode={addNode}
                  />
                </Handle>
              </div>
              <div className="condition-item-content">
                {
                  condition.conditions?.map(item => (
                    <div className="condition-child-item">
                      {
                        item.secondAttribute ? (
                          <>
                            <span className="text-blue-500">{item.firstAttribute}</span>
                            <span className="second-attribute-content">{item.operate + item.secondAttribute}</span>
                          </>
                        ) : '条件未设置'
                      }
                    </div>
                  ))
                }
              </div>
            </div>
          )
        }
        )}
        <div className="relative flex px-1 justify-end items-center condition-item-header">
          <div className="header-label">否则</div>
          <Handle
            type="source"
            position={Position.Right}
            id={`${data.id}-source-n`}
            className="edge-icon-default"
            onClick={() => {
              setCurrentId(`${data.id}-source-n`);
              setVisible(!visible);
            }}
          >
            <DragIcon 
              hasSource={data.hasSource} 
              visible={visible && currentId === `${data.id}-source-n`} 
              setVisible={setVisible} 
              addNode={addNode}
            />
          </Handle>
        </div>
      </div>

      <Handle type="target" position={Position.Left} id={`${data.id}-target`} className="edge-icon-default target">
        <img className="drag-icon" src={addIcon} />
      </Handle>
    </div>
  );
};

export default BranchNode;