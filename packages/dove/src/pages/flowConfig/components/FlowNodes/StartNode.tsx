import React from 'react';
import <PERSON>act<PERSON><PERSON>, {
    Position,
    Handle,
} from "reactflow";
import useFlowNodesHooks from './useFlowNodesHooks';
import NodeTitle from '../NodeTitle';
import DragIcon from '../DragIcon';
import { getMetaLabel } from '../../utils';

// 开始节点
const StartNode = ({ data, isConnectable, selected }) => {
    const { visible, setVisible } = useFlowNodesHooks();
    const label = getMetaLabel(data.metaData, data.upstreamSystems, 'startNode');
    const addNode = data.addNode;
    return (
        <div className={`flow-node bg-white rounded-lg p-3 w-40 ${selected ? 'selected outline outline-2 outline-blue-500' : ''}`}>
            <NodeTitle label={data.label} />
            <div className="flow-node-content">
                {data?.upstreamSystems && data.upstreamSystems.length > 0 && label ? label : '请选择发起方上游系统'}
            </div>
            <Handle
                type="source"
                position={Position.Right}
                id={`${data.id}-source`}
                className="edge-icon-default"
                onClick={(e) => {
                    setVisible(!visible);
                }}
            >
                <DragIcon
                    hasSource={data.hasSource}
                    handleAddNode={data.handleAddNode}
                    visible={visible}
                    setVisible={setVisible}
                    addNode={addNode}
                />
            </Handle>
        </div>
    );
};

export default StartNode;