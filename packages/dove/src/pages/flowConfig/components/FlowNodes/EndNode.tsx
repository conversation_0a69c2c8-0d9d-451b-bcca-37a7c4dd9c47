import React from 'react';
import <PERSON>act<PERSON><PERSON>, {
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>,
} from "reactflow";
import NodeTitle from '../NodeTitle';
import DragIcon from '../DragIcon';

const EndNode = ({ data, isConnectable, selected }) => {
  return (
    <div className={`flow-node bg-white rounded-lg p-3 w-40 text-center ${selected ? 'selected outline outline-2 outline-blue-500' : ''}`}>
      <NodeTitle label={data.label} />
      <Handle type="target" position={Position.Left} id={`${data.id}-target`} className="edge-icon-default" >
        <DragIcon type='target' hasSource={data.hasSource} />
      </Handle>
    </div>
  );
};

export default EndNode;
