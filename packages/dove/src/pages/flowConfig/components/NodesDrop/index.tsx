import React, { useEffect } from "react";
import { DropMenu } from '@roo/roo';

const iconStyle = {
    width: '24px',
    height: '24px',
    borderRadius: '8px',
    opacity: 1,
    background: '#D8D8D8',
    marginRight: '6px'
};

function NodeHeader(label) {
    return (
        <div className="flex items-center">
            <div className="node-icon" style={iconStyle}></div>
            <div className="font-bold">{label}</div>
        </div>
    )
}

const NodesList = [
    {
        name: '开始',
        type: 'startNode'
    },
    {
        name: '条件分支',
        type: 'branchNode'
    },
    {
        name: '调用agent',
        type: 'agentNode'
    },
    {
        name: '执行节点',
        type: 'delayNode'
    },
    {
        name: '企微添加好友',
        type: 'enterpriseWechatNode'
    },
    // 数据集读取和技能调用是P1级别，本期不做。
    // {
    //     name: '数据集读取',
    //     type: 'dataSourceNode'
    // },
    // 2025.06.04 v1.1需求解锁技能调用，新增历史沟通意向、商家诊断两个类型
    {
        name: '技能调用',
        type: 'skillNode'
    },
    {
        name: '历史沟通意向',
        type: 'historyTalkIntentionNode'
    },
    {
        name: '商家诊断',
        type: 'poiDiagnosisNode'
    },
    {
        name: '电销介入',
        type: 'telesalesNode'
    },
    {
        name: '结束',
        type: 'endNode'
    },
];
const NodesDrop = (props) => {
    const { type, children, setDraggingNode, visible, setVisible, addNode } = props;
    const icon = type === 'node' ? "添加节点" : children;
    const menu = NodesList.map(item => {
        return {
            key: item.type,
            label: <a onClick={ (e) => {
                if (type !== 'node') {
                    addNode(item.type, e);
                } else {
                    setDraggingNode(item);
                }
                setVisible(false);
            }}>{NodeHeader(item.name)}</a>
        }
    });

    return (
        <div className="nodes-drop">
            <DropMenu
                menu={menu}
                placement="rightTop"
                trigger="click"
                visible={visible} 
                onClickOutside={(e: any) => {
                    if (!e.target.classList.contains('react-flow__handle')) {
                        setVisible && setVisible(false)};
                    }
                }
            >
                <div onClick={() => {
                    setVisible && setVisible(!visible)}
                }>
                    { icon }
                </div>
            </DropMenu>
        </div>
    );
};

export default NodesDrop;