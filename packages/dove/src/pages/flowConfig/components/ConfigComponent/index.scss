.flow-config-component {
    background: #FCFCFD;
    position: absolute;
    z-index: 10000;
    right: 20px;
    top: 20px;
    bottom: 20px;
    width: 374px;
    overflow-y: auto;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0px 2px 6px 0px #DEDEDE4C;
}

.flow-config-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-operate {
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }

    svg {
        cursor: pointer;
        margin-left: 6px;
    }
}

.dropdown-item, .doveCustomRoo-dropdown-menu {
    a {
        padding-left: 16px !important;
        padding-right: 16px !important;
    }

    .copy-icon {
        &.disabled {
            cursor: not-allowed;
            color: #BFBFBF;
        }
    }

    .delete-icon {
        border-top: 1px solid #E8E8E8;
        color: red;
        
        &.disabled {
            cursor: not-allowed;
            color: #BFBFBF;
        }
    }
}

.basic-config-component {
    .basic-config-item  {
        margin-top: 12px;
    }

    .config-label {
        width: 72px;
    }

    .config-attribute-wrapper {
        width: calc(100% - 82px);
    }
}

.skill-params-list {
    .params-item {
        margin-top: 12px;
    }

    .config-label {
        width: 120px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;

        &.choose-skill-label {
            width: 64px;
        }
    }

    .ant-select {
        flex: 1;
    }
}