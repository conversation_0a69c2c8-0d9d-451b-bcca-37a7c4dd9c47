import React, { useMemo } from 'react';
import StartConfig from './StartConfig';
import BranchConfig from './BranchConfig';
import AgentConfig from './AgentConfig';
import DelayConfig from './DelayConfig';
import WechatConfig from './WechatConfig';
import TelesalesConfig from './TelesalesConfig';
import SkillConfig from './SkillConfig';
import DataSourceConfig from './DataSourceConfig';
import IntentionConfig from './IntentionConfig';
import DiagnosisConfig from './DiagnosisConfig';
import useAttriOpHooks from './useAttriOpHooks';

const ConfigFactory = (props) => {
    // processId,nodes,edges只在部分组件中使用
    const { selectedNode, metaData, nodeFormData, onChange, processId, nodes, edges } = props;
    const { attriOptions } = useAttriOpHooks({ selectedNode, nodes, edges, processId});
    const type = selectedNode.type;

    const agentList = useMemo(() => {
        if (!metaData.agentList) {
            return [];
        }
        return metaData.agentList?.map((item) => {
            return {
                value: item.id,
                label: item.name,
                intention: item.intention
            };
        });
    }, [metaData.agentList]);

    const downstreamSystems = useMemo(() => {
        if (!metaData.downstreamSystems) {
            return [];
        }
        return metaData.downstreamSystems.map((item) => {
            return {
                value: item.id,
                label: item.name
            };
        });
    }, [metaData.downstreamSystems]);

    const upstreamSystems = useMemo(() => {
        if (!metaData.upstreamSystems) {
            return [];
        }
        return metaData.upstreamSystems.map((item) => {
            return {
                value: item.id,
                label: item.name
            }
        });
    }, [metaData.downstreamSystems]);

    const ConfigComponent = useMemo(() => {
        let result: any = null;
        switch (type) {
            case 'startNode':
                result = (
                    <StartConfig
                        upstreamSystems={upstreamSystems}
                        nodeFormData={nodeFormData}
                        onChange={onChange}
                    />
                );
                break;
            case 'branchNode':
                result = (
                    <BranchConfig
                        nodeFormData={nodeFormData}
                        onChange={onChange}
                        attriOptions={attriOptions}
                    />
                );
                break;
            case 'agentNode':
                result = (
                    <AgentConfig
                        agentList={agentList}
                        nodeFormData={nodeFormData}
                        onChange={onChange}
                        attriOptions={attriOptions}
                    />
                );
                break;
            case 'delayNode':
                result = (
                    <DelayConfig
                        onChange={onChange}
                        nodeFormData={nodeFormData}
                    />
                );
                break;
            case 'enterpriseWechatNode':
                result = (
                    <WechatConfig
                        onChange={onChange}
                        nodeFormData={nodeFormData}
                    />
                );
                break;
            case 'telesalesNode':
                result = (
                    <TelesalesConfig
                        onChange={onChange}
                        nodeFormData={nodeFormData}
                        downstreamSystems={downstreamSystems}
                    />
                );
                break;
            case 'skillNode':
                result = (
                    <SkillConfig
                        onChange={onChange}
                        nodeFormData={nodeFormData}
                        attriOptions={attriOptions}
                    />
                );
                break;
            case 'dataSourceNode':
                result = (
                    <DataSourceConfig
                        onChange={onChange}
                        nodeFormData={nodeFormData}
                    />
                )
            case 'historyTalkIntentionNode':
                result = (
                    <IntentionConfig
                        onChange={onChange}
                        nodeFormData={nodeFormData}
                        agentList={agentList}
                    />
                );
                break;
            case 'poiDiagnosisNode':
                result = (
                    <DiagnosisConfig
                        onChange={onChange}
                        nodeFormData={nodeFormData}
                    />
                );
                break;
            default:
                result = null;
                break;
        }
        return result;
    }, [type, upstreamSystems, downstreamSystems, agentList, nodeFormData, onChange, processId, nodes, edges]);

    return (
        <div>
            {ConfigComponent}
        </div>
    );
};

export default ConfigFactory;