/**
 * 执行节点配置
 */
import React from 'react';
import { InputNumber } from 'antd';

const DelayConfig = (props) => {
    const { onChange, nodeFormData } = props;

    return (
        <div>
            <div className="flex items-center justify-start">
                <div className="config-label">执行时机</div>
                <div className="config-attribute-wrapper">
                    <InputNumber
                        className="config-default-number"
                        min={0}
                        value={nodeFormData.delayTime}
                        onChange={(value) => onChange({ ...nodeFormData, delayTime: value })}
                        suffix="小时"
                        style={{ width: '180px' }}
                    />
                </div>
            </div>
            <p className="text-xs text-gray-500 mt-1">配置执行延迟时间，单位为小时</p>
        </div>
    );
};

export default DelayConfig;