/**
 * 数据源配置
 * 目前还没有正式开始开发，后续跟着需求迭代完善；
 */
import React from 'react';
import InputComponent from '../../InputComponent';

const DataSourceConfig = (props) => {
    const { onChange, nodeFormData } = props;

    return (
        <div>
            <div className="flex items-center justify-start">
                <div className="config-label w-[56px]">表配置</div>
                <div className="config-attribute-wrapper">
                    <InputComponent
                        value={nodeFormData.dataSource}
                        style={{ width: '100%' }}
                        className="w-20 text-sm border border-gray-300 rounded-l-md px-2 py-1"
                        onChange={(value) => onChange({ ...nodeFormData, dataSource: value })}
                    />
                </div>
            </div>
            <div className="flex items-center justify-start">
                <div className="config-label w-[56px]">字段配置</div>
                <div className="config-attribute-wrapper">
                    <InputComponent
                        value={nodeFormData.field}
                        style={{ width: '100%' }}
                        className="mt-1"
                        placeholder="例如: name/date/address"
                        onChange={(value) => onChange({ ...nodeFormData, field: value })}
                    />
                </div>
            </div>
        </div>
    );
};

export default DataSourceConfig;