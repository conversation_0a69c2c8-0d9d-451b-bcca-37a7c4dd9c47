/**
 * 技能调用配置
 */
import React, { useEffect, useState } from 'react';
import { Select, Tooltip } from 'antd';
import { isEmpty, cloneDeep, isEqual } from 'lodash';
import { apiCaller } from '@mfe/cc-api-caller-pc';

const SkillConfig = (props) => {
    const { onChange, nodeFormData, attriOptions } = props;
    const [skillOptions, setSkillOptions] = useState([]);
    const [currentSkill, setCurrentSkill] = useState<any>(null);
    const [params, setParams] = useState<any>([]);

    const getSkillList = async () => {
        const res: any = await apiCaller.post(
            // @ts-ignore
            '/xianfu/api-v2/ai-infra/tool/searchToolList',
            {},
        );
        if (res.code === 0) {
            const invertData = res.data.map(item => {
                return {
                    label: item.showName,
                    value: item.name,
                    url: item.url,
                    argumentList: item.argumentList
                }
            }) || [];
            setSkillOptions(invertData);
        }
    };

    // 技能查询只执行一次；
    useEffect(() => {
        getSkillList();
    }, []);

    useEffect(() => {
        // 过滤value为null的数据
        const newParams = params.filter((item: any) => item[1] !== null);
        // 当前的参数和新参数不同才更新，不然就不更新，避免Object.entries引起的顺序错乱
        const currentParams = Object.fromEntries(newParams);
        if (!isEmpty(nodeFormData.params) && !isEqual(nodeFormData.params, currentParams)) {
            const newParams = Object.entries(nodeFormData.params);
            setParams(newParams);
        }
    }, [nodeFormData.params]);

    useEffect(() => {
        if (nodeFormData.toolName !== currentSkill?.value) {
            const currentSkill: any = skillOptions.find((item: any) => item.value === nodeFormData.toolName);
            setCurrentSkill(currentSkill);

            // 没有params数据输入的情况下，仍然能看到对应的参数列表
            if (currentSkill && isEmpty(params)) {
                const currentArguments = currentSkill?.argumentList?.map((item: any) => {
                    return [item.cursor, null];
                });
                setParams(currentArguments);
            }
        }
    }, [nodeFormData.toolName, skillOptions]);

    const onSkillChange = (value) => {
        const currentSkill: any = skillOptions.find((item: any) => item.value === value);
        setCurrentSkill(currentSkill);
        const newData = { ...nodeFormData, toolName: value, url: currentSkill.url, params: null };
        const currentArguments = currentSkill?.argumentList?.map((item: any) => {
            return [item.cursor, null];
        });
        setParams(currentArguments);
        onChange(newData);
    };

    const onParamsChange = (index, value) => {
        const newParams = cloneDeep(params);
        newParams[index][1] = value;
        setParams(newParams);
        onChange({ ...nodeFormData, params: Object.fromEntries(newParams) });
    };

    const getLabel = (paramKey) => {
        return currentSkill?.argumentList?.find((item: any) => item.cursor === paramKey)?.name;
    };

    return (
        <div className="skill-params-list">
            <div className="flex items-center justify-start">
                <div className="config-label choose-skill-label">选择技能</div>
                <Select
                    value={nodeFormData.toolName}
                    className="config-default-select"
                    onChange={onSkillChange}
                    options={skillOptions}
                    popupMatchSelectWidth={false}
                    placeholder="请选择技能"
                />
            </div>
            {
                !isEmpty(params) && params.map((item, index) => {
                    return (
                        <div className="params-item flex items-center justify-start">
                            <Tooltip title={getLabel(item[0])}>
                                <div className="config-label">{getLabel(item[0])}</div>
                            </Tooltip>
                            <Select
                                value={item[1]}
                                className="config-default-select"
                                onChange={(value) => onParamsChange(index, value)}
                                options={attriOptions}
                                popupMatchSelectWidth={false}
                                placeholder="请选择参数"
                            />
                        </div>
                    );
                })
            }
        </div>
    );
};

export default SkillConfig;