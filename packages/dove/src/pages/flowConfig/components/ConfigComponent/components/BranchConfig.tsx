/**
 * 条件分支配置
 */
import React from 'react';
import { Select, Button } from 'antd';
import { PlusCircle, Trash2, IterationCw } from "lucide-react";
import InputComponent from '../../InputComponent';
import { generateId } from "../../../utils";

const BranchConfig = (props) => {
    const { nodeFormData, onChange, attriOptions } = props;

    const changeOperate = (index) => {
        const newCondition = [...nodeFormData.condition];
        newCondition[index].operate = newCondition[index].operate === 'AND' ? 'OR' : 'AND';
        onChange({ ...nodeFormData, condition: newCondition });
    };

    // 更新条件
    const handleUpdateCondition = (index, innerIndex, field, value) => {
        const newCondition = [...nodeFormData.condition];
        newCondition[index].conditions[innerIndex] = {
            ...newCondition[index].conditions[innerIndex],
            [field]: value
        };
        onChange({ ...nodeFormData, condition: newCondition });
    };

    const handleDeleteConditionItem = (index, innerIndex) => {
        const newCondition = [...nodeFormData.condition];
        newCondition[index].conditions.splice(innerIndex, 1);
        onChange({ ...nodeFormData, condition: newCondition });
    };

    const addCondition = (index) => {
        const newCondition = [...nodeFormData.condition];
        newCondition[index].conditions.push({
            firstAttributeLabel: '',
            firstAttribute: '',
            secondType: 'Constant',
            secondAttribute: '',
            operate: '包含'
        });
        onChange({ ...nodeFormData, condition: newCondition });
    };

    // 删除条件
    const handleDeleteCondition = (index) => {
        const newCondition = [...nodeFormData.condition];
        newCondition.splice(index, 1);
        onChange({ ...nodeFormData, condition: newCondition });
    };

    // 添加条件
    const handleAddCondition = () => {
        let newCondition: any = [];
        if (nodeFormData.condition.length > 0 && nodeFormData.condition[nodeFormData.condition.length - 1].id === 'default') {
            newCondition = [...nodeFormData.condition];
            newCondition.splice(newCondition.length - 1, 0, {
                id: generateId('branch'),
                condition: "ELIF",
                conditions: [],
                operate: "AND"
            });
        } else {
            newCondition = [...nodeFormData.condition, { id: generateId('branch'), condition: "ELIF", conditions: [], operate: "AND" }];
        }
        onChange({ ...nodeFormData, condition: newCondition });
    };

    return (
        <div>
            <div className="space-y-2 mt-1">
                {nodeFormData.condition.filter(item => item.id !== 'default').map((condition, index) => {
                    return (
                        <div key={index} className="rounded-md">
                            <div className="condition-group">
                                <div className="condition flex items-start mb-2">
                                    {
                                        index === 0 ? (
                                            <div className="condition-label">
                                                <div className="config-label">如果</div>
                                                {
                                                    nodeFormData.condition.length > 1 && (<div className="priority-tip">优先级1</div>)
                                                }
                                            </div>
                                        ) : (
                                            <div className="condition-label">
                                                <div className="config-label">否则如果</div>
                                                <div className="priority-tip">优先级{index + 1}</div>
                                            </div>
                                        )
                                    }
                                    <div className="condition-content flex items-center gap-4 mb-2">
                                        {
                                            condition.conditions?.length > 1 && (
                                                <div className="condition-relation-section">
                                                    <div className="relation-line"></div>
                                                    <div className="operate-btn" onClick={() => changeOperate(index)} >
                                                        <div>{condition.operate}</div>
                                                        <IterationCw size={12} />
                                                    </div>
                                                </div>
                                            )
                                        }
                                        <div className="condition-attri-config">
                                            {
                                                condition.conditions?.map((data, innerIndex) => {
                                                    return (
                                                        <div className="condition flex items-center gap-1 text-sm">
                                                            <div className="condition-items">
                                                                <div className="flex items-center gap-1 mb-2">
                                                                    <Select
                                                                        placeholder="选择参数"
                                                                        value={data.firstAttribute}
                                                                        style={{ width: '180px' }}
                                                                        onChange={(value) => handleUpdateCondition(index, innerIndex, "firstAttribute", value)}
                                                                        options={attriOptions}
                                                                        popupMatchSelectWidth={false}
                                                                    />
                                                                </div>
                                                                <div className="flex items-center gap-1 mb-2">
                                                                    <Select
                                                                        value={data.operate}
                                                                        style={{ width: '90px' }}
                                                                        onChange={(value) => handleUpdateCondition(index, innerIndex, "operate", value)}
                                                                        options={[
                                                                            { value: '包含', label: '包含' },
                                                                            { value: '不包含', label: '不包含' },
                                                                            { value: '是', label: '是' },
                                                                            { value: '否', label: '否' },
                                                                            { value: '为空', label: '为空' },
                                                                            { value: '不为空', label: '不为空' }
                                                                        ]}
                                                                        popupMatchSelectWidth={false}
                                                                    />
                                                                    <InputComponent
                                                                        value={data.secondAttribute}
                                                                        style={{ width: '90px' }}
                                                                        className="w-20 text-sm border border-gray-300 rounded-l-md px-2 py-1"
                                                                        onChange={(value) => handleUpdateCondition(index, innerIndex, "secondAttribute", value)}
                                                                    />
                                                                </div>
                                                            </div>
                                                            <Trash2
                                                                className="h-4 w-4 cursor-pointer text-black-500"
                                                                onClick={() => handleDeleteConditionItem(index, innerIndex)}
                                                            />
                                                        </div>
                                                    )
                                                })
                                            }
                                        </div>
                                    </div>
                                </div>
                                <Button onClick={() => addCondition(index)}>+ 添加条件</Button>
                            </div>

                            {
                                index === 0 ? null : (
                                    <div className="flex justify-end">
                                        <Button
                                            onClick={() => handleDeleteCondition(index)}
                                            className="text-red-500 "
                                        >
                                            <Trash2 className="h-3 w-3" />
                                            删除
                                        </Button>
                                    </div>
                                )
                            }
                        </div>
                    )
                })}
                <Button
                    onClick={handleAddCondition}
                    className="w-full"
                >
                    <PlusCircle className="h-4 w-4 mr-1" />
                    否则如果
                </Button>
                <div className="border-t border-gray-200 pt-2">
                    <div className="config-label">否则</div> {/* 加粗标题 */}
                    <div className="text-sm text-gray-500">用于定义当“如果”条件不满足时应执行的逻辑。</div> {/* 淡色字体 */}
                </div>
            </div>
        </div>
    );
};

export default BranchConfig;