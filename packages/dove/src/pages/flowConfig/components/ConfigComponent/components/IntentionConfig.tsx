/**
 * 历史沟通意向配置
 */
import React, { useMemo } from 'react';
import { Select, InputNumber } from 'antd';
import dayjs from 'dayjs';

const IntentionConfig = (props) => {
    const { onChange, nodeFormData, agentList } = props;
    const currentAgent = agentList.find((item) => item.value === nodeFormData.agentId);

    const showCycleTime = useMemo(() => {
        if (nodeFormData.talkTimeMax > nodeFormData.talkTimeMin) {
            const daysDifference = dayjs(nodeFormData.talkTimeMax).diff(dayjs(nodeFormData.talkTimeMin), 'day');
            return daysDifference;
        }
        return null;
    }, [nodeFormData.talkTimeMin, nodeFormData.talkTimeMax]);

    return (
        <div className='basic-config-component'>
            <div className="basic-config-item flex items-center justify-start">
                <div className="config-label">时间周期</div>
                <div className="config-attribute-wrapper">
                    <InputNumber
                        className="config-default-number"
                        min={0}
                        value={showCycleTime}
                        onChange={(value) => {
                            if (!value && value !== 0) {
                                return;
                            }
                            const talkTimeMax = dayjs().valueOf(); 
                            const talkTimeMin = dayjs().subtract(value as any, 'day').valueOf();
                            onChange({ 
                                ...nodeFormData, 
                                talkTimeMax, 
                                talkTimeMin 
                            });
                        }}
                        addonBefore="近"
                        suffix="天"
                        style={{ width: '180px' }}
                    />
                </div>
            </div>
            <div className="basic-config-item flex items-center justify-start">
                <div className="config-label">沟通agent</div>
                <Select
                    value={nodeFormData.agentId}
                    className="config-default-select config-attribute-wrapper"
                    onChange={(value) => onChange({ ...nodeFormData, agentId: value })}
                    options={agentList}
                    popupMatchSelectWidth={false}
                    placeholder="请选择agent"
                />
            </div>
            {
                currentAgent && (
                    <div className="basic-config-item flex items-start justify-start">
                        <div className="config-label">沟通意向</div>
                        <div className="config-attribute-wrapper">
                            {
                                currentAgent.intention?.elementList?.map(item => {
                                    return <div className="intention-item">{`${item.tag}-${item.description}`}</div>
                                })
                            }
                        </div>
                    </div>
                )
            }
        </div>
    );
};

export default IntentionConfig;