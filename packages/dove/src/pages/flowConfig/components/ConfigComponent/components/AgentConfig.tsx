/**
 * agent配置
 */
import React, { useState, useEffect } from 'react';
import { Select, Button } from 'antd';
import { isEmpty, cloneDeep, isEqual } from 'lodash';
import { Trash2 } from "lucide-react";
import InputComponent from '../../InputComponent';

const AgentConfig = (props) => {
    const { agentList, nodeFormData, onChange, attriOptions } = props;
    const [agentParams, setAgentParams] = useState<any>([]);
    const currentAgent = agentList.find((item) => item.value === nodeFormData.agentId);

    useEffect(() => {
        // 当前的参数和新参数不同才更新，不然就不更新，避免Object.entries引起的顺序错乱
        const currentParams = Object.fromEntries(agentParams);
        if (!isEmpty(nodeFormData.params) && !isEqual(nodeFormData.params, currentParams) ) {
            const newParams = Object.entries(nodeFormData.params);
            setAgentParams(newParams);
        }
    }, [nodeFormData.params]);

    const addParams = () => {
        setAgentParams([...agentParams, [null, null]]);
    };

    const deleteItem = (index) => {
        const newParams = cloneDeep(agentParams);
        newParams.splice(index, 1);
        setAgentParams(newParams);
        onChange({ ...nodeFormData, params: Object.fromEntries(newParams) });
    };

    const changeParams = (index, value, innerIndex) => {
        const newParams = cloneDeep(agentParams);
        newParams[index][innerIndex] = value;
        setAgentParams(newParams);
        onChange({ ...nodeFormData, params: Object.fromEntries(newParams) });
    };

    return (
        <div className='basic-config-component'>
            <div className="basic-config-item flex items-center justify-start">
                <div className="config-label">调用agent</div>
                <Select
                    value={nodeFormData.agentId}
                    className="config-default-select config-attribute-wrapper"
                    onChange={(value) => onChange({ ...nodeFormData, agentId: value })}
                    options={agentList}
                    popupMatchSelectWidth={false}
                    placeholder="请选择agent"
                />
            </div>
            <div className="basic-config-item">
                <div >内容输入</div>
                <div className="params-list">
                    {
                        agentParams.map((item, index) => {
                            return (
                                <div className="basic-config-item flex items-center justify-start" key={index}>
                                    <InputComponent 
                                        placeholder="请输入参数名" 
                                        style={{ width: 110 }} 
                                        value={item[0]}
                                        onChange={(value) => changeParams(index, value, 0)}
                                    />
                                    <Select
                                        value={item[1]}
                                        className="config-default-select config-attribute-wrapper"
                                        onChange={(value) => changeParams(index, value, 1)}
                                        options={attriOptions}
                                        popupMatchSelectWidth={false}
                                        placeholder="请选择参数"
                                        style={{ flex: 1 }}
                                    />
                                    <Trash2
                                        className="h-4 w-4 cursor-pointer text-black-500"
                                        style={{marginLeft: 8}}
                                        onClick={() => deleteItem(index)}
                                    />
                                </div>
                            )
                        })
                    }
                </div>
                <div style={{marginTop: 12, textAlign: 'center'}}>
                    <Button onClick={addParams} style={{width: '100%'}}>添加参数</Button>
                </div>
            </div>
            <div className="basic-config-item">
                {
                    currentAgent && (
                        <div className="basic-config-item flex items-start justify-start">
                            <div className="config-label">沟通意向</div>
                            <div className="config-attribute-wrapper">
                                {
                                    currentAgent.intention?.elementList?.map(item => {
                                        return <div className="intention-item">{`${item.tag}-${item.description}`}</div>
                                    })
                                }
                            </div>
                        </div>
                    )
                }
            </div>
        </div>
    );
};

export default AgentConfig;
