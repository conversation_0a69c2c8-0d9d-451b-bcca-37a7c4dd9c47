/**
 * 电销接入配置
 */
import React from 'react';
import { Select } from 'antd';

const TelesalesConfig = (props) => {
    const { downstreamSystems, nodeFormData, onChange } = props;

    return (
        <div className="flex items-center justify-start">
            <div className="config-label">电销工具</div>
            <Select
                value={nodeFormData.downstreamSystem}
                className="config-default-select"
                onChange={(value) => onChange({ ...nodeFormData, downstreamSystem: value })}
                options={downstreamSystems}
                popupMatchSelectWidth={false}
                placeholder="请选择电销工具"
            />
        </div>
    );
};

export default TelesalesConfig;