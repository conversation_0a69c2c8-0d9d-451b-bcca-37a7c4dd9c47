/**
 * 商家诊断配置
 */
import React from 'react';
import { Select } from 'antd';
import { DiagnosisOptions } from '../../../constants';

const options = DiagnosisOptions.map(item => ({ label: item.name, value: item.id }));

const DiagnosisConfig = (props) => {
    const { nodeFormData, onChange } = props;
    return (
        <div className='flex items-center justify-start'>
            <div className="config-label">诊断场景</div>
            <Select
                value={nodeFormData.diagnosisScene}
                className="config-default-select config-attribute-wrapper"
                onChange={(value) => onChange({ ...nodeFormData, diagnosisScene: value })}
                options={options}
                popupMatchSelectWidth={false}
                placeholder="请选择诊断场景"
                style={{width: '260px'}}
            />
        </div>
    );
};

export default DiagnosisConfig;