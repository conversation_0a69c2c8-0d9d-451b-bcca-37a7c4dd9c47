/**
 * 当前节点所在路径，所需属性值的hooks
 * 目前所用的节点：
 * 1. 条件分支 branchNode;
 * 2. 调用agent agentNode;
 * 3. 技能调用 skillNode;
 */
import React, { useState, useEffect, useMemo } from 'react';
import { debounce } from 'lodash';
import { apiCaller } from '@mfe/cc-api-caller-pc';
const needAttriNodes = ['branchNode', 'agentNode', 'skillNode'];

const useAttriOpHooks = (props) => {
    const { nodes, edges, selectedNode, processId } = props;
    const [attriOptions, setAttriOptions] = useState([]);

    const getAttributes = async (processId, nodeId) => {
        try {
            const res: any = await apiCaller.post(
                // @ts-ignore
                '/xianfu/api-v2/ai-infra/schedule/process/attribute/list',
                {
                    processId,
                    nodeId
                },
            );

            if (res.code === 0) {
                const invertData = res.data.map(item => {
                    return {
                        label: item.attributeLabel,
                        value: item.attributeVal
                    }
                }) || [];
                setAttriOptions(invertData);
            }
        } catch (error) {
            console.error('Search call record failed:', error);
        }
    };

    const getProcessAttribute = debounce(getAttributes, 500);

    const watchNodes = useMemo(() => {
        return nodes.map(node => {
            return {
                id: node.id,
                type: node.type,
                data: node.data
            };
        });
    }, [nodes]);

    const watchEdges = useMemo(() => {
        return edges.map(edge => {
            return {
                id: edge.id,
                source: edge.source,
                target: edge.target,
                sourceHandle: edge.sourceHandle,
                targetHandle: edge.targetHandle
            };
        });
    }, [edges]);

    useEffect(() => {
        if (needAttriNodes.includes(selectedNode.type)) {
            getProcessAttribute(processId, selectedNode.id);
        }
    }, [selectedNode.id, processId, JSON.stringify(watchNodes), JSON.stringify(watchEdges)]);

    return {
        attriOptions
    };
};

export default useAttriOpHooks;
