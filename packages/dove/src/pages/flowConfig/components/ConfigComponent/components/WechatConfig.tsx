/**
 * 企微添加好友配置
 */
import React from 'react';
import { InputNumber } from 'antd';

const WechatConfig = (props) => {
    const { onChange, nodeFormData } = props;

    return (
        <div>
            <div className="flex items-center justify-start">
                <div className="config-label">监听时长</div>
                <div className="config-attribute-wrapper">
                    <InputNumber
                        className="config-default-number"
                        min={0}
                        value={nodeFormData.timeout}
                        onChange={(value) => onChange({ ...nodeFormData, timeout: value })}
                        suffix="小时"
                        style={{ width: '180px' }}
                    />
                </div>
            </div>
            <p className="text-xs text-gray-500 mt-1">手动输入xx小时，在xx小时内任务pending，持续监听topic，待达到xx小时后，跳出该节点执行下级节点</p>
        </div>
    );
};

export default WechatConfig;