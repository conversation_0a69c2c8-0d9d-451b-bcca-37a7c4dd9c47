/**
 * 开始配置
 */
import React from 'react';
import { Checkbox } from 'antd';

const StartConfig = (props) => {
    const { upstreamSystems, nodeFormData, onChange } = props;

    const handleInitiatorChange = (upstreamSystems) => {
        onChange({ ...nodeFormData, upstreamSystems: upstreamSystems });
    };

    return (
        <div>
            <div className="config-label">选择发起方</div>
            <div className="space-y-2 mt-1">
                <div className="flex flex-wrap gap-2">
                    <Checkbox.Group
                        options={upstreamSystems}
                        disabled
                        value={nodeFormData.upstreamSystems}
                        onChange={checkedValues => handleInitiatorChange(checkedValues)}
                    />
                </div>
            </div>
        </div>
    );
};

export default StartConfig;