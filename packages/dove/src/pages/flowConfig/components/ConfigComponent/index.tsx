import React, { useEffect, useMemo } from "react";
import { X, Ellipsis } from "lucide-react";
import { debounce } from "lodash";
import { DropMenu } from '@roo/roo';
import NodeTitle from "../NodeTitle";
import InputComponent from '../InputComponent';
import ConfigFactory from './components/ConfigFactory';
import { generateId, getNodeLabel } from "../../utils";
import './index.scss';

const ConfigComponent = (props) => {
    const {
        selectedNode,
        readOnly,
        nodeFormData,
        setNodeFormData,
        handleUpdateNode,
        onClose,
        removeNode,
        copyNode,
        metaData,
        processId,
        nodes,
        edges
    } = props;
    const [menuVisible, setMenuVisible] = React.useState(false);

    const changeForm = (data) => {
        if (readOnly || !selectedNode) return; // 只读模式不允许更新节点
        setNodeFormData(data);
        handleUpdateNode(data);
    };

    const onChange = debounce(changeForm, 300);

    const closeConfig = () => {
        onClose();
    };

    const cantCopy = React.useMemo(() => {
        return selectedNode.type === 'start' || selectedNode.type === 'end';
    }, [selectedNode]);

    const menu = [
        {
            label: <div className={`copy-icon ${cantCopy ? 'disabled' : ''}`}>复制节点</div>,
            onClick: () => {
                if (cantCopy) {
                    return;
                }
                copyNode();
                setMenuVisible(false);
            }
        },
        {
            label: <div className={`delete-icon ${cantCopy ? 'disabled' : ''}`}>删除节点</div>,
            onClick: () => {
                if (cantCopy) {
                    return;
                }
                removeNode();
                setMenuVisible(false);
            }
        },
    ];

    return (
        <div className="flow-config-component">
            <div className="flow-config-header">
                <NodeTitle label={getNodeLabel(selectedNode.type)} />
                <div className="header-operate">
                    <DropMenu
                        menu={menu}
                        placement="rightTop"
                        trigger="click"
                        visible={menuVisible}
                        onClickOutside={() => setMenuVisible(false)}
                    >
                        <div onClick={() => setMenuVisible(!menuVisible)}>
                            <Ellipsis size={14} />
                        </div>
                    </DropMenu>
                    <X size={14} onClick={closeConfig} />
                </div>
            </div>
            <div className="flow-config-body">
                <div className="flex items-center justify-start" style={{ marginBottom: 6 }}>
                    <div className="config-label" style={{ width: 56 }}>名称</div>
                    <InputComponent
                        value={nodeFormData.label}
                        onChange={(value) => onChange({ ...nodeFormData, label: value })}
                        placeholder="请输入节点名称"
                    />
                </div>
                <ConfigFactory 
                    selectedNode={selectedNode}
                    metaData={metaData}
                    nodeFormData={nodeFormData}
                    onChange={onChange}
                    processId={processId} 
                    nodes={nodes} 
                    edges={edges}
                />
            </div>
        </div>
    )
};

export default ConfigComponent;