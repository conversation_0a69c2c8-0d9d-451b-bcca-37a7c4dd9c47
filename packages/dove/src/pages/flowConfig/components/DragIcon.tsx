import React from 'react';
import NodesDrop from './NodesDrop';
import addIcon from '../../../assets/svg/addIcon.svg';

const DragIcon = (props) => {
  const { type, hasSource, visible, setVisible, addNode } = props;

  return (
    <div className={`drag-icon-component ${type || ''}`} >
      {hasSource && <div className={`normal-icon`}></div>}
      <NodesDrop
        type={'icon'}
        addNode={addNode}
        visible={visible}
        setVisible={setVisible}
      >
        <img className="drag-icon default-drag-icon" src={addIcon} />
      </NodesDrop>
    </div>
  )
};

export default DragIcon;