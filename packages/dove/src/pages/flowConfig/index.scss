.flow-config-page {
    height: 600px;
}

.bg-primary {
    background-color: rgb(15, 23, 42);

    &:hover {
        background-color: rgb(15, 23, 42);
    }
}

.edge-icon-default {
    background: transparent;
    border: none;
    width: 12px;
    height: 12px;
    right: -9px;
    transform: translate(0, -50%);

    &.target {
        right: 0;
    }

    .lucide {
        pointer-events: none;
    }
}

.custom-controls {
    z-index: 999;
    bottom: 0;

    .ant-select-selector {
        border: 0 !important;
        padding: 0 !important;

        .ant-select-selection-item {
            padding: 0 !important;

            &::after {
                content: none !important;
            }
        }
    }

    .ant-select-arrow {
        display: none !important;
    }
}

.nodes-drop {
    cursor: pointer;
}

.condition-item-header {
    margin-right: -12px;

    .header-label {
        margin-right: 6px;
    }
}

.condition-item {
    .condition-child-item {
        height: 30px;
        line-height: 30px;
        margin: 6px 0;
        border-radius: 8px;
        background: #F3F4F7;
        text-align: left;
        padding-left: 10px;
    }
}

.condition-label {
    width: 64px;

    label {
        font-size: 13px;
        color: #354052;
        font-weight: 600;
    }

    .priority-tip {
        font-size: 12px;
        color: #676f83;
    }
}

.condition-content {
    position: relative;

    .condition-relation-section {
        position: absolute;
        left: -20px;
        top: 12px;
        bottom: 16px;

        .relation-line {
            width: 5px;
            position: absolute;
            left: 16px;
            height: 100%;
            border: 1px solid rgba(16, 24, 40, .1411764706);
            border-right: 0;
            border-radius: 8px;
        }

        .operate-btn {
            position: absolute;
            display: flex;
            align-items: center;
            justify-content: center;
            top: 50%;
            transform: translate(-20px, -50%);
            border: 1px solid #d9d9d9;
            padding: 0 6px;
            color: #296dff;
            border-radius: 2px;
            background-color: white;
            margin-left: -12px;
            font-size: 12px;
            font-weight: 700;
            width: 58px;

            &:hover {
                cursor: pointer;
            }

            svg {
                margin-left: 2px;
            }
        }
    }

    .condition-attri-config {
        margin-left: 12px;
    }
}

.drag-icon-component {
    position: relative;
    pointer-events: none;
    margin-right: -4px;

    &.target {
        .normal-icon {
            display: none;
        }
    }

    .normal-icon {
        position: absolute;
        width: 2px;
        height: 8px;
        background-color: #2465FF;
        margin-right: -8px;
        margin-top: 2px;
    }


    // &:hover {
    //     .drag-icon {
    //         pointer-events: none;
    //     }
    // }

    .drag-icon {
        position: absolute;
        top: 0;
        left: -2px;

        &.default-drag-icon {
            pointer-events: none;
        }
    }
}

.flow-node {
    box-shadow: 0px 2px 6px 0px rgba(222, 222, 222, 0.3);

    &.branch-node {
        width: 280px;

        .condition-child-item {
            padding: 0 6px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .second-attribute-content {
            margin-left: 6px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    &.intention-node, &.diagnosis-node {
        width: 220px;
    }

    &.skill-node {
        width: 200px;

        .flow-node-content {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .drag-icon {
        width: 12px;
        height: 12px;
        display: none;
        pointer-events: all;
        cursor: crosshair;
    }

    &:hover, &.selected {
        box-shadow: 0 0 #0000, 0 0 #0000, #10182808 0 4px 6px -2px, #10182814 0 12px 16px -4px;

        .drag-icon {
            display: block;
        }

        .normal-icon {
            display: none;
        }
    }

    &.drag-status {
        height: 48px;
        width: 160px;
        z-index: 1000;
        transform: translate(-50%, -50%);
        position: absolute;
        cursor: move;
    }

    .node-label {
        color: #222;
        font-size: 12px;
        font-weight: 500;
    }
}

.config-default-select {
    width: 180px;
    margin-left: 10px;
}

.config-default-number {
    width: 100%;
}

.config-label {
    color: #222;
}

.config-attribute-wrapper {
    margin-left: 10px;
}

.flow-config-header {
    border-bottom: 1px solid rgb(240, 240, 240);
    padding-bottom: 16px;
}

.flow-config-body {
    padding-top: 16px;
}

.flow-node-content {
    min-height: 30px;
    line-height: 30px;
    margin: 6px 0;
    border-radius: 8px;
    background: #F3F4F7;
    text-align: left;
    padding: 0 6px;
    font-size: 12px;
}

.flow-config-page {
    position: fixed;
    height: 100%;
    width: 100%;

    .flow-config-header {
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;

        .left-section {
            display: flex;
            align-items: center;
        }
    }

    .process-information {
        margin-left: 6px;
        
        .process-name {
            font-size: 16px;
            font-weight: 600;
            color: #3D3D3D;
        }

        .process-updatetime {
            font-size: 12px;
            font-weight: normal;
            color: #999999;
        }
    }
}

.alert-delete-tip {
    position: fixed;
    left: 50%;
    transform: translate(-50%, -30px);
}