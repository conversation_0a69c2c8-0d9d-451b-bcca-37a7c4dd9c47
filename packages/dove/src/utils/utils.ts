import qs from 'qs';

// 获取url上search对象数据
export const getSearchObjFromUrl = () => {
    return qs.parse(window.location.search, {
        ignoreQueryPrefix: true,
    });
};

// 将对象作为search写入url（根据字段名，合并原url上的search）（不触发页面刷新）（保留hash）
export const setSearchObjToUrl = (obj: { [key: string]: any }) => {
    if (typeof obj !== 'object') return;
    // 原始search对象
    const originSearchObj = qs.parse(window.location.search, {
        ignoreQueryPrefix: true,
    });
    // 合并search对象
    const newSearchObj = Object.assign({}, originSearchObj, obj);
    // 将search对象转换为search字符串
    const searchStr = `?${qs.stringify(newSearchObj)}`;
    // search写入url，而且不触发页面刷新
    window.history.pushState(
        {},
        '',
        `${window.location.origin}${window.location.pathname}${searchStr}${window.location.hash}`,
    );
};

/**
 * 电话号加密
 * 举例：
 * - 11位手机号：12344445555 => 123****5555
 * - 非手机号：11112222 => 1111****
 * @param phoneNumber 原始电话号
 * @returns 加密后电话号
 */
export const encryptMiddleFourDigits = (phoneNumber: string) => {
    if (!phoneNumber) {
        return '';
    }
    // 去除所有非数字字符（假设手机号码格式可能包含空格、破折号等）
    phoneNumber = phoneNumber.replace(/\D/g, '');
    let prefix = '';
    let suffix = '';
    const len = phoneNumber.length;
    if (phoneNumber.length === 11) {
        prefix = phoneNumber.slice(0, 3);
        suffix = phoneNumber.slice(-4);
        return `${prefix}****${suffix}`;
    } else {
        const middle = parseInt(`${phoneNumber.length / 2}`);
        prefix = phoneNumber.slice(0, len - middle);
        suffix = Array.from({ length: middle })
            .map(() => '*')
            .join('');
        return `${prefix}${suffix}`;
    }
};

export const percentFormatter = (value: number, total, fractionDigits = 0) => {
    const number = (value / total) * 100 || 0;

    return number.toFixed(fractionDigits);
};

// 格式化时间展示（毫秒 => 分:秒）
export const formatMillisecond = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
        .toString()
        .padStart(2, '0')}`;
};


/**
 * 下载文件by文枢
 * @param url 支持文枢下载url
 */
export function downloadBySecDB(url) {
    if (window.secDBDownload) {
        window.secDBDownload.downloadFile(url);
    } else {
        window.open(url, '_blank');
    }
}