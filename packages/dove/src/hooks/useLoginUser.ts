import { apiCaller, MethodType } from '@mfe/cc-api-caller-pc';
import { useRequest } from 'ahooks';

const useLoginUser = () => {
    const fetchLoginUser = async () => {
        // 境内外path不一致，后边得看看怎么处理这种问题
        // 考虑建立一个映射表，在apiCaller内部消化
        const res = await apiCaller.get(
            // @ts-ignore
            '/dove/get/user/info',
            {},
        );
        if (res.code !== 0) {
            return;
        }

        return res.data as any as { mis: string; name: string; uid: number };
    };

    const { data: user } = useRequest(fetchLoginUser, {
        cacheKey: 'currentUser',
        cacheTime: 1000 * 60 * 60,
    });

    return {
        user,
    };
};
export default useLoginUser;
