import { useMemo } from 'react';
import { apiCaller } from '@mfe/cc-api-caller-pc';
import { useRequest } from 'ahooks';

/**
 * 获取所有 kpType 并去重
 * @param objectTypeEnum - 对象类型枚举数据
 * @returns 去重后的 kpType 数组
 */
export const getAllUniqueKpTypes = (objectTypeEnum: any[]) => {
    if (!Array.isArray(objectTypeEnum)) {
        return [];
    }

    const allKpTypes = objectTypeEnum.reduce((acc, item) => {
        if (Array.isArray(item.kpType)) {
            acc.push(...item.kpType);
        }
        return acc;
    }, []);

    return [...new Set(allKpTypes)];
};

/**
 * 获取【商家类型】对应【触达方式】、【触达对象】等可选项
 * @returns
 */
export const useOptionsByObjectType = ({ contactObjectType }) => {
    const { data: objectTypeEnum = [] } = useObjectType();
    /** 当前【商家类型】对应原始选项 */
    const currentObjectTypeEnum = useMemo(() => {
        return objectTypeEnum?.find(v => v.objectType === contactObjectType);
    }, [objectTypeEnum, contactObjectType]); // 当前【商家类型】对应原始选项

    /** 当前【商家类型】对应【触达方式】可选项 */
    const contactTypeOptions = useMemo(
        () =>
            (currentObjectTypeEnum?.contactType || []).map(v => ({
                // @ts-ignore
                label: v.name,
                // @ts-ignore
                value: v.code,
            })),
        [currentObjectTypeEnum],
    );

    /** 当前【商家类型】对应【触达对象】可选项 */
    const kpPriorityOptions = useMemo(
        () =>
            (currentObjectTypeEnum?.kpType || [])?.map(item => ({
                label: item,
                value: item,
            })),
        [currentObjectTypeEnum],
    );

    /** 当前【商家类型】对应商家tag，用于【选择商家】为"筛选"时，展示商家tag选项 */
    const contactTagOptions = useMemo(
        () => currentObjectTypeEnum?.contactTag || [],
        [currentObjectTypeEnum],
    );

    /** 所有去重后的 kpType */
    const allUniqueKpTypes = useMemo(
        () =>
            getAllUniqueKpTypes(objectTypeEnum)?.map(item => ({
                label: item,
                value: item,
            })),
        [objectTypeEnum],
    );

    return {
        objectTypeEnum,
        currentObjectTypeEnum,
        contactTypeOptions,
        kpPriorityOptions,
        contactTagOptions,
        allUniqueKpTypes,
    };
};

/**
 * 获取触达对象以及关联选项
 * @returns
 */
const useObjectType = () => {
    return useRequest(async () => {
        const res = await apiCaller.post(
            '/xianfu/api-v2/dove/object/type/query',
            {},
        );
        if (res.code !== 0) {
            return [];
        }
        // P1需求
        // return res.data?.data.concat([
        //     {
        //         objectType: 3,
        //         name: '非美团商家',
        //         kpType: [],
        //         contactType: [],
        //         contactTag: [],
        //     },
        // ]);
        return res.data?.data;
    }, {});
};

export default useObjectType;
