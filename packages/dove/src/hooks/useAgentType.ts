import { useRequest } from "ahooks";
import { useState } from "react";
import { apiCaller } from "@mfe/cc-api-caller-pc";
import { AgentTypeEnum } from "@src/constants";

export { AgentTypeEnum };
interface AgentType {
    code: number;
    msg: string;
    label?: string;
    value?: number;
}

export default function useAgentType() {
    const [agentTypeList, setAgentTypeList] = useState<AgentType[]>([]);

    const { loading, error } = useRequest(
        async () => {
            const apiPath = "/xianfu/api-v2/dove/agent/type";
            const res: any = await apiCaller.get(apiPath, {});
            if (res?.code === 0) {
                const data = (res?.data || [])?.map((i: AgentType) => ({
                    ...i,
                    value: i.code,
                    label: i.msg
                }))
                setAgentTypeList(data);
                return data;
            }
        },
        {
            onError: (err) => {
                console.error("Failed to fetch agent types:", err);
            },
        }
    );

    return {
        agentTypeList, // 关联agent类型 列表
        loading,       // 加载状态
        error,         // 错误信息
    };
}
