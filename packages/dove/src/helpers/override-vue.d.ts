declare module '@vue/runtime-dom' {
    namespace JSX {
        interface Element extends React.ReactElement<any, any> {}
        interface IntrinsicElements {
            [key: string]: any;
        }
        interface IntrinsicAttributes extends React.Attributes {}
    }
}

declare module 'vue' {
    namespace JSX {
        interface Element extends React.ReactElement<any, any> {}
        interface IntrinsicElements {
            [key: string]: any;
        }
        interface IntrinsicAttributes extends React.Attributes {}
    }
}
