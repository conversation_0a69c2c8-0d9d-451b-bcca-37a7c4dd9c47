// wrapper
import React, { ComponentType } from 'react';
import initSso from '../sso';
import { register } from '../request';

// 这个wrapper主要解决一些初始化的问题
// 在非模块联邦的场景下，这些初始化工作可以在main.js中的render函数中完成
// 但是模块联邦下，每个子应用都是独立运行的，没有main.js这个入口，所以需要在子应用中手动完成这些初始化工作
// 一般来说，如果你的副作用代码（网络请求、数据获取）需要在组件渲染之前完成，那么就需要把他放到ModuleFederationWrapper

export const ModuleFederationWrapper = <T extends ComponentType>(Comp: T) => {
    const LazyComp = React.lazy(() => {
        const defaultTracker = window.LXAnalytics('getTracker');
        defaultTracker('pageView', {}, {}, 'c_waimai_m_m_crm_bi_mspuss76');
        // 初始化sso-web
        initSso();

        // 网络请求初始化
        register();
        return new Promise<{ default: T }>((res, rej) => {
            res({
                // @ts-ignore
                default: (props = {}) => (
                    // @ts-ignore
                    <Comp {...props} isFromFederation={true} />
                ),
            });
        });
    });
    return props => <LazyComp {...props} />;
};
