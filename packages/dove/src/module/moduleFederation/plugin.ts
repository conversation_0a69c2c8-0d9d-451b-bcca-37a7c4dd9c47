// plugin
import path from 'path';
import fs from 'fs';

const resolve = (p: string) => path.resolve(process.cwd(), p);
const VARIABLE_NAME = '__ModuleFederationAutoInjection';
const VIRTUAL_PREFIX = ':virtual-f';

function convertExportDefaultsToConsts(code) {
    const regex = /^export\s+default\s+([\s\S]*?)(?=\s*(?:;|$))/gm;
    // 使用正则表达式替换匹配的代码
    return code.replace(regex, (match, p1) => {
        const trimmedContent = p1.trim();
        const variableName = VARIABLE_NAME;
        return `const ${variableName} = ${trimmedContent};`; // 返回新的const声明
    });
}

// 插件的作用是对通过Module Federation共享的组件做二次编辑
// 通过虚拟模块地址的方式，实现不修改源文件，用虚拟模块来对Module Federation的分享
// 并在这个虚拟模块中，做二次编辑，把一些副作用代码引入进来（i18n初始化、api-caller的globalConfig, axios的拦截器等）
export const moduleFederationInjection = (exposes: Record<string, string>) => {
    const withVirtual = Object.fromEntries(
        Object.entries(exposes).map(([key, p]) => {
            const [name, ext] = resolve(p).split('.');
            return [key, `${name}${VIRTUAL_PREFIX}.${ext}`];
        }),
    );

    const virtualPathSet = new Set(Object.values(withVirtual));

    const plugin = {
        name: 'auto-inject-federation-side-effect',
        resolveId(id) {
            if (virtualPathSet.has(id)) {
                return id;
            }

            return null;
        },
        load(id) {
            if (!virtualPathSet.has(id)) {
                return;
            }

            // 地址去除virtual
            const code = fs.readFileSync(
                resolve(id.replace(VIRTUAL_PREFIX, '')),
                'utf-8',
            );

            if (!code.includes('export default')) {
                console.warn('通过Module Federation共享的组件，请使用默认导出');
                return code;
            }
            const wrapperdCode = convertExportDefaultsToConsts(code);

            return `import { ModuleFederationWrapper } from '@src/module/moduleFederation/wrapper';
            ${wrapperdCode}
            export default ModuleFederationWrapper(${VARIABLE_NAME})`;
        },
    };

    return {
        plugin,
        exposes: withVirtual,
    };
};
