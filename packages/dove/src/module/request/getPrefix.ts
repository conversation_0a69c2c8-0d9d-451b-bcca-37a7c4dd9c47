// 我们以信鸽dove为例
// 通过webstatic构建的会增加环境变量API_PATH: /xianfu/api/dove
// 假设项目中有以下接口：
// 信鸽本身的：/dovecall-web/list, /dovecall-web/detail, /doveqc/list, /doveqc/detail
// 外部依赖的：/crm/visit/list(新拜访), /mfepro/xianfu/visit/jaguar/bdvisit/r/getPoiRecord(老拜访),  /poiRest/awakenTask/list(farmer 未营业商家)
// 公共服务的：/uicomponent/getLoginUser

//const API_PATH = import.meta.env.VITE_API_PREFIX // vite需要通过import.meta.env获取环境变量
const API_PATH = import.meta.env.VITE_API_PREFIX; // webpack可以通过DefinePlugin注入

export const getPrefix = (path: string, fallback = '') => {
    if (!API_PATH) {
        return '';
    }
    // 从上面假设看：如果是以/dove开头，那么这是一个信鸽本身的接口（这仅仅是示例，具体规则以实际业务逻辑为准）
    if (/^\/beeauth/.test(path) || /^\/impc/.test(path)) {
        return API_PATH;
    }

    // strategy分组 & dove分组
    if (/^\/strategy/.test(path) || /^\/dove\//.test(path)) {
        // 已经有/strategy了
        return '/xianfu/api';
    }

    // dove开头的请求，如doveqc、dovecall等
    if (/^\/dove/.test(path)) {
        // 已经有/strategy了
        return '/xianfu/api/dove';
    }

    if (/^\/uicomponent/.test(path)) {
        return '/xianfu/api/common';
    }

    // fass api
    if (
        /^\/api[queryAudioTask|asyncRecognizeSpeechFragment|recognizeSpeechFragment|getAudioToken]/.test(
            path,
        )
    ) {
        return API_PATH + '/fass';
    }

    return fallback;
};
