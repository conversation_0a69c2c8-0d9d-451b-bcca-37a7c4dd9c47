// 【触达类型】枚举
export enum ReachTypeEnum {
    SINGLE_COMMUNICATION = 1, // 单次沟通
    INTELLIGENT_SCHEDULING = 2, // 智能调度
}
export const ReachTypeName = {
    [ReachTypeEnum.SINGLE_COMMUNICATION]: '单次沟通',
    [ReachTypeEnum.INTELLIGENT_SCHEDULING]: '智能调度',
};
export const reachTypeOptions = [
    { label: '单次沟通', value: ReachTypeEnum.SINGLE_COMMUNICATION },
    { label: '智能调度', value: ReachTypeEnum.INTELLIGENT_SCHEDULING },
];

// 关联agent类型
export enum AgentTypeEnum {
    FRIDAY = -1,
    SCENE = 1,
    HQ = 2,
    CITY = 3,
}

export const AgentTypeMap = {
    [AgentTypeEnum.FRIDAY]: 'Friday',
    [AgentTypeEnum.SCENE]: '场景模板',
    [AgentTypeEnum.HQ]: '总部应用',
    [AgentTypeEnum.CITY]: '城市应用',
};

// 【触达方式】枚举
export enum ReachMethodEnum {
    AI_CALL = 2, // 智能外呼
    OFFLINE_AUDIO = 3, // 线下录音
    TEXT_CALL = 4, // 文本外呼
    IM_MASS_SEND = 5, // IM群发
    INTELLIGENT_SCHEDULING_CALL = 6, // 智能调度外呼（【触达类型】为"智能调度"时专用）
}
// 【触达方式】全量选项
export const reachMethodOptions = [
    { label: '智能外呼', value: ReachMethodEnum.AI_CALL },
    { label: '线下录音', value: ReachMethodEnum.OFFLINE_AUDIO },
    { label: '文本外呼', value: ReachMethodEnum.TEXT_CALL },
    { label: 'IM群发', value: ReachMethodEnum.IM_MASS_SEND },
    {
        label: '智能调度外呼',
        value: ReachMethodEnum.INTELLIGENT_SCHEDULING_CALL,
    },
];
// 【筛选框触达方式】枚举
export enum FilterReachMethodEnum {
    AI_CALL = 2, // 智能外呼
    TEXT_CALL = 4, // 文本外呼
    IM_MASS_SEND = 5, // IM群发
}

// 【筛选框触达方式】选项
export const filterReachMethodOptions = [
    { label: '智能外呼', value: FilterReachMethodEnum.AI_CALL },
    { label: '文本外呼', value: FilterReachMethodEnum.TEXT_CALL },
    { label: 'IM群发', value: FilterReachMethodEnum.IM_MASS_SEND },
];

// 触达对象圈选标签枚举
export enum ReachTargetTagEnum {
    DAILY_UNSIGNED = 1,
    DAILY_SIGN_REJECTED = 2,
    YESTERDAY_AOR_PUBLIC_SEA = 3,
    YESTERDAY_AOR_PUBLIC_SEA_WITHOUT_SG = 4,
    AOR_PUBLIC_SEA = 5,
}

// 触达对象圈选标签选项
export const reachTargetTagOptions = [
    { label: '每日新增待签约商家', value: ReachTargetTagEnum.DAILY_UNSIGNED },
    {
        label: '每日审核驳回待修改商家',
        value: ReachTargetTagEnum.DAILY_SIGN_REJECTED,
    },
    {
        label: '昨日蜂窝内所有公海商家（包含闪购门店）',
        value: ReachTargetTagEnum.YESTERDAY_AOR_PUBLIC_SEA,
    },
    {
        label: '昨日蜂窝内所有公海商家（不包含闪购门店）',
        value: ReachTargetTagEnum.YESTERDAY_AOR_PUBLIC_SEA_WITHOUT_SG,
    },
    {
        label: '蜂窝内所有公海商家（不包含闪购门店）',
        value: ReachTargetTagEnum.AOR_PUBLIC_SEA,
    },
];

// 任务状态枚举
export enum TaskStatusEnum {
    INIT = 1,
    DOING = 2,
    DONE = 3,
    CANCEL = 4,
    AUDIT = 5,
    REJECTED = 6,
    BAOSHIJIE_AUDITING = 7, // 保时洁审核中
    BAOSHIJIE_REJECTED = 8, // 保时洁审核驳回
}

// 任务状态选项
export const taskStatusOptions = [
    { label: '待执行', value: TaskStatusEnum.INIT, color: 'processing' },
    { label: '执行中', value: TaskStatusEnum.DOING, color: 'processing' },
    { label: '已完成', value: TaskStatusEnum.DONE, color: 'success' },
    { label: '已取消', value: TaskStatusEnum.CANCEL, color: 'error' },
    { label: '待审核', value: TaskStatusEnum.AUDIT, color: 'processing' },
    { label: '审核驳回', value: TaskStatusEnum.REJECTED, color: 'error' },
    {
        label: '保时洁审核中',
        value: TaskStatusEnum.BAOSHIJIE_AUDITING,
        color: 'error',
    },
    {
        label: '保时洁审核驳回',
        value: TaskStatusEnum.BAOSHIJIE_REJECTED,
        color: 'error',
    },
];

// 任务运转策略类型枚举
export enum TaskStrategyTypeEnum {
    DATE_RANGE = 1,
}

// 任务运转策略类型选项
export const taskStrategyTypeOptions = [
    { label: '指定日期范围运转任务', value: TaskStrategyTypeEnum.DATE_RANGE },
];

// 触达状态枚举
export enum ExecutionStatusEnum {
    WAITING = 1,
    DOING = 2,
    DONE = 3,
    CANCEL = 4,
}

// 触达状态选项
export const executionStatusOptions = [
    {
        label: '待执行',
        value: ExecutionStatusEnum.WAITING,
        color: 'processing',
    },
    { label: '执行中', value: ExecutionStatusEnum.DOING, color: 'processing' },
    { label: '已执行', value: ExecutionStatusEnum.DONE, color: 'success' },
    { label: '已取消', value: ExecutionStatusEnum.CANCEL, color: 'error' },
];

// 沟通结果枚举
export enum CommunicationResultEnum {
    SUCCESS = 1,
    FAIL = 2,
    READ = 3, // 已读
    UNREAD = 4, // 未读
}

// 沟通结果选项
export const communicationResultOptions = [
    {
        label: '成功',
        value: CommunicationResultEnum.SUCCESS,
        color: 'success',
    },
    { label: '失败', value: CommunicationResultEnum.FAIL, color: 'error' },
    { label: '已读', value: CommunicationResultEnum.READ, color: 'success' },
    { label: '未读', value: CommunicationResultEnum.UNREAD, color: 'default' },
];

// 触达系统，后端不维护，前端维护
// AGENT对应 ReachMethodEnum.AI_CALL ReachMethodEnum.TEXT_CALL ReachMethodEnum.IM_MASS_SEND这三个
// 其余的枚举应当和ReachMethodEnum保持一致
export enum ReachSystemEnum {
    AGENT = 1,
    VISIT = 3, // 拜访
}

export const reachSystemoptions = [
    { label: '智能沟通平台', value: ReachSystemEnum.AGENT },
    { label: '拜访系统', value: ReachSystemEnum.VISIT },
];

export const reachSystemMap = new Map([
    [ReachMethodEnum.AI_CALL, ReachSystemEnum.AGENT],
    [ReachMethodEnum.TEXT_CALL, ReachSystemEnum.AGENT],
    [ReachMethodEnum.IM_MASS_SEND, ReachSystemEnum.AGENT],
    [ReachMethodEnum.INTELLIGENT_SCHEDULING_CALL, ReachSystemEnum.AGENT],
    [ReachMethodEnum.OFFLINE_AUDIO, ReachSystemEnum.VISIT],
]);
// 调度流程发起方id枚举
export const SCHEDULE_FLOW_SYSTEM_ID_101 = 101; // 发起方为"任务列表"

// 【流程节点】枚举
export enum ProcessStatusEnum {
    INIT = 1, // 待执行
    AI_DOING = 2, // AI执行中
    AI_DONE = 3, // AI已完成
    TELE_SALE_DOING = 4, // 电销执行中
    TELE_SALE_DONE = 5, // 电销已完成
    BD_RECEIVE = 6, // BD已接管
    FAIL = 99, // 执行异常
}
export const processStatusOptions = [
    {
        label: '待执行',
        value: ProcessStatusEnum.INIT,
        color: 'default',
    },
    {
        label: 'AI执行中',
        value: ProcessStatusEnum.AI_DOING,
        color: 'processing',
    },
    {
        label: 'AI已完成',
        value: ProcessStatusEnum.AI_DONE,
        color: 'success',
    },
    {
        label: '电销执行中',
        value: ProcessStatusEnum.TELE_SALE_DOING,
        color: 'processing',
    },
    {
        label: '电销已完成',
        value: ProcessStatusEnum.TELE_SALE_DONE,
        color: 'success',
    },
    {
        label: 'BD已接管',
        value: ProcessStatusEnum.BD_RECEIVE,
        color: 'success',
    },
    {
        label: '执行异常',
        value: ProcessStatusEnum.FAIL,
        color: 'error',
    },
];

// 【是否接通】枚举
export enum ConnectedEnum {
    NOT_CONNECTED = 0, // 未接通
    CONNECTED = 1, // 已接通
}
export const connectedOptions = [
    { label: '未接通', value: ConnectedEnum.NOT_CONNECTED, color: '#ff4d4f' },
    { label: '已接通', value: ConnectedEnum.CONNECTED, color: '#52c41a' },
];

export const aiAvatar =
    'data:image/png;base64,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';

// 外呼挂断原因枚举
export enum ReleaseReasonEnum {
    MANUAL = 1, // AI挂断
    SYSTEM = 2, // 用户挂断
}

export const ReleaseReasonText = {
    [ReleaseReasonEnum.MANUAL]: 'AI挂断',
    [ReleaseReasonEnum.SYSTEM]: '用户挂断',
};
