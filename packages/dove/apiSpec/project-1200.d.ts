/**
 * selftest-250606-105950-209
 * @namespace project_1200Types
 * @time 2025-06-12 11:34:27
 * 该文件由 @mfe/cc-api-caller 自动生成
 */

export type project_1200Types = {
    /**
     * 接口 [根据mcp server查询工具列表↗](https://f1-better.sankuai.com/#/home?projectId=1200&api_project_id=99843)的 **请求类型**
     * 更新时间：2025-06-12 11:34:26
     */
    '/xianfu/api-v2/ai-infra/tool/getByMcpServer': {
        method: 'POST';
        request: {
            /** MCP服务器的URL地址 */
            mcpServer: string;
        };
        response: {
            /** 接口的访问地址 */
            url: string;
            /** 接口名称 */
            name: string;
            /** 接口的展示名称 */
            showName: string;
            /** 接口的详细描述 */
            description: string;
            argumentList?: {
                /** 参数名称 */
                name: string;
                /** 参数的数据类型 */
                type: string;
                /** 参数在请求体中的路径 */
                cursor: string;
                /** 指示此参数是否为必填项 */
                required: boolean;
            }[];
        }[];
    };
};
