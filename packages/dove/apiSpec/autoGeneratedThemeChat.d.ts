/**
 * pc
 * @namespace AutoGeneratedThemeChatTypes
 * 该文件由 @mfe/cc-api-caller 自动生成
 * powered by @mtfe/yapi2service
 */

export type AutoGeneratedThemeChatTypes = {
    /** 判断是否有正在进行中的会话主题 */
    '/impc/message/hasGoingThemeChat': {
        method: 'GET';
        request: {
            wmPoiId: string;
        };
        response: {
            status: number;
            themeChatId: number;
            themeChatCreateTime: string;
        };
    };
    /** 新建主题会话 */
    '/impc/message/createThemeChat': {
        method: 'POST';
        request: {
            wmPoiId: number;
            themeId: number;
        };
        response: {
            themeChatId: string;
            themeChatCreateTime: string;
        };
    };
    /** 查看可选择的会话主题列表 */
    '/impc/message/themeList': {
        method: 'GET';
        request: {
            themeType: string;
        };
        response: {
            themeId: number;
            themeName: string;
        }[];
    };
    /** 结束会话 */
    '/impc/message/endThemeChat': {
        method: 'POST';
        request: {
            themeChatId: number;
        };
        response: Record<string, never>;
    };
};
