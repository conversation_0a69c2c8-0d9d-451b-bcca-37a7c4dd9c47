{
    "originInfo": {
        "info": {
            "id": "de881c40-47ce-462a-9787-8ab230850f18",
            "commitId": "f5eafa56-5db8-4ac3-987d-ac6fae5b1e5a",
            "title": "信鸽",
            "baseUrl": "",
            "description": "",
            "managers": [
                "lirui114",
                "dujiale02",
                "lichen87",
                "huangjunhao10",
                "wangshixian03",
                "zhaijiawen03",
                "luohong08",
                "zhangzhu02"
            ],
            "category": {
                "信鸽电话接通率提升": [
                    "d0210b81-9d64-45c6-bbff-a2218bab883f",
                    "f3b0653d-506a-4838-a261-b669f0d253f6",
                    "adbe69af-b6a6-4f69-bf88-2dad7b131cf9",
                    "97560691-aced-4f6d-a6df-c011743c2da2",
                    "cce6090f-2bc7-4d77-a9df-82c2f72b5d8c",
                    "22542db6-9875-48c9-9876-63226df24dc9",
                    "67a0c9ad-8c5b-4813-ad07-ff424bb7cb29",
                    "d17b3b40-76a2-4b80-b426-77a769544513",
                    "3760d399-33db-4132-93fb-c16950dd4802",
                    "debcfd29-f9c3-43a8-8ea4-ba560e805d9e",
                    "0aff911c-4d9c-412b-ad56-28fe9b7cee5b",
                    "48877f44-d44c-435e-be17-c174f66e2f69",
                    "459a7d8c-0630-4344-87cd-174e4e9a361f"
                ],
                "沟通agent": [
                    "a759e45c-37fd-44fa-ab4e-2145992fc084",
                    "4cf1ac71-ce90-4ff4-81a7-ac767b6d0951",
                    "db5d6499-286f-4c4f-bf8e-29e917580809",
                    "7779f443-8265-4795-8397-0351fbe9980c",
                    "f96aca3b-96b5-4ba0-8c02-0e73232a1b5d",
                    "a2e4b7f3-c4df-4c08-bc5b-d80f9be0d147",
                    "c2e58323-873c-4a3c-99e9-9d839c858aab",
                    "72454569-b73d-4a07-ac9c-d891bed84bf6",
                    "47a5a0e7-51a9-46d2-aef1-e85f55bc1299",
                    "48685611-52e8-4d0b-b6e3-66d04c9c0db9",
                    "5087987a-671a-432f-881e-05a21654d69b",
                    "57544336-8793-4876-8ae4-f93d9ce091e3",
                    "a4def063-2680-4850-ab1a-a75f1da5ba92",
                    "957700b8-787a-4356-a2f4-c61fa8191022",
                    "297888bf-4298-497e-9da6-e1f71004dc56",
                    "0a19499c-9625-45de-a4ac-e1e4951411f5",
                    "e900611e-251b-45e4-a8e5-544a0c10cb57",
                    "d2dae4d7-0917-42fc-9ce6-4ed6103f5880"
                ],
                "BD数字分身": [
                    "7b64a983-10f9-493e-997d-e336b438f621",
                    "6a9ab8ff-cafe-4d2f-9480-119e8af480d4",
                    "0e5992d6-6202-4532-b469-1c5fa4aa7cac",
                    "bf9cdcd6-6ec9-43ef-860b-657d582b9398",
                    "56aeb516-e4fc-4630-a9f0-39820f4c59c8",
                    "f14a5a2f-1594-426d-8252-216033eef460",
                    "2ff6adc5-a967-4ac7-808e-2c0950bab6a7",
                    "332b2da4-d0e2-4b54-b64d-adadc39222a2",
                    "b879950f-7613-4c29-a97c-30c2979a5e44",
                    "8d620bf2-ffc6-4d3c-92e7-690773076957"
                ],
                "调度中心": [
                    "20432a00-e6f4-4045-a135-59333662d536",
                    "37d5ae07-4005-4866-a764-f3dc69bd03ce",
                    "5ae6636e-ca14-47d9-8f17-04db08212243",
                    "b6a41788-3b4b-468a-9608-7f234be42085",
                    "fb0f47ef-2024-4684-a8e9-9160ec55c2ae"
                ],
            },
            "branch": "master"
        },
        "apis": [
            {
                "id": "f96aca3b-96b5-4ba0-8c02-0e73232a1b5d",
                "commitId": "66a186bd-08d9-4dc4-9ae4-5eccb64968cd",
                "title": "复制 agent",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/agent/copy",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "id": {
                                "type": "integer"
                            }
                        },
                        "required": [
                            "id"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "const": null
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/f96aca3b-96b5-4ba0-8c02-0e73232a1b5d"
            },
            {
                "id": "a759e45c-37fd-44fa-ab4e-2145992fc084",
                "commitId": "de9292b9-cf58-48f2-9b0f-8cb0bfcad87e",
                "title": "创建 agent",
                "description": "https://km.sankuai.com/collabpage/2706582482#id-3.4%20%E6%8E%A5%E5%8F%A3%E4%BF%A1%E6%81%AF",
                "request": {
                    "template": "/xianfu/api-v2/dove/agent/create",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "name": {
                                "type": "string",
                                "format": "str"
                            },
                            "description": {
                                "type": "string",
                                "format": "str"
                            },
                            "agentChannel": {
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "type": {
                                            "type": "integer"
                                        },
                                        "jupiterTenantId": {
                                            "type": "string",
                                            "format": "str"
                                        },
                                        "botId": {
                                            "type": "string",
                                            "format": "str"
                                        },
                                        "routePoint": {
                                            "type": "string",
                                            "format": "str"
                                        },
                                        "timbreId": {
                                            "type": "string",
                                            "format": "str"
                                        }
                                    },
                                    "required": [
                                        "type",
                                        "jupiterTenantId",
                                        "botId",
                                        "routePoint",
                                        "timbreId"
                                    ]
                                }
                            },
                            "bizId": {
                                "type": "integer"
                            },
                            "objectType": {
                                "type": "integer"
                            },
                            "status": {
                                "type": "integer"
                            },
                            "auth": {
                                "type": "object",
                                "properties": {
                                    "type": {
                                        "type": "integer"
                                    },
                                    "org": {
                                        "items": {
                                            "type": "integer"
                                        }
                                    }
                                },
                                "required": [
                                    "type",
                                    "org"
                                ]
                            },
                            "placeholder": {
                                "items": {
                                    "type": "string",
                                    "format": "str"
                                }
                            },
                            "groupId": {
                                "type": "string",
                                "format": "str"
                            },
                            "prologue": {
                                "type": "string",
                                "format": "str"
                            },
                            "llmName": {
                                "type": "string",
                                "format": "str"
                            },
                            "llmAppId": {
                                "type": "string",
                                "format": "str"
                            },
                            "type": {
                                "type": "integer"
                            },
                            "sceneName": {
                                "type": "string",
                                "format": "str"
                            },
                            "systemPrompt": {
                                "type": "object",
                                "properties": {
                                    "prompt": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "inputParams": {
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "name": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "content": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "allowFrontLineChange": {
                                                    "type": "boolean",
                                                    "format": "bool"
                                                },
                                                "requiredFrontLineChange": {
                                                    "type": "boolean",
                                                    "format": "bool"
                                                }
                                            },
                                            "required": [
                                                "name",
                                                "content",
                                                "allowFrontLineChange",
                                                "requiredFrontLineChange"
                                            ]
                                        }
                                    }
                                },
                                "required": [
                                    "prompt",
                                    "inputParams"
                                ]
                            },
                            "skill": {
                                "type": "object",
                                "properties": {
                                    "ids": {
                                        "items": {
                                            "type": "number",
                                            "format": "long"
                                        }
                                    },
                                    "allowFrontLineChange": {
                                        "type": "boolean",
                                        "format": "bool"
                                    }
                                },
                                "required": [
                                    "ids",
                                    "allowFrontLineChange"
                                ]
                            },
                            "knowledgeBase": {
                                "type": "object",
                                "properties": {
                                    "ids": {
                                        "items": {
                                            "type": "number",
                                            "format": "long"
                                        }
                                    },
                                    "allowFrontLineChange": {
                                        "type": "boolean",
                                        "format": "bool"
                                    }
                                },
                                "required": [
                                    "ids",
                                    "allowFrontLineChange"
                                ]
                            },
                            "version": {
                                "type": "string",
                                "format": "str"
                            },
                            "intention": {
                                "type": "object",
                                "properties": {
                                    "elementList": {
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "tag": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "description": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "jupiterRank": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "tagPrompt": {
                                                    "type": "string",
                                                    "format": "str"
                                                }
                                            },
                                            "required": [
                                                "tag",
                                                "description",
                                                "jupiterRank",
                                                "tagPrompt"
                                            ]
                                        }
                                    },
                                    "llmName": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "systemPrompt": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "allowFrontLineChange": {
                                        "type": "boolean",
                                        "format": "bool"
                                    }
                                },
                                "required": [
                                    "elementList",
                                    "llmName",
                                    "systemPrompt",
                                    "allowFrontLineChange"
                                ]
                            }
                        },
                        "required": [
                            "name",
                            "description",
                            "agentChannel",
                            "bizId",
                            "objectType",
                            "status",
                            "auth",
                            "placeholder",
                            "groupId",
                            "prologue",
                            "llmName",
                            "llmAppId",
                            "type",
                            "sceneName",
                            "systemPrompt",
                            "skill",
                            "knowledgeBase",
                            "version",
                            "intention"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "const": 0
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "const": null
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/a759e45c-37fd-44fa-ab4e-2145992fc084"
            },
            {
                "id": "7779f443-8265-4795-8397-0351fbe9980c",
                "commitId": "81f725b4-1f43-4aea-b610-36fa5a481247",
                "title": "删除 agent",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/agent/delete",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "id": {
                                "type": "integer"
                            }
                        },
                        "required": [
                            "id"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "const": null
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/7779f443-8265-4795-8397-0351fbe9980c"
            },
            {
                "id": "db5d6499-286f-4c4f-bf8e-29e917580809",
                "commitId": "03cf518b-284e-4d2d-bdc1-34e80bf2bfc3",
                "title": "编辑 agent",
                "description": "https://km.sankuai.com/collabpage/2706582482#id-3.4%20%E6%8E%A5%E5%8F%A3%E4%BF%A1%E6%81%AF",
                "request": {
                    "template": "/xianfu/api-v2/dove/agent/edit",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "id": {
                                "type": "integer"
                            },
                            "name": {
                                "type": "string",
                                "format": "str"
                            },
                            "description": {
                                "type": "string",
                                "format": "str"
                            },
                            "agentChannel": {
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "type": {
                                            "type": "integer"
                                        },
                                        "jupiterTenantId": {
                                            "type": "string",
                                            "format": "str"
                                        },
                                        "botId": {
                                            "type": "string",
                                            "format": "str"
                                        },
                                        "timbreId": {
                                            "type": "string",
                                            "format": "str"
                                        }
                                    },
                                    "required": [
                                        "type",
                                        "jupiterTenantId",
                                        "botId",
                                        "timbreId"
                                    ]
                                }
                            },
                            "bizId": {
                                "type": "integer"
                            },
                            "objectType": {
                                "type": "integer"
                            },
                            "status": {
                                "type": "integer"
                            },
                            "auth": {
                                "type": "object",
                                "properties": {
                                    "type": {
                                        "type": "integer"
                                    },
                                    "org": {
                                        "items": {
                                            "type": "integer"
                                        }
                                    }
                                },
                                "required": [
                                    "type",
                                    "org"
                                ]
                            },
                            "placeholder": {
                                "items": {
                                    "type": "string",
                                    "format": "str"
                                }
                            },
                            "groupId": {
                                "type": "string",
                                "format": "str"
                            },
                            "prologue": {
                                "type": "string",
                                "format": "str"
                            },
                            "llmName": {
                                "type": "string",
                                "format": "str"
                            },
                            "llmAppId": {
                                "type": "string",
                                "format": "str"
                            },
                            "type": {
                                "type": "integer"
                            },
                            "sceneName": {
                                "type": "string",
                                "format": "str"
                            },
                            "systemPrompt": {
                                "type": "object",
                                "properties": {
                                    "prompt": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "inputParams": {
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "name": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "content": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "allowFrontLineChange": {
                                                    "type": "boolean",
                                                    "format": "bool"
                                                },
                                                "requiredFrontLineChange": {
                                                    "type": "boolean",
                                                    "format": "bool"
                                                }
                                            },
                                            "required": [
                                                "name",
                                                "content",
                                                "allowFrontLineChange",
                                                "requiredFrontLineChange"
                                            ]
                                        }
                                    }
                                },
                                "required": [
                                    "prompt",
                                    "inputParams"
                                ]
                            },
                            "skill": {
                                "type": "object",
                                "properties": {
                                    "ids": {
                                        "items": {
                                            "type": "number",
                                            "format": "long"
                                        }
                                    },
                                    "allowFrontLineChange": {
                                        "type": "boolean",
                                        "format": "bool"
                                    }
                                },
                                "required": [
                                    "ids",
                                    "allowFrontLineChange"
                                ]
                            },
                            "knowledgeBase": {
                                "type": "object",
                                "properties": {
                                    "ids": {
                                        "items": {
                                            "type": "number",
                                            "format": "long"
                                        }
                                    },
                                    "allowFrontLineChange": {
                                        "type": "boolean",
                                        "format": "bool"
                                    }
                                },
                                "required": [
                                    "ids",
                                    "allowFrontLineChange"
                                ]
                            },
                            "version": {
                                "type": "string",
                                "format": "str"
                            },
                            "intention": {
                                "type": "object",
                                "properties": {
                                    "elementList": {
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "tag": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "description": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "jupiterRank": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "tagPrompt": {
                                                    "type": "string",
                                                    "format": "str"
                                                }
                                            },
                                            "required": [
                                                "tag",
                                                "description",
                                                "jupiterRank",
                                                "tagPrompt"
                                            ]
                                        }
                                    },
                                    "llmName": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "systemPrompt": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "allowFrontLineChange": {
                                        "type": "boolean",
                                        "format": "bool"
                                    }
                                },
                                "required": [
                                    "elementList",
                                    "llmName",
                                    "systemPrompt",
                                    "allowFrontLineChange"
                                ]
                            }
                        },
                        "required": [
                            "id",
                            "name",
                            "description",
                            "agentChannel",
                            "bizId",
                            "objectType",
                            "status",
                            "auth",
                            "placeholder",
                            "groupId",
                            "prologue",
                            "llmName",
                            "llmAppId",
                            "type",
                            "sceneName",
                            "systemPrompt",
                            "skill",
                            "knowledgeBase",
                            "version",
                            "intention"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "const": 0
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "const": null
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/db5d6499-286f-4c4f-bf8e-29e917580809"
            },
            {
                "id": "4cf1ac71-ce90-4ff4-81a7-ac767b6d0951",
                "commitId": "e45de99b-25c9-486a-aedf-66a22daf110a",
                "title": "查询 agent",
                "description": "https://km.sankuai.com/collabpage/2706582482#id-3.4%20%E6%8E%A5%E5%8F%A3%E4%BF%A1%E6%81%AF",
                "request": {
                    "template": "/xianfu/api-v2/dove/agent/query",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "bizId": {
                                "type": "integer"
                            },
                            "channel": {
                                "type": "string",
                                "format": "str"
                            },
                            "owner": {
                                "type": "integer"
                            },
                            "status": {
                                "items": {
                                    "type": "integer"
                                }
                            },
                            "createTimeMin": {
                                "type": "number",
                                "format": "long"
                            },
                            "createTimeMax": {
                                "type": "number",
                                "format": "long"
                            },
                            "page": {
                                "type": "integer"
                            },
                            "pageSize": {
                                "type": "integer"
                            },
                            "type": {
                                "type": "integer"
                            },
                            "sceneName": {
                                "type": "string",
                                "format": "str"
                            },
                            "version": {
                                "type": "string",
                                "format": "str"
                            },
                            "groupId": {
                                "type": "number",
                                "format": "long"
                            },
                            "newestVersion": {
                                "type": "integer"
                            }
                        },
                        "required": [
                            "bizId",
                            "channel",
                            "owner",
                            "status",
                            "createTimeMin",
                            "createTimeMax",
                            "page",
                            "pageSize",
                            "type",
                            "sceneName",
                            "version",
                            "groupId",
                            "newestVersion"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "const": 0
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "type": "object",
                                    "properties": {
                                        "page": {
                                            "type": "integer"
                                        },
                                        "pageSize": {
                                            "type": "integer"
                                        },
                                        "total": {
                                            "type": "integer"
                                        },
                                        "data": {
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "id": {
                                                        "type": "integer"
                                                    },
                                                    "name": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "description": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "agentChannel": {
                                                        "items": {
                                                            "type": "object",
                                                            "properties": {
                                                                "type": {
                                                                    "type": "integer"
                                                                },
                                                                "jupiterTenantId": {
                                                                    "type": "string",
                                                                    "format": "str"
                                                                },
                                                                "botId": {
                                                                    "type": "string",
                                                                    "format": "str"
                                                                },
                                                                "timbreId": {
                                                                    "type": "string",
                                                                    "format": "str"
                                                                }
                                                            },
                                                            "required": [
                                                                "type",
                                                                "jupiterTenantId",
                                                                "botId",
                                                                "timbreId"
                                                            ]
                                                        }
                                                    },
                                                    "bizId": {
                                                        "type": "integer"
                                                    },
                                                    "objectType": {
                                                        "type": "integer"
                                                    },
                                                    "status": {
                                                        "type": "integer"
                                                    },
                                                    "auth": {
                                                        "type": "object",
                                                        "properties": {
                                                            "type": {
                                                                "type": "integer"
                                                            },
                                                            "org": {
                                                                "items": {
                                                                    "type": "integer"
                                                                }
                                                            }
                                                        },
                                                        "required": [
                                                            "type",
                                                            "org"
                                                        ]
                                                    },
                                                    "placeholder": {
                                                        "items": {
                                                            "type": "string",
                                                            "format": "str"
                                                        }
                                                    },
                                                    "owner": {
                                                        "type": "object",
                                                        "properties": {
                                                            "mis": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "name": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "id": {
                                                                "type": "integer"
                                                            }
                                                        },
                                                        "required": [
                                                            "mis",
                                                            "name",
                                                            "id"
                                                        ]
                                                    },
                                                    "lastOperator": {
                                                        "type": "object",
                                                        "properties": {
                                                            "mis": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "name": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "id": {
                                                                "type": "integer"
                                                            }
                                                        },
                                                        "required": [
                                                            "mis",
                                                            "name",
                                                            "id"
                                                        ]
                                                    },
                                                    "createTime": {
                                                        "type": "number",
                                                        "format": "long"
                                                    },
                                                    "updateTime": {
                                                        "type": "number",
                                                        "format": "long"
                                                    },
                                                    "groupId": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "prologue": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "llmName": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "llmAppId": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "type": {
                                                        "type": "integer"
                                                    },
                                                    "sceneName": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "systemPrompt": {
                                                        "type": "object",
                                                        "properties": {
                                                            "prompt": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "inputParams": {
                                                                "items": {
                                                                    "type": "object",
                                                                    "properties": {
                                                                        "name": {
                                                                            "type": "string",
                                                                            "format": "str"
                                                                        },
                                                                        "content": {
                                                                            "type": "string",
                                                                            "format": "str"
                                                                        },
                                                                        "allowFrontLineChange": {
                                                                            "type": "boolean",
                                                                            "format": "bool"
                                                                        },
                                                                        "requiredFrontLineChange": {
                                                                            "type": "boolean",
                                                                            "format": "bool"
                                                                        }
                                                                    },
                                                                    "required": [
                                                                        "name",
                                                                        "content",
                                                                        "allowFrontLineChange",
                                                                        "requiredFrontLineChange"
                                                                    ]
                                                                }
                                                            }
                                                        },
                                                        "required": [
                                                            "prompt",
                                                            "inputParams"
                                                        ]
                                                    },
                                                    "skill": {
                                                        "type": "object",
                                                        "properties": {
                                                            "ids": {
                                                                "items": {
                                                                    "type": "number",
                                                                    "format": "long"
                                                                }
                                                            },
                                                            "allowFrontLineChange": {
                                                                "type": "boolean",
                                                                "format": "bool"
                                                            }
                                                        },
                                                        "required": [
                                                            "ids",
                                                            "allowFrontLineChange"
                                                        ]
                                                    },
                                                    "knowledgeBase": {
                                                        "type": "object",
                                                        "properties": {
                                                            "ids": {
                                                                "items": {
                                                                    "type": "number",
                                                                    "format": "long"
                                                                }
                                                            },
                                                            "allowFrontLineChange": {
                                                                "type": "boolean",
                                                                "format": "bool"
                                                            }
                                                        },
                                                        "required": [
                                                            "ids",
                                                            "allowFrontLineChange"
                                                        ]
                                                    },
                                                    "version": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "intention": {
                                                        "type": "object",
                                                        "properties": {
                                                            "elementList": {
                                                                "items": {
                                                                    "type": "object",
                                                                    "properties": {
                                                                        "tag": {
                                                                            "type": "string",
                                                                            "format": "str"
                                                                        },
                                                                        "description": {
                                                                            "type": "string",
                                                                            "format": "str"
                                                                        },
                                                                        "jupiterRank": {
                                                                            "type": "string",
                                                                            "format": "str"
                                                                        },
                                                                        "tagPrompt": {
                                                                            "type": "string",
                                                                            "format": "str"
                                                                        }
                                                                    },
                                                                    "required": [
                                                                        "tag",
                                                                        "description",
                                                                        "jupiterRank",
                                                                        "tagPrompt"
                                                                    ]
                                                                }
                                                            },
                                                            "llmName": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "systemPrompt": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "allowFrontLineChange": {
                                                                "type": "boolean",
                                                                "format": "bool"
                                                            }
                                                        },
                                                        "required": [
                                                            "elementList",
                                                            "llmName",
                                                            "systemPrompt",
                                                            "allowFrontLineChange"
                                                        ]
                                                    }
                                                },
                                                "required": [
                                                    "id",
                                                    "name",
                                                    "description",
                                                    "agentChannel",
                                                    "bizId",
                                                    "objectType",
                                                    "status",
                                                    "auth",
                                                    "placeholder",
                                                    "owner",
                                                    "lastOperator",
                                                    "createTime",
                                                    "updateTime",
                                                    "groupId",
                                                    "prologue",
                                                    "llmName",
                                                    "llmAppId",
                                                    "type",
                                                    "sceneName",
                                                    "systemPrompt",
                                                    "skill",
                                                    "knowledgeBase",
                                                    "version",
                                                    "intention"
                                                ]
                                            }
                                        }
                                    },
                                    "required": [
                                        "page",
                                        "pageSize",
                                        "total",
                                        "data"
                                    ]
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/4cf1ac71-ce90-4ff4-81a7-ac767b6d0951"
            },
            {
                "id": "0e5992d6-6202-4532-b469-1c5fa4aa7cac",
                "commitId": "c5398b15-e729-484b-9db3-fe4e43c9bb53",
                "title": "新建技能",
                "description": "https://km.sankuai.com/collabpage/2706582482#id-3.4%20%E6%8E%A5%E5%8F%A3%E4%BF%A1%E6%81%AF",
                "request": {
                    "template": "/xianfu/api-v2/dove/agent/skill/create",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "type": {
                                "type": "integer"
                            },
                            "skillConfig": {
                                "type": "object",
                                "properties": {
                                    "name": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "description": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "apiType": {
                                        "type": "integer"
                                    },
                                    "params": {
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "name": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "type": {
                                                    "type": "integer"
                                                },
                                                "description": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "required": {
                                                    "type": "integer"
                                                },
                                                "defaultValue": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "enumValues": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "children": {
                                                    "items": {
                                                        "type": "object",
                                                        "properties": {}
                                                    }
                                                }
                                            },
                                            "required": [
                                                "name",
                                                "type",
                                                "description",
                                                "required",
                                                "defaultValue",
                                                "enumValues",
                                                "children"
                                            ]
                                        }
                                    },
                                    "timeout": {
                                        "type": "integer"
                                    },
                                    "result": {
                                        "type": "array",
                                        "items": [
                                            {
                                                "type": "object",
                                                "properties": {
                                                    "name": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "type": {
                                                        "type": "integer"
                                                    },
                                                    "description": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "children": {
                                                        "items": {
                                                            "type": "object",
                                                            "properties": {}
                                                        }
                                                    }
                                                },
                                                "required": [
                                                    "name",
                                                    "type",
                                                    "description",
                                                    "children"
                                                ]
                                            }
                                        ],
                                        "minItems": 1,
                                        "maxItems": 1
                                    },
                                    "url": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "method": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "headers": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "appkey": {
                                        "type": "string",
                                        "format": "str"
                                    }
                                },
                                "required": [
                                    "name",
                                    "description",
                                    "apiType",
                                    "params",
                                    "timeout",
                                    "result",
                                    "url",
                                    "method",
                                    "headers",
                                    "appkey"
                                ]
                            }
                        },
                        "required": [
                            "type",
                            "skillConfig"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "const": null
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/0e5992d6-6202-4532-b469-1c5fa4aa7cac"
            },
            {
                "id": "f14a5a2f-1594-426d-8252-216033eef460",
                "commitId": "26e30f28-60d2-40f8-ad66-8de6c2880a0d",
                "title": "删除/停用技能",
                "description": "https://km.sankuai.com/collabpage/2706582482#id-3.4%20%E6%8E%A5%E5%8F%A3%E4%BF%A1%E6%81%AF",
                "request": {
                    "template": "/xianfu/api-v2/dove/agent/skill/delete",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "id": {
                                "type": "integer"
                            }
                        },
                        "required": [
                            "id"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "const": null
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/f14a5a2f-1594-426d-8252-216033eef460"
            },
            {
                "id": "56aeb516-e4fc-4630-a9f0-39820f4c59c8",
                "commitId": "764d4078-8899-4012-8282-103a8a411092",
                "title": "编辑技能",
                "description": "https://km.sankuai.com/collabpage/2706582482#id-3.4%20%E6%8E%A5%E5%8F%A3%E4%BF%A1%E6%81%AF",
                "request": {
                    "template": "/xianfu/api-v2/dove/agent/skill/edit",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "id": {
                                "type": "integer"
                            },
                            "type": {
                                "type": "integer"
                            },
                            "skillConfig": {
                                "type": "object",
                                "properties": {
                                    "name": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "description": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "apiType": {
                                        "type": "integer"
                                    },
                                    "params": {
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "name": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "type": {
                                                    "type": "integer"
                                                },
                                                "description": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "required": {
                                                    "type": "integer"
                                                },
                                                "defaultValue": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "enumValues": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "children": {
                                                    "items": {
                                                        "type": "object",
                                                        "properties": {}
                                                    }
                                                }
                                            },
                                            "required": [
                                                "name",
                                                "type",
                                                "description",
                                                "required",
                                                "defaultValue",
                                                "enumValues",
                                                "children"
                                            ]
                                        }
                                    },
                                    "timeout": {
                                        "type": "integer"
                                    },
                                    "result": {
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "name": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "type": {
                                                    "type": "integer"
                                                },
                                                "description": {
                                                    "type": "string",
                                                    "format": "str"
                                                },
                                                "children": {
                                                    "items": {
                                                        "type": "object",
                                                        "properties": {}
                                                    }
                                                }
                                            },
                                            "required": [
                                                "name",
                                                "type",
                                                "description",
                                                "children"
                                            ]
                                        }
                                    },
                                    "url": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "method": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "headers": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "appkey": {
                                        "type": "string",
                                        "format": "str"
                                    }
                                },
                                "required": [
                                    "name",
                                    "description",
                                    "apiType",
                                    "params",
                                    "timeout",
                                    "result",
                                    "url",
                                    "method",
                                    "headers",
                                    "appkey"
                                ]
                            },
                            "status": {
                                "type": "integer"
                            }
                        },
                        "required": [
                            "id",
                            "type",
                            "skillConfig",
                            "status"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "const": null
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/56aeb516-e4fc-4630-a9f0-39820f4c59c8"
            },
            {
                "id": "bf9cdcd6-6ec9-43ef-860b-657d582b9398",
                "commitId": "f3722d43-2f46-4e13-8f65-a7afdb600eb0",
                "title": "查询技能",
                "description": "https://km.sankuai.com/collabpage/2706582482#id-3.4%20%E6%8E%A5%E5%8F%A3%E4%BF%A1%E6%81%AF",
                "request": {
                    "template": "/xianfu/api-v2/dove/agent/skill/query",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "type": {
                                "type": "integer"
                            },
                            "name": {
                                "type": "string",
                                "format": "str"
                            },
                            "status": {
                                "type": "integer"
                            },
                            "page": {
                                "type": "integer"
                            },
                            "pageSize": {
                                "type": "integer"
                            }
                        },
                        "required": [
                            "type",
                            "name",
                            "status",
                            "page",
                            "pageSize"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "type": "object",
                                    "properties": {
                                        "page": {
                                            "type": "integer"
                                        },
                                        "pageSize": {
                                            "type": "integer"
                                        },
                                        "total": {
                                            "type": "integer"
                                        },
                                        "data": {
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "id": {
                                                        "type": "integer"
                                                    },
                                                    "type": {
                                                        "type": "integer"
                                                    },
                                                    "skillConfig": {
                                                        "type": "object",
                                                        "properties": {
                                                            "name": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "description": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "apiType": {
                                                                "type": "integer"
                                                            },
                                                            "params": {
                                                                "items": {
                                                                    "type": "object",
                                                                    "properties": {
                                                                        "name": {
                                                                            "type": "string",
                                                                            "format": "str"
                                                                        },
                                                                        "type": {
                                                                            "type": "integer"
                                                                        },
                                                                        "description": {
                                                                            "type": "string",
                                                                            "format": "str"
                                                                        },
                                                                        "required": {
                                                                            "type": "integer"
                                                                        },
                                                                        "defaultValue": {
                                                                            "type": "string",
                                                                            "format": "str"
                                                                        },
                                                                        "enumValues": {
                                                                            "type": "string",
                                                                            "format": "str"
                                                                        },
                                                                        "children": {
                                                                            "items": {
                                                                                "type": "object",
                                                                                "properties": {}
                                                                            }
                                                                        }
                                                                    },
                                                                    "required": [
                                                                        "name",
                                                                        "type",
                                                                        "description",
                                                                        "required",
                                                                        "defaultValue",
                                                                        "enumValues",
                                                                        "children"
                                                                    ]
                                                                }
                                                            },
                                                            "timeout": {
                                                                "type": "integer"
                                                            },
                                                            "result": {
                                                                "items": {
                                                                    "type": "object",
                                                                    "properties": {
                                                                        "name": {
                                                                            "type": "string",
                                                                            "format": "str"
                                                                        },
                                                                        "type": {
                                                                            "type": "integer"
                                                                        },
                                                                        "description": {
                                                                            "type": "string",
                                                                            "format": "str"
                                                                        },
                                                                        "children": {
                                                                            "items": {
                                                                                "type": "object",
                                                                                "properties": {}
                                                                            }
                                                                        }
                                                                    },
                                                                    "required": [
                                                                        "name",
                                                                        "type",
                                                                        "description",
                                                                        "children"
                                                                    ]
                                                                }
                                                            },
                                                            "url": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "method": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "headers": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "appkey": {
                                                                "type": "string",
                                                                "format": "str"
                                                            }
                                                        },
                                                        "required": [
                                                            "name",
                                                            "description",
                                                            "apiType",
                                                            "params",
                                                            "timeout",
                                                            "result",
                                                            "url",
                                                            "method",
                                                            "headers",
                                                            "appkey"
                                                        ]
                                                    },
                                                    "refCount": {
                                                        "type": "integer"
                                                    },
                                                    "owner": {
                                                        "type": "object",
                                                        "properties": {
                                                            "id": {
                                                                "type": "integer"
                                                            },
                                                            "mis": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "name": {
                                                                "type": "string",
                                                                "format": "str"
                                                            }
                                                        },
                                                        "required": [
                                                            "id",
                                                            "mis",
                                                            "name"
                                                        ]
                                                    },
                                                    "lastOperator": {
                                                        "type": "object",
                                                        "properties": {
                                                            "id": {
                                                                "type": "integer"
                                                            },
                                                            "mis": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "name": {
                                                                "type": "string",
                                                                "format": "str"
                                                            }
                                                        },
                                                        "required": [
                                                            "id",
                                                            "mis",
                                                            "name"
                                                        ]
                                                    },
                                                    "status": {
                                                        "type": "integer"
                                                    },
                                                    "createTime": {
                                                        "type": "number",
                                                        "format": "long"
                                                    },
                                                    "updateTime": {
                                                        "type": "number",
                                                        "format": "long"
                                                    }
                                                },
                                                "required": [
                                                    "id",
                                                    "type",
                                                    "skillConfig",
                                                    "refCount",
                                                    "owner",
                                                    "lastOperator",
                                                    "status",
                                                    "createTime",
                                                    "updateTime"
                                                ]
                                            }
                                        }
                                    },
                                    "required": [
                                        "page",
                                        "pageSize",
                                        "total",
                                        "data"
                                    ]
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/bf9cdcd6-6ec9-43ef-860b-657d582b9398"
            },
            {
                "id": "d2dae4d7-0917-42fc-9ce6-4ed6103f5880",
                "commitId": "1cca9d1d-8b7e-4711-9efc-c2a16520cf5c",
                "title": "外呼给我试试",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/call/ai/try",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "tel": {
                                "type": "string",
                                "format": "str"
                            },
                            "agentId": {
                                "type": "string",
                                "format": "str"
                            },
                            "placeholder": {
                                "type": "object",
                                "properties": {}
                            }
                        },
                        "required": [
                            "tel",
                            "agentId",
                            "placeholder"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "const": 0
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "const": null
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/d2dae4d7-0917-42fc-9ce6-4ed6103f5880"
            },
            {
                "id": "2ff6adc5-a967-4ac7-808e-2c0950bab6a7",
                "commitId": "0fdc7bad-c5ba-4bc2-a741-22c9843ac625",
                "title": "LLM选择项查询",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/chat/llm/query",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "tenantId": {
                                "type": "string",
                                "format": "str"
                            }
                        },
                        "required": [
                            "tenantId"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "name": {
                                                "type": "string",
                                                "format": "str"
                                            },
                                            "description": {
                                                "type": "string",
                                                "format": "str"
                                            }
                                        },
                                        "required": [
                                            "name",
                                            "description"
                                        ]
                                    }
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/2ff6adc5-a967-4ac7-808e-2c0950bab6a7"
            },
            {
                "id": "b879950f-7613-4c29-a97c-30c2979a5e44",
                "commitId": "e1901b4b-3640-45a8-8b4f-9cc13ff88487",
                "title": "知识库列表查询",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/chat/rag/kb/query",
                    "method": "GET",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "id": {
                                                "type": "number",
                                                "format": "long",
                                                "description": " 知识库Id"
                                            },
                                            "name": {
                                                "type": "string",
                                                "format": "str",
                                                "description": " 知识库名称"
                                            }
                                        },
                                        "required": [
                                            "id",
                                            "name"
                                        ]
                                    }
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/b879950f-7613-4c29-a97c-30c2979a5e44"
            },
            {
                "id": "7b64a983-10f9-493e-997d-e336b438f621",
                "commitId": "b326bdb9-20d8-4248-8761-dff00ec3b4a3",
                "title": "创建文本测试",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/chat/try/create",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "agentId": {
                                "type": "integer"
                            },
                            "dynamicParams": {
                                "type": "object",
                                "properties": {
                                    "动态参数1": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "动态参数2": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "动态参数n": {
                                        "type": "string",
                                        "format": "str"
                                    }
                                },
                                "required": [
                                    "动态参数1",
                                    "动态参数2",
                                    "动态参数n"
                                ]
                            },
                            "templateParams": {
                                "type": "object",
                                "properties": {
                                    "模板参数1": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "模板参数2": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "模板参数n": {
                                        "type": "string",
                                        "format": "str"
                                    }
                                },
                                "required": [
                                    "模板参数1",
                                    "模板参数2",
                                    "模板参数n"
                                ]
                            }
                        },
                        "required": [
                            "agentId",
                            "dynamicParams",
                            "templateParams"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "type": "object",
                                    "properties": {
                                        "conversationId": {
                                            "type": "number",
                                            "format": "long"
                                        },
                                        "messages": {
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "type": {
                                                        "type": "integer"
                                                    },
                                                    "content": {
                                                        "type": "string",
                                                        "format": "str"
                                                    }
                                                },
                                                "required": [
                                                    "type",
                                                    "content"
                                                ]
                                            }
                                        },
                                        "tools": {
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "name": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "description": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "request": {
                                                        "type": "object",
                                                        "properties": {}
                                                    },
                                                    "response": {
                                                        "type": "string",
                                                        "format": "str"
                                                    }
                                                },
                                                "required": [
                                                    "name",
                                                    "description",
                                                    "request",
                                                    "response"
                                                ]
                                            }
                                        },
                                        "knowledge": {
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "name": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "recall": {
                                                        "items": {
                                                            "type": "string",
                                                            "format": "str"
                                                        }
                                                    }
                                                },
                                                "required": [
                                                    "name",
                                                    "recall"
                                                ]
                                            }
                                        }
                                    },
                                    "required": [
                                        "conversationId",
                                        "messages",
                                        "tools",
                                        "knowledge"
                                    ]
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/7b64a983-10f9-493e-997d-e336b438f621"
            },
            {
                "id": "6a9ab8ff-cafe-4d2f-9480-119e8af480d4",
                "commitId": "d7d00a95-f3ec-41e9-a829-b429363a47bc",
                "title": "发送文本测试消息",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/chat/try/interact",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "conversationId": {
                                "type": "number",
                                "format": "long"
                            },
                            "messages": {
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "type": {
                                            "type": "integer"
                                        },
                                        "content": {
                                            "type": "string",
                                            "format": "str"
                                        }
                                    },
                                    "required": [
                                        "type",
                                        "content"
                                    ]
                                }
                            }
                        },
                        "required": [
                            "conversationId",
                            "messages"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "type": "object",
                                    "properties": {
                                        "conversationId": {
                                            "type": "number",
                                            "format": "long"
                                        },
                                        "messages": {
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "type": {
                                                        "type": "integer"
                                                    },
                                                    "content": {
                                                        "type": "string",
                                                        "format": "str"
                                                    }
                                                },
                                                "required": [
                                                    "type",
                                                    "content"
                                                ]
                                            }
                                        },
                                        "tools": {
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "name": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "description": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "request": {
                                                        "type": "object",
                                                        "properties": {}
                                                    },
                                                    "response": {
                                                        "type": "string",
                                                        "format": "str"
                                                    }
                                                },
                                                "required": [
                                                    "name",
                                                    "description",
                                                    "request",
                                                    "response"
                                                ]
                                            }
                                        },
                                        "knowledge": {
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "name": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "recall": {
                                                        "items": {
                                                            "type": "string",
                                                            "format": "str"
                                                        }
                                                    }
                                                },
                                                "required": [
                                                    "name",
                                                    "recall"
                                                ]
                                            }
                                        }
                                    },
                                    "required": [
                                        "conversationId",
                                        "messages",
                                        "tools",
                                        "knowledge"
                                    ]
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/6a9ab8ff-cafe-4d2f-9480-119e8af480d4"
            },
            {
                "id": "332b2da4-d0e2-4b54-b64d-adadc39222a2",
                "commitId": "fc5a3f02-fa36-40e5-8db2-6610fa4fcc49",
                "title": "音色选择项查询",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/chat/voice/query",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "tenantId": {
                                "type": "string",
                                "format": "str"
                            }
                        },
                        "required": [
                            "tenantId"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "id": {
                                                "type": "string",
                                                "format": "str",
                                                "description": " 声音Id"
                                            },
                                            "name": {
                                                "type": "string",
                                                "format": "str",
                                                "description": " 声音名称"
                                            },
                                            "gender": {
                                                "type": "string",
                                                "format": "str",
                                                "description": " 性别"
                                            },
                                            "description": {
                                                "type": "string",
                                                "format": "str"
                                            }
                                        },
                                        "required": [
                                            "id",
                                            "name",
                                            "gender",
                                            "description"
                                        ]
                                    }
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/332b2da4-d0e2-4b54-b64d-adadc39222a2"
            },
            {
                "id": "297888bf-4298-497e-9da6-e1f71004dc56",
                "commitId": "33635f0a-1c3e-43f9-98e9-4288c3114095",
                "title": "任务明细导出",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/data/export",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "contactIdList": {
                                "items": {
                                    "type": "string",
                                    "format": "str"
                                }
                            }
                        },
                        "required": [
                            "contactIdList"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "const": null
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/297888bf-4298-497e-9da6-e1f71004dc56"
            },
            {
                "id": "957700b8-787a-4356-a2f4-c61fa8191022",
                "commitId": "97378af4-19e0-4791-8587-71e4b84e0585",
                "title": "沟通记录分页查询",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/data/query",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "bizId": {
                                "items": {
                                    "type": "integer"
                                }
                            },
                            "orgId": {
                                "items": {
                                    "type": "integer"
                                }
                            },
                            "taskId": {
                                "items": {
                                    "type": "number",
                                    "format": "long"
                                }
                            },
                            "staffId": {
                                "type": "integer"
                            },
                            "contactObjectType": {
                                "items": {
                                    "type": "integer"
                                }
                            },
                            "submitTimeMin": {
                                "type": "number",
                                "format": "long"
                            },
                            "submitTimeMax": {
                                "type": "number",
                                "format": "long"
                            },
                            "contactType": {
                                "items": {
                                    "type": "integer"
                                }
                            },
                            "reachStatus": {
                                "items": {
                                    "type": "integer"
                                }
                            },
                            "startTimeMin": {
                                "type": "number",
                                "format": "long"
                            },
                            "startTimeMax": {
                                "type": "number",
                                "format": "long"
                            },
                            "executionStatus": {
                                "items": {
                                    "type": "integer"
                                }
                            },
                            "rank": {
                                "items": {
                                    "type": "string",
                                    "format": "str"
                                }
                            },
                            "page": {
                                "type": "integer"
                            },
                            "pageSize": {
                                "type": "integer"
                            }
                        },
                        "required": [
                            "bizId",
                            "orgId",
                            "taskId",
                            "staffId",
                            "contactObjectType",
                            "submitTimeMin",
                            "submitTimeMax",
                            "contactType",
                            "reachStatus",
                            "startTimeMin",
                            "startTimeMax",
                            "executionStatus",
                            "rank",
                            "page",
                            "pageSize"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "const": 0
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "type": "object",
                                    "properties": {
                                        "page": {
                                            "type": "integer"
                                        },
                                        "pageSize": {
                                            "type": "integer"
                                        },
                                        "total": {
                                            "type": "number",
                                            "format": "long"
                                        },
                                        "data": {
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "contactId": {
                                                        "type": "number",
                                                        "format": "long"
                                                    },
                                                    "objectName": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "objectType": {
                                                        "type": "integer"
                                                    },
                                                    "objectTypeName": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "contactType": {
                                                        "type": "integer"
                                                    },
                                                    "bizId": {
                                                        "type": "integer"
                                                    },
                                                    "submitTime": {
                                                        "type": "integer"
                                                    },
                                                    "startTime": {
                                                        "type": "integer"
                                                    },
                                                    "talkingTimeLen": {
                                                        "type": "integer"
                                                    },
                                                    "rank": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "rankDesc": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "audio": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "releaseReasonMsg": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "staff": {
                                                        "type": "object",
                                                        "properties": {
                                                            "mis": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "name": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "id": {
                                                                "type": "integer"
                                                            }
                                                        },
                                                        "required": [
                                                            "mis",
                                                            "name",
                                                            "id"
                                                        ]
                                                    },
                                                    "reachStatus": {
                                                        "type": "integer"
                                                    },
                                                    "taskId": {
                                                        "type": "number",
                                                        "format": "long"
                                                    },
                                                    "taskName": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "contactContent": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "executionStatus": {
                                                        "type": "integer"
                                                    },
                                                    "skillExecutionList": {
                                                        "items": {
                                                            "type": "object",
                                                            "properties": {
                                                                "skillName": {
                                                                    "type": "string",
                                                                    "format": "str"
                                                                },
                                                                "executed": {
                                                                    "type": "boolean",
                                                                    "format": "bool"
                                                                }
                                                            },
                                                            "required": [
                                                                "skillName",
                                                                "executed"
                                                            ]
                                                        }
                                                    }
                                                },
                                                "required": [
                                                    "contactId",
                                                    "objectName",
                                                    "objectType",
                                                    "objectTypeName",
                                                    "contactType",
                                                    "bizId",
                                                    "submitTime",
                                                    "startTime",
                                                    "talkingTimeLen",
                                                    "rank",
                                                    "rankDesc",
                                                    "audio",
                                                    "releaseReasonMsg",
                                                    "staff",
                                                    "reachStatus",
                                                    "taskId",
                                                    "taskName",
                                                    "contactContent",
                                                    "executionStatus",
                                                    "skillExecutionList"
                                                ]
                                            }
                                        }
                                    },
                                    "required": [
                                        "page",
                                        "pageSize",
                                        "total",
                                        "data"
                                    ]
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/957700b8-787a-4356-a2f4-c61fa8191022"
            },
            {
                "id": "47a5a0e7-51a9-46d2-aef1-e85f55bc1299",
                "commitId": "c1a37a0b-e74e-4a71-98d7-3e1feaf7e867",
                "title": "触达对象类型信息查询",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/object/type/query",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {}
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "const": 0
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "type": "object",
                                    "properties": {
                                        "page": {
                                            "type": "integer"
                                        },
                                        "pageSize": {
                                            "type": "integer"
                                        },
                                        "total": {
                                            "type": "integer"
                                        },
                                        "data": {
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "objectType": {
                                                        "type": "integer"
                                                    },
                                                    "name": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "kpType": {
                                                        "items": {
                                                            "type": "string",
                                                            "format": "str"
                                                        }
                                                    },
                                                    "contactType": {
                                                        "items": {
                                                            "type": "integer"
                                                        }
                                                    },
                                                    "contactTag": {
                                                        "items": {
                                                            "type": "integer"
                                                        }
                                                    }
                                                },
                                                "required": [
                                                    "objectType",
                                                    "name",
                                                    "kpType",
                                                    "contactType",
                                                    "contactTag"
                                                ]
                                            }
                                        }
                                    },
                                    "required": [
                                        "page",
                                        "pageSize",
                                        "total",
                                        "data"
                                    ]
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/47a5a0e7-51a9-46d2-aef1-e85f55bc1299"
            },
            {
                "id": "c2e58323-873c-4a3c-99e9-9d839c858aab",
                "commitId": "451f9a8a-fe7f-444e-83fd-89ad9b21899e",
                "title": "启用参数",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/placeholder/active",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "id": {
                                "type": "integer"
                            }
                        },
                        "required": [
                            "id"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "const": 0
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "const": null
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/c2e58323-873c-4a3c-99e9-9d839c858aab"
            },
            {
                "id": "72454569-b73d-4a07-ac9c-d891bed84bf6",
                "commitId": "8984f540-351e-4fd3-ad03-6cb766c8c9d8",
                "title": "停用参数",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/placeholder/inactive",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "id": {
                                "type": "integer"
                            }
                        },
                        "required": [
                            "id"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "const": 0
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "const": null
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/72454569-b73d-4a07-ac9c-d891bed84bf6"
            },
            {
                "id": "a2e4b7f3-c4df-4c08-bc5b-d80f9be0d147",
                "commitId": "9660cc67-8249-4d0c-aa73-366bd6e1eff3",
                "title": "查询参数",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/placeholder/query",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "name": {
                                "type": "string",
                                "format": "str"
                            },
                            "showName": {
                                "type": "string",
                                "format": "str"
                            },
                            "status": {
                                "type": "integer"
                            },
                            "objectType": {
                                "type": "integer"
                            },
                            "page": {
                                "type": "integer"
                            },
                            "pageSize": {
                                "type": "integer"
                            }
                        },
                        "required": [
                            "name",
                            "showName",
                            "status",
                            "objectType",
                            "page",
                            "pageSize"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "const": 0
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "type": "object",
                                    "properties": {
                                        "page": {
                                            "type": "integer"
                                        },
                                        "pageSize": {
                                            "type": "integer"
                                        },
                                        "total": {
                                            "type": "integer"
                                        },
                                        "data": {
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "id": {
                                                        "type": "integer"
                                                    },
                                                    "name": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "showName": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "status": {
                                                        "type": "integer"
                                                    },
                                                    "objectType": {
                                                        "type": "integer"
                                                    }
                                                },
                                                "required": [
                                                    "id",
                                                    "name",
                                                    "showName",
                                                    "status",
                                                    "objectType"
                                                ]
                                            }
                                        }
                                    },
                                    "required": [
                                        "page",
                                        "pageSize",
                                        "total",
                                        "data"
                                    ]
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/a2e4b7f3-c4df-4c08-bc5b-d80f9be0d147"
            },
            {
                "id": "5087987a-671a-432f-881e-05a21654d69b",
                "commitId": "45f41e31-770a-45af-b03e-3ebe33205015",
                "title": "取消任务",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/task/cancel",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "taskIdList": {
                                "items": {
                                    "type": "integer"
                                }
                            }
                        },
                        "required": [
                            "taskIdList"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "const": null
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/5087987a-671a-432f-881e-05a21654d69b"
            },
            {
                "id": "48685611-52e8-4d0b-b6e3-66d04c9c0db9",
                "commitId": "7ed6a350-f567-4db5-8982-31cc80c5d259",
                "title": "创建任务",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/task/create",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "taskName": {
                                "type": "string",
                                "format": "str"
                            },
                            "taskType": {
                                "type": "string",
                                "format": "str"
                            },
                            "bizId": {
                                "type": "integer"
                            },
                            "poiDto": {
                                "type": "object",
                                "properties": {
                                    "contactObjectType": {
                                        "type": "integer"
                                    },
                                    "contactObjectIdList": {
                                        "items": {
                                            "type": "number",
                                            "format": "long"
                                        }
                                    },
                                    "kpPriority": {
                                        "items": {
                                            "type": "string",
                                            "format": "str"
                                        }
                                    },
                                    "contactTag": {
                                        "type": "integer"
                                    }
                                },
                                "required": [
                                    "contactObjectType",
                                    "contactObjectIdList",
                                    "kpPriority",
                                    "contactTag"
                                ]
                            },
                            "channelDto": {
                                "type": "object",
                                "properties": {
                                    "contactType": {
                                        "type": "integer"
                                    },
                                    "agentId": {
                                        "type": "number",
                                        "format": "long"
                                    },
                                    "contactContent": {
                                        "type": "string",
                                        "format": "str"
                                    },
                                    "contactImages": {
                                        "items": {
                                            "type": "string",
                                            "format": "str"
                                        }
                                    },
                                    "massSendTitle": {
                                        "type": "string",
                                        "format": "str"
                                    }
                                },
                                "required": [
                                    "contactType",
                                    "agentId",
                                    "contactContent",
                                    "contactImages",
                                    "massSendTitle"
                                ]
                            },
                            "workStrategyDto": {
                                "type": "object",
                                "properties": {
                                    "type": {
                                        "type": "integer"
                                    },
                                    "startTime": {
                                        "type": "number",
                                        "format": "long"
                                    },
                                    "endTime": {
                                        "type": "number",
                                        "format": "long"
                                    }
                                },
                                "required": [
                                    "type",
                                    "startTime",
                                    "endTime"
                                ]
                            }
                        },
                        "required": [
                            "taskName",
                            "taskType",
                            "bizId",
                            "poiDto",
                            "channelDto",
                            "workStrategyDto"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "type": "integer"
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/48685611-52e8-4d0b-b6e3-66d04c9c0db9"
            },
            {
                "id": "57544336-8793-4876-8ae4-f93d9ce091e3",
                "commitId": "00eb4bd2-838a-4d12-badc-62d279d900fd",
                "title": "搜索任务",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/task/search",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "taskName": {
                                "type": "string",
                                "format": "str"
                            },
                            "creatorUid": {
                                "items": {
                                    "type": "integer"
                                }
                            },
                            "taskType": {
                                "type": "string",
                                "format": "str"
                            },
                            "taskStatus": {
                                "items": {
                                    "type": "integer"
                                }
                            },
                            "taskStartTimeMin": {
                                "type": "number",
                                "format": "long"
                            },
                            "taskStartTimeMax": {
                                "type": "number",
                                "format": "long"
                            },
                            "createTimeMin": {
                                "type": "number",
                                "format": "long"
                            },
                            "createTimeMax": {
                                "type": "number",
                                "format": "long"
                            },
                            "creatorOrgId": {
                                "items": {
                                    "type": "integer"
                                }
                            },
                            "page": {
                                "type": "integer"
                            },
                            "pageSize": {
                                "type": "integer"
                            }
                        },
                        "required": [
                            "taskName",
                            "creatorUid",
                            "taskType",
                            "taskStatus",
                            "taskStartTimeMin",
                            "taskStartTimeMax",
                            "createTimeMin",
                            "createTimeMax",
                            "creatorOrgId",
                            "page",
                            "pageSize"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "const": 0
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "type": "object",
                                    "properties": {
                                        "page": {
                                            "type": "integer"
                                        },
                                        "pageSize": {
                                            "type": "integer"
                                        },
                                        "total": {
                                            "type": "number",
                                            "format": "long"
                                        },
                                        "data": {
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "id": {
                                                        "type": "number",
                                                        "format": "long"
                                                    },
                                                    "bizId": {
                                                        "type": "integer"
                                                    },
                                                    "taskName": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "taskType": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "poiDto": {
                                                        "type": "object",
                                                        "properties": {
                                                            "contactObjectType": {
                                                                "type": "integer"
                                                            },
                                                            "contactObjectIdList": {
                                                                "items": {
                                                                    "type": "number",
                                                                    "format": "long"
                                                                }
                                                            },
                                                            "kpPriority": {
                                                                "items": {
                                                                    "type": "string",
                                                                    "format": "str"
                                                                }
                                                            },
                                                            "contactTag": {
                                                                "type": "integer"
                                                            },
                                                            "contactObjectTypeName": {
                                                                "type": "string",
                                                                "format": "str"
                                                            }
                                                        },
                                                        "required": [
                                                            "contactObjectType",
                                                            "contactObjectIdList",
                                                            "kpPriority",
                                                            "contactTag",
                                                            "contactObjectTypeName"
                                                        ]
                                                    },
                                                    "channelDto": {
                                                        "type": "object",
                                                        "properties": {
                                                            "contactType": {
                                                                "type": "integer"
                                                            },
                                                            "agentId": {
                                                                "type": "number",
                                                                "format": "long"
                                                            },
                                                            "contactContent": {
                                                                "type": "string",
                                                                "format": "str"
                                                            }
                                                        },
                                                        "required": [
                                                            "contactType",
                                                            "agentId",
                                                            "contactContent"
                                                        ]
                                                    },
                                                    "taskWorkStrategy": {
                                                        "type": "object",
                                                        "properties": {
                                                            "type": {
                                                                "type": "integer"
                                                            },
                                                            "startTime": {
                                                                "type": "number",
                                                                "format": "long"
                                                            },
                                                            "endTime": {
                                                                "type": "number",
                                                                "format": "long"
                                                            }
                                                        },
                                                        "required": [
                                                            "type",
                                                            "startTime",
                                                            "endTime"
                                                        ]
                                                    },
                                                    "creator": {
                                                        "type": "object",
                                                        "properties": {
                                                            "mis": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "name": {
                                                                "type": "string",
                                                                "format": "str"
                                                            },
                                                            "id": {
                                                                "type": "integer"
                                                            }
                                                        },
                                                        "required": [
                                                            "mis",
                                                            "name",
                                                            "id"
                                                        ]
                                                    },
                                                    "createTime": {
                                                        "type": "number",
                                                        "format": "long"
                                                    },
                                                    "updateTime": {
                                                        "type": "number",
                                                        "format": "long"
                                                    },
                                                    "statisticDto": {
                                                        "type": "object",
                                                        "properties": {
                                                            "taskProgress": {
                                                                "type": "object",
                                                                "properties": {
                                                                    "waitingCount": {
                                                                        "type": "integer"
                                                                    },
                                                                    "doingCount": {
                                                                        "type": "integer"
                                                                    },
                                                                    "doneCount": {
                                                                        "type": "integer"
                                                                    },
                                                                    "cancelCount": {
                                                                        "type": "integer"
                                                                    }
                                                                },
                                                                "required": [
                                                                    "waitingCount",
                                                                    "doingCount",
                                                                    "doneCount",
                                                                    "cancelCount"
                                                                ]
                                                            },
                                                            "contactSuccessCount": {
                                                                "type": "integer"
                                                            },
                                                            "contactFailCount": {
                                                                "type": "integer"
                                                            },
                                                            "executionAvgTime": {
                                                                "type": "integer"
                                                            },
                                                            "successTag": {
                                                                "items": {
                                                                    "type": "object",
                                                                    "properties": {
                                                                        "tagCode": {
                                                                            "type": "string",
                                                                            "format": "str"
                                                                        },
                                                                        "tagName": {
                                                                            "type": "string",
                                                                            "format": "str"
                                                                        },
                                                                        "count": {
                                                                            "type": "integer"
                                                                        }
                                                                    },
                                                                    "required": [
                                                                        "tagCode",
                                                                        "tagName",
                                                                        "count"
                                                                    ]
                                                                }
                                                            },
                                                            "failTag": {
                                                                "items": {
                                                                    "type": "object",
                                                                    "properties": {
                                                                        "tagName": {
                                                                            "type": "string",
                                                                            "format": "str"
                                                                        },
                                                                        "count": {
                                                                            "type": "integer"
                                                                        }
                                                                    },
                                                                    "required": [
                                                                        "tagName",
                                                                        "count"
                                                                    ]
                                                                }
                                                            }
                                                        },
                                                        "required": [
                                                            "taskProgress",
                                                            "contactSuccessCount",
                                                            "contactFailCount",
                                                            "executionAvgTime",
                                                            "successTag",
                                                            "failTag"
                                                        ]
                                                    }
                                                },
                                                "required": [
                                                    "id",
                                                    "bizId",
                                                    "taskName",
                                                    "taskType",
                                                    "poiDto",
                                                    "channelDto",
                                                    "taskWorkStrategy",
                                                    "creator",
                                                    "createTime",
                                                    "updateTime",
                                                    "statisticDto"
                                                ]
                                            }
                                        }
                                    },
                                    "required": [
                                        "page",
                                        "pageSize",
                                        "total",
                                        "data"
                                    ]
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/57544336-8793-4876-8ae4-f93d9ce091e3"
            },
            {
                "id": "a4def063-2680-4850-ab1a-a75f1da5ba92",
                "commitId": "53740c24-402f-4fe9-bafe-6186391a391e",
                "title": "任务统计",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/task/statistic",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "taskId": {
                                "type": "number",
                                "format": "long"
                            }
                        },
                        "required": [
                            "taskId"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "type": "object",
                                    "properties": {
                                        "taskProgress": {
                                            "type": "object",
                                            "properties": {
                                                "waitingCount": {
                                                    "type": "integer"
                                                },
                                                "doingCount": {
                                                    "type": "integer"
                                                },
                                                "doneCount": {
                                                    "type": "integer"
                                                },
                                                "cancelCount": {
                                                    "type": "integer"
                                                }
                                            },
                                            "required": [
                                                "waitingCount",
                                                "doingCount",
                                                "doneCount",
                                                "cancelCount"
                                            ]
                                        },
                                        "contactSuccessCount": {
                                            "type": "integer"
                                        },
                                        "contactFailCount": {
                                            "type": "integer"
                                        },
                                        "executionAvgTime": {
                                            "type": "integer"
                                        },
                                        "successTag": {
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "tagCode": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "tagName": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "count": {
                                                        "type": "integer"
                                                    }
                                                },
                                                "required": [
                                                    "tagCode",
                                                    "tagName",
                                                    "count"
                                                ]
                                            }
                                        },
                                        "failTag": {
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "tagCode": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "tagName": {
                                                        "type": "string",
                                                        "format": "str"
                                                    },
                                                    "count": {
                                                        "type": "integer"
                                                    }
                                                },
                                                "required": [
                                                    "tagCode",
                                                    "tagName",
                                                    "count"
                                                ]
                                            }
                                        }
                                    },
                                    "required": [
                                        "taskProgress",
                                        "contactSuccessCount",
                                        "contactFailCount",
                                        "executionAvgTime",
                                        "successTag",
                                        "failTag"
                                    ]
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/a4def063-2680-4850-ab1a-a75f1da5ba92"
            },
            {
                "id": "e900611e-251b-45e4-a8e5-544a0c10cb57",
                "commitId": "e540ce35-be77-4fc6-9874-7a79d607419e",
                "title": "任务类型模糊匹配",
                "description": "",
                "request": {
                    "template": "/xianfu/api-v2/dove/task/type/search",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "body": {
                        "type": "object",
                        "properties": {
                            "taskType": {
                                "type": "string",
                                "format": "str"
                            }
                        },
                        "required": [
                            "taskType"
                        ]
                    },
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "type": "object",
                            "properties": {
                                "code": {
                                    "type": "integer"
                                },
                                "msg": {
                                    "type": "string",
                                    "format": "str"
                                },
                                "data": {
                                    "items": {
                                        "type": "string",
                                        "format": "str"
                                    }
                                }
                            },
                            "required": [
                                "code",
                                "msg",
                                "data"
                            ]
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/e900611e-251b-45e4-a8e5-544a0c10cb57"
            },
            {
                "id": "0a19499c-9625-45de-a4ac-e1e4951411f5",
                "commitId": "d343aba8-86b4-4678-968e-113b0d27c5fa",
                "title": "任务外呼对话明细",
                "description": "",
                "request": {
                    "template": "/xianfu/api/dove/data/text",
                    "method": "POST",
                    "headers": [],
                    "paths": [],
                    "queries": [],
                    "bodyType": "Json"
                },
                "responses": [
                    {
                        "title": "默认返回值",
                        "statusCode": 200,
                        "headers": [],
                        "body": {
                            "const": null
                        }
                    }
                ],
                "doc": "http://a.sankuai.com/projects/api/view/0a19499c-9625-45de-a4ac-e1e4951411f5"
            }
        ],
        "declares": {}
    },
    "apiInfo": {
        "5087987a-671a-432f-881e-05a21654d69b": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "5087987a-671a-432f-881e-05a21654d69b",
            "apiName": "postXianfuApiV2DoveTaskCancel",
            "apiTitle": "取消任务",
            "commitId": "45f41e31-770a-45af-b03e-3ebe33205015",
            "fileName": "postXianfuApiV2DoveTaskCancel.ts",
            "typeFileName": "postXianfuApiV2DoveTaskCancel.d.ts",
            "fileSuffix": "ts"
        },
        "48685611-52e8-4d0b-b6e3-66d04c9c0db9": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "48685611-52e8-4d0b-b6e3-66d04c9c0db9",
            "apiName": "postXianfuApiV2DoveTaskCreate",
            "apiTitle": "创建任务",
            "commitId": "7ed6a350-f567-4db5-8982-31cc80c5d259",
            "fileName": "postXianfuApiV2DoveTaskCreate.ts",
            "typeFileName": "postXianfuApiV2DoveTaskCreate.d.ts",
            "fileSuffix": "ts"
        },
        "f96aca3b-96b5-4ba0-8c02-0e73232a1b5d": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "f96aca3b-96b5-4ba0-8c02-0e73232a1b5d",
            "apiName": "postXianfuApiV2DoveAgentCopy",
            "apiTitle": "复制 agent",
            "commitId": "66a186bd-08d9-4dc4-9ae4-5eccb64968cd",
            "fileName": "postXianfuApiV2DoveAgentCopy.ts",
            "typeFileName": "postXianfuApiV2DoveAgentCopy.d.ts",
            "fileSuffix": "ts"
        },
        "a759e45c-37fd-44fa-ab4e-2145992fc084": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "a759e45c-37fd-44fa-ab4e-2145992fc084",
            "apiName": "postXianfuApiV2DoveAgentCreate",
            "apiTitle": "创建 agent",
            "commitId": "de9292b9-cf58-48f2-9b0f-8cb0bfcad87e",
            "fileName": "postXianfuApiV2DoveAgentCreate.ts",
            "typeFileName": "postXianfuApiV2DoveAgentCreate.d.ts",
            "fileSuffix": "ts"
        },
        "7779f443-8265-4795-8397-0351fbe9980c": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "7779f443-8265-4795-8397-0351fbe9980c",
            "apiName": "postXianfuApiV2DoveAgentDelete",
            "apiTitle": "删除 agent",
            "commitId": "81f725b4-1f43-4aea-b610-36fa5a481247",
            "fileName": "postXianfuApiV2DoveAgentDelete.ts",
            "typeFileName": "postXianfuApiV2DoveAgentDelete.d.ts",
            "fileSuffix": "ts"
        },
        "db5d6499-286f-4c4f-bf8e-29e917580809": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "db5d6499-286f-4c4f-bf8e-29e917580809",
            "apiName": "postXianfuApiV2DoveAgentEdit",
            "apiTitle": "编辑 agent",
            "commitId": "03cf518b-284e-4d2d-bdc1-34e80bf2bfc3",
            "fileName": "postXianfuApiV2DoveAgentEdit.ts",
            "typeFileName": "postXianfuApiV2DoveAgentEdit.d.ts",
            "fileSuffix": "ts"
        },
        "4cf1ac71-ce90-4ff4-81a7-ac767b6d0951": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "4cf1ac71-ce90-4ff4-81a7-ac767b6d0951",
            "apiName": "postXianfuApiV2DoveAgentQuery",
            "apiTitle": "查询 agent",
            "commitId": "e45de99b-25c9-486a-aedf-66a22daf110a",
            "fileName": "postXianfuApiV2DoveAgentQuery.ts",
            "typeFileName": "postXianfuApiV2DoveAgentQuery.d.ts",
            "fileSuffix": "ts"
        },
        "0a19499c-9625-45de-a4ac-e1e4951411f5": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "0a19499c-9625-45de-a4ac-e1e4951411f5",
            "apiName": "postXianfuApiDoveDataText",
            "apiTitle": "任务外呼对话明细",
            "commitId": "d343aba8-86b4-4678-968e-113b0d27c5fa",
            "fileName": "postXianfuApiDoveDataText.ts",
            "typeFileName": "postXianfuApiDoveDataText.d.ts",
            "fileSuffix": "ts"
        },
        "47a5a0e7-51a9-46d2-aef1-e85f55bc1299": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "47a5a0e7-51a9-46d2-aef1-e85f55bc1299",
            "apiName": "postXianfuApiV2DoveObjectTypeQuery",
            "apiTitle": "触达对象类型信息查询",
            "commitId": "c1a37a0b-e74e-4a71-98d7-3e1feaf7e867",
            "fileName": "postXianfuApiV2DoveObjectTypeQuery.ts",
            "typeFileName": "postXianfuApiV2DoveObjectTypeQuery.d.ts",
            "fileSuffix": "ts"
        },
        "c2e58323-873c-4a3c-99e9-9d839c858aab": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "c2e58323-873c-4a3c-99e9-9d839c858aab",
            "apiName": "postXianfuApiV2DovePlaceholderActive",
            "apiTitle": "启用参数",
            "commitId": "451f9a8a-fe7f-444e-83fd-89ad9b21899e",
            "fileName": "postXianfuApiV2DovePlaceholderActive.ts",
            "typeFileName": "postXianfuApiV2DovePlaceholderActive.d.ts",
            "fileSuffix": "ts"
        },
        "72454569-b73d-4a07-ac9c-d891bed84bf6": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "72454569-b73d-4a07-ac9c-d891bed84bf6",
            "apiName": "postXianfuApiV2DovePlaceholderInactive",
            "apiTitle": "停用参数",
            "commitId": "8984f540-351e-4fd3-ad03-6cb766c8c9d8",
            "fileName": "postXianfuApiV2DovePlaceholderInactive.ts",
            "typeFileName": "postXianfuApiV2DovePlaceholderInactive.d.ts",
            "fileSuffix": "ts"
        },
        "a2e4b7f3-c4df-4c08-bc5b-d80f9be0d147": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "a2e4b7f3-c4df-4c08-bc5b-d80f9be0d147",
            "apiName": "postXianfuApiV2DovePlaceholderQuery",
            "apiTitle": "查询参数",
            "commitId": "9660cc67-8249-4d0c-aa73-366bd6e1eff3",
            "fileName": "postXianfuApiV2DovePlaceholderQuery.ts",
            "typeFileName": "postXianfuApiV2DovePlaceholderQuery.d.ts",
            "fileSuffix": "ts"
        },
        "297888bf-4298-497e-9da6-e1f71004dc56": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "297888bf-4298-497e-9da6-e1f71004dc56",
            "apiName": "postXianfuApiV2DoveDataExport",
            "apiTitle": "任务明细导出",
            "commitId": "33635f0a-1c3e-43f9-98e9-4288c3114095",
            "fileName": "postXianfuApiV2DoveDataExport.ts",
            "typeFileName": "postXianfuApiV2DoveDataExport.d.ts",
            "fileSuffix": "ts"
        },
        "957700b8-787a-4356-a2f4-c61fa8191022": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "957700b8-787a-4356-a2f4-c61fa8191022",
            "apiName": "postXianfuApiV2DoveDataQuery",
            "apiTitle": "沟通记录分页查询",
            "commitId": "97378af4-19e0-4791-8587-71e4b84e0585",
            "fileName": "postXianfuApiV2DoveDataQuery.ts",
            "typeFileName": "postXianfuApiV2DoveDataQuery.d.ts",
            "fileSuffix": "ts"
        },
        "a4def063-2680-4850-ab1a-a75f1da5ba92": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "a4def063-2680-4850-ab1a-a75f1da5ba92",
            "apiName": "postXianfuApiV2DoveTaskStatistic",
            "apiTitle": "任务统计",
            "commitId": "53740c24-402f-4fe9-bafe-6186391a391e",
            "fileName": "postXianfuApiV2DoveTaskStatistic.ts",
            "typeFileName": "postXianfuApiV2DoveTaskStatistic.d.ts",
            "fileSuffix": "ts"
        },
        "57544336-8793-4876-8ae4-f93d9ce091e3": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "57544336-8793-4876-8ae4-f93d9ce091e3",
            "apiName": "postXianfuApiV2DoveTaskSearch",
            "apiTitle": "搜索任务",
            "commitId": "00eb4bd2-838a-4d12-badc-62d279d900fd",
            "fileName": "postXianfuApiV2DoveTaskSearch.ts",
            "typeFileName": "postXianfuApiV2DoveTaskSearch.d.ts",
            "fileSuffix": "ts"
        },
        "e900611e-251b-45e4-a8e5-544a0c10cb57": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "e900611e-251b-45e4-a8e5-544a0c10cb57",
            "apiName": "postXianfuApiV2DoveTaskTypeSearch",
            "apiTitle": "任务类型模糊匹配",
            "commitId": "e540ce35-be77-4fc6-9874-7a79d607419e",
            "fileName": "postXianfuApiV2DoveTaskTypeSearch.ts",
            "typeFileName": "postXianfuApiV2DoveTaskTypeSearch.d.ts",
            "fileSuffix": "ts"
        },
        "d2dae4d7-0917-42fc-9ce6-4ed6103f5880": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "d2dae4d7-0917-42fc-9ce6-4ed6103f5880",
            "apiName": "postXianfuApiV2DoveCallAiTry",
            "apiTitle": "外呼给我试试",
            "commitId": "1cca9d1d-8b7e-4711-9efc-c2a16520cf5c",
            "fileName": "postXianfuApiV2DoveCallAiTry.ts",
            "typeFileName": "postXianfuApiV2DoveCallAiTry.d.ts",
            "fileSuffix": "ts"
        },
        "0e5992d6-6202-4532-b469-1c5fa4aa7cac": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "0e5992d6-6202-4532-b469-1c5fa4aa7cac",
            "apiName": "postXianfuApiV2DoveAgentSkillCreate",
            "apiTitle": "新建技能",
            "commitId": "c5398b15-e729-484b-9db3-fe4e43c9bb53",
            "fileName": "postXianfuApiV2DoveAgentSkillCreate.ts",
            "typeFileName": "postXianfuApiV2DoveAgentSkillCreate.d.ts",
            "fileSuffix": "ts"
        },
        "f14a5a2f-1594-426d-8252-216033eef460": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "f14a5a2f-1594-426d-8252-216033eef460",
            "apiName": "postXianfuApiV2DoveAgentSkillDelete",
            "apiTitle": "删除/停用技能",
            "commitId": "26e30f28-60d2-40f8-ad66-8de6c2880a0d",
            "fileName": "postXianfuApiV2DoveAgentSkillDelete.ts",
            "typeFileName": "postXianfuApiV2DoveAgentSkillDelete.d.ts",
            "fileSuffix": "ts"
        },
        "56aeb516-e4fc-4630-a9f0-39820f4c59c8": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "56aeb516-e4fc-4630-a9f0-39820f4c59c8",
            "apiName": "postXianfuApiV2DoveAgentSkillEdit",
            "apiTitle": "编辑技能",
            "commitId": "764d4078-8899-4012-8282-103a8a411092",
            "fileName": "postXianfuApiV2DoveAgentSkillEdit.ts",
            "typeFileName": "postXianfuApiV2DoveAgentSkillEdit.d.ts",
            "fileSuffix": "ts"
        },
        "bf9cdcd6-6ec9-43ef-860b-657d582b9398": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "bf9cdcd6-6ec9-43ef-860b-657d582b9398",
            "apiName": "postXianfuApiV2DoveAgentSkillQuery",
            "apiTitle": "查询技能",
            "commitId": "f3722d43-2f46-4e13-8f65-a7afdb600eb0",
            "fileName": "postXianfuApiV2DoveAgentSkillQuery.ts",
            "typeFileName": "postXianfuApiV2DoveAgentSkillQuery.d.ts",
            "fileSuffix": "ts"
        },
        "2ff6adc5-a967-4ac7-808e-2c0950bab6a7": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "2ff6adc5-a967-4ac7-808e-2c0950bab6a7",
            "apiName": "postXianfuApiV2DoveChatLlmQuery",
            "apiTitle": "LLM选择项查询",
            "commitId": "0fdc7bad-c5ba-4bc2-a741-22c9843ac625",
            "fileName": "postXianfuApiV2DoveChatLlmQuery.ts",
            "typeFileName": "postXianfuApiV2DoveChatLlmQuery.d.ts",
            "fileSuffix": "ts"
        },
        "b879950f-7613-4c29-a97c-30c2979a5e44": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "b879950f-7613-4c29-a97c-30c2979a5e44",
            "apiName": "getXianfuApiV2DoveChatRagKbQuery",
            "apiTitle": "知识库列表查询",
            "commitId": "e1901b4b-3640-45a8-8b4f-9cc13ff88487",
            "fileName": "getXianfuApiV2DoveChatRagKbQuery.ts",
            "typeFileName": "getXianfuApiV2DoveChatRagKbQuery.d.ts",
            "fileSuffix": "ts"
        },
        "7b64a983-10f9-493e-997d-e336b438f621": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "7b64a983-10f9-493e-997d-e336b438f621",
            "apiName": "postXianfuApiV2DoveChatTryCreate",
            "apiTitle": "创建文本测试",
            "commitId": "b326bdb9-20d8-4248-8761-dff00ec3b4a3",
            "fileName": "postXianfuApiV2DoveChatTryCreate.ts",
            "typeFileName": "postXianfuApiV2DoveChatTryCreate.d.ts",
            "fileSuffix": "ts"
        },
        "6a9ab8ff-cafe-4d2f-9480-119e8af480d4": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "6a9ab8ff-cafe-4d2f-9480-119e8af480d4",
            "apiName": "postXianfuApiV2DoveChatTryInteract",
            "apiTitle": "发送文本测试消息",
            "commitId": "d7d00a95-f3ec-41e9-a829-b429363a47bc",
            "fileName": "postXianfuApiV2DoveChatTryInteract.ts",
            "typeFileName": "postXianfuApiV2DoveChatTryInteract.d.ts",
            "fileSuffix": "ts"
        },
        "332b2da4-d0e2-4b54-b64d-adadc39222a2": {
            "projectName": "信鸽",
            "fileDir": "./xianfu",
            "apiId": "332b2da4-d0e2-4b54-b64d-adadc39222a2",
            "apiName": "postXianfuApiV2DoveChatVoiceQuery",
            "apiTitle": "音色选择项查询",
            "commitId": "fc5a3f02-fa36-40e5-8db2-6610fa4fcc49",
            "fileName": "postXianfuApiV2DoveChatVoiceQuery.ts",
            "typeFileName": "postXianfuApiV2DoveChatVoiceQuery.d.ts",
            "fileSuffix": "ts"
        }
    }
}
