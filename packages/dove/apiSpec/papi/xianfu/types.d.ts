/**
 * 该文件由 @mfe/cc-api-caller 自动生成
 * powered by @datafe/apihub
 * createtime Wed Apr 16 2025 15:27:56 GMT+0800 (中国标准时间)
 */

export type AutoGeneratedXianfu1744788476360Types = {
    '/xianfu/api-v2/dove/agent/copy': {
        method: 'POST';

        request: {
            id: number;
        };

        response: null;
    };

    '/xianfu/api-v2/dove/agent/create': {
        method: 'POST';

        request: {
            name: string;
            description: string;
            agentChannel: {
                type: number;
                jupiterTenantId: string;
                botId: string;
                routePoint: string;
                timbreId: string;
            }[];
            bizId: number;
            objectType: number;
            status: number;
            auth: {
                type: number;
                org: number[];
            };
            placeholder: string[];
            groupId: string;
            prologue: string;
            llmName: string;
            llmAppId: string;
            type: number;
            sceneName: string;
            systemPrompt: {
                prompt: string;
                inputParams: {
                    name: string;
                    content: string;
                    allowFrontLineChange: boolean;
                    requiredFrontLineChange: boolean;
                }[];
            };
            skill: {
                ids: number[];
                allowFrontLineChange: boolean;
            };
            knowledgeBase: {
                ids: number[];
                allowFrontLineChange: boolean;
            };
            version: string;
            intention: {
                elementList: {
                    tag: string;
                    description: string;
                    jupiterRank: string;
                    tagPrompt: string;
                }[];
                llmName: string;
                systemPrompt: string;
                allowFrontLineChange: boolean;
            };
        };

        response: null;
    };

    '/xianfu/api-v2/dove/agent/delete': {
        method: 'POST';

        request: {
            id: number;
        };

        response: null;
    };

    '/xianfu/api-v2/dove/agent/active': {
        method: 'POST';

        request: {
            id: number;
        };

        response: null;
    };

    '/xianfu/api-v2/dove/agent/edit': {
        method: 'POST';

        request: {
            id: number;
            name: string;
            description: string;
            agentChannel: {
                type: number;
                jupiterTenantId: string;
                botId: string;
                timbreId: string;
            }[];
            bizId: number;
            objectType: number;
            status: number;
            auth: {
                type: number;
                org: number[];
            };
            placeholder: string[];
            groupId: string;
            prologue: string;
            llmName: string;
            llmAppId: string;
            type: number;
            sceneName: string;
            systemPrompt: {
                prompt: string;
                inputParams: {
                    name: string;
                    content: string;
                    allowFrontLineChange: boolean;
                    requiredFrontLineChange: boolean;
                }[];
            };
            skill: {
                ids: number[];
                allowFrontLineChange: boolean;
            };
            knowledgeBase: {
                ids: number[];
                allowFrontLineChange: boolean;
            };
            version: string;
            intention: {
                elementList: {
                    tag: string;
                    description: string;
                    jupiterRank: string;
                    tagPrompt: string;
                }[];
                llmName: string;
                systemPrompt: string;
                allowFrontLineChange: boolean;
            };
        };

        response: null;
    };

    '/xianfu/api-v2/dove/agent/query': {
        method: 'POST';

        request: {
            bizId: number;
            channel: string;
            owner: number;
            status: number[];
            createTimeMin: number;
            createTimeMax: number;
            page: number;
            pageSize: number;
            type: number;
            sceneName: string;
            version: string;
            groupId: number;
            newestVersion: number;
        };

        response: {
            page: number;
            pageSize: number;
            total: number;
            data: {
                id: number;
                name: string;
                description: string;
                agentChannel: {
                    type: number;
                    jupiterTenantId: string;
                    botId: string;
                    timbreId: string;
                }[];
                bizId: number;
                objectType: number;
                status: number;
                auth: {
                    type: number;
                    org: number[];
                };
                placeholder: string[];
                owner: {
                    mis: string;
                    name: string;
                    id: number;
                };
                lastOperator: {
                    mis: string;
                    name: string;
                    id: number;
                };
                createTime: number;
                updateTime: number;
                groupId: string;
                prologue: string;
                llmName: string;
                llmAppId: string;
                type: number;
                sceneName: string;
                systemPrompt: {
                    prompt: string;
                    inputParams: {
                        name: string;
                        content: string;
                        allowFrontLineChange: boolean;
                        requiredFrontLineChange: boolean;
                    }[];
                };
                skill: {
                    ids: number[];
                    allowFrontLineChange: boolean;
                };
                knowledgeBase: {
                    ids: number[];
                    allowFrontLineChange: boolean;
                };
                version: string;
                intention: {
                    elementList: {
                        tag: string;
                        description: string;
                        jupiterRank: string;
                        tagPrompt: string;
                    }[];
                    llmName: string;
                    systemPrompt: string;
                    allowFrontLineChange: boolean;
                };
            }[];
        };
    };

    '/xianfu/api-v2/dove/agent/skill/create': {
        method: 'POST';

        request: {
            type: number;
            skillConfig: {
                name: string;
                description: string;
                apiType: number;
                params: {
                    name: string;
                    type: number;
                    description: string;
                    required: number;
                    defaultValue: string;
                    enumValues: string;
                    children: {}[];
                }[];
                timeout: number;
                result: [
                    {
                        name: string;
                        type: number;
                        description: string;
                        children: {}[];
                    },
                ];
                url: string;
                method: string;
                headers: string;
                appkey: string;
            };
        };

        response: null;
    };

    '/xianfu/api-v2/dove/agent/skill/delete': {
        method: 'POST';

        request: {
            id: number;
        };

        response: null;
    };

    '/xianfu/api-v2/dove/agent/skill/edit': {
        method: 'POST';

        request: {
            id: number;
            type: number;
            skillConfig: {
                name: string;
                description: string;
                apiType: number;
                params: {
                    name: string;
                    type: number;
                    description: string;
                    required: number;
                    defaultValue: string;
                    enumValues: string;
                    children: {}[];
                }[];
                timeout: number;
                result: {
                    name: string;
                    type: number;
                    description: string;
                    children: {}[];
                }[];
                url: string;
                method: string;
                headers: string;
                appkey: string;
            };
            status: number;
        };

        response: null;
    };

    '/xianfu/api-v2/dove/agent/skill/query': {
        method: 'POST';

        request: {
            type: number;
            name: string;
            status: number;
            page: number;
            pageSize: number;
        };

        response: {
            page: number;
            pageSize: number;
            total: number;
            data: {
                id: number;
                type: number;
                skillConfig: {
                    name: string;
                    description: string;
                    apiType: number;
                    params: {
                        name: string;
                        type: number;
                        description: string;
                        required: number;
                        defaultValue: string;
                        enumValues: string;
                        children: {}[];
                    }[];
                    timeout: number;
                    result: {
                        name: string;
                        type: number;
                        description: string;
                        children: {}[];
                    }[];
                    url: string;
                    method: string;
                    headers: string;
                    appkey: string;
                };
                refCount: number;
                owner: {
                    id: number;
                    mis: string;
                    name: string;
                };
                lastOperator: {
                    id: number;
                    mis: string;
                    name: string;
                };
                status: number;
                createTime: number;
                updateTime: number;
            }[];
        };
    };

    '/xianfu/api-v2/dove/call/ai/try': {
        method: 'POST';

        request: {
            tel: string;
            agentId: string;
            placeholder: {};
        };

        response: null;
    };

    '/xianfu/api-v2/dove/chat/llm/query': {
        method: 'POST';

        request: {
            tenantId: string;
        };

        response: {
            name: string;
            description: string;
        }[];
    };

    '/xianfu/api-v2/dove/chat/rag/kb/query': {
        method: 'GET';
        request: Record<string, never>;

        response: {
            /**  知识库Id */
            id: number;
            /**  知识库名称 */
            name: string;
        }[];
    };

    '/xianfu/api-v2/dove/chat/try/create': {
        method: 'POST';

        request: {
            agentId: number;
            dynamicParams: {
                动态参数1: string;
                动态参数2: string;
                动态参数n: string;
            };
            templateParams: {
                模板参数1: string;
                模板参数2: string;
                模板参数n: string;
            };
        };

        response: {
            conversationId: number;
            messages: {
                type: number;
                content: string;
            }[];
            tools: {
                name: string;
                description: string;
                request: {};
                response: string;
            }[];
            knowledge: {
                name: string;
                recall: string[];
            }[];
        };
    };

    '/xianfu/api-v2/dove/chat/try/interact': {
        method: 'POST';

        request: {
            conversationId: number;
            messageList: {
                type: number;
                content: string;
            }[];
        };

        response: {
            conversationId: number;
            messages: {
                type: number;
                content: string;
            }[];
            tools: {
                name: string;
                description: string;
                request: {};
                response: string;
            }[];
            knowledge: {
                name: string;
                recall: string[];
            }[];
        };
    };

    '/xianfu/api-v2/dove/chat/voice/query': {
        method: 'POST';

        request: {
            tenantId: string;
        };

        response: {
            /**  声音Id */
            id: string;
            /**  声音名称 */
            name: string;
            /**  性别 */
            gender: string;
            description: string;
        }[];
    };

    '/xianfu/api-v2/dove/data/export': {
        method: 'POST';

        request: {
            contactIdList: string[];
        };

        response: null;
    };

    '/xianfu/api-v2/dove/data/query': {
        method: 'POST';

        request: {
            bizId: number[];
            orgId: number[];
            taskId: number[];
            staffId: number;
            contactObjectType: number[];
            submitTimeMin: number;
            submitTimeMax: number;
            contactType: number[];
            reachStatus: number[];
            startTimeMin: number;
            startTimeMax: number;
            executionStatus: number[];
            rank: string[];
            page: number;
            pageSize: number;
        };

        response: {
            page: number;
            pageSize: number;
            total: number;
            data: {
                contactId: number;
                objectName: string;
                objectType: number;
                objectTypeName: string;
                contactType: number;
                bizId: number;
                submitTime: number;
                startTime: number;
                talkingTimeLen: number;
                rank: string;
                rankDesc: string;
                audio: string;
                releaseReasonMsg: string;
                staff: {
                    mis: string;
                    name: string;
                    id: number;
                };
                reachStatus: number;
                taskId: number;
                taskName: string;
                contactContent: string;
                contactImages: string[];
                massSendTitle: string;
                executionStatus: number;
                skillExecutionList: {
                    skillName: string;
                    executed: boolean;
                }[];
            }[];
        };
    };

    '/xianfu/api-v2/dove/object/type/query': {
        method: 'POST';

        request: {};

        response: {
            page: number;
            pageSize: number;
            total: number;
            data: {
                objectType: number;
                name: string;
                kpType: string[];
                contactType: number[];
                contactTag: number[];
            }[];
        };
    };

    '/xianfu/api-v2/dove/placeholder/active': {
        method: 'POST';

        request: {
            id: number;
        };

        response: null;
    };

    '/xianfu/api-v2/dove/placeholder/inactive': {
        method: 'POST';

        request: {
            id: number;
        };

        response: null;
    };

    '/xianfu/api-v2/dove/placeholder/query': {
        method: 'POST';

        request: {
            name: string;
            showName: string;
            status: number;
            objectType: number;
            page: number;
            pageSize: number;
        };

        response: {
            page: number;
            pageSize: number;
            total: number;
            data: {
                id: number;
                name: string;
                showName: string;
                status: number;
                objectType: number;
            }[];
        };
    };

    '/xianfu/api-v2/dove/task/cancel': {
        method: 'POST';

        request: {
            taskIdList: number[];
        };

        response: null;
    };

    '/xianfu/api-v2/dove/task/create': {
        method: 'POST';

        request: {
            taskName: string;
            taskType: string;
            bizId: number;
            poiDto: {
                contactObjectType: number;
                contactObjectIdList: number[];
                kpPriority: string[];
                contactTag: number;
            };
            channelDto: {
                contactType: number;
                agentId: number;
                contactContent: string;
                contactImages: string[];
                massSendTitle: string;
            };
            workStrategyDto: {
                type: number;
                startTime: number;
                endTime: number;
            };
        };

        response: number;
    };

    '/xianfu/api-v2/dove/task/search': {
        method: 'POST';

        request: {
            taskName: string;
            creatorUid: number[];
            taskType: string;
            taskStatus: number[];
            taskStartTimeMin: number;
            taskStartTimeMax: number;
            createTimeMin: number;
            createTimeMax: number;
            creatorOrgId: number[];
            page: number;
            pageSize: number;
        };

        response: {
            page: number;
            pageSize: number;
            total: number;
            data: {
                id: number;
                bizId: number;
                taskName: string;
                taskType: string;
                poiDto: {
                    contactObjectType: number;
                    contactObjectIdList: number[];
                    kpPriority: string[];
                    contactTag: number;
                    contactObjectTypeName: string;
                };
                channelDto: {
                    contactType: number;
                    agentId: number;
                    contactContent: string;
                    contactImages: string[];
                    massSendTitle: string;
                };
                taskWorkStrategy: {
                    type: number;
                    startTime: number;
                    endTime: number;
                };
                creator: {
                    mis: string;
                    name: string;
                    id: number;
                };
                createTime: number;
                updateTime: number;
                statisticDto: {
                    taskProgress: {
                        waitingCount: number;
                        doingCount: number;
                        doneCount: number;
                        cancelCount: number;
                    };
                    contactSuccessCount: number;
                    contactFailCount: number;
                    executionAvgTime: number;
                    successTag: {
                        tagCode: string;
                        tagName: string;
                        count: number;
                    }[];
                    failTag: {
                        tagName: string;
                        count: number;
                    }[];
                };
            }[];
        };
    };

    '/xianfu/api-v2/dove/task/statistic': {
        method: 'POST';

        request: {
            taskId: number;
        };

        response: {
            taskProgress: {
                waitingCount: number;
                doingCount: number;
                doneCount: number;
                cancelCount: number;
            };
            contactSuccessCount: number;
            contactFailCount: number;
            executionAvgTime: number;
            successTag: {
                tagCode: string;
                tagName: string;
                count: number;
            }[];
            failTag: {
                tagCode: string;
                tagName: string;
                count: number;
            }[];
        };
    };

    '/xianfu/api-v2/dove/task/type/search': {
        method: 'POST';

        request: {
            taskType: string;
        };

        response: string[];
    };

    '/xianfu/api/dove/data/text': {
        method: 'POST';
        request: Record<string, never>;

        response: null;
    };

    '/xianfu/api-v2/dove/agent/scene/list': {
        method: 'POST';
        request: {
            type: number;
        };
    };
};
