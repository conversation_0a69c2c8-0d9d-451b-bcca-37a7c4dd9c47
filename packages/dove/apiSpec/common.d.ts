interface CommonItem {
    childrenList: CommonItem[];
    id: number;
}

export type COMMON_API = {
    '/uicomponent/getLoginUser': {
        request: Record<string, any>;
        response: {
            id: number;
            login: string;
            name: string;
        };
    };
    '/wmcrm/v1/c/menu/daxiang-user-info': {
        request: {
            misIds: string[];
        };
        response: {
            avatarUrl: string[];
            imUid: number;
            misId: string;
            name: string;
            org: string;
            orgId: string;
            uid: number;
        }[];
    };
    '/uicomponent/api/orgs/getByPid': {
        request: {
            sources: string;
            parentId: string;
        };
        response: {
            isHq: boolean;
            list: CommonItem[];
        };
    };
    '/dovecall-web/configure/queryPoiType': {
        request: {
            bizId: number;
        };
        response: {
            list: { poiType: number; poiName: string }[];
        };
    };
    '/dovecall-web/agentCall/queryUserBiz': {
        request: Record<string, unknown>;
        response: {
            bizList: { bizId: number; bizName: string }[];

            chooseBiz: { bizId: number; bizName: string };
        };
    };
    '/uicomponent/employs': {
        request: {
            content: string;
            type: string;
        };
        response: {
            login: string;
            name: string;
            id: number;
        }[];
    };
};
